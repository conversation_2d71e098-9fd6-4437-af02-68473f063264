#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Alert Model for فوترها (Fawterha)
Represents an inventory alert in the system
"""

from datetime import datetime

class InventoryAlert:
    """Inventory alert model class."""
    
    # Alert types
    TYPE_LOW_STOCK = "low_stock"
    TYPE_EXPIRY = "expiry"
    TYPE_OUT_OF_STOCK = "out_of_stock"
    
    def __init__(self, id=None, product_id=None, warehouse_id=None,
                 alert_type=TYPE_LOW_STOCK, message="", is_read=False,
                 created_at=None):
        """Initialize an inventory alert object.
        
        Args:
            id (int, optional): Alert ID. Defaults to None.
            product_id (int, optional): Product ID. Defaults to None.
            warehouse_id (int, optional): Warehouse ID. Defaults to None.
            alert_type (str, optional): Alert type. Defaults to TYPE_LOW_STOCK.
            message (str, optional): Alert message. Defaults to "".
            is_read (bool, optional): Whether the alert has been read. Defaults to False.
            created_at (datetime, optional): Creation timestamp. Defaults to None.
        """
        self.id = id
        self.product_id = product_id
        self.warehouse_id = warehouse_id
        self.alert_type = alert_type
        self.message = message
        self.is_read = is_read
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_db_row(cls, row):
        """Create an InventoryAlert object from a database row.
        
        Args:
            row: Database row (sqlite3.Row)
            
        Returns:
            InventoryAlert: InventoryAlert object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)
            
        # Create the inventory alert object with all fields from the row
        inventory_alert = cls(
            id=row_dict.get('id'),
            product_id=row_dict.get('product_id'),
            warehouse_id=row_dict.get('warehouse_id'),
            alert_type=row_dict.get('alert_type', cls.TYPE_LOW_STOCK),
            message=row_dict.get('message', ''),
            is_read=bool(row_dict.get('is_read', False)),
            created_at=row_dict.get('created_at')
        )
        
        return inventory_alert
    
    def to_dict(self):
        """Convert the inventory alert object to a dictionary.
        
        Returns:
            dict: Dictionary representation of the inventory alert
        """
        return {
            'id': self.id,
            'product_id': self.product_id,
            'warehouse_id': self.warehouse_id,
            'alert_type': self.alert_type,
            'message': self.message,
            'is_read': self.is_read,
            'created_at': self.created_at
        }
    
    def mark_as_read(self):
        """Mark the alert as read."""
        self.is_read = True
    
    def __str__(self):
        """Return a string representation of the inventory alert.
        
        Returns:
            str: String representation
        """
        return f"{self.alert_type}: {self.message}"
