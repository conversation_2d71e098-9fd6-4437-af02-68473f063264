#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel Exporter for فوترها (Fawterha)
Exports data to Excel files
"""

import os
from datetime import datetime
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side


def export_report_to_excel(file_path, report_type, start_date, end_date, headers, rows, total_sales, invoice_count):
    """Export a report to an Excel file.

    Args:
        file_path (str): Path to save the Excel file
        report_type (str): Report type
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Report data rows
        total_sales (str): Total sales amount
        invoice_count (str): Invoice count
    """
    try:
        # Create a new workbook
        wb = openpyxl.Workbook()
        ws = wb.active

        # Set RTL direction
        ws.sheet_view.rightToLeft = True

        # Define styles
        title_font = Font(name='Arial', size=16, bold=True, color="0D47A1")  # Dark blue
        header_font = Font(name='Arial', size=12, bold=True, color="FFFFFF")  # White
        normal_font = Font(name='Arial', size=11)
        bold_font = Font(name='Arial', size=11, bold=True)
        amount_font = Font(name='Arial', size=11, bold=True, color="2E7D32")  # Green

        header_fill = PatternFill(start_color="1976D2", end_color="1976D2", fill_type="solid")  # Blue
        alt_row_fill = PatternFill(start_color="F5F5F5", end_color="F5F5F5", fill_type="solid")  # Light gray
        summary_fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")  # Light blue

        centered = Alignment(horizontal="center", vertical="center", wrap_text=True)
        right_aligned = Alignment(horizontal="right", vertical="center")

        thin_border = Border(
            left=Side(style='thin', color="BDBDBD"),
            right=Side(style='thin', color="BDBDBD"),
            top=Side(style='thin', color="BDBDBD"),
            bottom=Side(style='thin', color="BDBDBD")
        )

        # Insert rows at the top for the header
        ws.insert_rows(1, 4)

        # Add report header with improved styling
        ws['A1'] = f"تقرير {report_type}"
        ws['A1'].font = title_font
        ws['A1'].alignment = centered
        ws.merge_cells('A1:C1')

        ws['A2'] = f"الفترة: من {start_date} إلى {end_date}"
        ws['A2'].font = normal_font
        ws['A2'].alignment = centered
        ws.merge_cells('A2:C2')

        ws['A3'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"
        ws['A3'].font = normal_font
        ws['A3'].alignment = centered
        ws.merge_cells('A3:C3')

        # Style the headers (now at row 5)
        header_row = 5
        for col in range(1, len(headers) + 1):
            cell = ws.cell(row=header_row, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = centered
            cell.border = thin_border

        # Style the data rows
        for row_idx in range(header_row + 1, header_row + len(rows) + 1):
            # Apply alternating row colors
            row_fill = alt_row_fill if row_idx % 2 == 0 else None

            for col_idx in range(1, len(headers) + 1):
                cell = ws.cell(row=row_idx, column=col_idx)

                # Apply different styling based on column
                if col_idx == 1:  # First column (Month/Year/Customer)
                    cell.font = bold_font
                    cell.alignment = centered
                elif col_idx == 2:  # Second column (Invoice count)
                    cell.font = normal_font
                    cell.alignment = centered
                elif col_idx == 3:  # Third column (Amount)
                    cell.font = amount_font
                    cell.alignment = right_aligned

                    # Try to extract numeric value from amount string
                    try:
                        # Extract numeric part from strings like "1234.56 ر.س"
                        if isinstance(cell.value, str) and "ر.س" in cell.value:
                            numeric_part = cell.value.replace("ر.س", "").strip()
                            cell.value = float(numeric_part)
                            cell.number_format = '#,##0.00 "ر.س"'
                    except:
                        # If conversion fails, keep as string
                        pass

                if row_fill:
                    cell.fill = row_fill
                cell.border = thin_border

        # Add summary with improved styling
        summary_row = header_row + len(rows) + 2

        # Summary header
        summary_cell = ws.cell(row=summary_row - 1, column=1, value="ملخص التقرير")
        summary_cell.font = title_font
        summary_cell.alignment = centered
        ws.merge_cells(f'A{summary_row - 1}:C{summary_row - 1}')

        # Total sales
        total_label = ws.cell(row=summary_row, column=1, value="إجمالي المبيعات:")
        total_label.font = bold_font
        total_label.alignment = right_aligned

        total_value = ws.cell(row=summary_row, column=2, value=total_sales)
        total_value.font = amount_font
        total_value.alignment = right_aligned

        # Try to extract numeric value from total sales string
        try:
            if isinstance(total_sales, str) and "ر.س" in total_sales:
                numeric_part = total_sales.replace("ر.س", "").strip()
                total_value.value = float(numeric_part)
                total_value.number_format = '#,##0.00 "ر.س"'
        except:
            pass

        # Invoice count
        count_label = ws.cell(row=summary_row + 1, column=1, value="عدد الفواتير:")
        count_label.font = bold_font
        count_label.alignment = right_aligned

        count_value = ws.cell(row=summary_row + 1, column=2, value=invoice_count)
        count_value.font = bold_font
        count_value.alignment = right_aligned

        # Apply fill to summary section
        for row in range(summary_row - 1, summary_row + 2):
            for col in range(1, 4):
                cell = ws.cell(row=row, column=col)
                cell.fill = summary_fill
                cell.border = thin_border

        # Auto-adjust column widths
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column].width = adjusted_width

        # Save the workbook to the final file path
        wb.save(file_path)

        return True
    except Exception as e:
        print(f"Error exporting to Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def export_invoice_to_excel(file_path, invoice, customer, items, company_info):
    """Export an invoice to an Excel file.

    Args:
        file_path (str): Path to save the Excel file
        invoice (Invoice): Invoice object
        customer (Customer): Customer object
        items (list): List of invoice items
        company_info (dict): Company information
    """
    # Create a new workbook
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = f"فاتورة {invoice.invoice_number}"

    # Set RTL direction
    ws.sheet_view.rightToLeft = True

    # Add company information
    ws['A1'] = company_info.get('company_name', '')
    ws['A1'].font = Font(size=16, bold=True)
    ws.merge_cells('A1:D1')

    ws['A2'] = company_info.get('company_address', '')
    ws.merge_cells('A2:D2')

    ws['A3'] = company_info.get('company_phone', '')
    ws.merge_cells('A3:D3')

    ws['A4'] = company_info.get('company_email', '')
    ws.merge_cells('A4:D4')

    # Add invoice header
    ws['A6'] = f"فاتورة رقم: {invoice.invoice_number}"
    ws['A6'].font = Font(size=14, bold=True)
    ws.merge_cells('A6:D6')

    ws['A7'] = f"التاريخ: {invoice.issue_date.strftime('%Y-%m-%d')}"
    ws.merge_cells('A7:D7')

    if invoice.due_date:
        ws['A8'] = f"تاريخ الاستحقاق: {invoice.due_date.strftime('%Y-%m-%d')}"
        ws.merge_cells('A8:D8')

    # Add customer information
    ws['A10'] = "معلومات العميل:"
    ws['A10'].font = Font(bold=True)
    ws.merge_cells('A10:D10')

    ws['A11'] = f"الاسم: {customer.name}"
    ws.merge_cells('A11:D11')

    if customer.email:
        ws['A12'] = f"البريد الإلكتروني: {customer.email}"
        ws.merge_cells('A12:D12')

    if customer.phone:
        ws['A13'] = f"الهاتف: {customer.phone}"
        ws.merge_cells('A13:D13')

    if customer.address:
        ws['A14'] = f"العنوان: {customer.address}"
        ws.merge_cells('A14:D14')

    # Add items table
    ws['A16'] = "المنتجات والخدمات:"
    ws['A16'].font = Font(bold=True)
    ws.merge_cells('A16:F16')

    # Add headers
    headers = ["الوصف", "الكمية", "سعر الوحدة", "الخصم", "الضريبة", "الإجمالي"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=17, column=col, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="DDDDDD", end_color="DDDDDD", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")

    # Add items
    for row_idx, item in enumerate(items, 18):
        ws.cell(row=row_idx, column=1, value=item.description)
        ws.cell(row=row_idx, column=2, value=item.quantity)
        ws.cell(row=row_idx, column=3, value=item.unit_price)
        ws.cell(row=row_idx, column=4, value=item.discount)
        ws.cell(row=row_idx, column=5, value=item.tax)
        ws.cell(row=row_idx, column=6, value=item.total)

    # Add totals
    totals_row = 18 + len(items) + 1

    ws.cell(row=totals_row, column=5, value="المجموع الفرعي:")
    ws.cell(row=totals_row, column=5).alignment = Alignment(horizontal="right")
    ws.cell(row=totals_row, column=6, value=invoice.subtotal)

    ws.cell(row=totals_row + 1, column=5, value="الخصم:")
    ws.cell(row=totals_row + 1, column=5).alignment = Alignment(horizontal="right")
    ws.cell(row=totals_row + 1, column=6, value=invoice.discount)

    ws.cell(row=totals_row + 2, column=5, value="الضريبة:")
    ws.cell(row=totals_row + 2, column=5).alignment = Alignment(horizontal="right")
    ws.cell(row=totals_row + 2, column=6, value=invoice.tax)

    ws.cell(row=totals_row + 3, column=5, value="الإجمالي:")
    ws.cell(row=totals_row + 3, column=5).alignment = Alignment(horizontal="right")
    ws.cell(row=totals_row + 3, column=5).font = Font(bold=True)
    ws.cell(row=totals_row + 3, column=6, value=invoice.total)
    ws.cell(row=totals_row + 3, column=6).font = Font(bold=True)

    # Add notes if available
    if invoice.notes:
        notes_row = totals_row + 5
        ws.cell(row=notes_row, column=1, value="ملاحظات:")
        ws.cell(row=notes_row, column=1).font = Font(bold=True)
        ws.merge_cells(f'A{notes_row}:F{notes_row}')

        ws.cell(row=notes_row + 1, column=1, value=invoice.notes)
        ws.merge_cells(f'A{notes_row + 1}:F{notes_row + 1}')

    # Auto-adjust column widths
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            if cell.value:
                max_length = max(max_length, len(str(cell.value)))
        adjusted_width = (max_length + 2) * 1.2
        ws.column_dimensions[column].width = adjusted_width

    # Save the workbook
    wb.save(file_path)


def export_to_excel(file_path, title, headers, data):
    """Export data to an Excel file.

    Args:
        file_path (str): Path to save the Excel file
        title (str): Report title
        headers (list): Column headers
        data (list): Data rows

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create a new workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = title

        # Set RTL direction
        ws.sheet_view.rightToLeft = True

        # Define styles
        title_font = Font(name='Arial', size=16, bold=True, color="0D47A1")  # Dark blue
        header_font = Font(name='Arial', size=12, bold=True, color="FFFFFF")  # White
        normal_font = Font(name='Arial', size=11)

        header_fill = PatternFill(start_color="0D47A1", end_color="0D47A1", fill_type="solid")  # Dark blue
        alt_row_fill = PatternFill(start_color="F5F5F5", end_color="F5F5F5", fill_type="solid")  # Light grey

        centered = Alignment(horizontal='center', vertical='center')
        right_aligned = Alignment(horizontal='right', vertical='center')

        thin_border = Border(
            left=Side(style='thin', color="BDBDBD"),
            right=Side(style='thin', color="BDBDBD"),
            top=Side(style='thin', color="BDBDBD"),
            bottom=Side(style='thin', color="BDBDBD")
        )

        # Add title
        ws['A1'] = title
        ws['A1'].font = title_font
        ws['A1'].alignment = centered
        ws.merge_cells(f'A1:{openpyxl.utils.get_column_letter(len(headers))}1')

        # Add date
        ws['A2'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"
        ws['A2'].font = normal_font
        ws['A2'].alignment = centered
        ws.merge_cells(f'A2:{openpyxl.utils.get_column_letter(len(headers))}2')

        # Add headers
        header_row = 4
        for col_idx, header_text in enumerate(headers, 1):
            cell = ws.cell(row=header_row, column=col_idx, value=header_text)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = centered
            cell.border = thin_border

        # Add data
        for row_idx, row_data in enumerate(data, header_row + 1):
            # Apply alternating row fill
            row_fill = alt_row_fill if row_idx % 2 == 0 else None

            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)
                cell.font = normal_font

                # Apply alignment based on data type
                if isinstance(value, (int, float)):
                    cell.alignment = right_aligned
                else:
                    cell.alignment = centered

                if row_fill:
                    cell.fill = row_fill
                cell.border = thin_border

        # Auto-adjust column widths
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column].width = adjusted_width

        # Save the workbook
        wb.save(file_path)

        return True
    except Exception as e:
        print(f"Error exporting to Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def export_customer_statement_to_excel(file_path, customer, start_date, end_date, headers, rows, total_invoices, total_paid, balance):
    """Export a customer statement to an Excel file.

    Args:
        file_path (str): Path to save the Excel file
        customer (Customer): Customer object
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Statement data rows
        total_invoices (str): Total invoices amount
        total_paid (str): Total paid amount
        balance (str): Current balance
    """
    try:
        # Create a new workbook
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = f"كشف حساب {customer.name}"

        # Set RTL direction
        ws.sheet_view.rightToLeft = True

        # Define styles
        title_font = Font(name='Arial', size=16, bold=True, color="0D47A1")  # Dark blue
        subtitle_font = Font(name='Arial', size=14, bold=True, color="0D47A1")  # Dark blue
        header_font = Font(name='Arial', size=12, bold=True, color="FFFFFF")  # White
        normal_font = Font(name='Arial', size=11)
        bold_font = Font(name='Arial', size=11, bold=True)
        debit_font = Font(name='Arial', size=11, bold=True, color="B71C1C")  # Dark red
        credit_font = Font(name='Arial', size=11, bold=True, color="2E7D32")  # Dark green

        centered = Alignment(horizontal='center', vertical='center')
        right_aligned = Alignment(horizontal='right', vertical='center')
        left_aligned = Alignment(horizontal='left', vertical='center')

        header_fill = PatternFill(start_color="0D47A1", end_color="0D47A1", fill_type="solid")  # Dark blue
        alt_row_fill = PatternFill(start_color="F5F5F5", end_color="F5F5F5", fill_type="solid")  # Light grey
        summary_fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")  # Light blue

        thin_border = Border(
            left=Side(style='thin', color="BDBDBD"),
            right=Side(style='thin', color="BDBDBD"),
            top=Side(style='thin', color="BDBDBD"),
            bottom=Side(style='thin', color="BDBDBD")
        )

        # Set column widths
        for col_idx, header in enumerate(headers, 1):
            col_letter = openpyxl.utils.get_column_letter(col_idx)
            ws.column_dimensions[col_letter].width = 20

        # Insert rows at the top for the header
        ws.insert_rows(1, 6)

        # Add statement header with improved styling
        ws['A1'] = "كشف حساب العميل"
        ws['A1'].font = title_font
        ws['A1'].alignment = centered
        ws.merge_cells('A1:G1')

        ws['A2'] = f"العميل: {customer.name}"
        ws['A2'].font = subtitle_font
        ws['A2'].alignment = centered
        ws.merge_cells('A2:G2')

        ws['A3'] = f"الفترة: من {start_date} إلى {end_date}"
        ws['A3'].font = normal_font
        ws['A3'].alignment = centered
        ws.merge_cells('A3:G3')

        ws['A4'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"
        ws['A4'].font = normal_font
        ws['A4'].alignment = centered
        ws.merge_cells('A4:G4')

        # Add customer details if available
        customer_details = []
        if customer.email:
            customer_details.append(f"البريد الإلكتروني: {customer.email}")
        if customer.phone:
            customer_details.append(f"الهاتف: {customer.phone}")
        if customer.address:
            customer_details.append(f"العنوان: {customer.address}")

        if customer_details:
            ws['A5'] = " | ".join(customer_details)
            ws['A5'].font = normal_font
            ws['A5'].alignment = centered
            ws.merge_cells('A5:G5')

        # Style the headers (now at row 7)
        header_row = 7
        for col_idx, header_text in enumerate(headers, 1):
            cell = ws.cell(row=header_row, column=col_idx, value=header_text)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = centered
            cell.border = thin_border

        # Add data rows
        for row_idx, row_data in enumerate(rows, header_row + 1):
            # Apply alternating row fill
            row_fill = alt_row_fill if row_idx % 2 == 0 else None

            for col_idx, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_idx, column=col_idx, value=value)

                # Apply different styling based on column
                if col_idx <= 3:  # Date, Reference, Type
                    cell.font = normal_font
                    cell.alignment = centered
                elif col_idx == 4:  # Description
                    cell.font = normal_font
                    cell.alignment = right_aligned
                elif col_idx == 5:  # Debit
                    cell.font = debit_font if value else normal_font
                    cell.alignment = left_aligned
                elif col_idx == 6:  # Credit
                    cell.font = credit_font if value else normal_font
                    cell.alignment = left_aligned
                elif col_idx == 7:  # Balance
                    cell.font = bold_font
                    cell.alignment = left_aligned

                if row_fill:
                    cell.fill = row_fill
                cell.border = thin_border

        # Add summary with improved styling
        summary_row = header_row + len(rows) + 2

        # Summary header
        summary_cell = ws.cell(row=summary_row - 1, column=1, value="ملخص الحساب")
        summary_cell.font = subtitle_font
        summary_cell.alignment = centered
        ws.merge_cells(f'A{summary_row - 1}:G{summary_row - 1}')

        # Total invoices
        total_invoices_label = ws.cell(row=summary_row, column=1, value="إجمالي الفواتير:")
        total_invoices_label.font = bold_font
        total_invoices_label.alignment = right_aligned

        total_invoices_value = ws.cell(row=summary_row, column=2, value=total_invoices)
        total_invoices_value.font = debit_font
        total_invoices_value.alignment = left_aligned

        # Total paid
        total_paid_label = ws.cell(row=summary_row + 1, column=1, value="إجمالي المدفوعات:")
        total_paid_label.font = bold_font
        total_paid_label.alignment = right_aligned

        total_paid_value = ws.cell(row=summary_row + 1, column=2, value=total_paid)
        total_paid_value.font = credit_font
        total_paid_value.alignment = left_aligned

        # Balance
        balance_label = ws.cell(row=summary_row + 2, column=1, value="الرصيد الحالي:")
        balance_label.font = bold_font
        balance_label.alignment = right_aligned

        balance_value = ws.cell(row=summary_row + 2, column=2, value=balance)
        balance_value.font = bold_font
        balance_value.alignment = left_aligned

        # Apply fill to summary section
        for row in range(summary_row - 1, summary_row + 3):
            for col in range(1, 3):
                cell = ws.cell(row=row, column=col)
                cell.fill = summary_fill
                cell.border = thin_border

        # Auto-adjust column widths
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column].width = adjusted_width

        # Save the workbook to the final file path
        wb.save(file_path)

        return True
    except Exception as e:
        print(f"Error exporting to Excel: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
