#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Schema for فوترها (<PERSON>aw<PERSON><PERSON>)
Defines the database tables for the Point of Sale system
"""

def create_pos_tables(connection):
    """Create POS database tables if they don't exist.

    Args:
        connection: SQLite database connection
    """
    cursor = connection.cursor()

    # Categories table is now created in the main schema.py file
    # to ensure it's created before the products table

    # Create users table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        password TEXT NOT NULL,
        name TEXT NOT NULL,
        role TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create activity logs table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS activity_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        username TEXT,
        activity_type TEXT NOT NULL,
        entity_type TEXT,
        entity_id INTEGER,
        description TEXT,
        details TEXT,
        ip_address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE SET NULL
    )
    ''')

    # Create pos_sessions table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS pos_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        end_time TIMESTAMP,
        starting_cash REAL DEFAULT 0,
        ending_cash REAL DEFAULT 0,
        status TEXT DEFAULT 'open',
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
    )
    ''')

    # Create pos_transactions table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS pos_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        session_id INTEGER NOT NULL,
        invoice_id INTEGER,
        transaction_type TEXT NOT NULL,
        amount REAL NOT NULL,
        payment_method TEXT NOT NULL,
        reference TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (session_id) REFERENCES pos_sessions (id),
        FOREIGN KEY (invoice_id) REFERENCES invoices (id)
    )
    ''')

    # Create pos_payment_methods table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS pos_payment_methods (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        is_active INTEGER DEFAULT 1,
        requires_reference INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create pos_settings table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS pos_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create pos_inventory table for POS-specific inventory
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS pos_inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        location TEXT,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
    )
    ''')

    # Create pos_inventory_transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS pos_inventory_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pos_inventory_id INTEGER NOT NULL,
        transaction_type TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (pos_inventory_id) REFERENCES pos_inventory (id) ON DELETE CASCADE
    )
    ''')

    # Check if products table has barcode column
    cursor.execute("PRAGMA table_info(products)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]

    # Add barcode column if it doesn't exist
    if 'barcode' not in column_names:
        cursor.execute('''
        ALTER TABLE products ADD COLUMN barcode TEXT
        ''')

    # Insert default payment methods if they don't exist
    cursor.execute('''
    SELECT COUNT(*) FROM pos_payment_methods
    ''')
    payment_methods_count = cursor.fetchone()[0]

    if payment_methods_count == 0:
        cursor.execute('''
        INSERT INTO pos_payment_methods (name, is_active, requires_reference)
        VALUES
        ('نقدي', 1, 0),
        ('بطاقة ائتمان', 1, 1),
        ('تحويل بنكي', 1, 1)
        ''')

    # Insert default settings if they don't exist
    cursor.execute('''
    SELECT COUNT(*) FROM pos_settings
    ''')
    settings_count = cursor.fetchone()[0]

    if settings_count == 0:
        cursor.execute('''
        INSERT INTO pos_settings (key, value)
        VALUES
        ('receipt_header', 'فوترها - نظام نقاط البيع'),
        ('receipt_footer', 'شكراً لتسوقكم معنا'),
        ('thermal_printer_name', ''),
        ('cash_drawer_command', ''),
        ('barcode_scanner_enabled', '1'),
        ('default_payment_method', '1')
        ''')

    # Default categories are now inserted in the main schema.py file

    # Insert default admin user if no users exist
    cursor.execute('''
    SELECT COUNT(*) FROM users
    ''')
    users_count = cursor.fetchone()[0]

    if users_count == 0:
        # Default password is 'admin' - in production this should be hashed
        cursor.execute('''
        INSERT INTO users (username, password, name, role)
        VALUES ('admin', 'admin', 'مدير النظام', 'admin')
        ''')

    # Commit the changes
    connection.commit()
