#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Formatting utilities for فوترها (Fawterha)
Provides functions for formatting currency, dates, etc.
"""

from datetime import datetime

def format_currency(amount, currency_code="EGP"):
    """Format a currency amount.
    
    Args:
        amount (float): Amount to format
        currency_code (str, optional): Currency code. Defaults to "EGP".
        
    Returns:
        str: Formatted currency string
    """
    if currency_code == "EGP":
        return f"{amount:.2f} ج.م"
    elif currency_code == "USD":
        return f"${amount:.2f}"
    elif currency_code == "EUR":
        return f"€{amount:.2f}"
    elif currency_code == "SAR":
        return f"{amount:.2f} ر.س"
    elif currency_code == "AED":
        return f"{amount:.2f} د.إ"
    else:
        return f"{amount:.2f} {currency_code}"
        
def format_date(date_str, format_str="%Y-%m-%d %H:%M:%S"):
    """Format a date string.
    
    Args:
        date_str (str): Date string to format
        format_str (str, optional): Format string. Defaults to "%Y-%m-%d %H:%M:%S".
        
    Returns:
        str: Formatted date string
    """
    if not date_str:
        return ""
        
    try:
        # Try to parse the date string
        if isinstance(date_str, str):
            # SQLite date format
            date_obj = datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        else:
            # Already a datetime object
            date_obj = date_str
            
        # Format the date
        return date_obj.strftime("%Y-%m-%d %H:%M:%S")
    except Exception:
        # Return the original string if parsing fails
        return date_str
