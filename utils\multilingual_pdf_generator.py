#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Multilingual PDF Generator for فوترها (Fawterha)
Generates PDF files with proper multilingual text support
"""

import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Import Arabic text handling libraries
import arabic_reshaper
from bidi.algorithm import get_display

# Import translation manager
from utils.translation_manager import get_translation_manager, tr


# Register fonts for different languages
def setup_fonts():
    """Set up fonts for different languages."""
    try:
        # Register Arabic fonts
        fonts_registered = []
        
        # Try to register Tahoma (good for Arabic and Latin scripts)
        try:
            pdfmetrics.registerFont(TTFont('Tahoma', 'C:/Windows/Fonts/tahoma.ttf'))
            pdfmetrics.registerFont(TTFont('Tahoma-Bold', 'C:/Windows/Fonts/tahomabd.ttf'))
            fonts_registered.extend(['Tahoma', 'Tahoma-Bold'])
        except:
            print("Could not register Tahoma font")
        
        # Try to register Arabic Typesetting
        try:
            pdfmetrics.registerFont(TTFont('Arabic Typesetting', 'C:/Windows/Fonts/arabtype.ttf'))
            fonts_registered.append('Arabic Typesetting')
        except:
            print("Could not register Arabic Typesetting font")
        
        # Try to register Traditional Arabic
        try:
            pdfmetrics.registerFont(TTFont('Traditional Arabic', 'C:/Windows/Fonts/trado.ttf'))
            fonts_registered.append('Traditional Arabic')
        except:
            print("Could not register Traditional Arabic font")
        
        # Try to register Simplified Arabic
        try:
            pdfmetrics.registerFont(TTFont('Simplified Arabic', 'C:/Windows/Fonts/simpo.ttf'))
            fonts_registered.append('Simplified Arabic')
        except:
            print("Could not register Simplified Arabic font")
        
        # Try to register Arial (fallback for Latin scripts)
        try:
            pdfmetrics.registerFont(TTFont('Arial', 'C:/Windows/Fonts/arial.ttf'))
            fonts_registered.append('Arial')
        except:
            print("Could not register Arial font")
        
        # Try to register Almarai (if available)
        almarai_fonts = {
            'Almarai-Regular': 'resources/fonts/Almarai-Regular.ttf',
            'Almarai-Bold': 'resources/fonts/Almarai-Bold.ttf',
            'Almarai-Light': 'resources/fonts/Almarai-Light.ttf',
            'Almarai-ExtraBold': 'resources/fonts/Almarai-ExtraBold.ttf'
        }
        
        for font_name, font_path in almarai_fonts.items():
            if os.path.exists(font_path):
                try:
                    pdfmetrics.registerFont(TTFont(font_name, font_path))
                    fonts_registered.append(font_name)
                except:
                    print(f"Could not register {font_name} font")
        
        # Print registered fonts
        for font in fonts_registered:
            print(f"Registered {font} font")
        
        return len(fonts_registered) > 0
    except Exception as e:
        print(f"Error setting up fonts: {str(e)}")
        return False


# Function to fix Arabic text for proper display
def fix_arabic_text(text):
    """Fix Arabic text for proper display in PDF using arabic_reshaper and python-bidi.
    
    Args:
        text (str): Text that may contain Arabic characters
        
    Returns:
        str: Fixed text ready for PDF display
    """
    if not text:
        return ""
    
    try:
        # Check if text contains Arabic characters
        if any('\u0600' <= c <= '\u06FF' for c in text):
            # Reshape Arabic text
            reshaped_text = arabic_reshaper.reshape(text)
            # Apply bidirectional algorithm
            bidi_text = get_display(reshaped_text)
            return bidi_text
        else:
            # Return as is for non-Arabic text
            return text
    except Exception as e:
        print(f"Error fixing Arabic text: {str(e)}")
        return text  # Return original text if there's an error


# Function to process text based on language
def process_text(text, language_code=None):
    """Process text based on language.
    
    Args:
        text (str): Text to process
        language_code (str, optional): Language code. If None, uses current language.
        
    Returns:
        str: Processed text ready for PDF display
    """
    if not text:
        return ""
    
    # Get current language if not specified
    if language_code is None:
        translation_manager = get_translation_manager()
        language_code = translation_manager.current_language
    
    # Process based on language
    if language_code == 'ar':
        return fix_arabic_text(text)
    else:
        return text


# Generate PDF for customer statement
def generate_customer_statement_pdf(file_path, customer, start_date, end_date, headers, rows, total_invoices, total_paid, balance, language_code=None):
    """Generate a PDF file for a customer statement.
    
    Args:
        file_path (str): Path to save the PDF file
        customer: Customer object
        start_date (str): Start date of the statement
        end_date (str): End date of the statement
        headers (list): List of column headers
        rows (list): List of rows (each row is a list of cells)
        total_invoices (str): Total invoices amount
        total_paid (str): Total paid amount
        balance (str): Balance amount
        language_code (str, optional): Language code. If None, uses current language.
    """
    # Set up fonts
    setup_fonts()
    
    # Get current language if not specified
    if language_code is None:
        translation_manager = get_translation_manager()
        language_code = translation_manager.current_language
    
    # Create PDF document
    doc = SimpleDocTemplate(
        file_path,
        pagesize=A4,
        rightMargin=1.5*cm,
        leftMargin=1.5*cm,
        topMargin=2*cm,
        bottomMargin=2*cm
    )
    
    # Create elements list
    elements = []
    
    # Define styles
    styles = getSampleStyleSheet()
    
    # Choose font based on language
    if language_code == 'ar':
        font_name = 'Tahoma'  # Good for Arabic
    else:
        font_name = 'Arial'  # Good for Latin scripts
    
    # Create custom styles
    arabic_title_style = ParagraphStyle(
        'ArabicTitle',
        parent=styles['Title'],
        fontName=font_name,
        fontSize=18,
        alignment=1 if language_code != 'ar' else 2,  # 1=center for LTR, 2=right for RTL
        leading=24
    )
    
    arabic_heading_style = ParagraphStyle(
        'ArabicHeading',
        parent=styles['Heading2'],
        fontName=font_name,
        fontSize=14,
        alignment=0 if language_code != 'ar' else 2,  # 0=left for LTR, 2=right for RTL
        leading=18
    )
    
    arabic_normal_style = ParagraphStyle(
        'ArabicNormal',
        parent=styles['Normal'],
        fontName=font_name,
        fontSize=12,
        alignment=0 if language_code != 'ar' else 2,  # 0=left for LTR, 2=right for RTL
        leading=16
    )
    
    # Add title
    title_text = tr('customers.customer_statement', 'Customer Statement')
    elements.append(Paragraph(process_text(title_text, language_code), arabic_title_style))
    elements.append(Spacer(1, 0.5*cm))
    
    # Add customer information
    customer_label = tr('customers.customer_name', 'Customer')
    elements.append(Paragraph(process_text(f"{customer_label}: {customer.name}", language_code), arabic_heading_style))
    elements.append(Spacer(1, 0.2*cm))
    
    period_label = tr('common.from', 'From')
    to_label = tr('common.to', 'To')
    elements.append(Paragraph(process_text(f"{period_label} {start_date} {to_label} {end_date}", language_code), arabic_normal_style))
    
    report_date_label = tr('reports.report_period', 'Report Date')
    elements.append(Paragraph(process_text(f"{report_date_label}: {datetime.now().strftime('%Y-%m-%d')}", language_code), arabic_normal_style))
    
    if customer.email:
        email_label = tr('customers.customer_email', 'Email')
        elements.append(Paragraph(process_text(f"{email_label}: {customer.email}", language_code), arabic_normal_style))
    
    if customer.phone:
        phone_label = tr('customers.customer_phone', 'Phone')
        elements.append(Paragraph(process_text(f"{phone_label}: {customer.phone}", language_code), arabic_normal_style))
    
    if customer.address:
        address_label = tr('customers.customer_address', 'Address')
        elements.append(Paragraph(process_text(f"{address_label}: {customer.address}", language_code), arabic_normal_style))
    
    elements.append(Spacer(1, 0.5*cm))
    
    # Prepare table data
    # Process headers based on language
    processed_headers = [process_text(header, language_code) for header in headers]
    table_data = [processed_headers]
    
    # Process rows based on language
    for row in rows:
        processed_row = []
        for cell in row:
            if isinstance(cell, str):
                processed_row.append(process_text(cell, language_code))
            else:
                processed_row.append(cell)
        table_data.append(processed_row)
    
    # Create table
    table = Table(table_data, repeatRows=1)
    
    # Define table style
    table_style = TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), font_name),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 1), (-1, -1), font_name),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ])
    
    # Apply table style
    table.setStyle(table_style)
    
    # Add table to elements
    elements.append(table)
    elements.append(Spacer(1, 0.5*cm))
    
    # Add summary
    total_invoices_label = tr('reports.total_sales', 'Total Invoices')
    total_paid_label = tr('reports.total_paid', 'Total Paid')
    balance_label = tr('reports.total_remaining', 'Balance')
    
    elements.append(Paragraph(process_text(f"{total_invoices_label}: {total_invoices}", language_code), arabic_heading_style))
    elements.append(Paragraph(process_text(f"{total_paid_label}: {total_paid}", language_code), arabic_heading_style))
    elements.append(Paragraph(process_text(f"{balance_label}: {balance}", language_code), arabic_heading_style))
    
    # Add footer
    elements.append(Spacer(1, 1*cm))
    
    app_name = tr('app_name', 'Fawterha')
    footer_text = tr('messages.generated_by', 'Generated by') + f" {app_name} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
    elements.append(Paragraph(process_text(footer_text, language_code), arabic_normal_style))
    
    # Build the PDF
    doc.build(elements)
