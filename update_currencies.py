#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Update Currencies Script for فوترها (Fawterha)
Updates currency exchange rates in the database
"""

import os
import sqlite3

def update_currency_rates(db_path):
    """Update currency exchange rates in a specific database.

    Args:
        db_path (str): Path to the database file
    """
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Start transaction
        cursor.execute("BEGIN IMMEDIATE TRANSACTION")

        try:
            # Update currency exchange rates
            updated_currencies = [
                ('EGP', 1.0),      # Primary currency
                ('USD', 50.6),     # 1 USD = 50.6 EGP (متوسط سعر البنك المركزي)
                ('EUR', 56.9),     # 1 EUR = 56.9 EGP (متوسط سعر البنك المركزي)
                ('SAR', 13.65),    # 1 SAR = 13.65 EGP (متوسط سعر البنك المركزي)
                ('AED', 13.55)     # 1 AED = 13.55 EGP (متوسط سعر السوق)
            ]

            for code, rate in updated_currencies:
                # Check if currency exists
                cursor.execute("SELECT id FROM currencies WHERE code = ?", (code,))
                row = cursor.fetchone()

                if row:
                    # Update existing currency
                    cursor.execute("""
                    UPDATE currencies
                    SET exchange_rate = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE code = ?
                    """, (rate, code))
                    print(f"Updated {code} exchange rate to {rate}")
                else:
                    # Add new currency
                    is_primary = 1 if code == 'EGP' else 0
                    name = get_currency_name(code)
                    symbol = get_currency_symbol(code)

                    cursor.execute("""
                    INSERT INTO currencies (code, name, symbol, exchange_rate, is_primary, is_active)
                    VALUES (?, ?, ?, ?, ?, 1)
                    """, (code, name, symbol, rate, is_primary))
                    print(f"Added new currency {code} with exchange rate {rate}")

            # Make sure EGP is set as primary
            cursor.execute("""
            UPDATE currencies
            SET is_primary = 1, exchange_rate = 1.0
            WHERE code = 'EGP'
            """)

            # Make sure other currencies are not primary
            cursor.execute("""
            UPDATE currencies
            SET is_primary = 0
            WHERE code != 'EGP'
            """)

            # Commit transaction
            conn.execute("COMMIT")
            print("Currency exchange rates updated successfully!")

        except Exception as e:
            # Rollback transaction on error
            conn.execute("ROLLBACK")
            print(f"Error updating currency exchange rates: {str(e)}")
        finally:
            # Close connection
            conn.close()

    except Exception as e:
        print(f"Error: {str(e)}")

def update_currencies():
    """Update currency exchange rates in all available databases."""
    # Try all possible database paths
    if os.path.exists("fawterha.db"):
        db_path = "fawterha.db"
        print(f"Using database at {db_path}")
        update_currency_rates(db_path)

    if os.path.exists("database.db"):
        db_path = "database.db"
        print(f"Using database at {db_path}")
        update_currency_rates(db_path)

    # Then try user's documents folder
    documents_path = os.path.expanduser("~/Documents/Fawterha")
    db_path = os.path.join(documents_path, "fawterha.db")

    if os.path.exists(db_path):
        print(f"Using database at {db_path}")
        update_currency_rates(db_path)

def get_currency_name(code):
    """Get the currency name for a currency code.

    Args:
        code (str): Currency code

    Returns:
        str: Currency name
    """
    currency_names = {
        'EGP': "الجنيه المصري",
        'USD': "الدولار الأمريكي",
        'EUR': "اليورو",
        'SAR': "الريال السعودي",
        'AED': "الدرهم الإماراتي",
        'GBP': "الجنيه الإسترليني",
        'KWD': "الدينار الكويتي",
        'QAR': "الريال القطري",
        'BHD': "الدينار البحريني",
        'OMR': "الريال العماني"
    }
    return currency_names.get(code, code)

def get_currency_symbol(code):
    """Get the currency symbol for a currency code.

    Args:
        code (str): Currency code

    Returns:
        str: Currency symbol
    """
    currency_symbols = {
        'EGP': "ج.م",
        'USD': "$",
        'EUR': "€",
        'SAR': "ر.س",
        'AED': "د.إ",
        'GBP': "£",
        'KWD': "د.ك",
        'QAR': "ر.ق",
        'BHD': "د.ب",
        'OMR': "ر.ع"
    }
    return currency_symbols.get(code, code)

if __name__ == "__main__":
    update_currencies()
