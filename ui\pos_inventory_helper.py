#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory Helper UI for فوترها (Fawterha)
Provides utility functions for POS inventory UI management
"""

from PySide6.QtWidgets import QMessageBox
from utils.inventory_helper import get_inventory_manager
from ui.pos_inventory_view import POSInventoryView
from utils.translation_manager import tr, get_translation_manager

def initialize_pos_inventory_view(db_manager, pos_manager, parent=None):
    """Initialize the POS inventory view safely with error handling.

    Args:
        db_manager: Database manager instance
        pos_manager: POS manager instance
        parent: Parent widget for the inventory view

    Returns:
        POSInventoryView or None: Inventory view instance or None if initialization fails
    """
    # Connect to translation manager to ensure translations are updated
    translation_manager = get_translation_manager()

    # Set up a handler to update the inventory view when language changes
    def update_translations_on_language_change(language_code):
        # Find all inventory views and update their translations
        if parent and hasattr(parent, "pos_inventory_view") and parent.pos_inventory_view:
            if hasattr(parent.pos_inventory_view, "update_translations"):
                parent.pos_inventory_view.update_translations()

    # Connect the handler to the language_changed signal
    translation_manager.language_changed.connect(update_translations_on_language_change)
    try:
        # Check if inventory manager can be initialized
        inventory_manager = get_inventory_manager(db_manager, show_error=False)

        if not inventory_manager:
            # Show a warning but continue with initialization
            print("Warning: Inventory manager initialization failed, POS inventory view may have limited functionality")
            if parent:
                QMessageBox.warning(
                    parent,
                    tr("errors.warning", "تحذير"),
                    tr("inventory.manager_init_failed", "لم يتم تهيئة مدير المخزون بشكل صحيح. قد تكون وظائف عرض المخزون محدودة.")
                )

        # Create inventory view
        inventory_view = POSInventoryView(db_manager, pos_manager, parent)

        return inventory_view

    except Exception as e:
        error_message = f"خطأ في تهيئة عرض مخزون نقاط البيع: {str(e)}"
        print(error_message)

        if parent:
            QMessageBox.warning(
                parent,
                tr("errors.inventory_view_init_error", "خطأ في تهيئة عرض المخزون"),
                error_message
            )

        return None
