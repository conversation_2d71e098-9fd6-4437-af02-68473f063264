#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Transaction Detail Model for فوترها (Fawterha)
Represents a detail line in a financial transaction
"""

from datetime import datetime


class TransactionDetail:
    """Transaction detail model class."""

    def __init__(self, id=None, transaction_id=None, account_id=None, debit=0.0, credit=0.0,
                 description="", created_at=None):
        """Initialize a transaction detail.

        Args:
            id (int, optional): Transaction detail ID
            transaction_id (int, optional): Transaction ID
            account_id (int, optional): Account ID
            debit (float): Debit amount
            credit (float): Credit amount
            description (str): Description
            created_at (datetime, optional): Creation timestamp
        """
        self.id = id
        self.transaction_id = transaction_id
        self.account_id = account_id
        self.debit = debit
        self.credit = credit
        self.description = description
        self.created_at = created_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create a TransactionDetail object from a database row.

        Args:
            row (tuple or dict): Database row containing transaction detail data

        Returns:
            TransactionDetail: TransactionDetail object
        """
        # Handle both tuple and dictionary formats
        if isinstance(row, dict):
            return cls(
                id=row.get('id'),
                transaction_id=row.get('transaction_id'),
                account_id=row.get('account_id'),
                debit=float(row.get('debit', 0.0)),
                credit=float(row.get('credit', 0.0)),
                description=row.get('description', ''),
                created_at=row.get('created_at')
            )
        else:
            # Handle tuple format (indexed access)
            try:
                return cls(
                    id=row[0],
                    transaction_id=row[1],
                    account_id=row[2],
                    debit=float(row[3]),
                    credit=float(row[4]),
                    description=row[5],
                    created_at=row[6]
                )
            except (IndexError, TypeError) as e:
                print(f"Error creating TransactionDetail from row: {e}")
                print(f"Row data: {row}")
                # Return a default transaction detail object
                return cls()

    def to_dict(self):
        """Convert the transaction detail to a dictionary.

        Returns:
            dict: Dictionary representation of the transaction detail
        """
        return {
            'id': self.id,
            'transaction_id': self.transaction_id,
            'account_id': self.account_id,
            'debit': self.debit,
            'credit': self.credit,
            'description': self.description,
            'created_at': self.created_at
        }

    def __str__(self):
        """Return a string representation of the transaction detail.

        Returns:
            str: String representation
        """
        if self.debit > 0:
            return f"Debit: {self.debit} - {self.description}"
        else:
            return f"Credit: {self.credit} - {self.description}"
