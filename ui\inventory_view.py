#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory View for فوترها (Fawterha)
Provides a UI for managing inventory
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDialog, QFormLayout, QLineEdit, QDoubleSpinBox, QSpinBox,
    QMessageBox, QTabWidget, QGroupBox, QRadioButton, QDateEdit
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont, QColor

from database.inventory_manager import InventoryManager
from models.inventory_transaction import InventoryTransaction
from models.product import Product
from utils.currency_helper import format_currency
from ui.themes import THEMES
from utils.translation_manager import tr, TranslationManager


class InventoryView(QWidget):
    """Inventory management view."""

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the inventory view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()
        self.db_manager = db_manager
        self.inventory_manager = InventoryManager(db_manager)
        self.currency_manager = currency_manager

        # Get primary currency
        self.primary_currency = None
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()

        # Get current theme from settings
        self.theme_key = "default"
        try:
            theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
            if theme_setting and theme_setting[0]['value']:
                self.theme_key = theme_setting[0]['value']
        except Exception as e:
            print(f"Error loading theme setting: {e}")
            self.theme_key = "default"

        # Get theme colors
        self.theme = THEMES.get(self.theme_key, THEMES["default"])

        # Get translation manager
        self.translation_manager = TranslationManager()

        # Connect to language changes
        self.translation_manager.language_changed.connect(self.refresh_ui_translations)

        self.init_ui()
        self.load_data()

    def init_ui(self):
        """Initialize the UI."""
        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Main layout
        main_layout = QVBoxLayout(self)

        # Create tabs
        self.tabs = QTabWidget()

        # Products tab
        products_tab = QWidget()
        products_layout = QVBoxLayout(products_tab)

        # Products header
        products_header = QHBoxLayout()
        products_header.addWidget(QLabel(f"<h2>{tr('inventory.products_management', 'إدارة مخزون المنتجات')}</h2>"))

        # Add buttons
        refresh_button = QPushButton(tr("common.refresh", "تحديث"))
        refresh_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["primary"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }}
            QPushButton:hover {{
                background-color: {self.theme["primary_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["primary_dark"]};
            }}
        """)
        refresh_button.setMinimumHeight(40)
        refresh_button.clicked.connect(self.load_data)
        products_header.addWidget(refresh_button)

        add_stock_button = QPushButton(tr("inventory.add_stock", "إضافة مخزون"))
        add_stock_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["success"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }}
            QPushButton:hover {{
                background-color: {self.theme["success_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["success_dark"]};
            }}
        """)
        add_stock_button.setMinimumHeight(40)
        add_stock_button.clicked.connect(self.add_stock)
        products_header.addWidget(add_stock_button)

        adjust_stock_button = QPushButton(tr("inventory.adjust_stock", "تعديل المخزون"))
        adjust_stock_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["warning"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }}
            QPushButton:hover {{
                background-color: {self.theme["warning_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["warning_dark"]};
            }}
        """)
        adjust_stock_button.setMinimumHeight(40)
        adjust_stock_button.clicked.connect(self.adjust_stock)
        products_header.addWidget(adjust_stock_button)

        products_layout.addLayout(products_header)

        # Products table
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            tr("inventory.product", "المنتج"),
            tr("common.description", "الوصف"),
            tr("products.price", "السعر"),
            tr("inventory.available_quantity", "الكمية المتاحة"),
            tr("inventory.min_level", "الحد الأدنى"),
            tr("inventory.status", "الحالة")
        ])

        # Set table properties
        self.products_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.products_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.products_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.products_table.setAlternatingRowColors(True)

        # Set table style based on theme
        self.products_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.theme.get("table_bg", self.theme.get("panel_bg", "#ffffff"))};
                alternate-background-color: {self.theme.get("table_alternate_bg", self.theme.get("main_bg", "#f5f5f5"))};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                gridline-color: {self.theme["border_color"]};
            }}
            QTableWidget::item {{
                padding: 6px;
                border-bottom: 1px solid {self.theme["border_color"]};
                color: {self.theme["main_text"]};
            }}
            QTableWidget::item:selected {{
                background-color: {self.theme["primary_light"]};
                color: {self.theme["main_text"]};
            }}
            QHeaderView::section {{
                background-color: {self.theme["primary_dark"]};
                color: {self.theme["button_text"]};
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)

        products_layout.addWidget(self.products_table)

        # Transactions tab
        transactions_tab = QWidget()
        transactions_layout = QVBoxLayout(transactions_tab)

        # Transactions header
        transactions_header = QHBoxLayout()
        transactions_header.addWidget(QLabel(f"<h2>{tr('inventory.stock_movement', 'حركة المخزون')}</h2>"))

        # Filter controls
        filter_layout = QHBoxLayout()

        filter_layout.addWidget(QLabel(f"{tr('inventory.product', 'المنتج')}:"))
        self.product_filter = QComboBox()
        self.product_filter.addItem(tr("common.all", "الكل"), None)
        filter_layout.addWidget(self.product_filter)

        filter_layout.addWidget(QLabel(f"{tr('inventory.transaction_type', 'نوع الحركة')}:"))
        self.transaction_type_filter = QComboBox()
        self.transaction_type_filter.addItem(tr("common.all", "الكل"), None)
        self.transaction_type_filter.addItem(tr("inventory.purchase", "شراء"), InventoryTransaction.TYPE_PURCHASE)
        self.transaction_type_filter.addItem(tr("inventory.sale", "بيع"), InventoryTransaction.TYPE_SALE)
        self.transaction_type_filter.addItem(tr("inventory.adjustment", "تعديل"), InventoryTransaction.TYPE_ADJUSTMENT)
        self.transaction_type_filter.addItem(tr("inventory.return", "مرتجع"), InventoryTransaction.TYPE_RETURN)
        filter_layout.addWidget(self.transaction_type_filter)

        filter_button = QPushButton(tr("common.filter", "تصفية"))
        filter_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["secondary"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }}
            QPushButton:hover {{
                background-color: {self.theme["secondary_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["secondary_dark"]};
            }}
        """)
        filter_button.setMinimumHeight(40)
        filter_button.clicked.connect(self.apply_filters)
        filter_layout.addWidget(filter_button)

        transactions_header.addLayout(filter_layout)

        transactions_layout.addLayout(transactions_header)

        # Transactions table
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(6)
        self.transactions_table.setHorizontalHeaderLabels([
            tr("common.date", "التاريخ"),
            tr("inventory.product", "المنتج"),
            tr("inventory.transaction_type", "نوع الحركة"),
            tr("inventory.quantity", "الكمية"),
            tr("inventory.reference", "المرجع"),
            tr("common.notes", "ملاحظات")
        ])

        # Set table properties
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transactions_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.transactions_table.setAlternatingRowColors(True)

        # Set table style based on theme
        self.transactions_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.theme.get("table_bg", self.theme.get("panel_bg", "#ffffff"))};
                alternate-background-color: {self.theme.get("table_alternate_bg", self.theme.get("main_bg", "#f5f5f5"))};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                gridline-color: {self.theme["border_color"]};
            }}
            QTableWidget::item {{
                padding: 6px;
                border-bottom: 1px solid {self.theme["border_color"]};
                color: {self.theme["main_text"]};
            }}
            QTableWidget::item:selected {{
                background-color: {self.theme["primary_light"]};
                color: {self.theme["main_text"]};
            }}
            QHeaderView::section {{
                background-color: {self.theme["secondary_dark"]};
                color: {self.theme["button_text"]};
                padding: 8px;
                border: none;
                font-weight: bold;
            }}
        """)

        transactions_layout.addWidget(self.transactions_table)

        # Add tabs
        self.tabs.addTab(products_tab, tr("products.products", "المنتجات"))
        self.tabs.addTab(transactions_tab, tr("inventory.stock_movement", "حركة المخزون"))

        main_layout.addWidget(self.tabs)

    def refresh_ui_translations(self):
        """Refresh UI translations when language changes."""
        print("=== INVENTORY VIEW: refresh_ui_translations CALLED ===")

        # Update tab texts
        if hasattr(self, 'tabs'):
            for i in range(self.tabs.count()):
                current_text = self.tabs.tabText(i)
                print(f"Tab {i}: '{current_text}'")

                if current_text == "المنتجات":
                    self.tabs.setTabText(i, self.translation_manager.get_text("products.products", "Products"))
                    print("🔴 FORCED UPDATE: Tab 'المنتجات' → 'Products'")
                elif current_text == "حركة المخزون":
                    self.tabs.setTabText(i, self.translation_manager.get_text("inventory.stock_movement", "Stock Movement"))
                    print("🔴 FORCED UPDATE: Tab 'حركة المخزون' → 'Stock Movement'")
                elif current_text == "Products":
                    print("Products tab already in English")
                elif current_text == "Stock Movement":
                    print("Stock Movement tab already in English")

        print("=== INVENTORY VIEW: translations refreshed successfully ===")

    def showEvent(self, event):
        """Handle show event."""
        super().showEvent(event)
        # Force translation update when view is shown
        if hasattr(self, 'translation_manager') and self.translation_manager.current_language != 'ar':
            self.refresh_ui_translations()

    def load_data(self):
        """Load inventory data."""
        self.load_products()
        self.load_transactions()
        self.load_product_filter()

    def load_products(self):
        """Load products with inventory data."""
        # Refresh primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()
            if self.primary_currency:
                print(f"Inventory: Refreshed primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
            else:
                print("Inventory: No primary currency found after refresh")

        # Get products
        query = """
        SELECT * FROM products
        WHERE type = 'product'
        ORDER BY name
        """
        rows = self.db_manager.execute_query(query)

        # Debug print
        print("\n=== Products from Database ===")
        for row in rows:
            print(f"ID: {row['id']}, Name: {row['name']}, Stock: {row['stock_quantity']}, Min: {row['min_stock_level']}, Track: {row['track_inventory']}")

        # Clear table
        self.products_table.setRowCount(0)

        # Get currency symbol
        currency_symbol = tr("currency.sar_symbol", "ر.س")  # Default symbol
        if hasattr(self, 'primary_currency') and self.primary_currency:
            currency_symbol = self.primary_currency.symbol
            print(f"Using primary currency in inventory: {self.primary_currency.code} ({currency_symbol})")

        # Add products to table
        for row in rows:
            product = Product.from_db_row(row)
            # Debug print
            print(f"Product after conversion: {product.name}, Stock: {product.stock_quantity}, Min: {product.min_stock_level}, Track: {product.track_inventory}")

            row_position = self.products_table.rowCount()
            self.products_table.insertRow(row_position)

            # Set product data
            self.products_table.setItem(row_position, 0, QTableWidgetItem(product.name))
            self.products_table.setItem(row_position, 1, QTableWidgetItem(product.description))

            # Format price with currency symbol
            price_text = format_currency(product.price, currency_symbol)
            price_item = QTableWidgetItem(price_text)
            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.products_table.setItem(row_position, 2, price_item)

            # Set inventory data
            quantity_item = QTableWidgetItem(str(product.stock_quantity))
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row_position, 3, quantity_item)

            min_level_item = QTableWidgetItem(str(product.min_stock_level))
            min_level_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row_position, 4, min_level_item)

            # Set status
            status_item = QTableWidgetItem()
            # Debug print
            print(f"Product: {product.name}, track_inventory: {product.track_inventory}, type: {type(product.track_inventory)}")
            print(f"Stock: {product.stock_quantity}, Min: {product.min_stock_level}")

            if not product.track_inventory:
                status_text = tr("inventory.not_tracked", "غير متتبع")
                status_color = QColor(128, 128, 128)  # Gray
            elif product.stock_quantity <= 0:
                status_text = tr("inventory.out_of_stock", "نفذ")
                status_color = QColor(255, 0, 0)  # Red
            elif product.stock_quantity <= product.min_stock_level:
                status_text = tr("inventory.low_stock", "منخفض")
                status_color = QColor(255, 165, 0)  # Orange
            else:
                status_text = tr("inventory.available", "متوفر")
                status_color = QColor(0, 128, 0)  # Green

            status_item.setText(status_text)
            status_item.setForeground(status_color)
            status_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row_position, 5, status_item)

    def load_transactions(self, product_id=None, transaction_type=None):
        """Load inventory transactions.

        Args:
            product_id (int, optional): Filter by product ID. Defaults to None.
            transaction_type (str, optional): Filter by transaction type. Defaults to None.
        """
        # Get transactions
        transactions = self.inventory_manager.get_transactions(product_id, transaction_type)

        # Clear table
        self.transactions_table.setRowCount(0)

        # Add transactions to table
        for transaction in transactions:
            row_position = self.transactions_table.rowCount()
            self.transactions_table.insertRow(row_position)

            # Get product name
            product_query = "SELECT name FROM products WHERE id = ?"
            product_rows = self.db_manager.execute_query(product_query, (transaction.product_id,))
            product_name = product_rows[0]['name'] if product_rows else tr("common.unknown", "غير معروف")

            # Format transaction type
            if transaction.transaction_type == InventoryTransaction.TYPE_PURCHASE:
                type_text = tr("inventory.purchase", "شراء")
            elif transaction.transaction_type == InventoryTransaction.TYPE_SALE:
                type_text = tr("inventory.sale", "بيع")
            elif transaction.transaction_type == InventoryTransaction.TYPE_ADJUSTMENT:
                type_text = tr("inventory.adjustment", "تعديل")
            elif transaction.transaction_type == InventoryTransaction.TYPE_RETURN:
                type_text = tr("inventory.return", "مرتجع")
            else:
                type_text = transaction.transaction_type

            # Format reference
            reference_text = ""
            if transaction.reference_type and transaction.reference_id:
                if transaction.reference_type == "invoice":
                    invoice_query = "SELECT invoice_number FROM invoices WHERE id = ?"
                    invoice_rows = self.db_manager.execute_query(invoice_query, (transaction.reference_id,))
                    if invoice_rows:
                        reference_text = f"{tr('invoices.invoice', 'فاتورة')} {invoice_rows[0]['invoice_number']}"
                else:
                    reference_text = f"{transaction.reference_type} #{transaction.reference_id}"

            # Set transaction data
            self.transactions_table.setItem(row_position, 0, QTableWidgetItem(str(transaction.created_at)))
            self.transactions_table.setItem(row_position, 1, QTableWidgetItem(product_name))
            self.transactions_table.setItem(row_position, 2, QTableWidgetItem(type_text))

            quantity_item = QTableWidgetItem(str(transaction.quantity))
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row_position, 3, quantity_item)

            self.transactions_table.setItem(row_position, 4, QTableWidgetItem(reference_text))
            self.transactions_table.setItem(row_position, 5, QTableWidgetItem(transaction.notes))

    def load_product_filter(self):
        """Load products for the filter combobox."""
        # Get products
        query = """
        SELECT id, name FROM products
        WHERE type = 'product'
        ORDER BY name
        """
        rows = self.db_manager.execute_query(query)

        # Clear combobox
        self.product_filter.clear()
        self.product_filter.addItem(tr("common.all", "الكل"), None)

        # Add products to combobox
        for row in rows:
            self.product_filter.addItem(row['name'], row['id'])

    def apply_filters(self):
        """Apply filters to transactions table."""
        product_id = self.product_filter.currentData()
        transaction_type = self.transaction_type_filter.currentData()

        self.load_transactions(product_id, transaction_type)

    def add_stock(self):
        """Add stock to a product."""
        dialog = AddStockDialog(self, self.db_manager)
        if dialog.exec():
            self.load_data()

    def adjust_stock(self):
        """Adjust stock for a product."""
        dialog = AdjustStockDialog(self, self.db_manager)
        if dialog.exec():
            self.load_data()


class AddStockDialog(QDialog):
    """Dialog for adding stock to a product."""

    def __init__(self, parent, db_manager):
        """Initialize the dialog.

        Args:
            parent: Parent widget
            db_manager: Database manager instance
        """
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = InventoryManager(db_manager)

        # Get current theme from settings
        self.theme_key = "default"
        try:
            theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
            if theme_setting and theme_setting[0]['value']:
                self.theme_key = theme_setting[0]['value']
        except Exception as e:
            print(f"Error loading theme setting: {e}")
            self.theme_key = "default"

        # Get theme colors
        self.theme = THEMES.get(self.theme_key, THEMES["default"])

        self.setWindowTitle(tr("inventory.add_stock", "إضافة مخزون"))
        self.setMinimumWidth(400)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Set dialog style
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.theme["main_bg"]};
                color: {self.theme["main_text"]};
            }}
            QLabel {{
                color: {self.theme["main_text"]};
            }}
            QComboBox, QSpinBox, QLineEdit {{
                background-color: {self.theme.get("input_bg", "#ffffff")};
                color: {self.theme["input_text"]};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                padding: 5px;
            }}
        """)

        self.init_ui()
        self.load_products()

    def init_ui(self):
        """Initialize the UI."""
        layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # Product selection
        self.product_combo = QComboBox()
        form_layout.addRow(f"{tr('inventory.product', 'المنتج')}:", self.product_combo)

        # Quantity
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(1)
        self.quantity_spin.setMaximum(10000)
        self.quantity_spin.setValue(1)
        form_layout.addRow(f"{tr('inventory.quantity', 'الكمية')}:", self.quantity_spin)

        # Notes
        self.notes_edit = QLineEdit()
        form_layout.addRow(f"{tr('common.notes', 'ملاحظات')}:", self.notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        buttons_layout = QHBoxLayout()

        save_button = QPushButton(tr("common.save", "حفظ"))
        save_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["success"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme["success_dark"]};
            }}
        """)
        save_button.clicked.connect(self.save)
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["danger"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme["danger_dark"]};
            }}
        """)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def load_products(self):
        """Load products for the combobox."""
        # Get products
        query = """
        SELECT id, name FROM products
        WHERE type = 'product' AND track_inventory = 1
        ORDER BY name
        """
        rows = self.db_manager.execute_query(query)

        # Clear combobox
        self.product_combo.clear()

        # Add products to combobox
        for row in rows:
            self.product_combo.addItem(row['name'], row['id'])

    def save(self):
        """Save the stock addition."""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("inventory.select_product", "يجب اختيار منتج"))
            return

        product_id = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        notes = self.notes_edit.text()

        # Create transaction
        transaction = InventoryTransaction(
            product_id=product_id,
            transaction_type=InventoryTransaction.TYPE_PURCHASE,
            quantity=quantity,
            notes=notes
        )

        try:
            # Add transaction
            self.inventory_manager.add_transaction(transaction)

            QMessageBox.information(self, tr("messages.success", "نجاح"), tr("inventory.add_stock_success", "تم إضافة المخزون بنجاح"))
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("inventory.add_stock_error", f"حدث خطأ أثناء إضافة المخزون: {str(e)}"))


class AdjustStockDialog(QDialog):
    """Dialog for adjusting stock for a product."""

    def __init__(self, parent, db_manager):
        """Initialize the dialog.

        Args:
            parent: Parent widget
            db_manager: Database manager instance
        """
        super().__init__(parent)
        self.db_manager = db_manager
        self.inventory_manager = InventoryManager(db_manager)

        # Get current theme from settings
        self.theme_key = "default"
        try:
            theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
            if theme_setting and theme_setting[0]['value']:
                self.theme_key = theme_setting[0]['value']
        except Exception as e:
            print(f"Error loading theme setting: {e}")
            self.theme_key = "default"

        # Get theme colors
        self.theme = THEMES.get(self.theme_key, THEMES["default"])

        self.setWindowTitle(tr("inventory.adjust_stock", "تعديل المخزون"))
        self.setMinimumWidth(400)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Set dialog style
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.theme["main_bg"]};
                color: {self.theme["main_text"]};
            }}
            QLabel {{
                color: {self.theme["main_text"]};
            }}
            QComboBox, QSpinBox, QLineEdit {{
                background-color: {self.theme.get("input_bg", "#ffffff")};
                color: {self.theme["input_text"]};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                padding: 5px;
            }}
            QGroupBox {{
                color: {self.theme["main_text"]};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                margin-top: 1ex;
                padding: 10px;
            }}
            QRadioButton {{
                color: {self.theme["main_text"]};
            }}
        """)

        self.init_ui()
        self.load_products()

    def init_ui(self):
        """Initialize the UI."""
        layout = QVBoxLayout(self)

        form_layout = QFormLayout()

        # Product selection
        self.product_combo = QComboBox()
        self.product_combo.currentIndexChanged.connect(self.update_current_stock)
        form_layout.addRow("المنتج:", self.product_combo)

        # Current stock
        self.current_stock_label = QLabel("0")
        form_layout.addRow("المخزون الحالي:", self.current_stock_label)

        # Adjustment type
        adjustment_group = QGroupBox("نوع التعديل")
        adjustment_layout = QHBoxLayout(adjustment_group)

        self.add_radio = QRadioButton("إضافة")
        self.add_radio.setChecked(True)
        adjustment_layout.addWidget(self.add_radio)

        self.subtract_radio = QRadioButton("خصم")
        adjustment_layout.addWidget(self.subtract_radio)

        self.set_radio = QRadioButton("تعيين")
        adjustment_layout.addWidget(self.set_radio)

        form_layout.addRow(adjustment_group)

        # Quantity
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMinimum(0)
        self.quantity_spin.setMaximum(10000)
        self.quantity_spin.setValue(0)
        form_layout.addRow("الكمية:", self.quantity_spin)

        # Notes
        self.notes_edit = QLineEdit()
        form_layout.addRow("ملاحظات:", self.notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        buttons_layout = QHBoxLayout()

        save_button = QPushButton("حفظ")
        save_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["success"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme["success_dark"]};
            }}
        """)
        save_button.clicked.connect(self.save)
        buttons_layout.addWidget(save_button)

        cancel_button = QPushButton("إلغاء")
        cancel_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["danger"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme["danger_dark"]};
            }}
        """)
        cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(cancel_button)

        layout.addLayout(buttons_layout)

    def load_products(self):
        """Load products for the combobox."""
        # Get products
        query = """
        SELECT id, name, stock_quantity FROM products
        WHERE type = 'product' AND track_inventory = 1
        ORDER BY name
        """
        rows = self.db_manager.execute_query(query)

        # Clear combobox
        self.product_combo.clear()

        # Add products to combobox
        for row in rows:
            self.product_combo.addItem(row['name'], row['id'])

        # Update current stock
        self.update_current_stock()

    def update_current_stock(self):
        """Update the current stock label."""
        if self.product_combo.currentIndex() == -1:
            self.current_stock_label.setText("0")
            return

        product_id = self.product_combo.currentData()

        # Get current stock
        stock = self.inventory_manager.get_product_stock(product_id)

        self.current_stock_label.setText(str(stock))

    def save(self):
        """Save the stock adjustment."""
        if self.product_combo.currentIndex() == -1:
            QMessageBox.warning(self, "خطأ", "يجب اختيار منتج")
            return

        product_id = self.product_combo.currentData()
        quantity = self.quantity_spin.value()
        notes = self.notes_edit.text()

        # Get current stock
        current_stock = int(self.current_stock_label.text())

        # Calculate new stock based on adjustment type
        if self.add_radio.isChecked():
            new_stock = current_stock + quantity
            transaction_quantity = quantity
        elif self.subtract_radio.isChecked():
            if quantity > current_stock:
                QMessageBox.warning(self, "خطأ", "الكمية المراد خصمها أكبر من المخزون الحالي")
                return
            new_stock = current_stock - quantity
            transaction_quantity = -quantity
        else:  # Set
            new_stock = quantity
            transaction_quantity = quantity - current_stock

        # Create transaction
        transaction = InventoryTransaction(
            product_id=product_id,
            transaction_type=InventoryTransaction.TYPE_ADJUSTMENT,
            quantity=transaction_quantity,
            notes=notes
        )

        try:
            # Debug print before adding transaction
            print(f"Adjustment transaction - Product ID: {product_id}")
            print(f"Current stock: {current_stock}")
            print(f"Adjustment type: {'Add' if self.add_radio.isChecked() else 'Subtract' if self.subtract_radio.isChecked() else 'Set'}")
            print(f"Quantity: {quantity}")
            print(f"Transaction quantity: {transaction_quantity}")
            print(f"Expected new stock: {new_stock}")

            # Add transaction
            self.inventory_manager.add_transaction(transaction)

            # Debug print after adding transaction
            updated_stock = self.inventory_manager.get_product_stock(product_id)
            print(f"Actual new stock after transaction: {updated_stock}")

            QMessageBox.information(self, "نجاح", "تم تعديل المخزون بنجاح")
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المخزون: {str(e)}")
