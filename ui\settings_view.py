#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Settings View for فوترها (Fawterha)
Manages application settings
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QLineEdit, QFormLayout, QComboBox, QFileDialog, QMessageBox,
    QGroupBox, QSpinBox, QScrollArea, QFrame, QTabWidget, QCheckBox,
    QProgressDialog
)
from PySide6.QtCore import Qt, Signal, QSize, QThread, QObject, Signal as pyqtSignal
from PySide6.QtGui import QPixmap, QImage, QIcon, QColor

from ui.themes import THEMES
from ui.user_management_view import UserManagementView
from database.currency_manager import CurrencyManager
from database.language_manager import get_language_manager
from models.currency import Currency
from utils.translation_manager import get_translation_manager, tr
import smtplib
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from datetime import datetime


class EmailWorker(QObject):
    """Worker class for sending emails in a separate thread."""

    # Define signals
    finished = pyqtSignal()
    error = pyqtSignal(str)
    success = pyqtSignal(str)

    def __init__(self, smtp_server, smtp_port, smtp_username, smtp_password,
                 smtp_use_tls, smtp_from_email, smtp_from_name, recipient):
        """Initialize the worker with email settings.

        Args:
            smtp_server (str): SMTP server address
            smtp_port (int): SMTP server port
            smtp_username (str): SMTP username
            smtp_password (str): SMTP password
            smtp_use_tls (bool): Whether to use TLS
            smtp_from_email (str): Sender email address
            smtp_from_name (str): Sender name
            recipient (str): Recipient email address
        """
        super().__init__()
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.smtp_username = smtp_username
        self.smtp_password = smtp_password
        self.smtp_use_tls = smtp_use_tls
        self.smtp_from_email = smtp_from_email
        self.smtp_from_name = smtp_from_name
        self.recipient = recipient

    def send_email(self):
        """Send a test email using the provided settings."""
        try:
            print(f"EmailWorker: Starting to send email to {self.recipient}")
            print(f"EmailWorker: SMTP Server: {self.smtp_server}, Port: {self.smtp_port}")
            print(f"EmailWorker: Username: {self.smtp_username}, Use TLS: {self.smtp_use_tls}")

            # Create email message
            msg = MIMEMultipart()
            # Fix the From header format to comply with RFC 5322
            if self.smtp_from_name:
                # Use proper format for From header with name and email
                from email.utils import formataddr
                msg['From'] = formataddr((self.smtp_from_name, self.smtp_from_email))
            else:
                # Just use the email address if no name is provided
                msg['From'] = self.smtp_from_email
            msg['To'] = self.recipient
            msg['Subject'] = tr("settings.test_email_subject", "اختبار إعدادات البريد الإلكتروني - فوترها")

            # Create HTML content
            html_content = f"""
            <html dir="rtl">
            <head>
                <style>
                    body {{ font-family: Arial, sans-serif; direction: rtl; text-align: right; }}
                    .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                    .header {{ background-color: #0d47a1; color: white; padding: 10px; text-align: center; }}
                    .content {{ padding: 20px; border: 1px solid #ddd; }}
                    .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>اختبار إعدادات البريد الإلكتروني</h1>
                    </div>
                    <div class="content">
                        <p>مرحباً،</p>
                        <p>هذه رسالة اختبار من تطبيق فوترها للتأكد من صحة إعدادات البريد الإلكتروني.</p>
                        <p>إذا تلقيت هذه الرسالة، فهذا يعني أن إعدادات البريد الإلكتروني تعمل بشكل صحيح.</p>
                        <p>تاريخ ووقت الإرسال: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                    </div>
                    <div class="footer">
                        <p>فوترها - نظام إدارة الفواتير</p>
                    </div>
                </div>
            </body>
            </html>
            """

            # Attach HTML content to email
            msg.attach(MIMEText(html_content, 'html'))

            print("EmailWorker: Creating SMTP connection...")
            # Create SMTP connection
            try:
                # Check if we should use SSL instead of TLS
                if int(self.smtp_port) == 465:
                    print("EmailWorker: Using SMTP_SSL for port 465")
                    server = smtplib.SMTP_SSL(self.smtp_server, int(self.smtp_port), timeout=10)
                else:
                    print("EmailWorker: Using standard SMTP")
                    server = smtplib.SMTP(self.smtp_server, int(self.smtp_port), timeout=10)
                print("EmailWorker: SMTP connection established")
            except Exception as conn_error:
                error_msg = f"فشل الاتصال بخادم SMTP: {str(conn_error)}"
                print(f"EmailWorker: Connection error - {error_msg}")

                # Add more detailed error information
                if "timed out" in str(conn_error):
                    error_msg += "\n\nتأكد من صحة عنوان الخادم والمنفذ وتأكد من اتصالك بالإنترنت."
                elif "getaddrinfo failed" in str(conn_error):
                    error_msg += "\n\nتعذر العثور على خادم SMTP. تأكد من صحة عنوان الخادم."
                elif "refused" in str(conn_error):
                    error_msg += "\n\nرفض الخادم الاتصال. تأكد من صحة رقم المنفذ."

                self.error.emit(error_msg)
                self.finished.emit()
                return

            try:
                server.ehlo()
                print("EmailWorker: EHLO command sent")

                # Use TLS if enabled and not already using SSL
                if self.smtp_use_tls and int(self.smtp_port) != 465:
                    print("EmailWorker: Starting TLS...")
                    try:
                        server.starttls()
                        server.ehlo()
                        print("EmailWorker: TLS started")
                    except (smtplib.SMTPNotSupportedError, smtplib.SMTPException) as e:
                        print(f"EmailWorker: STARTTLS error - {str(e)}")
                        error_msg = "خادم SMTP لا يدعم STARTTLS. يرجى استخدام المنفذ 465 مع SSL أو تعطيل TLS."
                        self.error.emit(error_msg)
                        try:
                            server.quit()
                        except:
                            pass
                        self.finished.emit()
                        return

                # Login to SMTP server
                print("EmailWorker: Logging in...")
                server.login(self.smtp_username, self.smtp_password)
                print("EmailWorker: Login successful")

                # Send email
                print("EmailWorker: Sending email...")
                try:
                    # Use a more direct approach to ensure delivery
                    print("EmailWorker: Preparing to send email directly...")

                    # Get all recipients
                    all_recipients = []
                    if msg['To']:
                        all_recipients.extend([addr.strip() for addr in msg['To'].split(',')])
                    if msg.get('Cc'):
                        all_recipients.extend([addr.strip() for addr in msg['Cc'].split(',')])
                    if msg.get('Bcc'):
                        all_recipients.extend([addr.strip() for addr in msg['Bcc'].split(',')])

                    # Convert message to string
                    message_str = msg.as_string()

                    # Send the message directly
                    print(f"EmailWorker: Sending to recipients: {all_recipients}")
                    refused = server.sendmail(msg['From'], all_recipients, message_str)

                    # Check if there were any rejected recipients
                    if refused:
                        rejected_recipients = list(refused.keys())
                        error_msg = f"فشل إرسال البريد الإلكتروني إلى: {', '.join(rejected_recipients)}"
                        print(f"EmailWorker: Send error - {error_msg}")
                        self.error.emit(error_msg)
                    else:
                        print("EmailWorker: Email sent successfully")
                        # Emit success signal
                        self.success.emit(self.recipient)

                except smtplib.SMTPRecipientsRefused as e:
                    error_msg = f"تم رفض المستلمين: {str(e)}"
                    print(f"EmailWorker: Recipients refused - {error_msg}")
                    self.error.emit(error_msg)
                except smtplib.SMTPSenderRefused as e:
                    error_msg = f"تم رفض المرسل: {str(e)}"
                    print(f"EmailWorker: Sender refused - {error_msg}")
                    self.error.emit(error_msg)
                except smtplib.SMTPDataError as e:
                    error_msg = f"خطأ في بيانات البريد الإلكتروني: {str(e)}"
                    print(f"EmailWorker: Data error - {error_msg}")
                    self.error.emit(error_msg)
                except Exception as e:
                    error_msg = f"خطأ أثناء إرسال البريد الإلكتروني: {str(e)}"
                    print(f"EmailWorker: Send error - {error_msg}")
                    self.error.emit(error_msg)
                finally:
                    try:
                        server.quit()
                        print("EmailWorker: SMTP connection closed")
                    except:
                        print("EmailWorker: Error closing SMTP connection")

            except smtplib.SMTPAuthenticationError:
                error_msg = "فشل تسجيل الدخول: اسم المستخدم أو كلمة المرور غير صحيحة"
                print(f"EmailWorker: Authentication error - {error_msg}")
                self.error.emit(error_msg)
                try:
                    server.quit()
                except:
                    pass

            except smtplib.SMTPException as smtp_error:
                error_msg = f"خطأ في بروتوكول SMTP: {str(smtp_error)}"
                print(f"EmailWorker: SMTP error - {error_msg}")
                self.error.emit(error_msg)
                try:
                    server.quit()
                except:
                    pass

            except Exception as e:
                error_msg = f"خطأ أثناء إرسال البريد الإلكتروني: {str(e)}"
                print(f"EmailWorker: General error - {error_msg}")
                self.error.emit(error_msg)
                try:
                    server.quit()
                except:
                    pass

        except Exception as e:
            # Emit error signal for any other exceptions
            error_msg = f"خطأ غير متوقع: {str(e)}"
            print(f"EmailWorker: Unexpected error - {error_msg}")
            self.error.emit(error_msg)

        # Emit finished signal
        print("EmailWorker: Process completed")
        self.finished.emit()


class SettingsView(QWidget):
    """Widget for managing application settings."""

    # Signals to notify when settings are changed
    theme_changed = Signal(str)
    language_changed = Signal(str)

    # Signal to be emitted when the widget is about to be destroyed
    destroyed = Signal()

    def __init__(self, db_manager, theme_manager=None):
        """Initialize the settings view.

        Args:
            db_manager: Database manager instance
            theme_manager: Theme manager instance (optional)
        """
        super().__init__()

        # Connect the destroyed signal to cleanup method
        self.destroyed.connect(self.cleanup)

        self.db_manager = db_manager
        self.theme_manager = theme_manager
        self.currency_manager = CurrencyManager(db_manager)
        self.language_manager = get_language_manager(db_manager)
        self.translation_manager = get_translation_manager()

        # Create main layout
        main_layout = QVBoxLayout(self)

        # Create tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Create general settings tab
        general_tab = QWidget()
        self.tab_widget.addTab(general_tab, tr("settings.general", "عام"))

        # Create user management tab
        self.user_management_tab = UserManagementView(self.db_manager)
        self.tab_widget.addTab(self.user_management_tab, tr("users.management", "إدارة المستخدمين"))

        # Create activity log tab
        from ui.activity_log_view import ActivityLogView
        self.activity_log_tab = ActivityLogView(self.db_manager)
        self.tab_widget.addTab(self.activity_log_tab, tr("activity_log.title", "سجل الأنشطة"))

        # Create a scroll area to contain all general settings
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QScrollArea.NoFrame)

        # Create a container widget for the scroll area
        container = QWidget()
        scroll_area.setWidget(container)

        # Create layout for the general tab
        general_layout = QVBoxLayout(general_tab)
        general_layout.addWidget(scroll_area)

        # Create layout for the container
        layout = QVBoxLayout(container)

        # Company information section
        company_group = QGroupBox(tr("settings.company_info", "معلومات الشركة"))
        layout.addWidget(company_group)

        company_layout = QFormLayout(company_group)

        self.company_name_edit = QLineEdit()
        company_layout.addRow(tr("customers.customer_name", "اسم الشركة:"), self.company_name_edit)

        self.company_address_edit = QLineEdit()
        company_layout.addRow(tr("customers.customer_address", "العنوان:"), self.company_address_edit)

        self.company_phone_edit = QLineEdit()
        company_layout.addRow(tr("customers.customer_phone", "رقم الهاتف:"), self.company_phone_edit)

        self.company_email_edit = QLineEdit()
        company_layout.addRow(tr("customers.customer_email", "البريد الإلكتروني:"), self.company_email_edit)

        # Email settings section
        email_group = QGroupBox(tr("settings.email_settings", "إعدادات البريد الإلكتروني"))
        layout.addWidget(email_group)

        email_layout = QFormLayout(email_group)

        self.smtp_server_edit = QLineEdit()
        email_layout.addRow(tr("settings.smtp_server", "خادم SMTP:"), self.smtp_server_edit)

        self.smtp_port_edit = QLineEdit()
        email_layout.addRow(tr("settings.smtp_port", "منفذ SMTP:"), self.smtp_port_edit)

        self.smtp_username_edit = QLineEdit()
        email_layout.addRow(tr("settings.smtp_username", "اسم المستخدم:"), self.smtp_username_edit)

        self.smtp_password_edit = QLineEdit()
        self.smtp_password_edit.setEchoMode(QLineEdit.Password)
        email_layout.addRow(tr("settings.smtp_password", "كلمة المرور:"), self.smtp_password_edit)

        self.smtp_use_tls_check = QCheckBox(tr("settings.smtp_use_tls", "استخدام TLS"))
        self.smtp_use_tls_check.setChecked(True)
        email_layout.addRow("", self.smtp_use_tls_check)

        self.smtp_from_email_edit = QLineEdit()
        email_layout.addRow(tr("settings.smtp_from_email", "البريد الإلكتروني المرسل:"), self.smtp_from_email_edit)

        self.smtp_from_name_edit = QLineEdit()
        email_layout.addRow(tr("settings.smtp_from_name", "اسم المرسل:"), self.smtp_from_name_edit)

        # Test email button
        self.test_email_button = QPushButton(tr("settings.test_email", "اختبار البريد الإلكتروني"))
        self.test_email_button.clicked.connect(self.test_email)
        email_layout.addRow("", self.test_email_button)

        # Logo section
        logo_layout = QHBoxLayout()
        company_layout.addRow(tr("settings.company_logo", "الشعار:"), logo_layout)

        self.logo_preview = QLabel()
        self.logo_preview.setFixedSize(150, 150)
        self.logo_preview.setAlignment(Qt.AlignCenter)
        self.logo_preview.setStyleSheet("border: 1px solid #ccc;")
        logo_layout.addWidget(self.logo_preview)

        logo_buttons_layout = QVBoxLayout()
        logo_layout.addLayout(logo_buttons_layout)

        self.select_logo_button = QPushButton(tr("settings.select_logo", "اختيار شعار"))
        self.select_logo_button.setProperty("style", "primary")  # Set property for theme-aware styling
        self.select_logo_button.setMinimumHeight(28)
        self.select_logo_button.clicked.connect(self.select_logo)
        logo_buttons_layout.addWidget(self.select_logo_button)

        self.clear_logo_button = QPushButton(tr("settings.remove_logo", "إزالة الشعار"))
        self.clear_logo_button.setProperty("style", "danger")  # Set property for theme-aware styling
        self.clear_logo_button.setMinimumHeight(28)
        self.clear_logo_button.clicked.connect(self.clear_logo)
        logo_buttons_layout.addWidget(self.clear_logo_button)

        logo_buttons_layout.addStretch()

        # Invoice settings section
        invoice_group = QGroupBox(tr("settings.invoice_settings", "إعدادات الفواتير"))
        layout.addWidget(invoice_group)

        invoice_layout = QFormLayout(invoice_group)

        # Get primary currency info to display
        primary_currency = self.currency_manager.get_primary_currency()
        if primary_currency:
            currency_info = QLabel(f"{primary_currency.name} ({primary_currency.symbol})")
            currency_info.setStyleSheet("font-weight: bold; color: #0d47a1;")
        else:
            currency_info = QLabel(tr("currency.no_primary_currency", "لم يتم تعيين عملة رئيسية"))
            currency_info.setStyleSheet("font-weight: bold; color: #f44336;")

        # Add note about currency management
        currency_note = QLabel(tr("currency.change_primary_note", "يمكنك تغيير العملة الرئيسية من تبويبة 'العملات'"))
        currency_note.setStyleSheet("font-style: italic; color: #757575;")

        currency_layout = QVBoxLayout()
        currency_layout.addWidget(currency_info)
        currency_layout.addWidget(currency_note)

        invoice_layout.addRow(tr("currency.primary_currency", "العملة الرئيسية:"), currency_layout)

        self.tax_rate_spin = QSpinBox()
        self.tax_rate_spin.setMinimum(0)
        self.tax_rate_spin.setMaximum(100)
        self.tax_rate_spin.setSuffix("%")
        invoice_layout.addRow(tr("invoices.tax_rate", "نسبة الضريبة:"), self.tax_rate_spin)

        self.invoice_prefix_edit = QLineEdit()
        invoice_layout.addRow(tr("invoices.invoice_prefix", "بادئة رقم الفاتورة:"), self.invoice_prefix_edit)

        self.next_invoice_number_edit = QLineEdit()
        invoice_layout.addRow(tr("invoices.next_invoice_number", "رقم الفاتورة التالي:"), self.next_invoice_number_edit)

        # Theme settings section
        theme_group = QGroupBox(tr("settings.theme_settings", "إعدادات النمط"))
        layout.addWidget(theme_group)

        theme_layout = QFormLayout(theme_group)

        self.theme_combo = QComboBox()
        self.theme_combo.setMinimumWidth(200)
        self.theme_combo.setIconSize(QSize(24, 24))

        # Add themes to combo box with preview colors
        for theme_key, theme_data in THEMES.items():
            # Create a colored icon for the theme
            pixmap = QPixmap(24, 24)
            if "main_bg_gradient" in theme_data and theme_data["main_bg_gradient"]:
                # For gradients, use the primary color
                pixmap.fill(QColor(theme_data["primary"]))
            else:
                pixmap.fill(QColor(theme_data["main_bg"]))

            # Add the item with icon and theme name
            self.theme_combo.addItem(QIcon(pixmap), theme_data["name"], theme_key)

        # The theme combo will be styled by the global theme system
        self.theme_combo.currentIndexChanged.connect(self.on_theme_changed)
        theme_layout.addRow(tr("settings.theme", "النمط:"), self.theme_combo)

        # Theme description
        self.theme_description = QLabel()
        # The theme description will be styled by the global theme system
        theme_layout.addRow("", self.theme_description)

        # Add theme preview panel
        self.theme_preview = QWidget()
        self.theme_preview.setMinimumHeight(100)
        self.theme_preview.setMinimumWidth(300)

        # Create preview layout
        preview_layout = QVBoxLayout(self.theme_preview)

        # Add preview elements
        preview_title = QLabel(tr("settings.theme_preview_title", "معاينة النمط"))
        preview_title.setAlignment(Qt.AlignCenter)
        preview_layout.addWidget(preview_title)

        # Add sample button
        sample_button = QPushButton(tr("settings.sample_button", "زر تجريبي"))
        sample_button.setEnabled(True)
        preview_layout.addWidget(sample_button)

        # Add sample input
        sample_input = QLineEdit(tr("settings.sample_input", "حقل إدخال تجريبي"))
        preview_layout.addWidget(sample_input)

        # Add to theme layout
        theme_layout.addRow(tr("settings.preview", "معاينة:"), self.theme_preview)

        # Update theme description when combo box changes
        self.update_theme_description()

        # Language settings section
        language_group = QGroupBox(tr("settings.language_settings", "إعدادات اللغة"))
        layout.addWidget(language_group)

        language_layout = QFormLayout(language_group)

        self.language_combo = QComboBox()
        self.language_combo.setMinimumWidth(200)

        # Add available languages to combo box
        available_languages = self.language_manager.get_available_languages()
        for lang in available_languages:
            self.language_combo.addItem(lang['name'], lang['code'])

        self.language_combo.currentIndexChanged.connect(self.on_language_changed)
        language_layout.addRow(tr("settings.language", "اللغة:"), self.language_combo)

        # Language description
        self.language_description = QLabel(tr("settings.language_description", "تغيير لغة واجهة المستخدم في التطبيق"))
        language_layout.addRow("", self.language_description)

        # Language note
        language_note = QLabel(tr("settings.language_note", "ملاحظة: سيتم تطبيق تغيير اللغة عند إعادة تشغيل التطبيق"))
        language_note.setStyleSheet("font-style: italic; color: #757575;")
        language_layout.addRow("", language_note)

        # Buttons section
        buttons_layout = QHBoxLayout()
        layout.addLayout(buttons_layout)

        self.save_button = QPushButton(tr("settings.save_settings", "حفظ الإعدادات"))
        self.save_button.setProperty("style", "success")  # Set property for theme-aware styling
        self.save_button.setMinimumHeight(32)
        self.save_button.clicked.connect(self.save_settings)
        buttons_layout.addWidget(self.save_button)

        self.reset_button = QPushButton(tr("settings.reset", "إعادة تعيين"))
        self.reset_button.setProperty("style", "warning")  # Set property for theme-aware styling
        self.reset_button.setMinimumHeight(32)
        self.reset_button.clicked.connect(self.load_settings)
        buttons_layout.addWidget(self.reset_button)

        # Load settings
        self.load_settings()

    def load_settings(self):
        """Load settings from the database."""
        query = "SELECT key, value FROM settings"
        rows = self.db_manager.execute_query(query)

        settings = {}
        for row in rows:
            settings[row['key']] = row['value']

        # Set company values
        self.company_name_edit.setText(settings.get('company_name', ''))
        self.company_address_edit.setText(settings.get('company_address', ''))
        self.company_phone_edit.setText(settings.get('company_phone', ''))
        self.company_email_edit.setText(settings.get('company_email', ''))

        # Set logo
        logo_path = settings.get('company_logo', '')
        if logo_path:
            self.load_logo(logo_path)
        else:
            self.clear_logo()

        # Set email settings
        self.smtp_server_edit.setText(settings.get('smtp_server', 'smtp.gmail.com'))
        self.smtp_port_edit.setText(settings.get('smtp_port', '587'))
        self.smtp_username_edit.setText(settings.get('smtp_username', ''))
        self.smtp_password_edit.setText(settings.get('smtp_password', ''))
        self.smtp_use_tls_check.setChecked(settings.get('smtp_use_tls', 'true').lower() == 'true')
        self.smtp_from_email_edit.setText(settings.get('smtp_from_email', '<EMAIL>'))
        self.smtp_from_name_edit.setText(settings.get('smtp_from_name', 'فوترها'))

        # Set tax rate
        tax_rate = settings.get('tax_rate', '15')
        self.tax_rate_spin.setValue(int(tax_rate))

        # Set invoice prefix and number
        self.invoice_prefix_edit.setText(settings.get('invoice_prefix', 'INV-'))
        self.next_invoice_number_edit.setText(settings.get('next_invoice_number', '1001'))

        # Set theme
        theme = settings.get('theme', 'default')
        for i in range(self.theme_combo.count()):
            if self.theme_combo.itemData(i) == theme:
                self.theme_combo.setCurrentIndex(i)
                break

        # Set language
        language = settings.get('language', 'ar')
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == language:
                self.language_combo.setCurrentIndex(i)
                break

    def update_theme_description(self):
        """Update the theme description based on the selected theme."""
        theme_key = self.theme_combo.currentData()
        if theme_key in THEMES:
            self.theme_description.setText(THEMES[theme_key]["description"])
        else:
            self.theme_description.setText("")

    def on_theme_changed(self, _=None):
        """Handle theme change event.

        Args:
            _ (int, optional): Index of the selected theme (unused)
        """
        self.update_theme_description()

        # Get the selected theme key
        theme_key = self.theme_combo.currentData()

        # If theme manager is available, apply the theme
        if self.theme_manager:
            self.theme_manager.apply_theme(theme_key)

        # Emit signal with the selected theme key
        self.theme_changed.emit(theme_key)

    def on_language_changed(self, _=None):
        """Handle language change event.

        Args:
            _ (int, optional): Index of the selected language (unused)
        """
        # Get the selected language code
        language_code = self.language_combo.currentData()

        # Emit signal with the selected language code
        self.language_changed.emit(language_code)



    def save_settings(self):
        """Save settings to the database."""
        settings = {
            'company_name': self.company_name_edit.text(),
            'company_address': self.company_address_edit.text(),
            'company_phone': self.company_phone_edit.text(),
            'company_email': self.company_email_edit.text(),
            'company_logo': self.logo_path if hasattr(self, 'logo_path') else '',
            'tax_rate': str(self.tax_rate_spin.value()),
            'invoice_prefix': self.invoice_prefix_edit.text(),
            'next_invoice_number': self.next_invoice_number_edit.text(),
            'theme': self.theme_combo.currentData(),
            'language': self.language_combo.currentData(),
            'smtp_server': self.smtp_server_edit.text(),
            'smtp_port': self.smtp_port_edit.text(),
            'smtp_username': self.smtp_username_edit.text(),
            'smtp_password': self.smtp_password_edit.text(),
            'smtp_use_tls': 'true' if self.smtp_use_tls_check.isChecked() else 'false',
            'smtp_from_email': self.smtp_from_email_edit.text(),
            'smtp_from_name': self.smtp_from_name_edit.text()
        }

        try:
            conn = self.db_manager.connect()
            cursor = conn.cursor()

            for key, value in settings.items():
                cursor.execute(
                    "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
                    (value, key)
                )

            conn.commit()

            QMessageBox.information(self, tr("messages.success", "نجاح"), tr("messages.settings_saved", "تم حفظ الإعدادات بنجاح"))
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("messages.settings_save_error", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}"))
        finally:
            self.db_manager.close()

    def select_logo(self):
        """Select a logo file."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, tr("settings.select_logo_title", "اختيار شعار"), "", tr("settings.image_files", "صور (*.png *.jpg *.jpeg)")
        )

        if file_path:
            self.load_logo(file_path)
            self.logo_path = file_path

    def load_logo(self, file_path):
        """Load a logo from a file.

        Args:
            file_path (str): Path to the logo file
        """
        pixmap = QPixmap(file_path)
        if not pixmap.isNull():
            pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.logo_preview.setPixmap(pixmap)
            self.logo_path = file_path
        else:
            self.clear_logo()
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("settings.image_not_recognized", "لم يتم التعرف على ملف الصورة"))

    def clear_logo(self):
        """Clear the logo."""
        self.logo_preview.clear()
        self.logo_preview.setText(tr("settings.no_logo", "لا يوجد شعار"))
        if hasattr(self, 'logo_path'):
            delattr(self, 'logo_path')

    def test_email(self):
        """Test email settings by sending a test email in a separate thread."""
        # Get email settings
        smtp_server = self.smtp_server_edit.text().strip()
        smtp_port = self.smtp_port_edit.text().strip()
        smtp_username = self.smtp_username_edit.text().strip()
        smtp_password = self.smtp_password_edit.text().strip()
        smtp_use_tls = self.smtp_use_tls_check.isChecked()
        smtp_from_email = self.smtp_from_email_edit.text().strip()
        smtp_from_name = self.smtp_from_name_edit.text().strip()

        # Validate settings
        if not smtp_server or not smtp_port:
            QMessageBox.warning(
                self,
                tr("settings.email_error", "خطأ في البريد الإلكتروني"),
                tr("settings.email_server_required", "يجب إدخال خادم SMTP ومنفذ SMTP")
            )
            return

        if not smtp_username or not smtp_password:
            QMessageBox.warning(
                self,
                tr("settings.email_error", "خطأ في البريد الإلكتروني"),
                tr("settings.email_credentials_required", "يجب إدخال اسم المستخدم وكلمة المرور")
            )
            return

        if not smtp_from_email:
            QMessageBox.warning(
                self,
                tr("settings.email_error", "خطأ في البريد الإلكتروني"),
                tr("settings.email_from_required", "يجب إدخال البريد الإلكتروني المرسل")
            )
            return

        # Validate sender email format
        if '@' not in smtp_from_email or '.' not in smtp_from_email:
            QMessageBox.warning(
                self,
                tr("settings.invalid_email", "بريد إلكتروني غير صالح"),
                tr("settings.invalid_sender_email", "عنوان البريد الإلكتروني للمرسل غير صالح")
            )
            return

        # Validate port number
        try:
            port_num = int(smtp_port)
            if port_num <= 0 or port_num > 65535:
                raise ValueError("Port out of range")
        except ValueError:
            QMessageBox.warning(
                self,
                tr("settings.invalid_port", "رقم منفذ غير صالح"),
                tr("settings.invalid_port_message", "يرجى إدخال رقم منفذ صالح (1-65535)")
            )
            return

        # Get test recipient
        from PySide6.QtWidgets import QInputDialog
        recipient, ok = QInputDialog.getText(
            self,
            tr("settings.test_email", "اختبار البريد الإلكتروني"),
            tr("settings.test_email_recipient", "أدخل البريد الإلكتروني لإرسال رسالة اختبار إليه:")
        )

        if not ok or not recipient:
            return

        # Validate recipient email format with more strict validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, recipient):
            QMessageBox.warning(
                self,
                tr("settings.invalid_email", "بريد إلكتروني غير صالح"),
                tr("settings.invalid_email_message", "يرجى إدخال عنوان بريد إلكتروني صالح")
            )
            return

        # Print debug info
        print(f"Test Email: Starting email test to {recipient}")
        print(f"Test Email: SMTP Server: {smtp_server}, Port: {smtp_port}")
        print(f"Test Email: Username: {smtp_username}, Use TLS: {smtp_use_tls}")

        # Check for common SMTP server configurations
        common_smtp_servers = {
            "gmail.com": {"server": "smtp.gmail.com", "ports": [587, 465]},
            "yahoo.com": {"server": "smtp.mail.yahoo.com", "ports": [587, 465]},
            "outlook.com": {"server": "smtp-mail.outlook.com", "ports": [587]},
            "hotmail.com": {"server": "smtp-mail.outlook.com", "ports": [587]},
            "live.com": {"server": "smtp-mail.outlook.com", "ports": [587]},
            "office365.com": {"server": "smtp.office365.com", "ports": [587]},
            "aol.com": {"server": "smtp.aol.com", "ports": [587, 465]},
            "zoho.com": {"server": "smtp.zoho.com", "ports": [587, 465]},
            "mail.ru": {"server": "smtp.mail.ru", "ports": [465]},
            "yandex.com": {"server": "smtp.yandex.com", "ports": [465]},
        }

        # Check if the email domain matches a known provider
        email_domain = smtp_from_email.split('@')[-1].lower()
        for domain, config in common_smtp_servers.items():
            if email_domain.endswith(domain):
                if smtp_server != config["server"]:
                    if QMessageBox.question(
                        self,
                        tr("settings.smtp_suggestion", "اقتراح إعدادات SMTP"),
                        tr("settings.smtp_suggestion_message", f"يبدو أنك تستخدم {domain}. هل تريد استخدام خادم SMTP الموصى به ({config['server']}) بدلاً من ({smtp_server})؟"),
                        QMessageBox.Yes | QMessageBox.No
                    ) == QMessageBox.Yes:
                        smtp_server = config["server"]
                        self.smtp_server_edit.setText(smtp_server)

                if int(smtp_port) not in config["ports"]:
                    recommended_port = config["ports"][0]
                    if QMessageBox.question(
                        self,
                        tr("settings.port_suggestion", "اقتراح منفذ SMTP"),
                        tr("settings.port_suggestion_message", f"يبدو أن المنفذ الموصى به لـ {domain} هو {recommended_port}. هل تريد استخدام هذا المنفذ بدلاً من {smtp_port}؟"),
                        QMessageBox.Yes | QMessageBox.No
                    ) == QMessageBox.Yes:
                        smtp_port = str(recommended_port)
                        self.smtp_port_edit.setText(smtp_port)

                        # Update TLS setting based on port
                        if recommended_port == 465:
                            smtp_use_tls = True
                            self.smtp_use_tls_check.setChecked(True)
                break

        # Create progress dialog
        progress = QProgressDialog(
            tr("settings.sending_email", "جاري إرسال البريد الإلكتروني..."),
            tr("settings.cancel", "إلغاء"),
            0, 0, self
        )
        progress.setWindowTitle(tr("settings.test_email", "اختبار البريد الإلكتروني"))
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)  # Show immediately

        # Create worker and thread
        self.email_thread = QThread()
        self.email_worker = EmailWorker(
            smtp_server, smtp_port, smtp_username, smtp_password,
            smtp_use_tls, smtp_from_email, smtp_from_name, recipient
        )
        self.email_worker.moveToThread(self.email_thread)

        # Connect signals
        self.email_thread.started.connect(self.email_worker.send_email)
        self.email_worker.finished.connect(self.email_thread.quit)
        self.email_worker.finished.connect(self.email_worker.deleteLater)
        self.email_thread.finished.connect(self.email_thread.deleteLater)
        self.email_thread.finished.connect(progress.close)

        # Connect success and error signals
        self.email_worker.success.connect(self.on_email_success)
        self.email_worker.error.connect(self.on_email_error)

        # Connect cancel button
        progress.canceled.connect(self.cancel_email_test)

        # Start the thread
        self.email_thread.start()

        # Show progress dialog
        progress.exec()

    def cleanup(self):
        """Clean up resources before the widget is destroyed."""
        # Stop any running email threads
        if hasattr(self, 'email_thread') and self.email_thread.isRunning():
            self.email_thread.quit()
            self.email_thread.wait(1000)  # Wait for 1 second

    def on_email_success(self, recipient):
        """Handle successful email sending.

        Args:
            recipient (str): Email recipient
        """
        QMessageBox.information(
            self,
            tr("settings.email_success", "نجاح"),
            tr("settings.email_sent", f"تم إرسال رسالة اختبار بنجاح إلى {recipient}")
        )

    def on_email_error(self, error_message):
        """Handle email sending error.

        Args:
            error_message (str): Error message
        """
        # Add troubleshooting tips based on the error message
        troubleshooting_tips = ""

        if "authentication failed" in error_message.lower() or "اسم المستخدم أو كلمة المرور غير صحيحة" in error_message:
            troubleshooting_tips = tr("settings.auth_error_tips", """
            نصائح لحل المشكلة:
            • تأكد من صحة اسم المستخدم وكلمة المرور
            • إذا كنت تستخدم Gmail، قد تحتاج إلى:
              - تمكين "وصول التطبيقات الأقل أمانًا" في إعدادات الحساب
              - أو إنشاء "كلمة مرور للتطبيق" إذا كان التحقق بخطوتين مفعلاً
            """)
        elif "starttls" in error_message.lower():
            troubleshooting_tips = tr("settings.starttls_error_tips", """
            نصائح لحل المشكلة:
            • استخدم المنفذ 465 مع SSL بدلاً من TLS
            • أو قم بتعطيل خيار TLS واستخدم المنفذ 587
            • تأكد من أن خادم SMTP يدعم STARTTLS
            """)
        elif "timeout" in error_message.lower() or "timed out" in error_message.lower():
            troubleshooting_tips = tr("settings.timeout_error_tips", """
            نصائح لحل المشكلة:
            • تأكد من اتصالك بالإنترنت
            • تأكد من صحة عنوان خادم SMTP
            • تحقق من إعدادات جدار الحماية
            """)
        elif "refused" in error_message.lower():
            troubleshooting_tips = tr("settings.connection_refused_tips", """
            نصائح لحل المشكلة:
            • تأكد من صحة رقم المنفذ
            • تأكد من أن خادم SMTP يقبل اتصالات خارجية
            • تحقق من إعدادات جدار الحماية
            """)

        # Create the full error message
        full_error_message = tr("settings.email_send_error", f"حدث خطأ أثناء إرسال البريد الإلكتروني: {error_message}")

        # Add troubleshooting tips if available
        if troubleshooting_tips:
            full_error_message += f"\n\n{troubleshooting_tips}"

        QMessageBox.critical(
            self,
            tr("settings.email_error", "خطأ في البريد الإلكتروني"),
            full_error_message
        )

    def cancel_email_test(self):
        """Cancel the email test."""
        if hasattr(self, 'email_thread') and self.email_thread.isRunning():
            print("Test Email: Cancelling email test")
            self.email_thread.quit()
            self.email_thread.wait(1000)  # Wait for 1 second
