#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
User Model for فوترها (Fawterha)
Represents a user in the system
"""

from datetime import datetime

class User:
    """User model class."""

    # User roles
    ROLE_ADMIN = 'admin'
    ROLE_MANAGER = 'manager'
    ROLE_CASHIER = 'cashier'
    ROLE_ACCOUNTANT = 'accountant'
    ROLE_INVENTORY = 'inventory'

    def __init__(self, id=None, username="", password="", name="", role=ROLE_CASHIER,
                 is_active=True, created_at=None, updated_at=None):
        """Initialize a user object.

        Args:
            id (int, optional): User ID. Defaults to None.
            username (str, optional): Username. Defaults to "".
            password (str, optional): Password. Defaults to "".
            name (str, optional): Full name. Defaults to "".
            role (str, optional): User role. Defaults to ROLE_CASHIER.
            is_active (bool, optional): Whether the user is active. Defaults to True.
            created_at (datetime, optional): Creation timestamp. Defaults to None.
            updated_at (datetime, optional): Update timestamp. Defaults to None.
        """
        self.id = id
        self.username = username
        self.password = password
        self.name = name
        self.role = role
        self.is_active = is_active
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create a User object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            User: User object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)

        # Create the user object with all fields from the row
        user = cls(
            id=row_dict.get('id'),
            username=row_dict.get('username', ''),
            password=row_dict.get('password', ''),
            name=row_dict.get('name', ''),
            role=row_dict.get('role', cls.ROLE_CASHIER),
            is_active=bool(row_dict.get('is_active', True)),
            created_at=row_dict.get('created_at'),
            updated_at=row_dict.get('updated_at')
        )

        return user

    def to_dict(self):
        """Convert the user object to a dictionary.

        Returns:
            dict: Dictionary representation of the user
        """
        return {
            'id': self.id,
            'username': self.username,
            'name': self.name,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def has_permission(self, permission):
        """Check if the user has a specific permission.

        Args:
            permission (str): Permission to check

        Returns:
            bool: True if the user has the permission, False otherwise
        """
        # Admin has all permissions
        if self.role == self.ROLE_ADMIN:
            return True

        # Manager has all permissions except user management
        if self.role == self.ROLE_MANAGER:
            if permission in ['manage_users', 'delete_user']:
                return False
            return True

        # Cashier has limited permissions
        if self.role == self.ROLE_CASHIER:
            cashier_permissions = [
                'create_pos_session', 'close_pos_session',
                'create_pos_transaction', 'view_pos_transactions',
                'create_invoice', 'view_invoice', 'view_products',
                'view_customers', 'create_customer'
            ]
            return permission in cashier_permissions

        # Accountant has accounting-related permissions
        if self.role == self.ROLE_ACCOUNTANT:
            accountant_permissions = [
                'view_invoices', 'view_reports', 'view_transactions',
                'create_transaction', 'view_accounts', 'view_customers',
                'view_products', 'export_reports', 'view_accounting_periods'
            ]
            return permission in accountant_permissions

        # Inventory supervisor has inventory-related permissions
        if self.role == self.ROLE_INVENTORY:
            inventory_permissions = [
                'view_products', 'create_product', 'edit_product',
                'view_inventory', 'update_inventory', 'view_inventory_transactions',
                'create_inventory_transaction', 'view_pos_inventory',
                'update_pos_inventory', 'view_inventory_reports'
            ]
            return permission in inventory_permissions

        return False

    def __str__(self):
        """Return a string representation of the user.

        Returns:
            str: String representation
        """
        return f"{self.name} ({self.username})"
