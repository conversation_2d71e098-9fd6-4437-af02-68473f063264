#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Translation Manager for فوترها (Fawterha)
Manages translations and language switching
"""

import os
import json
import logging
from PySide6.QtCore import QObject, Signal, QCoreApplication, QTranslator

# Set up logging
logger = logging.getLogger(__name__)

class TranslationManager(QObject):
    """Manages translations and language switching for the application."""
    
    # Signal emitted when language changes
    language_changed = Signal(str)
    
    def __init__(self, app=None):
        """Initialize the translation manager.
        
        Args:
            app: The QApplication instance
        """
        super().__init__()
        self.app = app
        self.translator = QTranslator()
        self.current_language = "ar"  # Default to Arabic
        self.translations = {}
        self.available_languages = []
        self.load_available_languages()
    
    def load_available_languages(self):
        """Load the list of available languages from the translations directory."""
        try:
            translations_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "translations")
            self.available_languages = []
            
            if not os.path.exists(translations_dir):
                logger.error(f"Translations directory not found: {translations_dir}")
                return
            
            for filename in os.listdir(translations_dir):
                if filename.endswith(".json"):
                    lang_code = filename.split(".")[0]
                    try:
                        with open(os.path.join(translations_dir, filename), 'r', encoding='utf-8') as f:
                            lang_data = json.load(f)
                            language_name = lang_data.get("language_name", lang_code)
                            self.available_languages.append({
                                "code": lang_code,
                                "name": language_name,
                                "direction": lang_data.get("direction", "ltr")
                            })
                    except Exception as e:
                        logger.error(f"Error loading language {lang_code}: {str(e)}")
            
            logger.info(f"Available languages: {', '.join([lang['name'] for lang in self.available_languages])}")
        except Exception as e:
            logger.error(f"Error loading available languages: {str(e)}")
    
    def get_available_languages(self):
        """Get the list of available languages.
        
        Returns:
            list: List of available languages with code and name
        """
        return self.available_languages
    
    def load_language(self, language_code):
        """Load a language by its code.
        
        Args:
            language_code (str): The language code to load
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            translations_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "translations")
            file_path = os.path.join(translations_dir, f"{language_code}.json")
            
            if not os.path.exists(file_path):
                logger.error(f"Translation file not found: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                self.translations = json.load(f)
            
            self.current_language = language_code
            
            # Emit signal that language has changed
            self.language_changed.emit(language_code)
            
            logger.info(f"Loaded language: {language_code}")
            return True
        except Exception as e:
            logger.error(f"Error loading language {language_code}: {str(e)}")
            return False
    
    def get_text(self, key, default=None):
        """Get translated text for a key.
        
        Args:
            key (str): The translation key in dot notation (e.g., "common.save")
            default (str, optional): Default text if key not found
            
        Returns:
            str: Translated text or default if not found
        """
        try:
            parts = key.split('.')
            value = self.translations
            
            for part in parts:
                if part in value:
                    value = value[part]
                else:
                    return default if default is not None else key
            
            return value
        except Exception as e:
            logger.error(f"Error getting translation for key {key}: {str(e)}")
            return default if default is not None else key
    
    def get_direction(self):
        """Get the text direction for the current language.
        
        Returns:
            str: "rtl" for right-to-left languages, "ltr" otherwise
        """
        try:
            return self.translations.get("direction", "ltr")
        except Exception:
            return "ltr"
    
    def is_rtl(self):
        """Check if the current language is right-to-left.
        
        Returns:
            bool: True if RTL, False otherwise
        """
        return self.get_direction() == "rtl"


# Global instance
_instance = None

def get_translation_manager(app=None):
    """Get the global TranslationManager instance.
    
    Args:
        app: The QApplication instance (only needed on first call)
        
    Returns:
        TranslationManager: The global instance
    """
    global _instance
    if _instance is None:
        _instance = TranslationManager(app)
    return _instance

def tr(key, default=None):
    """Shorthand function to get translated text.
    
    Args:
        key (str): The translation key in dot notation
        default (str, optional): Default text if key not found
        
    Returns:
        str: Translated text
    """
    return get_translation_manager().get_text(key, default)
