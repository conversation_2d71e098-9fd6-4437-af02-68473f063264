#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Transaction Model for فوترها (Fawterha)
Represents a financial transaction in the accounting system
"""

from datetime import datetime


class Transaction:
    """Transaction model class."""

    # Transaction statuses
    STATUS_DRAFT = 'draft'
    STATUS_POSTED = 'posted'
    STATUS_VOIDED = 'voided'

    # Reference types
    REF_TYPE_INVOICE = 'invoice'
    REF_TYPE_PAYMENT = 'payment'
    REF_TYPE_EXPENSE = 'expense'
    REF_TYPE_MANUAL = 'manual'

    def __init__(self, id=None, transaction_number="", transaction_date=None, description="",
                 reference_type=None, reference_id=None, amount=0.0, currency_id=None,
                 status=STATUS_POSTED, created_by="", created_at=None, updated_at=None,
                 details=None):
        """Initialize a transaction.

        Args:
            id (int, optional): Transaction ID
            transaction_number (str): Transaction number
            transaction_date (datetime): Transaction date
            description (str): Transaction description
            reference_type (str, optional): Reference type (invoice, payment, expense, manual)
            reference_id (int, optional): Reference ID
            amount (float): Transaction amount
            currency_id (int, optional): Currency ID
            status (str): Transaction status (draft, posted, voided)
            created_by (str): User who created the transaction
            created_at (datetime, optional): Creation timestamp
            updated_at (datetime, optional): Last update timestamp
            details (list, optional): List of TransactionDetail objects
        """
        self.id = id
        self.transaction_number = transaction_number
        self.transaction_date = transaction_date or datetime.now().date()
        self.description = description
        self.reference_type = reference_type
        self.reference_id = reference_id
        self.amount = amount
        self.currency_id = currency_id
        self.status = status
        self.created_by = created_by
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        self.details = details or []

    @classmethod
    def from_db_row(cls, row, details=None):
        """Create a Transaction object from a database row.

        Args:
            row (tuple or dict): Database row containing transaction data
            details (list, optional): List of TransactionDetail objects

        Returns:
            Transaction: Transaction object
        """
        # Handle both tuple and dictionary formats
        if isinstance(row, dict):
            return cls(
                id=row.get('id'),
                transaction_number=row.get('transaction_number', ''),
                transaction_date=row.get('transaction_date'),
                description=row.get('description', ''),
                reference_type=row.get('reference_type'),
                reference_id=row.get('reference_id'),
                amount=float(row.get('amount', 0.0)),
                currency_id=row.get('currency_id'),
                status=row.get('status', cls.STATUS_POSTED),
                created_by=row.get('created_by', ''),
                created_at=row.get('created_at'),
                updated_at=row.get('updated_at'),
                details=details
            )
        else:
            # Handle tuple format (indexed access)
            try:
                return cls(
                    id=row[0],
                    transaction_number=row[1],
                    transaction_date=row[2],
                    description=row[3],
                    reference_type=row[4],
                    reference_id=row[5],
                    amount=float(row[6]),
                    currency_id=row[7],
                    status=row[8],
                    created_by=row[9],
                    created_at=row[10],
                    updated_at=row[11],
                    details=details
                )
            except (IndexError, TypeError) as e:
                print(f"Error creating Transaction from row: {e}")
                print(f"Row data: {row}")
                # Return a default transaction object
                return cls()

    def to_dict(self):
        """Convert the transaction to a dictionary.

        Returns:
            dict: Dictionary representation of the transaction
        """
        return {
            'id': self.id,
            'transaction_number': self.transaction_number,
            'transaction_date': self.transaction_date,
            'description': self.description,
            'reference_type': self.reference_type,
            'reference_id': self.reference_id,
            'amount': self.amount,
            'currency_id': self.currency_id,
            'status': self.status,
            'created_by': self.created_by,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'details': [detail.to_dict() for detail in self.details] if self.details else []
        }

    def __str__(self):
        """Return a string representation of the transaction.

        Returns:
            str: String representation
        """
        return f"{self.transaction_number} - {self.description}"

    def is_balanced(self):
        """Check if the transaction is balanced (debits = credits).

        Returns:
            bool: True if the transaction is balanced, False otherwise
        """
        if not self.details:
            return False

        total_debit = sum(detail.debit for detail in self.details)
        total_credit = sum(detail.credit for detail in self.details)

        # Check if debits equal credits (with a small tolerance for floating point errors)
        return abs(total_debit - total_credit) < 0.001
