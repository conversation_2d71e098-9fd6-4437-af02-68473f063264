#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Products View for فوترها (Fawterha)
Manages products and services data display and editing
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QLineEdit, QFormLayout,
    QDialog, QMessageBox, QHeaderView, QAbstractItemView,
    QComboBox, QDoubleSpinBox, QTextEdit, QTabWidget, QDateEdit
)
from PySide6.QtCore import Qt, Signal, QDate
from PySide6.QtGui import QFont, QColor

from models.product import Product
from utils.currency_helper import get_currency_symbol, format_currency
from utils.translation_manager import tr


class ProductDialog(QDialog):
    """Dialog for adding or editing a product."""

    def __init__(self, parent=None, product=None):
        """Initialize the product dialog.

        Args:
            parent: Parent widget
            product (Product, optional): Product to edit. Defaults to None.
        """
        super().__init__(parent)

        self.product = product
        self.setWindowTitle(tr("products.add_new", "إضافة منتج/خدمة جديدة") if not product else tr("products.edit", "تعديل بيانات المنتج/الخدمة"))
        self.setMinimumWidth(500)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Get db_manager from parent
        self.db_manager = parent.db_manager if hasattr(parent, 'db_manager') else None

        # Create layout
        layout = QVBoxLayout(self)

        # Create form layout
        form_layout = QFormLayout()
        layout.addLayout(form_layout)

        # Add form fields
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("products.enter_name", "أدخل اسم المنتج أو الخدمة"))
        form_layout.addRow(tr("products.name", "الاسم:"), self.name_edit)

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText(tr("products.enter_description", "أدخل وصف المنتج أو الخدمة"))
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow(tr("common.description", "الوصف:"), self.description_edit)

        # Get primary currency
        currency_symbol = tr("currency.sar_symbol", "ر.س")  # Default symbol
        try:
            if hasattr(parent, 'currency_manager') and parent.currency_manager:
                primary_currency = parent.currency_manager.get_primary_currency()
                if primary_currency:
                    currency_symbol = primary_currency.symbol
            elif hasattr(parent, 'db_manager'):
                # Fallback to getting currency from settings
                db_manager = parent.db_manager
                currency_query = "SELECT value FROM settings WHERE key = 'currency'"
                currency_rows = db_manager.execute_query(currency_query)
                currency = currency_rows[0]['value'] if currency_rows else "SAR"
                currency_symbol = get_currency_symbol(currency)
        except Exception as e:
            print(f"Error getting default currency: {e}")

        self.price_spin = QDoubleSpinBox()
        self.price_spin.setMinimum(0.0)
        self.price_spin.setMaximum(9999999.99)
        self.price_spin.setDecimals(2)
        self.price_spin.setSuffix(f" {currency_symbol}")
        form_layout.addRow(tr("products.price", "السعر:"), self.price_spin)

        self.tax_rate_spin = QDoubleSpinBox()
        self.tax_rate_spin.setMinimum(0.0)
        self.tax_rate_spin.setMaximum(100.0)
        self.tax_rate_spin.setDecimals(2)
        self.tax_rate_spin.setSuffix(" %")
        self.tax_rate_spin.setValue(15.0)  # Default tax rate
        form_layout.addRow(tr("products.tax_rate", "نسبة الضريبة:"), self.tax_rate_spin)

        self.type_combo = QComboBox()
        self.type_combo.addItem(tr("products.product_type", "منتج"), "product")
        self.type_combo.addItem(tr("products.service_type", "خدمة"), "service")
        form_layout.addRow(tr("products.type", "النوع:"), self.type_combo)

        # Add inventory fields (only visible for products)
        self.inventory_widget = QWidget()
        inventory_layout = QFormLayout(self.inventory_widget)

        self.track_inventory_check = QComboBox()
        self.track_inventory_check.addItem(tr("common.no", "لا"), False)
        self.track_inventory_check.addItem(tr("common.yes", "نعم"), True)
        inventory_layout.addRow(tr("inventory.track_inventory", "تتبع المخزون:"), self.track_inventory_check)

        self.stock_quantity_spin = QDoubleSpinBox()
        self.stock_quantity_spin.setMinimum(0)
        self.stock_quantity_spin.setMaximum(999999)
        self.stock_quantity_spin.setDecimals(0)
        inventory_layout.addRow(tr("inventory.current_quantity", "الكمية الحالية:"), self.stock_quantity_spin)

        self.min_stock_level_spin = QDoubleSpinBox()
        self.min_stock_level_spin.setMinimum(0)
        self.min_stock_level_spin.setMaximum(999999)
        self.min_stock_level_spin.setDecimals(0)
        inventory_layout.addRow(tr("inventory.min_stock_level", "الحد الأدنى للمخزون:"), self.min_stock_level_spin)

        # Add expiry date field
        self.expiry_date_edit = QDateEdit()
        self.expiry_date_edit.setCalendarPopup(True)
        self.expiry_date_edit.setDisplayFormat("yyyy-MM-dd")
        self.expiry_date_edit.setMinimumDate(QDate.currentDate())
        self.expiry_date_edit.setSpecialValueText(tr("inventory.no_expiry", "بدون تاريخ انتهاء"))
        self.expiry_date_edit.setDate(QDate.currentDate().addYears(1))  # Default to 1 year from now
        inventory_layout.addRow(tr("inventory.expiry_date", "تاريخ انتهاء الصلاحية:"), self.expiry_date_edit)

        # Add barcode field
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText(tr("inventory.barcode_placeholder", "سيتم إنشاؤه تلقائياً"))
        self.barcode_edit.setReadOnly(True)
        inventory_layout.addRow(tr("inventory.barcode", "الباركود:"), self.barcode_edit)

        # Add warehouse selection
        self.warehouse_combo = QComboBox()
        # We'll populate this in the constructor after we have access to the db_manager
        inventory_layout.addRow(tr("inventory.warehouse", "المستودع:"), self.warehouse_combo)

        form_layout.addRow(tr("inventory.management", "إدارة المخزون:"), self.inventory_widget)

        # Show/hide inventory fields based on product type
        self.type_combo.currentIndexChanged.connect(self.toggle_inventory_fields)

        # Add buttons
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)

        self.save_button = QPushButton(tr("common.save", "حفظ"))
        self.save_button.setProperty("style", "primary")  # Set property for theme-aware styling
        self.save_button.setMinimumHeight(40)
        self.save_button.setMinimumWidth(120)
        self.save_button.clicked.connect(self.accept)
        button_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.setProperty("style", "secondary")  # Set property for theme-aware styling
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.setMinimumWidth(120)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        # Fill form if editing an existing product
        if product:
            self.name_edit.setText(product.name)
            self.description_edit.setText(product.description)
            self.price_spin.setValue(product.price)
            self.tax_rate_spin.setValue(product.tax_rate)

            # Set product type
            for i in range(self.type_combo.count()):
                if self.type_combo.itemData(i) == product.type:
                    self.type_combo.setCurrentIndex(i)
                    break

            # Set inventory fields
            self.track_inventory_check.setCurrentIndex(1 if product.track_inventory else 0)
            self.stock_quantity_spin.setValue(product.stock_quantity)
            self.min_stock_level_spin.setValue(product.min_stock_level)

            # Set expiry date if available
            if hasattr(product, 'expiry_date') and product.expiry_date:
                try:
                    expiry_date = QDate.fromString(product.expiry_date, "yyyy-MM-dd")
                    if expiry_date.isValid():
                        self.expiry_date_edit.setDate(expiry_date)
                except Exception as e:
                    print(f"Error setting expiry date: {str(e)}")

            # Set barcode if available
            if hasattr(product, 'barcode') and product.barcode:
                self.barcode_edit.setText(product.barcode)

        # Load warehouses
        self.load_warehouses()

        # Initialize inventory fields visibility
        self.toggle_inventory_fields()

    def load_warehouses(self):
        """Load warehouses into the warehouse combo box."""
        if not self.db_manager:
            return

        try:
            # Clear the combo box
            self.warehouse_combo.clear()

            # Add default warehouse
            self.warehouse_combo.addItem(tr("inventory.default_warehouse", "المستودع الرئيسي"), 1)

            # Get warehouses from database
            query = """
            SELECT id, name FROM warehouses
            WHERE is_active = 1
            ORDER BY name
            """
            rows = self.db_manager.execute_query(query)

            # Add warehouses to combo box
            for row in rows:
                if row['id'] != 1:  # Skip default warehouse (already added)
                    self.warehouse_combo.addItem(row['name'], row['id'])

            # Set current warehouse
            if self.product and hasattr(self.product, 'warehouse_id') and self.product.warehouse_id:
                index = self.warehouse_combo.findData(self.product.warehouse_id)
                if index >= 0:
                    self.warehouse_combo.setCurrentIndex(index)
        except Exception as e:
            print(f"Error loading warehouses: {str(e)}")

    def toggle_inventory_fields(self):
        """Show/hide inventory fields based on product type."""
        is_product = self.type_combo.currentData() == "product"
        self.inventory_widget.setVisible(is_product)

    def get_product_data(self):
        """Get the product data from the form.

        Returns:
            dict: Product data
        """
        product_type = self.type_combo.currentData()
        data = {
            'name': self.name_edit.text(),
            'description': self.description_edit.toPlainText(),
            'price': self.price_spin.value(),
            'tax_rate': self.tax_rate_spin.value(),
            'type': product_type
        }

        # Add inventory data only for products
        if product_type == "product":
            # Get expiry date
            expiry_date = None
            if self.expiry_date_edit.date() != self.expiry_date_edit.minimumDate():
                expiry_date = self.expiry_date_edit.date().toString("yyyy-MM-dd")

            data.update({
                'track_inventory': self.track_inventory_check.currentData(),
                'stock_quantity': int(self.stock_quantity_spin.value()),
                'min_stock_level': int(self.min_stock_level_spin.value()),
                'expiry_date': expiry_date,
                'barcode': self.barcode_edit.text() if self.barcode_edit.text() else None,
                'warehouse_id': self.warehouse_combo.currentData()
            })

        return data


class ProductsView(QWidget):
    """Widget for managing products and services."""

    product_selected = Signal(Product)

    def __init__(self, db_manager, select_mode=False, currency_manager=None):
        """Initialize the products view.

        Args:
            db_manager: Database manager instance
            select_mode (bool, optional): Whether to enable product selection mode. Defaults to False.
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.select_mode = select_mode
        self.currency_manager = currency_manager

        # Get primary currency
        self.primary_currency = None
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()

        # Create layout
        layout = QVBoxLayout(self)

        # Create tabs for products and services
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # Create products tab
        self.products_tab = QWidget()
        self.tab_widget.addTab(self.products_tab, tr("products.products", "المنتجات"))

        # Create services tab
        self.services_tab = QWidget()
        self.tab_widget.addTab(self.services_tab, tr("products.services", "الخدمات"))

        # Set up products tab
        self.setup_products_tab()

        # Set up services tab
        self.setup_services_tab()

        # Load products and services
        self.load_products()
        self.load_services()

    def setup_products_tab(self):
        """Set up the products tab."""
        layout = QVBoxLayout(self.products_tab)

        # Create toolbar
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)

        # Add new product button with theme-aware styling
        self.add_product_button = QPushButton(tr("products.add_new_product", "إضافة منتج جديد"))
        self.add_product_button.setProperty("style", "primary")  # Set property for theme-aware styling
        self.add_product_button.setMinimumHeight(40)
        self.add_product_button.clicked.connect(self.create_new_product)
        toolbar_layout.addWidget(self.add_product_button)

        # Add search field with theme-aware styling
        self.product_search_edit = QLineEdit()
        self.product_search_edit.setPlaceholderText(tr("products.search_product", "بحث عن منتج..."))
        self.product_search_edit.setMinimumHeight(40)
        self.product_search_edit.textChanged.connect(self.filter_products)
        toolbar_layout.addWidget(self.product_search_edit)

        # Create products table
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(5 if not self.select_mode else 6)
        headers = [
            tr("products.name", "الاسم"),
            tr("common.description", "الوصف"),
            tr("products.price", "السعر"),
            tr("products.tax_rate", "نسبة الضريبة"),
            ""
        ]
        if self.select_mode:
            headers.insert(4, tr("common.select", "اختيار"))
        self.products_table.setHorizontalHeaderLabels(headers)
        self.products_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.products_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.products_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.products_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        if self.select_mode:
            self.products_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
            self.products_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)
            self.products_table.setColumnWidth(5, 150)
        else:
            self.products_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Fixed)
            self.products_table.setColumnWidth(4, 150)
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.products_table.setAlternatingRowColors(True)
        self.products_table.verticalHeader().setVisible(False)
        self.products_table.setShowGrid(True)
        self.products_table.verticalHeader().setDefaultSectionSize(60)  # Increase row height
        layout.addWidget(self.products_table)

    def setup_services_tab(self):
        """Set up the services tab."""
        layout = QVBoxLayout(self.services_tab)

        # Create toolbar
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)

        # Add new service button with theme-aware styling
        self.add_service_button = QPushButton(tr("products.add_new_service", "إضافة خدمة جديدة"))
        self.add_service_button.setProperty("style", "success")  # Set property for theme-aware styling
        self.add_service_button.setMinimumHeight(40)
        self.add_service_button.clicked.connect(self.create_new_service)
        toolbar_layout.addWidget(self.add_service_button)

        # Add search field with theme-aware styling
        self.service_search_edit = QLineEdit()
        self.service_search_edit.setPlaceholderText(tr("products.search_service", "بحث عن خدمة..."))
        self.service_search_edit.setMinimumHeight(40)
        self.service_search_edit.textChanged.connect(self.filter_services)
        toolbar_layout.addWidget(self.service_search_edit)

        # Create services table
        self.services_table = QTableWidget()
        self.services_table.setColumnCount(5 if not self.select_mode else 6)
        headers = [
            tr("products.name", "الاسم"),
            tr("common.description", "الوصف"),
            tr("products.price", "السعر"),
            tr("products.tax_rate", "نسبة الضريبة"),
            ""
        ]
        if self.select_mode:
            headers.insert(4, tr("common.select", "اختيار"))
        self.services_table.setHorizontalHeaderLabels(headers)
        self.services_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.services_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.services_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.services_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        if self.select_mode:
            self.services_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
            self.services_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)
            self.services_table.setColumnWidth(5, 150)
        else:
            self.services_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Fixed)
            self.services_table.setColumnWidth(4, 150)
        self.services_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.services_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.services_table.setAlternatingRowColors(True)
        self.services_table.verticalHeader().setVisible(False)
        self.services_table.setShowGrid(True)
        self.services_table.verticalHeader().setDefaultSectionSize(60)  # Increase row height
        layout.addWidget(self.services_table)

    def load_products(self):
        """Load products from the database with error handling."""
        try:
            # Refresh primary currency
            if hasattr(self, 'currency_manager') and self.currency_manager:
                self.primary_currency = self.currency_manager.get_primary_currency()
                if self.primary_currency:
                    print(f"Products: Refreshed primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
                else:
                    print("Products: No primary currency found after refresh")

            query = "SELECT * FROM products WHERE type = 'product' ORDER BY name"
            rows = self.db_manager.execute_query(query)

            self.products_table.setRowCount(0)

            for row in rows:
                product = Product.from_db_row(row)
                self.add_product_to_table(product, self.products_table)
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("products.load_products_error", f"حدث خطأ أثناء تحميل المنتجات: {str(e)}"))

    def load_services(self):
        """Load services from the database with error handling."""
        try:
            # Refresh primary currency
            if hasattr(self, 'currency_manager') and self.currency_manager:
                self.primary_currency = self.currency_manager.get_primary_currency()
                if self.primary_currency:
                    print(f"Services: Refreshed primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
                else:
                    print("Services: No primary currency found after refresh")

            query = "SELECT * FROM products WHERE type = 'service' ORDER BY name"
            rows = self.db_manager.execute_query(query)

            self.services_table.setRowCount(0)

            for row in rows:
                product = Product.from_db_row(row)
                self.add_product_to_table(product, self.services_table)
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("products.load_services_error", f"حدث خطأ أثناء تحميل الخدمات: {str(e)}"))

    def add_product_to_table(self, product, table):
        """Add a product to the table with error handling.

        Args:
            product (Product): Product to add
            table (QTableWidget): Table to add the product to
        """
        try:
            row_position = table.rowCount()
            table.insertRow(row_position)

            # Get primary currency
            currency_symbol = tr("currency.sar_symbol", "ر.س")  # Default symbol
            if hasattr(self, 'primary_currency') and self.primary_currency:
                currency_symbol = self.primary_currency.symbol
            elif hasattr(self, 'currency_manager') and self.currency_manager:
                # Refresh primary currency
                self.primary_currency = self.currency_manager.get_primary_currency()
                if self.primary_currency:
                    currency_symbol = self.primary_currency.symbol
                    print(f"Refreshed primary currency: {self.primary_currency.code} ({currency_symbol})")
                else:
                    # Fallback to getting currency from settings
                    try:
                        currency_query = "SELECT value FROM settings WHERE key = 'currency'"
                        currency_rows = self.db_manager.execute_query(currency_query)
                        currency = currency_rows[0]['value'] if currency_rows else "SAR"
                        currency_symbol = get_currency_symbol(currency)
                    except Exception as e:
                        print(f"Error getting currency: {e}")
            else:
                # Fallback to getting currency from settings
                try:
                    currency_query = "SELECT value FROM settings WHERE key = 'currency'"
                    currency_rows = self.db_manager.execute_query(currency_query)
                    currency = currency_rows[0]['value'] if currency_rows else "SAR"
                    currency_symbol = get_currency_symbol(currency)
                except Exception as e:
                    print(f"Error getting currency: {e}")

            # Set product data
            table.setItem(row_position, 0, QTableWidgetItem(product.name))
            table.setItem(row_position, 1, QTableWidgetItem(product.description))

            # Format price with currency symbol
            if hasattr(self, 'primary_currency') and self.primary_currency:
                price_item = QTableWidgetItem(f"{product.price:.2f} {self.primary_currency.symbol}")
            else:
                price_item = QTableWidgetItem(f"{product.price:.2f} {currency_symbol}")

            price_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            table.setItem(row_position, 2, price_item)

            tax_item = QTableWidgetItem(f"{product.tax_rate:.2f}%")
            tax_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(row_position, 3, tax_item)
        except Exception as e:
            print(f"Error adding product to table: {str(e)}")

        # Add buttons based on mode
        if self.select_mode:
            try:
                # Add select button
                select_button = QPushButton(tr("common.select", "اختيار"))
                select_button.setStyleSheet("""
                    QPushButton {
                        background-color: #4caf50;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                        font-size: 10pt;
                        min-height: 28px;
                        margin: 2px;
                        max-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #388e3c;
                    }
                    QPushButton:pressed {
                        background-color: #2e7d32;
                    }
                """)
                # Use a lambda function that captures the current product
                current_product = product  # Create a local copy
                select_button.clicked.connect(lambda _, p=current_product: self.select_product(p))
                table.setCellWidget(row_position, 4, select_button)

                # Add edit button
                edit_button = QPushButton(tr("common.edit", "تعديل"))
                edit_button.setStyleSheet("""
                    QPushButton {
                        background-color: #1976d2;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                        font-size: 10pt;
                        min-height: 28px;
                        margin: 2px;
                        max-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #1565c0;
                    }
                    QPushButton:pressed {
                        background-color: #0d47a1;
                    }
                """)
                product_id = product.id  # Create a local copy of the ID
                edit_button.clicked.connect(lambda _, pid=product_id: self.edit_product(pid))
                table.setCellWidget(row_position, 5, edit_button)
            except Exception as e:
                print(f"Error setting up buttons in select mode: {str(e)}")
        else:
            try:
                # Add edit button (non-select mode)
                edit_button = QPushButton(tr("common.edit", "تعديل"))
                edit_button.setStyleSheet("""
                    QPushButton {
                        background-color: #1976d2;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-weight: bold;
                        font-size: 10pt;
                        min-height: 28px;
                        margin: 2px;
                        max-width: 100px;
                    }
                    QPushButton:hover {
                        background-color: #1565c0;
                    }
                    QPushButton:pressed {
                        background-color: #0d47a1;
                    }
                """)
                product_id = product.id  # Create a local copy of the ID
                edit_button.clicked.connect(lambda _, pid=product_id: self.edit_product(pid))
                table.setCellWidget(row_position, 4, edit_button)
            except Exception as e:
                print(f"Error setting up edit button in non-select mode: {str(e)}")

        # Store product ID in the first column item
        try:
            if table.item(row_position, 0):
                table.item(row_position, 0).setData(Qt.UserRole, product.id)
        except Exception as e:
            print(f"Error storing product ID in table: {str(e)}")

    def create_new_product(self):
        """Create a new product."""
        product = Product(type="product")
        self.show_product_dialog(product)

    def create_new_service(self):
        """Create a new service."""
        product = Product(type="service")
        self.show_product_dialog(product)

    def show_product_dialog(self, product):
        """Show the product dialog.

        Args:
            product (Product): Product to edit
        """
        dialog = ProductDialog(self, product)
        if dialog.exec():
            product_data = dialog.get_product_data()

            # Validate data
            if not product_data['name']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال اسم المنتج أو الخدمة")
                return

            if product.id:
                # Update existing product
                if product_data['type'] == 'product':
                    query = """
                    UPDATE products
                    SET name = ?, description = ?, price = ?, tax_rate = ?, type = ?,
                        stock_quantity = ?, min_stock_level = ?, track_inventory = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                    """
                    params = (
                        product_data['name'],
                        product_data['description'],
                        product_data['price'],
                        product_data['tax_rate'],
                        product_data['type'],
                        product_data['stock_quantity'],
                        product_data['min_stock_level'],
                        1 if product_data['track_inventory'] else 0,
                        product.id
                    )
                else:
                    # For services, don't update inventory fields
                    query = """
                    UPDATE products
                    SET name = ?, description = ?, price = ?, tax_rate = ?, type = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                    """
                    params = (
                        product_data['name'],
                        product_data['description'],
                        product_data['price'],
                        product_data['tax_rate'],
                        product_data['type'],
                        product.id
                    )

                try:
                    self.db_manager.execute_update(query, params)

                    # Reload products and services
                    self.load_products()
                    self.load_services()

                    QMessageBox.information(self, tr("messages.success", "نجاح"), tr("products.update_success", "تم تحديث بيانات المنتج/الخدمة بنجاح"))
                except Exception as e:
                    QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("products.update_error", f"حدث خطأ أثناء تحديث بيانات المنتج/الخدمة: {str(e)}"))
            else:
                # Insert new product
                if product_data['type'] == 'product':
                    query = """
                    INSERT INTO products (
                        name, description, price, tax_rate, type,
                        stock_quantity, min_stock_level, track_inventory
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        product_data['name'],
                        product_data['description'],
                        product_data['price'],
                        product_data['tax_rate'],
                        product_data['type'],
                        product_data['stock_quantity'],
                        product_data['min_stock_level'],
                        1 if product_data['track_inventory'] else 0
                    )
                else:
                    # For services, don't include inventory fields
                    query = """
                    INSERT INTO products (name, description, price, tax_rate, type)
                    VALUES (?, ?, ?, ?, ?)
                    """
                    params = (
                        product_data['name'],
                        product_data['description'],
                        product_data['price'],
                        product_data['tax_rate'],
                        product_data['type']
                    )

                try:
                    # Insert product and ignore returned ID
                    self.db_manager.execute_insert(query, params)

                    # Reload products and services
                    self.load_products()
                    self.load_services()

                    QMessageBox.information(self, tr("messages.success", "نجاح"), tr("products.add_success", "تم إضافة المنتج/الخدمة بنجاح"))
                except Exception as e:
                    QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("products.add_error", f"حدث خطأ أثناء إضافة المنتج/الخدمة: {str(e)}"))

    def edit_product(self, product_id):
        """Edit a product.

        Args:
            product_id (int): Product ID
        """
        # Get product from database
        query = "SELECT * FROM products WHERE id = ?"
        rows = self.db_manager.execute_query(query, (product_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("products.product_not_found", "لم يتم العثور على المنتج/الخدمة"))
            return

        product = Product.from_db_row(rows[0])

        # Show edit dialog
        self.show_product_dialog(product)

    def select_product(self, product):
        """Select a product and emit the product_selected signal.

        Args:
            product (Product): Selected product
        """
        try:
            self.selected_product = product
            self.product_selected.emit(product)
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("products.select_error", f"حدث خطأ أثناء اختيار المنتج: {str(e)}"))

    def get_selected_product(self):
        """Get the selected product.

        Returns:
            Product: The selected product
        """
        if hasattr(self, 'selected_product'):
            return self.selected_product
        return None

    def filter_products(self, text):
        """Filter products by search text.

        Args:
            text (str): Search text
        """
        self.filter_table(self.products_table, text)

    def filter_services(self, text):
        """Filter services by search text.

        Args:
            text (str): Search text
        """
        self.filter_table(self.services_table, text)

    def filter_table(self, table, text):
        """Filter a table by search text.

        Args:
            table (QTableWidget): Table to filter
            text (str): Search text
        """
        for row in range(table.rowCount()):
            show_row = False

            for col in range(2):  # Check first 2 columns (name and description)
                item = table.item(row, col)
                if item and text.lower() in item.text().lower():
                    show_row = True
                    break

            table.setRowHidden(row, not show_row)

    # Using the imported get_currency_symbol function instead
