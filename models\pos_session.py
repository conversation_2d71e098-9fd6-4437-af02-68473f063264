#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Session Model for فوترها (Fawterha)
Represents a point of sale session
"""

from datetime import datetime

class POSSession:
    """POS Session model class."""

    # Session statuses
    STATUS_OPEN = 'open'
    STATUS_CLOSED = 'closed'

    def __init__(self, id=None, user_id=None, start_time=None, end_time=None,
                 starting_cash=0.0, ending_cash=0.0, status=STATUS_OPEN,
                 notes="", created_at=None, updated_at=None):
        """Initialize a POS session object.

        Args:
            id (int, optional): Session ID. Defaults to None.
            user_id (int, optional): User ID. Defaults to None.
            start_time (datetime, optional): Session start time. Defaults to None.
            end_time (datetime, optional): Session end time. Defaults to None.
            starting_cash (float, optional): Starting cash amount. Defaults to 0.0.
            ending_cash (float, optional): Ending cash amount. Defaults to 0.0.
            status (str, optional): Session status. Defaults to STATUS_OPEN.
            notes (str, optional): Session notes. Defaults to "".
            created_at (datetime, optional): Creation timestamp. Defaults to None.
            updated_at (datetime, optional): Update timestamp. Defaults to None.
        """
        self.id = id
        self.user_id = user_id
        self.start_time = start_time or datetime.now()
        self.end_time = end_time
        self.starting_cash = starting_cash
        self.ending_cash = ending_cash
        self.status = status
        self.notes = notes
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        
        # Additional properties
        self.user = None
        self.transactions = []
        self.total_sales = 0.0
        self.total_cash = 0.0
        self.total_card = 0.0
        self.total_other = 0.0

    @classmethod
    def from_db_row(cls, row):
        """Create a POSSession object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            POSSession: POSSession object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)

        # Create the session object with all fields from the row
        session = cls(
            id=row_dict.get('id'),
            user_id=row_dict.get('user_id'),
            start_time=row_dict.get('start_time'),
            end_time=row_dict.get('end_time'),
            starting_cash=float(row_dict.get('starting_cash', 0.0)),
            ending_cash=float(row_dict.get('ending_cash', 0.0)),
            status=row_dict.get('status', cls.STATUS_OPEN),
            notes=row_dict.get('notes', ''),
            created_at=row_dict.get('created_at'),
            updated_at=row_dict.get('updated_at')
        )

        return session

    def to_dict(self):
        """Convert the session object to a dictionary.

        Returns:
            dict: Dictionary representation of the session
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'starting_cash': self.starting_cash,
            'ending_cash': self.ending_cash,
            'status': self.status,
            'notes': self.notes,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'total_sales': self.total_sales,
            'total_cash': self.total_cash,
            'total_card': self.total_card,
            'total_other': self.total_other
        }

    def close(self, ending_cash, notes=""):
        """Close the session.

        Args:
            ending_cash (float): Ending cash amount
            notes (str, optional): Closing notes. Defaults to "".

        Returns:
            bool: True if the session was closed successfully, False otherwise
        """
        if self.status == self.STATUS_CLOSED:
            return False

        self.ending_cash = ending_cash
        self.end_time = datetime.now()
        self.status = self.STATUS_CLOSED
        self.notes = notes
        self.updated_at = datetime.now()
        return True

    def is_open(self):
        """Check if the session is open.

        Returns:
            bool: True if the session is open, False otherwise
        """
        return self.status == self.STATUS_OPEN

    def __str__(self):
        """Return a string representation of the session.

        Returns:
            str: String representation
        """
        return f"Session #{self.id} ({self.status})"
