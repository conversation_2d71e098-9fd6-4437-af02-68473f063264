#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
فوترها - Fawterha
Invoice Management Application for Small Businesses
Copyright © Hadou Design

This is the main entry point for the application.
"""

import sys
import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale, QLibraryInfo
from PySide6.QtGui import QFont, QFontDatabase
from ui.main_window import MainWindow
from database.db_manager import DatabaseManager
from database.currency_manager import CurrencyManager
from database.language_manager import LanguageManager, get_language_manager
from ui.font_loader import load_fonts
from ui.theme_manager import ThemeManager
from utils.translation_manager import get_translation_manager


def setup_database():
    """Initialize the database if it doesn't exist and repair if needed.
    Includes enhanced error handling and recovery mechanisms.
    """
    from PySide6.QtWidgets import QMessageBox, QApplication
    import time

    # Create database manager
    db_manager = DatabaseManager()

    # Maximum number of attempts to setup the database
    max_attempts = 3
    attempt = 0

    while attempt < max_attempts:
        try:
            # Try to setup the database
            db_manager.setup_database()

            # If successful, verify the connection works
            try:
                # Simple test query
                test_query = "SELECT 1"
                result = db_manager.execute_query(test_query)
                if result and len(result) > 0:
                    print("Database connection verified successfully")
                    return db_manager
            except Exception as test_error:
                print(f"Database verification failed: {test_error}")
                # Continue to repair process
                raise test_error

        except Exception as e:
            attempt += 1
            print(f"Error setting up database (attempt {attempt}/{max_attempts}): {e}")

            # Process events to keep UI responsive
            QApplication.processEvents()

            # Show error message on first attempt only
            if attempt == 1:
                error_msg = f"حدث خطأ أثناء تحميل قاعدة البيانات: {str(e)}"
                QMessageBox.warning(None, "خطأ في قاعدة البيانات", error_msg)

            # Determine if we should try to repair
            error_str = str(e).lower()
            should_repair = any(msg in error_str for msg in [
                "database is locked",
                "disk image is malformed",
                "no such table",
                "closed database"
            ])

            if should_repair:
                # Only show repair message on first attempt
                if attempt == 1:
                    repair_msg = "سيتم محاولة إصلاح قاعدة البيانات الآن."
                    QMessageBox.information(None, "إصلاح قاعدة البيانات", repair_msg)

                print(f"Attempting database repair (attempt {attempt}/{max_attempts})...")

                # Attempt to repair
                if db_manager.repair_database():
                    print("Database repair reported success")

                    # Wait a moment to ensure file system operations complete
                    time.sleep(1)

                    # If this was the last attempt, inform the user
                    if attempt == max_attempts:
                        success_msg = "تم إصلاح قاعدة البيانات، ولكن قد تكون هناك مشاكل متبقية."
                        QMessageBox.information(None, "تم الإصلاح", success_msg)
                else:
                    print("Database repair reported failure")

                    # If this was the last attempt, show fatal error
                    if attempt == max_attempts:
                        fatal_msg = "فشل إصلاح قاعدة البيانات بعد عدة محاولات. يرجى إعادة تشغيل التطبيق."
                        QMessageBox.critical(None, "خطأ فادح", fatal_msg)
            else:
                # For other errors, wait and retry
                wait_time = 1 * attempt  # Increase wait time with each attempt
                print(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

                # If this was the last attempt, show fatal error
                if attempt == max_attempts:
                    fatal_msg = f"فشل الاتصال بقاعدة البيانات بعد {max_attempts} محاولات. يرجى إعادة تشغيل التطبيق."
                    QMessageBox.critical(None, "خطأ فادح", fatal_msg)

    # If we've exhausted all attempts, return the db_manager anyway
    # The application will try to handle database errors at runtime
    return db_manager


def setup_translation(app, db_manager):
    """Setup translation for the application.

    Args:
        app: The QApplication instance
        db_manager: Database manager instance

    Returns:
        QTranslator: The translator instance
    """
    # Create translator
    translator = QTranslator()

    # Initialize translation manager
    translation_manager = get_translation_manager(app)

    # Initialize language manager
    language_manager = get_language_manager(db_manager)

    # Get current language from settings
    current_language = language_manager.get_current_language()

    # Load the language
    translation_manager.load_language(current_language)

    print(f"Loaded language: {current_language}")

    return translator


def main():
    """Main application entry point."""
    # Create the application
    app = QApplication(sys.argv)
    app.setApplicationName("فوترها")
    app.setOrganizationName("Hadou Design")

    # Load custom fonts
    loaded_fonts = load_fonts()
    if loaded_fonts:
        print(f"تم تحميل الخطوط: {', '.join(loaded_fonts)}")
        # Set Almarai as default font if available
        if 'Almarai' in loaded_fonts:
            default_font = QFont('Almarai', 10)
            app.setFont(default_font)

    # Setup database
    db_manager = setup_database()

    # Setup currency manager
    currency_manager = CurrencyManager(db_manager)

    # Setup translation
    translator = setup_translation(app, db_manager)
    app.installTranslator(translator)

    # Get the global language manager instance
    language_manager = get_language_manager()

    # Create theme manager
    theme_manager = ThemeManager(db_manager)

    # Apply current theme
    theme_manager.apply_theme()

    # Create and show the main window
    main_window = MainWindow(db_manager, currency_manager, theme_manager, language_manager)

    # Connect theme changed signal from theme manager to main window
    theme_manager.theme_changed.connect(main_window.on_theme_changed)

    # Connect language changed signal from translation manager to main window
    translation_manager = get_translation_manager()
    translation_manager.language_changed.connect(main_window.on_language_changed)

    # Apply translations to UI
    main_window.apply_translations()

    main_window.show()

    # Start the event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
