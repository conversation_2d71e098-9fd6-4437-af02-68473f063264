#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Preview for فوترها (Fawterha)
Displays and prints invoices
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QScrollArea, QWidget, QFrame, QGridLayout, QSpacerItem,
    QSizePolicy, QFileDialog, QMessageBox, QComboBox
)
from PySide6.QtCore import Qt, QSize, QMargins, QUrl, QPoint
from PySide6.QtGui import QFont, QPixmap, QPainter, QPdfWriter, QPageLayout, QPageSize, QIcon
from PySide6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

import os
from datetime import datetime
from utils.currency_helper import get_currency_symbol, format_currency
from models.invoice_template import InvoiceTemplate


class InvoicePreviewDialog(QDialog):
    """Dialog for previewing and printing invoices."""

    def __init__(self, parent=None, invoice=None, customer=None, items=None, company_info=None, db_manager=None, template_id=None):
        """Initialize the invoice preview dialog.

        Args:
            parent: Parent widget
            invoice: Invoice object
            customer: Customer object
            items: List of invoice items
            company_info: Company information dictionary
            db_manager: Database manager instance
            template_id: Template ID to use (optional)
        """
        super().__init__(parent)

        self.invoice = invoice
        self.customer = customer
        self.items = items
        self.company_info = company_info
        self.db_manager = db_manager

        # Load template
        self.template = None
        if template_id:
            # Load specific template
            template_query = "SELECT * FROM invoice_templates WHERE id = ?"
            template_rows = self.db_manager.execute_query(template_query, (template_id,))
            if template_rows:
                self.template = InvoiceTemplate.from_db_row(template_rows[0])

        if not self.template:
            # Load default template
            default_template_query = "SELECT value FROM settings WHERE key = 'default_template_id'"
            default_template_rows = self.db_manager.execute_query(default_template_query)
            if default_template_rows and default_template_rows[0]['value']:
                template_id = default_template_rows[0]['value']
                template_query = "SELECT * FROM invoice_templates WHERE id = ?"
                template_rows = self.db_manager.execute_query(template_query, (template_id,))
                if template_rows:
                    self.template = InvoiceTemplate.from_db_row(template_rows[0])

        if not self.template:
            # Load first template as fallback
            template_query = "SELECT * FROM invoice_templates LIMIT 1"
            template_rows = self.db_manager.execute_query(template_query)
            if template_rows:
                self.template = InvoiceTemplate.from_db_row(template_rows[0])
            else:
                # Create default template if none exists
                self.template = InvoiceTemplate(
                    name="القالب الافتراضي",
                    description="القالب الافتراضي للفواتير",
                    is_default=True
                )

        # Set window properties
        self.setWindowTitle(f"معاينة الفاتورة رقم {invoice.invoice_number}")
        self.setMinimumSize(800, 600)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        main_layout = QVBoxLayout(self)

        # Create top toolbar layout
        top_layout = QHBoxLayout()
        main_layout.addLayout(top_layout)

        # Add template selection
        template_layout = QHBoxLayout()
        top_layout.addLayout(template_layout)

        template_label = QLabel("قالب الفاتورة:")
        template_label.setStyleSheet("font-weight: bold;")
        template_layout.addWidget(template_label)

        self.template_combo = QComboBox()
        self.template_combo.setMinimumWidth(200)
        self.template_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #bdbdbd;
                border-radius: 4px;
                background-color: white;
                min-height: 30px;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #bdbdbd;
            }
        """)

        # Load templates
        template_query = "SELECT id, name, is_default FROM invoice_templates ORDER BY is_default DESC, name"
        template_rows = self.db_manager.execute_query(template_query)

        selected_index = 0
        for i, row in enumerate(template_rows):
            template_name = f"{row['name']} {'(افتراضي)' if row['is_default'] else ''}"
            self.template_combo.addItem(template_name, row['id'])

            # Select current template
            if self.template and self.template.id == row['id']:
                selected_index = i

        self.template_combo.setCurrentIndex(selected_index)
        self.template_combo.currentIndexChanged.connect(self.change_template)
        template_layout.addWidget(self.template_combo)

        top_layout.addStretch()

        # Create buttons layout
        buttons_layout = QHBoxLayout()
        main_layout.addLayout(buttons_layout)

        # Add print button
        self.print_button = QPushButton("طباعة")
        if os.path.exists("resources/icons/print.png"):
            self.print_button.setIcon(QIcon(QPixmap("resources/icons/print.png")))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)
        self.print_button.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_button)

        # Add print preview button
        self.preview_button = QPushButton("معاينة الطباعة")
        if os.path.exists("resources/icons/preview.png"):
            self.preview_button.setIcon(QIcon(QPixmap("resources/icons/preview.png")))
        self.preview_button.setStyleSheet("""
            QPushButton {
                background-color: #00897b;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #00796b;
            }
            QPushButton:pressed {
                background-color: #004d40;
            }
        """)
        self.preview_button.clicked.connect(self.preview_print)
        buttons_layout.addWidget(self.preview_button)

        # Add export to PDF button
        self.pdf_button = QPushButton("تصدير إلى PDF")
        if os.path.exists("resources/icons/pdf.png"):
            self.pdf_button.setIcon(QIcon(QPixmap("resources/icons/pdf.png")))
        self.pdf_button.setStyleSheet("""
            QPushButton {
                background-color: #e53935;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
        """)
        self.pdf_button.clicked.connect(self.export_to_pdf)
        buttons_layout.addWidget(self.pdf_button)

        # Add close button
        self.close_button = QPushButton("إغلاق")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #616161;
            }
            QPushButton:pressed {
                background-color: #424242;
            }
        """)
        self.close_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.close_button)

        # Create scroll area for invoice preview
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #f0f0f0;
                border: none;
            }
        """)
        main_layout.addWidget(scroll_area)

        # Create invoice content widget
        self.invoice_widget = QWidget()
        self.invoice_widget.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.MinimumExpanding)

        # Set fixed width for better print layout
        self.invoice_widget.setMinimumWidth(595)  # A4 width in points
        self.invoice_widget.setMaximumWidth(595)  # A4 width in points

        # Apply template styles
        if self.template:
            self.invoice_widget.setStyleSheet(f"""
                background-color: white;
                color: {self.template.text_color};
                font-family: {self.template.font_family}, 'Segoe UI', 'Arial', sans-serif;
                font-size: {self.template.font_size}pt;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            """)
        else:
            self.invoice_widget.setStyleSheet("""
                background-color: white;
                color: #212121;
                font-family: 'Segoe UI', 'Arial', sans-serif;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
            """)

        scroll_area.setWidget(self.invoice_widget)
        scroll_area.setAlignment(Qt.AlignCenter)  # Center the invoice in the scroll area

        # Create invoice layout
        self.invoice_layout = QVBoxLayout(self.invoice_widget)
        self.invoice_layout.setContentsMargins(40, 40, 40, 40)
        self.invoice_layout.setSpacing(20)

        # Populate invoice content
        self.create_invoice_content()

    def create_invoice_content(self):
        """Create the invoice content."""
        # Get colors from template
        header_color = self.template.header_color if self.template else "#0d47a1"
        text_color = self.template.text_color if self.template else "#212121"
        accent_color = self.template.accent_color if self.template else "#1e88e5"

        # Add company header if template allows
        if not self.template or self.template.show_header:
            header_frame = QFrame()
            header_frame.setStyleSheet(f"""
                background-color: {header_color};
                border-radius: 8px;
                padding: 10px;
                margin-bottom: 20px;
            """)
            self.invoice_layout.addWidget(header_frame)

            header_layout = QHBoxLayout(header_frame)

            # Company logo if template allows
            if (not self.template or self.template.show_logo) and self.company_info.get('company_logo') and os.path.exists(self.company_info.get('company_logo')):
                logo_label = QLabel()
                logo_pixmap = QPixmap(self.company_info.get('company_logo'))
                logo_label.setPixmap(logo_pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation))
                header_layout.addWidget(logo_label)

            # Company info
            company_info_layout = QVBoxLayout()
            header_layout.addLayout(company_info_layout)

            company_name = QLabel(self.company_info.get('company_name', ''))
            company_name.setStyleSheet("font-size: 24pt; font-weight: bold; color: white;")
            company_info_layout.addWidget(company_name)

        if self.company_info.get('company_address'):
            company_address = QLabel(self.company_info.get('company_address'))
            company_address.setStyleSheet("font-size: 12pt;")
            company_info_layout.addWidget(company_address)

        if self.company_info.get('company_phone'):
            company_phone = QLabel(f"هاتف: {self.company_info.get('company_phone')}")
            company_phone.setStyleSheet("font-size: 12pt;")
            company_info_layout.addWidget(company_phone)

        if self.company_info.get('company_email'):
            company_email = QLabel(f"بريد إلكتروني: {self.company_info.get('company_email')}")
            company_email.setStyleSheet("font-size: 12pt;")
            company_info_layout.addWidget(company_email)

        # Add invoice title
        invoice_title = QLabel("فاتورة")
        invoice_title.setStyleSheet(f"font-size: 28pt; font-weight: bold; color: {accent_color};")
        invoice_title.setAlignment(Qt.AlignCenter)
        self.invoice_layout.addWidget(invoice_title)

        # Add invoice details
        details_frame = QFrame()
        details_frame.setFrameShape(QFrame.StyledPanel)
        details_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 12pt;
            }
        """)
        self.invoice_layout.addWidget(details_frame)

        details_layout = QGridLayout(details_frame)
        details_layout.setContentsMargins(20, 20, 20, 20)
        details_layout.setSpacing(10)

        # Invoice number
        details_layout.addWidget(QLabel("رقم الفاتورة:"), 0, 0)
        details_layout.addWidget(QLabel(self.invoice.invoice_number), 0, 1)

        # Invoice date
        details_layout.addWidget(QLabel("تاريخ الإصدار:"), 1, 0)
        details_layout.addWidget(QLabel(self.invoice.issue_date.strftime("%Y-%m-%d")), 1, 1)

        # Due date
        if self.invoice.due_date:
            details_layout.addWidget(QLabel("تاريخ الاستحقاق:"), 2, 0)
            details_layout.addWidget(QLabel(self.invoice.due_date.strftime("%Y-%m-%d")), 2, 1)

        # Status
        details_layout.addWidget(QLabel("الحالة:"), 3, 0)
        status_label = QLabel(self.get_status_display(self.invoice.status))
        status_label.setStyleSheet(f"font-weight: bold; color: {self.get_status_color(self.invoice.status)};")
        details_layout.addWidget(status_label, 3, 1)

        # Add customer details
        customer_frame = QFrame()
        customer_frame.setFrameShape(QFrame.StyledPanel)
        customer_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                background-color: #f5f5f5;
            }
            QLabel {
                font-size: 12pt;
            }
        """)
        self.invoice_layout.addWidget(customer_frame)

        customer_layout = QVBoxLayout(customer_frame)
        customer_layout.setContentsMargins(20, 20, 20, 20)
        customer_layout.setSpacing(10)

        customer_title = QLabel("معلومات العميل")
        customer_title.setStyleSheet(f"font-size: 16pt; font-weight: bold; color: {accent_color};")
        customer_layout.addWidget(customer_title)

        customer_layout.addWidget(QLabel(f"الاسم: {self.customer.name}"))

        if self.customer.email:
            customer_layout.addWidget(QLabel(f"البريد الإلكتروني: {self.customer.email}"))

        if self.customer.phone:
            customer_layout.addWidget(QLabel(f"الهاتف: {self.customer.phone}"))

        if self.customer.address:
            customer_layout.addWidget(QLabel(f"العنوان: {self.customer.address}"))

        # Add items table
        items_frame = QFrame()
        items_frame.setFrameShape(QFrame.StyledPanel)
        items_frame.setStyleSheet("""
            QFrame {
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                background-color: white;
            }
            QLabel {
                font-size: 12pt;
            }
        """)
        self.invoice_layout.addWidget(items_frame)

        items_layout = QVBoxLayout(items_frame)
        items_layout.setContentsMargins(20, 20, 20, 20)
        items_layout.setSpacing(10)

        items_title = QLabel("المنتجات والخدمات")
        items_title.setStyleSheet(f"font-size: 16pt; font-weight: bold; color: {accent_color};")
        items_layout.addWidget(items_title)

        # Create items table
        items_grid = QGridLayout()
        items_grid.setSpacing(10)
        items_layout.addLayout(items_grid)

        # Add header
        headers = ["الوصف", "الكمية", "سعر الوحدة", "الخصم", "الضريبة", "الإجمالي"]
        for col, header in enumerate(headers):
            header_label = QLabel(header)
            header_label.setStyleSheet(f"font-weight: bold; background-color: {accent_color}; color: white; padding: 5px; border-radius: 4px;")
            header_label.setAlignment(Qt.AlignCenter)
            items_grid.addWidget(header_label, 0, col)

        # Add items
        currency = self.company_info.get('currency', 'SAR')
        currency_symbol = get_currency_symbol(currency)

        for row, item in enumerate(self.items, 1):
            # Description
            desc_label = QLabel(item.description)
            desc_label.setWordWrap(True)
            items_grid.addWidget(desc_label, row, 0)

            # Quantity
            qty_label = QLabel(str(item.quantity))
            qty_label.setAlignment(Qt.AlignCenter)
            items_grid.addWidget(qty_label, row, 1)

            # Unit price
            price_label = QLabel(f"{item.unit_price:.2f} {currency_symbol}")
            price_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            items_grid.addWidget(price_label, row, 2)

            # Discount
            discount_label = QLabel(f"{item.discount:.2f} {currency_symbol}")
            discount_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            items_grid.addWidget(discount_label, row, 3)

            # Tax
            tax_label = QLabel(f"{item.tax:.2f} {currency_symbol}")
            tax_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            items_grid.addWidget(tax_label, row, 4)

            # Total
            total_label = QLabel(f"{item.total:.2f} {currency_symbol}")
            total_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            total_label.setStyleSheet("font-weight: bold;")
            items_grid.addWidget(total_label, row, 5)

        # Add totals
        totals_layout = QGridLayout()
        totals_layout.setContentsMargins(0, 20, 0, 0)
        items_layout.addLayout(totals_layout)

        # Subtotal
        totals_layout.addWidget(QLabel("المجموع الفرعي:"), 0, 0, Qt.AlignRight)
        subtotal_label = QLabel(f"{self.invoice.subtotal:.2f} {currency_symbol}")
        subtotal_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        totals_layout.addWidget(subtotal_label, 0, 1)

        # Discount
        totals_layout.addWidget(QLabel("الخصم:"), 1, 0, Qt.AlignRight)
        discount_label = QLabel(f"{self.invoice.discount:.2f} {currency_symbol}")
        discount_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        totals_layout.addWidget(discount_label, 1, 1)

        # Tax
        totals_layout.addWidget(QLabel("الضريبة:"), 2, 0, Qt.AlignRight)
        tax_label = QLabel(f"{self.invoice.tax:.2f} {currency_symbol}")
        tax_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        totals_layout.addWidget(tax_label, 2, 1)

        # Total
        totals_layout.addWidget(QLabel("الإجمالي:"), 3, 0, Qt.AlignRight)
        total_label = QLabel(f"{self.invoice.total:.2f} {currency_symbol}")
        total_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        total_label.setStyleSheet(f"font-weight: bold; font-size: 14pt; color: {accent_color};")
        totals_layout.addWidget(total_label, 3, 1)

        # Add notes if available
        if self.invoice.notes:
            notes_frame = QFrame()
            notes_frame.setFrameShape(QFrame.StyledPanel)
            notes_frame.setStyleSheet("""
                QFrame {
                    border: 2px solid #bdbdbd;
                    border-radius: 8px;
                    background-color: #f5f5f5;
                }
                QLabel {
                    font-size: 12pt;
                }
            """)
            self.invoice_layout.addWidget(notes_frame)

            notes_layout = QVBoxLayout(notes_frame)
            notes_layout.setContentsMargins(20, 20, 20, 20)
            notes_layout.setSpacing(10)

            notes_title = QLabel("ملاحظات")
            notes_title.setStyleSheet(f"font-size: 16pt; font-weight: bold; color: {accent_color};")
            notes_layout.addWidget(notes_title)

            notes_content = QLabel(self.invoice.notes)
            notes_content.setWordWrap(True)
            notes_layout.addWidget(notes_content)

        # Add footer if template allows
        if not self.template or self.template.show_footer:
            footer_text = self.template.footer_text if self.template and self.template.footer_text else f"تم إنشاء هذه الفاتورة بواسطة تطبيق فوترها"
            footer_label = QLabel(f"{footer_text} - {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            footer_label.setAlignment(Qt.AlignCenter)
            footer_label.setStyleSheet("color: #757575; font-size: 10pt;")
            self.invoice_layout.addWidget(footer_label)

        # Add spacer at the end
        self.invoice_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

    def get_status_display(self, status):
        """Get the display text for a status.

        Args:
            status (str): Status code

        Returns:
            str: Display text
        """
        status_map = {
            'draft': "مسودة",
            'pending': "منتظرة",
            'paid': "مدفوعة",
            'cancelled': "ملغاة"
        }
        return status_map.get(status, status)

    def get_status_color(self, status):
        """Get the color for a status.

        Args:
            status (str): Status code

        Returns:
            str: Color code
        """
        status_colors = {
            'draft': "#546e7a",      # Blue-gray
            'pending': "#ff8f00",    # Amber
            'paid': "#2e7d32",       # Green
            'cancelled': "#c62828"   # Red
        }
        return status_colors.get(status, "#212121")

    # Using the imported get_currency_symbol function instead

    def change_template(self, index):
        """Change the invoice template.

        Args:
            index (int): Index of the selected template
        """
        template_id = self.template_combo.itemData(index)

        # Load template
        template_query = "SELECT * FROM invoice_templates WHERE id = ?"
        template_rows = self.db_manager.execute_query(template_query, (template_id,))

        if template_rows:
            self.template = InvoiceTemplate.from_db_row(template_rows[0])

            # Clear invoice content
            for i in reversed(range(self.invoice_layout.count())):
                widget = self.invoice_layout.itemAt(i).widget()
                if widget:
                    widget.deleteLater()
                layout = self.invoice_layout.itemAt(i).layout()
                if layout:
                    # Clear layout items
                    for j in reversed(range(layout.count())):
                        widget = layout.itemAt(j).widget()
                        if widget:
                            widget.deleteLater()

            # Update invoice widget style
            self.invoice_widget.setStyleSheet(f"""
                background-color: white;
                color: {self.template.text_color};
                font-family: {self.template.font_family}, 'Segoe UI', 'Arial', sans-serif;
                font-size: {self.template.font_size}pt;
            """)

            # Recreate invoice content
            self.create_invoice_content()

    def print_invoice(self):
        """Print the invoice."""
        try:
            # Create printer with high resolution
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPageSize(QPageSize.A4))
            printer.setPageOrientation(QPageLayout.Portrait)
            printer.setFullPage(True)

            # Show print dialog
            dialog = QPrintDialog(printer, self)
            dialog.setWindowTitle("طباعة الفاتورة")

            if dialog.exec() == QPrintDialog.Accepted:
                self.print_to_printer(printer)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء تهيئة الطباعة: {str(e)}")
            import traceback
            traceback.print_exc()

    def preview_print(self):
        """Show print preview dialog."""
        try:
            # Create printer with high resolution
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPageSize(QPageSize.A4))
            printer.setPageOrientation(QPageLayout.Portrait)
            printer.setFullPage(True)

            # Create preview dialog
            preview = QPrintPreviewDialog(printer, self)
            preview.setWindowTitle("معاينة طباعة الفاتورة")
            preview.setMinimumSize(900, 700)

            # Connect paint request to our print function
            preview.paintRequested.connect(self.print_to_printer)

            # Show the dialog
            preview.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في المعاينة", f"حدث خطأ أثناء تهيئة معاينة الطباعة: {str(e)}")
            import traceback
            traceback.print_exc()

    def print_to_printer(self, printer):
        """Print the invoice to a printer.

        Args:
            printer: QPrinter object
        """
        try:
            # Configure printer
            printer.setPageOrientation(QPageLayout.Portrait)
            printer.setPageSize(QPageSize(QPageSize.A4))

            # Start painting
            painter = QPainter()
            success = painter.begin(printer)

            if not success:
                QMessageBox.warning(self, "خطأ في الطباعة", "لا يمكن بدء الطباعة. يرجى التحقق من إعدادات الطابعة.")
                return

            # Set rendering hints for better quality
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, True)

            # Get the printer's page rectangle
            page_rect = printer.pageRect(QPrinter.Point)

            # Get the size of our invoice widget
            widget_size = self.invoice_widget.size()

            # Calculate scale factor to fit the page with margins
            margin = 20  # points
            available_width = page_rect.width() - (2 * margin)
            available_height = page_rect.height() - (2 * margin)

            x_scale = available_width / widget_size.width()
            y_scale = available_height / widget_size.height()

            # Use the smaller scale to ensure everything fits
            scale = min(x_scale, y_scale)

            # Calculate the position to center the content
            x_pos = page_rect.left() + margin + (available_width - (widget_size.width() * scale)) / 2
            y_pos = page_rect.top() + margin + (available_height - (widget_size.height() * scale)) / 2

            # Save the current state of the painter
            painter.save()

            # Move to the position and scale
            painter.translate(x_pos, y_pos)
            painter.scale(scale, scale)

            # Render the invoice - need to provide targetOffset parameter
            self.invoice_widget.render(painter, targetOffset=QPoint(0, 0))

            # Restore the painter state
            painter.restore()

            # End painting
            painter.end()

        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء الطباعة: {str(e)}")
            import traceback
            traceback.print_exc()  # Print the full traceback for debugging

    def export_to_pdf(self):
        """Export the invoice to PDF."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الفاتورة كملف PDF",
            f"فاتورة_{self.invoice.invoice_number}.pdf",
            "ملفات PDF (*.pdf)"
        )

        if file_path:
            try:
                # Create PDF writer
                writer = QPdfWriter(file_path)
                writer.setPageSize(QPageSize(QPageSize.A4))
                writer.setPageOrientation(QPageLayout.Portrait)
                writer.setResolution(300)  # Higher resolution for better quality
                writer.setCreator("فوترها - نظام إدارة الفواتير")
                writer.setTitle(f"فاتورة رقم {self.invoice.invoice_number}")

                # Create a painter
                painter = QPainter()
                success = painter.begin(writer)

                if not success:
                    QMessageBox.warning(self, "خطأ في التصدير", "لا يمكن إنشاء ملف PDF. يرجى التحقق من صلاحيات الملف.")
                    return

                # Set rendering hints for better quality
                painter.setRenderHint(QPainter.Antialiasing, True)
                painter.setRenderHint(QPainter.TextAntialiasing, True)
                painter.setRenderHint(QPainter.SmoothPixmapTransform, True)

                # Get the page rectangle
                page_rect = writer.pageRect(QPrinter.Point)

                # Get the size of our invoice widget
                widget_size = self.invoice_widget.size()

                # Calculate scale factor to fit the page with margins
                margin = 20  # points
                available_width = page_rect.width() - (2 * margin)
                available_height = page_rect.height() - (2 * margin)

                x_scale = available_width / widget_size.width()
                y_scale = available_height / widget_size.height()

                # Use the smaller scale to ensure everything fits
                scale = min(x_scale, y_scale)

                # Calculate the position to center the content
                x_pos = page_rect.left() + margin + (available_width - (widget_size.width() * scale)) / 2
                y_pos = page_rect.top() + margin + (available_height - (widget_size.height() * scale)) / 2

                # Save the current state of the painter
                painter.save()

                # Move to the position and scale
                painter.translate(x_pos, y_pos)
                painter.scale(scale, scale)

                # Render the invoice - need to provide targetOffset parameter
                self.invoice_widget.render(painter, targetOffset=QPoint(0, 0))

                # Restore the painter state
                painter.restore()

                # End painting
                painter.end()

                QMessageBox.information(self, "تم التصدير", "تم تصدير الفاتورة إلى ملف PDF بنجاح.")

                # Ask if user wants to open the PDF
                open_pdf = QMessageBox.question(
                    self,
                    "فتح الملف",
                    "هل تريد فتح ملف PDF؟",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )

                if open_pdf == QMessageBox.Yes:
                    # Open the PDF file with the default application
                    import subprocess
                    import platform

                    if platform.system() == 'Windows':
                        os.startfile(file_path)
                    elif platform.system() == 'Darwin':  # macOS
                        subprocess.call(('open', file_path))
                    else:  # Linux
                        subprocess.call(('xdg-open', file_path))

            except Exception as e:
                QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء تصدير الفاتورة: {str(e)}")
                import traceback
                traceback.print_exc()  # Print the full traceback for debugging
