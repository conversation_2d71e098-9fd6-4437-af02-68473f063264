#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Activity Log Model for فوترها (Fawterha)
Represents a user activity log entry in the system
"""

from datetime import datetime

class ActivityLog:
    """Activity Log model class."""

    # Activity types
    TYPE_LOGIN = 'login'
    TYPE_LOGOUT = 'logout'
    TYPE_CREATE = 'create'
    TYPE_UPDATE = 'update'
    TYPE_DELETE = 'delete'
    TYPE_VIEW = 'view'
    TYPE_EXPORT = 'export'
    TYPE_IMPORT = 'import'
    TYPE_PRINT = 'print'
    TYPE_OTHER = 'other'

    # Entity types
    ENTITY_USER = 'user'
    ENTITY_PRODUCT = 'product'
    ENTITY_CUSTOMER = 'customer'
    ENTITY_INVOICE = 'invoice'
    ENTITY_TRANSACTION = 'transaction'
    ENTITY_INVENTORY = 'inventory'
    ENTITY_POS_SESSION = 'pos_session'
    ENTITY_ACCOUNT = 'account'
    ENTITY_SYSTEM = 'system'

    def __init__(self, id=None, user_id=None, username=None, activity_type=None, 
                 entity_type=None, entity_id=None, description=None, details=None,
                 ip_address=None, created_at=None):
        """Initialize an activity log entry.

        Args:
            id (int, optional): Activity log ID. Defaults to None.
            user_id (int, optional): User ID who performed the activity. Defaults to None.
            username (str, optional): Username who performed the activity. Defaults to None.
            activity_type (str, optional): Type of activity (login, create, update, etc). Defaults to None.
            entity_type (str, optional): Type of entity (user, product, invoice, etc). Defaults to None.
            entity_id (int, optional): ID of the entity. Defaults to None.
            description (str, optional): Brief description of the activity. Defaults to None.
            details (str, optional): Additional details or JSON data. Defaults to None.
            ip_address (str, optional): IP address of the user. Defaults to None.
            created_at (datetime, optional): Creation timestamp. Defaults to None.
        """
        self.id = id
        self.user_id = user_id
        self.username = username
        self.activity_type = activity_type
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.description = description
        self.details = details
        self.ip_address = ip_address
        self.created_at = created_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create an ActivityLog object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            ActivityLog: ActivityLog object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)

        # Create the activity log object with all fields from the row
        activity_log = cls(
            id=row_dict.get('id'),
            user_id=row_dict.get('user_id'),
            username=row_dict.get('username'),
            activity_type=row_dict.get('activity_type'),
            entity_type=row_dict.get('entity_type'),
            entity_id=row_dict.get('entity_id'),
            description=row_dict.get('description'),
            details=row_dict.get('details'),
            ip_address=row_dict.get('ip_address'),
            created_at=row_dict.get('created_at')
        )

        return activity_log

    def to_dict(self):
        """Convert the activity log object to a dictionary.

        Returns:
            dict: Dictionary representation of the activity log
        """
        return {
            'id': self.id,
            'user_id': self.user_id,
            'username': self.username,
            'activity_type': self.activity_type,
            'entity_type': self.entity_type,
            'entity_id': self.entity_id,
            'description': self.description,
            'details': self.details,
            'ip_address': self.ip_address,
            'created_at': self.created_at
        }

    def __str__(self):
        """Return a string representation of the activity log.

        Returns:
            str: String representation
        """
        return f"{self.activity_type} - {self.entity_type} - {self.description}"
