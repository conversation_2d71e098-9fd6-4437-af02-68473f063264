#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Font Loader for فوترها (Fawterha)
Loads custom fonts for the application
"""

import os
from PySide6.QtGui import QFontDatabase, QFont

def load_fonts():
    """
    Load custom fonts for the application.
    Almarai is a modern Arabic font that provides excellent readability.
    """
    # Create fonts directory if it doesn't exist
    fonts_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resources', 'fonts')
    os.makedirs(fonts_dir, exist_ok=True)
    
    # List of font files to check and load
    font_files = [
        ('Almarai-Regular.ttf', 'https://fonts.google.com/download?family=Almarai'),
        ('Almarai-Bold.ttf', 'https://fonts.google.com/download?family=Almarai'),
        ('Almarai-Light.ttf', 'https://fonts.google.com/download?family=Almarai'),
        ('Almarai-ExtraBold.ttf', 'https://fonts.google.com/download?family=Almarai')
    ]
    
    # Check if fonts exist, if not print a message to download them
    missing_fonts = []
    for font_file, download_url in font_files:
        font_path = os.path.join(fonts_dir, font_file)
        if not os.path.exists(font_path):
            missing_fonts.append((font_file, download_url))
    
    if missing_fonts:
        print("بعض الخطوط غير موجودة. يرجى تنزيل خط Almarai من:")
        print("https://fonts.google.com/specimen/Almarai")
        print("وضع الملفات التالية في المجلد resources/fonts:")
        for font_file, _ in missing_fonts:
            print(f"- {font_file}")
    
    # Load available fonts
    loaded_fonts = []
    for font_file, _ in font_files:
        font_path = os.path.join(fonts_dir, font_file)
        if os.path.exists(font_path):
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                loaded_fonts.append(QFontDatabase.applicationFontFamilies(font_id)[0])
    
    return loaded_fonts

def set_default_font():
    """
    Set Almarai as the default font for the application if available.
    """
    loaded_fonts = load_fonts()
    
    if 'Almarai' in loaded_fonts:
        default_font = QFont('Almarai', 10)
        QApplication.setFont(default_font)
        return True
    
    return False
