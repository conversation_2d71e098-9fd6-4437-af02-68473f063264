#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Template Dialog for فوترها (Fawterha)
Dialog for adding or editing invoice templates
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QLineEdit, QTextEdit, QCheckBox, QSpinBox, QComboBox,
    QPushButton, QColorDialog, QGroupBox, QTabWidget,
    QScrollArea, QWidget, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QFont

from models.invoice_template import InvoiceTemplate
from utils.translation_manager import tr


class ColorPickerButton(QPushButton):
    """Custom button for picking colors."""

    color_changed = Signal(QColor)

    def __init__(self, color=None, parent=None):
        """Initialize the color picker button.

        Args:
            color (QColor, optional): Initial color. Defaults to None.
            parent: Parent widget
        """
        super().__init__(parent)
        self.color = color or QColor("#000000")
        self.setFixedSize(40, 30)
        self.clicked.connect(self.pick_color)
        self.update_color()

    def pick_color(self):
        """Show color dialog and update color."""
        color = QColorDialog.getColor(self.color, self.parent(), "اختر اللون")
        if color.isValid():
            self.color = color
            self.update_color()
            self.color_changed.emit(color)

    def update_color(self):
        """Update button style to show the selected color."""
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.color.name()};
                border: 1px solid #bdbdbd;
                border-radius: 4px;
            }}
            QPushButton:hover {{
                border: 1px solid #212121;
            }}
        """)

    def get_color(self):
        """Get the selected color.

        Returns:
            str: Color in hex format
        """
        return self.color.name()

    def set_color(self, color):
        """Set the color.

        Args:
            color (str): Color in hex format
        """
        self.color = QColor(color)
        self.update_color()


class TemplatePreview(QFrame):
    """Widget for previewing invoice templates."""

    def __init__(self, parent=None):
        """Initialize the template preview.

        Args:
            parent: Parent widget
        """
        super().__init__(parent)

        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setMinimumHeight(400)

        # Create layout
        layout = QVBoxLayout(self)

        # Create preview label
        self.preview_label = QLabel(tr("templates.preview", "معاينة القالب"))
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setStyleSheet("""
            font-size: 16pt;
            font-weight: bold;
            color: #0d47a1;
            margin-bottom: 10px;
        """)
        layout.addWidget(self.preview_label)

        # Create preview content
        self.preview_content = QWidget()
        layout.addWidget(self.preview_content)

        # Create preview layout
        self.preview_layout = QVBoxLayout(self.preview_content)

        # Initialize with default style
        self.update_preview({
            'header_color': '#0d47a1',
            'text_color': '#212121',
            'accent_color': '#1e88e5',
            'font_family': 'Arial',
            'font_size': 12,
            'show_logo': True,
            'show_header': True,
            'show_footer': True,
            'footer_text': 'تم إنشاء هذه الفاتورة بواسطة تطبيق فوترها'
        })

    def update_preview(self, template_data):
        """Update the preview with template data.

        Args:
            template_data (dict): Template data
        """
        # Clear previous content
        for i in reversed(range(self.preview_layout.count())):
            widget = self.preview_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Create header
        if template_data.get('show_header', True):
            header = QFrame()
            header.setStyleSheet(f"""
                background-color: {template_data.get('header_color', '#0d47a1')};
                border-radius: 4px;
                padding: 10px;
                margin-bottom: 10px;
            """)
            header_layout = QHBoxLayout(header)

            # Add logo placeholder
            if template_data.get('show_logo', True):
                logo = QLabel("شعار الشركة")
                logo.setStyleSheet("""
                    background-color: white;
                    color: #757575;
                    border-radius: 4px;
                    padding: 10px;
                    font-weight: bold;
                """)
                logo.setFixedSize(80, 80)
                logo.setAlignment(Qt.AlignCenter)
                header_layout.addWidget(logo)

            # Add company info
            company_info = QLabel("""
                <div style='color: white;'>
                    <h2>اسم الشركة</h2>
                    <p>عنوان الشركة</p>
                    <p>هاتف: *********</p>
                    <p>البريد الإلكتروني: <EMAIL></p>
                </div>
            """)
            header_layout.addWidget(company_info)

            self.preview_layout.addWidget(header)

        # Create invoice title
        title = QLabel("فاتورة")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet(f"""
            font-size: 24pt;
            font-weight: bold;
            color: {template_data.get('accent_color', '#1e88e5')};
            margin: 20px 0;
        """)
        self.preview_layout.addWidget(title)

        # Create invoice details
        details = QFrame()
        details.setStyleSheet(f"""
            background-color: #f5f5f5;
            border: 1px solid #bdbdbd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            color: {template_data.get('text_color', '#212121')};
        """)
        details_layout = QFormLayout(details)
        details_layout.addRow("رقم الفاتورة:", QLabel("INV-1001"))
        details_layout.addRow("تاريخ الإصدار:", QLabel("2023-01-01"))
        details_layout.addRow("تاريخ الاستحقاق:", QLabel("2023-02-01"))
        details_layout.addRow("الحالة:", QLabel("مدفوعة"))
        self.preview_layout.addWidget(details)

        # Create customer details
        customer = QFrame()
        customer.setStyleSheet(f"""
            background-color: #f5f5f5;
            border: 1px solid #bdbdbd;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            color: {template_data.get('text_color', '#212121')};
        """)
        customer_layout = QVBoxLayout(customer)
        customer_title = QLabel("معلومات العميل")
        customer_title.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {template_data.get('accent_color', '#1e88e5')};
            margin-bottom: 5px;
        """)
        customer_layout.addWidget(customer_title)
        customer_layout.addWidget(QLabel("اسم العميل: أحمد محمد"))
        customer_layout.addWidget(QLabel("البريد الإلكتروني: <EMAIL>"))
        customer_layout.addWidget(QLabel("الهاتف: *********"))
        customer_layout.addWidget(QLabel("العنوان: الرياض، المملكة العربية السعودية"))
        self.preview_layout.addWidget(customer)

        # Create items table (simplified)
        items = QLabel("""
            <table width='100%' style='border-collapse: collapse;'>
                <tr style='background-color: """ + template_data.get('accent_color', '#1e88e5') + """; color: white;'>
                    <th style='padding: 8px; text-align: right;'>الوصف</th>
                    <th style='padding: 8px; text-align: center;'>الكمية</th>
                    <th style='padding: 8px; text-align: center;'>السعر</th>
                    <th style='padding: 8px; text-align: center;'>الإجمالي</th>
                </tr>
                <tr style='background-color: #f5f5f5;'>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0;'>منتج 1</td>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0; text-align: center;'>2</td>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0; text-align: center;'>100.00 ر.س</td>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0; text-align: center;'>200.00 ر.س</td>
                </tr>
                <tr>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0;'>منتج 2</td>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0; text-align: center;'>1</td>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0; text-align: center;'>150.00 ر.س</td>
                    <td style='padding: 8px; border-bottom: 1px solid #e0e0e0; text-align: center;'>150.00 ر.س</td>
                </tr>
            </table>
        """)
        items.setStyleSheet(f"color: {template_data.get('text_color', '#212121')};")
        self.preview_layout.addWidget(items)

        # Create totals
        totals = QLabel("""
            <div style='text-align: left; margin-top: 20px;'>
                <p>المجموع الفرعي: 350.00 ر.س</p>
                <p>الخصم: 0.00 ر.س</p>
                <p>الضريبة: 52.50 ر.س</p>
                <p style='font-weight: bold; font-size: 14pt;'>الإجمالي: 402.50 ر.س</p>
            </div>
        """)
        totals.setStyleSheet(f"color: {template_data.get('text_color', '#212121')};")
        self.preview_layout.addWidget(totals)

        # Create footer
        if template_data.get('show_footer', True):
            footer = QLabel(template_data.get('footer_text', 'تم إنشاء هذه الفاتورة بواسطة تطبيق فوترها'))
            footer.setAlignment(Qt.AlignCenter)
            footer.setStyleSheet("""
                color: #757575;
                font-size: 10pt;
                margin-top: 40px;
                padding-top: 10px;
                border-top: 1px solid #e0e0e0;
            """)
            self.preview_layout.addWidget(footer)

        # Update font family and size
        font_family = template_data.get('font_family', 'Arial')
        font_size = template_data.get('font_size', 12)
        self.preview_content.setStyleSheet(f"""
            font-family: {font_family}, 'Segoe UI', 'Arial', sans-serif;
            font-size: {font_size}pt;
        """)


class TemplateDialog(QDialog):
    """Dialog for adding or editing an invoice template."""

    def __init__(self, parent=None, template=None, db_manager=None):
        """Initialize the template dialog.

        Args:
            parent: Parent widget
            template (InvoiceTemplate, optional): Template to edit. Defaults to None.
            db_manager: Database manager instance
        """
        super().__init__(parent)

        self.template = template or InvoiceTemplate()
        self.db_manager = db_manager

        self.setWindowTitle(tr("templates.add_new", "إضافة قالب جديد") if not template or not template.id else tr("templates.edit", "تعديل قالب"))
        self.setMinimumSize(900, 600)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        main_layout = QHBoxLayout(self)

        # Create left panel (settings)
        settings_panel = QWidget()
        settings_panel.setMaximumWidth(400)
        main_layout.addWidget(settings_panel)

        settings_layout = QVBoxLayout(settings_panel)

        # Create settings form
        form_scroll = QScrollArea()
        form_scroll.setWidgetResizable(True)
        form_scroll.setFrameShape(QFrame.NoFrame)
        settings_layout.addWidget(form_scroll)

        form_widget = QWidget()
        form_scroll.setWidget(form_widget)

        form_layout = QVBoxLayout(form_widget)

        # Basic settings
        basic_group = QGroupBox(tr("templates.basic_settings", "الإعدادات الأساسية"))
        basic_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #0d47a1;
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: white;
            }
        """)
        form_layout.addWidget(basic_group)

        basic_layout = QFormLayout(basic_group)
        basic_layout.setLabelAlignment(Qt.AlignRight)
        basic_layout.setFormAlignment(Qt.AlignRight)

        name_label = QLabel(tr("templates.name_label", "اسم القالب:"))
        name_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("templates.name_placeholder", "أدخل اسم القالب"))
        self.name_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-size: 12pt;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #1e88e5;
            }
        """)
        if self.template.name:
            self.name_edit.setText(self.template.name)
        basic_layout.addRow(name_label, self.name_edit)

        desc_label = QLabel(tr("common.description", "الوصف:"))
        desc_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.description_edit = QTextEdit()
        self.description_edit.setPlaceholderText(tr("templates.description_placeholder", "أدخل وصف القالب"))
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setStyleSheet("""
            QTextEdit {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-size: 12pt;
            }
            QTextEdit:focus {
                border: 2px solid #1e88e5;
            }
        """)
        if self.template.description:
            self.description_edit.setText(self.template.description)
        basic_layout.addRow(desc_label, self.description_edit)

        self.is_default_check = QCheckBox(tr("templates.use_as_default", "استخدام كقالب افتراضي"))
        self.is_default_check.setStyleSheet("""
            QCheckBox {
                font-size: 12pt;
                color: #212121;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #1e88e5;
                border-radius: 4px;
                background-color: #1e88e5;
            }
        """)
        self.is_default_check.setChecked(self.template.is_default)
        basic_layout.addRow("", self.is_default_check)

        # Colors settings
        colors_group = QGroupBox(tr("templates.colors", "الألوان"))
        colors_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #0d47a1;
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: white;
            }
        """)
        form_layout.addWidget(colors_group)

        colors_layout = QFormLayout(colors_group)
        colors_layout.setLabelAlignment(Qt.AlignRight)
        colors_layout.setFormAlignment(Qt.AlignRight)

        self.header_color_button = ColorPickerButton(QColor(self.template.header_color))
        self.header_color_button.color_changed.connect(self.update_preview)
        colors_layout.addRow(tr("templates.header_color", "لون الترويسة:"), self.header_color_button)

        self.text_color_button = ColorPickerButton(QColor(self.template.text_color))
        self.text_color_button.color_changed.connect(self.update_preview)
        colors_layout.addRow(tr("templates.text_color", "لون النص:"), self.text_color_button)

        self.accent_color_button = ColorPickerButton(QColor(self.template.accent_color))
        self.accent_color_button.color_changed.connect(self.update_preview)
        colors_layout.addRow(tr("templates.accent_color", "لون التمييز:"), self.accent_color_button)

        # Font settings
        font_group = QGroupBox("الخط")
        font_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #0d47a1;
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: white;
            }
        """)
        form_layout.addWidget(font_group)

        font_layout = QFormLayout(font_group)
        font_layout.setLabelAlignment(Qt.AlignRight)
        font_layout.setFormAlignment(Qt.AlignRight)

        font_family_label = QLabel("نوع الخط:")
        font_family_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.font_family_combo = QComboBox()
        self.font_family_combo.addItems(["Arial", "Tahoma", "Verdana", "Times New Roman", "Courier New"])
        self.font_family_combo.setCurrentText(self.template.font_family)
        self.font_family_combo.currentTextChanged.connect(self.update_preview)
        self.font_family_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-size: 12pt;
                min-height: 20px;
            }
            QComboBox:focus {
                border: 2px solid #1e88e5;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #bdbdbd;
            }
        """)
        font_layout.addRow(font_family_label, self.font_family_combo)

        font_size_label = QLabel("حجم الخط:")
        font_size_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.font_size_spin = QSpinBox()
        self.font_size_spin.setMinimum(8)
        self.font_size_spin.setMaximum(16)
        self.font_size_spin.setValue(self.template.font_size)
        self.font_size_spin.valueChanged.connect(self.update_preview)
        self.font_size_spin.setStyleSheet("""
            QSpinBox {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-size: 12pt;
                min-height: 20px;
            }
            QSpinBox:focus {
                border: 2px solid #1e88e5;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                border-left: 1px solid #bdbdbd;
            }
        """)
        font_layout.addRow(font_size_label, self.font_size_spin)

        # Display settings
        display_group = QGroupBox("إعدادات العرض")
        display_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #0d47a1;
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                margin-top: 16px;
                padding-top: 16px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 10px;
                background-color: white;
            }
        """)
        form_layout.addWidget(display_group)

        display_layout = QFormLayout(display_group)
        display_layout.setLabelAlignment(Qt.AlignRight)
        display_layout.setFormAlignment(Qt.AlignRight)

        logo_label = QLabel("إظهار الشعار:")
        logo_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.show_logo_check = QCheckBox()
        self.show_logo_check.setChecked(self.template.show_logo)
        self.show_logo_check.stateChanged.connect(self.update_preview)
        self.show_logo_check.setStyleSheet("""
            QCheckBox {
                font-size: 12pt;
                color: #212121;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #1e88e5;
                border-radius: 4px;
                background-color: #1e88e5;
            }
        """)
        display_layout.addRow(logo_label, self.show_logo_check)

        header_label = QLabel("إظهار الترويسة:")
        header_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.show_header_check = QCheckBox()
        self.show_header_check.setChecked(self.template.show_header)
        self.show_header_check.stateChanged.connect(self.update_preview)
        self.show_header_check.setStyleSheet("""
            QCheckBox {
                font-size: 12pt;
                color: #212121;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #1e88e5;
                border-radius: 4px;
                background-color: #1e88e5;
            }
        """)
        display_layout.addRow(header_label, self.show_header_check)

        footer_label = QLabel("إظهار التذييل:")
        footer_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.show_footer_check = QCheckBox()
        self.show_footer_check.setChecked(self.template.show_footer)
        self.show_footer_check.stateChanged.connect(self.update_preview)
        self.show_footer_check.setStyleSheet("""
            QCheckBox {
                font-size: 12pt;
                color: #212121;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #1e88e5;
                border-radius: 4px;
                background-color: #1e88e5;
            }
        """)
        display_layout.addRow(footer_label, self.show_footer_check)

        footer_text_label = QLabel("نص التذييل:")
        footer_text_label.setStyleSheet("font-weight: bold; color: #212121; font-size: 12pt;")

        self.footer_text_edit = QLineEdit()
        self.footer_text_edit.setText(self.template.footer_text or "تم إنشاء هذه الفاتورة بواسطة تطبيق فوترها")
        self.footer_text_edit.textChanged.connect(self.update_preview)
        self.footer_text_edit.setStyleSheet("""
            QLineEdit {
                border: 2px solid #bdbdbd;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-size: 12pt;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 2px solid #1e88e5;
            }
        """)
        display_layout.addRow(footer_text_label, self.footer_text_edit)

        # Add buttons
        buttons_layout = QHBoxLayout()
        settings_layout.addLayout(buttons_layout)

        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #1e88e5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)
        self.save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e0e0e0;
                color: #212121;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #bdbdbd;
            }
            QPushButton:pressed {
                background-color: #9e9e9e;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        # Create right panel (preview)
        preview_panel = QWidget()
        main_layout.addWidget(preview_panel, 1)  # Stretch factor 1

        preview_layout = QVBoxLayout(preview_panel)

        # Create preview widget
        self.preview = TemplatePreview()
        preview_layout.addWidget(self.preview)

        # Update preview with initial data
        self.update_preview()

    def update_preview(self):
        """Update the template preview."""
        template_data = {
            'header_color': self.header_color_button.get_color(),
            'text_color': self.text_color_button.get_color(),
            'accent_color': self.accent_color_button.get_color(),
            'font_family': self.font_family_combo.currentText(),
            'font_size': self.font_size_spin.value(),
            'show_logo': self.show_logo_check.isChecked(),
            'show_header': self.show_header_check.isChecked(),
            'show_footer': self.show_footer_check.isChecked(),
            'footer_text': self.footer_text_edit.text()
        }
        self.preview.update_preview(template_data)

    def get_template_data(self):
        """Get the template data from the form.

        Returns:
            dict: Template data
        """
        return {
            'name': self.name_edit.text(),
            'description': self.description_edit.toPlainText(),
            'is_default': self.is_default_check.isChecked(),
            'header_color': self.header_color_button.get_color(),
            'text_color': self.text_color_button.get_color(),
            'accent_color': self.accent_color_button.get_color(),
            'font_family': self.font_family_combo.currentText(),
            'font_size': self.font_size_spin.value(),
            'show_logo': self.show_logo_check.isChecked(),
            'show_header': self.show_header_check.isChecked(),
            'show_footer': self.show_footer_check.isChecked(),
            'footer_text': self.footer_text_edit.text()
        }
