#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Item Model for فوترها (Fawterha)
Represents an item in an invoice
"""

class InvoiceItem:
    """Invoice item model class."""

    def __init__(self, id=None, invoice_id=None, description="", quantity=1.0,
                 unit_price=0.0, discount=0.0, tax=0.0, total=0.0, product_id=None,
                 is_product=False, track_inventory=False):
        """Initialize an invoice item object.

        Args:
            id (int, optional): Item ID. Defaults to None.
            invoice_id (int, optional): Invoice ID. Defaults to None.
            description (str, optional): Item description. Defaults to "".
            quantity (float, optional): Quantity. Defaults to 1.0.
            unit_price (float, optional): Unit price. Defaults to 0.0.
            discount (float, optional): Discount amount. Defaults to 0.0.
            tax (float, optional): Tax amount. Defaults to 0.0.
            total (float, optional): Total amount. Defaults to 0.0.
            product_id (int, optional): Product ID for inventory tracking. Defaults to None.
            is_product (bool, optional): Whether this item is a product (not a service). Defaults to False.
            track_inventory (bool, optional): Whether to track inventory for this item. Defaults to False.
        """
        self.id = id
        self.invoice_id = invoice_id
        self.description = description
        self.quantity = quantity
        self.unit_price = unit_price
        self.discount = discount
        self.tax = tax
        self.total = total or (quantity * unit_price - discount + tax)
        self.product_id = product_id
        self.is_product = is_product
        self.track_inventory = track_inventory

    @classmethod
    def from_db_row(cls, row):
        """Create an InvoiceItem object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            InvoiceItem: InvoiceItem object
        """
        # Check if product_id exists in the row
        try:
            product_id = row['product_id'] if 'product_id' in row else None
            is_product = row['is_product'] if 'is_product' in row else False
            track_inventory = row['track_inventory'] if 'track_inventory' in row else False
        except (KeyError, TypeError):
            product_id = None
            is_product = False
            track_inventory = False

        return cls(
            id=row['id'],
            invoice_id=row['invoice_id'],
            description=row['description'],
            quantity=row['quantity'],
            unit_price=row['unit_price'],
            discount=row['discount'],
            tax=row['tax'],
            total=row['total'],
            product_id=product_id,
            is_product=is_product,
            track_inventory=track_inventory
        )

    def calculate_total(self):
        """Calculate the total for this item."""
        self.total = self.quantity * self.unit_price - self.discount + self.tax
        return self.total

    def to_dict(self):
        """Convert the invoice item object to a dictionary.

        Returns:
            dict: Dictionary representation of the invoice item
        """
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'description': self.description,
            'quantity': self.quantity,
            'unit_price': self.unit_price,
            'discount': self.discount,
            'tax': self.tax,
            'total': self.total,
            'product_id': self.product_id if hasattr(self, 'product_id') else None,
            'is_product': self.is_product if hasattr(self, 'is_product') else False,
            'track_inventory': self.track_inventory if hasattr(self, 'track_inventory') else False
        }

    def __str__(self):
        """Return a string representation of the invoice item.

        Returns:
            str: String representation
        """
        return f"{self.description} - {self.quantity} x {self.unit_price}"
