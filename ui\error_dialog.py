#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Error Dialog for فوترها (Fawterha)
Provides custom error dialogs for common application errors
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QMessageBox, QApplication, QTextEdit
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QPixmap

class DatabaseErrorDialog(QDialog):
    """Custom dialog for database errors with repair options."""
    
    repair_requested = Signal()
    retry_requested = Signal()
    
    def __init__(self, parent=None, error_message="", error_details=""):
        """Initialize the database error dialog.
        
        Args:
            parent: Parent widget
            error_message: Main error message to display
            error_details: Detailed error information
        """
        super().__init__(parent)
        
        # Set window properties
        self.setWindowTitle("خطأ في قاعدة البيانات")
        self.setMinimumSize(500, 300)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # Create layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # Create error icon and message
        header_layout = QHBoxLayout()
        
        # Error icon
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        try:
            # Try to load error icon
            error_icon = QIcon.fromTheme("dialog-error")
            if not error_icon.isNull():
                icon_label.setPixmap(error_icon.pixmap(48, 48))
            else:
                # Fallback to system icon
                icon_label.setPixmap(QMessageBox.standardIcon(QMessageBox.Critical).pixmap(48, 48))
        except:
            # If all else fails, just show text
            icon_label.setText("❌")
            icon_label.setStyleSheet("font-size: 24pt;")
        
        header_layout.addWidget(icon_label)
        
        # Error message
        message_label = QLabel(error_message or "حدث خطأ في قاعدة البيانات")
        message_label.setStyleSheet("font-size: 14pt; font-weight: bold;")
        message_label.setWordWrap(True)
        header_layout.addWidget(message_label, 1)
        
        layout.addLayout(header_layout)
        
        # Add details if provided
        if error_details:
            details_label = QLabel("تفاصيل الخطأ:")
            details_label.setStyleSheet("font-weight: bold;")
            layout.addWidget(details_label)
            
            details_edit = QTextEdit()
            details_edit.setReadOnly(True)
            details_edit.setPlainText(error_details)
            details_edit.setMaximumHeight(100)
            layout.addWidget(details_edit)
        
        # Add help text
        help_label = QLabel(
            "قد تكون قاعدة البيانات مقفلة أو تالفة. يمكنك محاولة إصلاحها أو إعادة المحاولة."
        )
        help_label.setWordWrap(True)
        help_label.setStyleSheet("color: #555;")
        layout.addWidget(help_label)
        
        # Add buttons
        button_layout = QHBoxLayout()
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.reject)
        
        retry_button = QPushButton("إعادة المحاولة")
        retry_button.clicked.connect(self._on_retry)
        retry_button.setStyleSheet("background-color: #4CAF50; color: white;")
        
        repair_button = QPushButton("إصلاح قاعدة البيانات")
        repair_button.clicked.connect(self._on_repair)
        repair_button.setStyleSheet("background-color: #2196F3; color: white;")
        
        button_layout.addWidget(close_button)
        button_layout.addWidget(retry_button)
        button_layout.addWidget(repair_button)
        
        layout.addLayout(button_layout)
    
    def _on_repair(self):
        """Emit signal to request database repair."""
        self.repair_requested.emit()
        self.accept()
    
    def _on_retry(self):
        """Emit signal to request operation retry."""
        self.retry_requested.emit()
        self.accept()

def show_database_error(parent=None, error_message="", error_details="", on_repair=None, on_retry=None):
    """Show a database error dialog with repair and retry options.
    
    Args:
        parent: Parent widget
        error_message: Main error message to display
        error_details: Detailed error information
        on_repair: Callback function when repair is requested
        on_retry: Callback function when retry is requested
        
    Returns:
        int: Dialog result code
    """
    dialog = DatabaseErrorDialog(parent, error_message, error_details)
    
    if on_repair:
        dialog.repair_requested.connect(on_repair)
    
    if on_retry:
        dialog.retry_requested.connect(on_retry)
    
    return dialog.exec()
