#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Account Category Model for فوترها (Fawterha)
Represents an account category in the accounting system
"""

from datetime import datetime


class AccountCategory:
    """Account category model class."""

    # Account category types
    TYPE_ASSET = 'asset'
    TYPE_LIABILITY = 'liability'
    TYPE_EQUITY = 'equity'
    TYPE_REVENUE = 'revenue'
    TYPE_EXPENSE = 'expense'

    def __init__(self, id=None, name="", code="", type="", description="", parent_id=None,
                 created_at=None, updated_at=None):
        """Initialize an account category.

        Args:
            id (int, optional): Category ID
            name (str): Category name
            code (str): Category code
            type (str): Category type (asset, liability, equity, revenue, expense)
            description (str): Category description
            parent_id (int, optional): Parent category ID
            created_at (datetime, optional): Creation timestamp
            updated_at (datetime, optional): Last update timestamp
        """
        self.id = id
        self.name = name
        self.code = code
        self.type = type
        self.description = description
        self.parent_id = parent_id
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create an AccountCategory object from a database row.

        Args:
            row (tuple or dict): Database row containing account category data

        Returns:
            AccountCategory: AccountCategory object
        """
        # Handle both tuple and dictionary formats
        if isinstance(row, dict):
            return cls(
                id=row.get('id'),
                name=row.get('name', ''),
                code=row.get('code', ''),
                type=row.get('type', ''),
                description=row.get('description', ''),
                parent_id=row.get('parent_id'),
                created_at=row.get('created_at'),
                updated_at=row.get('updated_at')
            )
        else:
            # Handle tuple format (indexed access)
            try:
                return cls(
                    id=row[0],
                    name=row[1],
                    code=row[2],
                    type=row[3],
                    description=row[4],
                    parent_id=row[5],
                    created_at=row[6],
                    updated_at=row[7]
                )
            except (IndexError, TypeError) as e:
                print(f"Error creating AccountCategory from row: {e}")
                print(f"Row data: {row}")
                # Return a default account category object
                return cls()

    def to_dict(self):
        """Convert the account category to a dictionary.

        Returns:
            dict: Dictionary representation of the account category
        """
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'type': self.type,
            'description': self.description,
            'parent_id': self.parent_id,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def __str__(self):
        """Return a string representation of the account category.

        Returns:
            str: String representation
        """
        return f"{self.code} - {self.name}"
