#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory Model for فوترها (Fawterha)
Represents inventory items specific to the Point of Sale system
"""

class POSInventory:
    """POS Inventory model class."""

    def __init__(self, id=None, product_id=None, stock_quantity=0, min_stock_level=0,
                 location=None, notes=None, created_at=None, updated_at=None,
                 product_name=None, product_barcode=None, category_name=None):
        """Initialize a POS inventory object.

        Args:
            id (int, optional): Inventory ID. Defaults to None.
            product_id (int, optional): Product ID. Defaults to None.
            stock_quantity (int, optional): Current stock quantity. Defaults to 0.
            min_stock_level (int, optional): Minimum stock level. Defaults to 0.
            location (str, optional): Storage location. Defaults to None.
            notes (str, optional): Notes. Defaults to None.
            created_at (str, optional): Creation timestamp. Defaults to None.
            updated_at (str, optional): Update timestamp. Defaults to None.
            product_name (str, optional): Product name (for display). Defaults to None.
            product_barcode (str, optional): Product barcode (for display). Defaults to None.
            category_name (str, optional): Category name (for display). Defaults to None.
        """
        self.id = id
        self.product_id = product_id
        
        # Ensure inventory values are integers
        try:
            self.stock_quantity = int(stock_quantity)
        except (ValueError, TypeError):
            self.stock_quantity = 0

        try:
            self.min_stock_level = int(min_stock_level)
        except (ValueError, TypeError):
            self.min_stock_level = 0

        self.location = location
        self.notes = notes
        self.created_at = created_at
        self.updated_at = updated_at
        
        # Display fields (not stored in pos_inventory table)
        self.product_name = product_name
        self.product_barcode = product_barcode
        self.category_name = category_name

    @classmethod
    def from_db_row(cls, row):
        """Create a POSInventory object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            POSInventory: POSInventory object
        """
        # Convert row to dict for easier access
        row_dict = dict(row)

        # Create the inventory object with all fields from the row
        inventory = cls(
            id=row_dict.get('id'),
            product_id=row_dict.get('product_id'),
            stock_quantity=row_dict.get('stock_quantity', 0),
            min_stock_level=row_dict.get('min_stock_level', 0),
            location=row_dict.get('location'),
            notes=row_dict.get('notes'),
            created_at=row_dict.get('created_at'),
            updated_at=row_dict.get('updated_at'),
            product_name=row_dict.get('product_name'),
            product_barcode=row_dict.get('barcode'),
            category_name=row_dict.get('category_name')
        )

        return inventory

    def to_dict(self):
        """Convert the inventory object to a dictionary.

        Returns:
            dict: Dictionary representation of the inventory
        """
        return {
            'id': self.id,
            'product_id': self.product_id,
            'stock_quantity': self.stock_quantity,
            'min_stock_level': self.min_stock_level,
            'location': self.location,
            'notes': self.notes,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'product_name': self.product_name,
            'product_barcode': self.product_barcode,
            'category_name': self.category_name
        }

    def __str__(self):
        """Return a string representation of the inventory.

        Returns:
            str: String representation
        """
        return f"{self.product_name} (Stock: {self.stock_quantity})"
