#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Arabic PDF Generator for فوترها (Fawterha)
Generates PDF files with proper Arabic text support
"""

import os
import tempfile
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Import Arabic text handling libraries
import arabic_reshaper
from bidi.algorithm import get_display

# Register Arabic fonts
def setup_arabic_fonts():
    """Set up Arabic fonts for ReportLab."""
    try:
        # Try to register Tahoma font (good for Arabic)
        tahoma_path = "C:/Windows/Fonts/tahoma.ttf"
        tahoma_bold_path = "C:/Windows/Fonts/tahomabd.ttf"

        if os.path.exists(tahoma_path):
            pdfmetrics.registerFont(TTFont('Tahoma', tahoma_path))
            print("Registered Tahoma font")

        if os.path.exists(tahoma_bold_path):
            pdfmetrics.registerFont(TTFont('Tahoma-Bold', tahoma_bold_path))
            print("Registered Tahoma-Bold font")

        # Try to register Arabic Typesetting font
        arabic_path = "C:/Windows/Fonts/arabtype.ttf"
        if os.path.exists(arabic_path):
            pdfmetrics.registerFont(TTFont('Arabic', arabic_path))
            print("Registered Arabic Typesetting font")

        # Try to register Traditional Arabic font
        trad_arabic_path = "C:/Windows/Fonts/trado.ttf"
        if os.path.exists(trad_arabic_path):
            pdfmetrics.registerFont(TTFont('TraditionalArabic', trad_arabic_path))
            print("Registered Traditional Arabic font")

        # Try to register Simplified Arabic font
        simp_arabic_path = "C:/Windows/Fonts/simpo.ttf"
        if os.path.exists(simp_arabic_path):
            pdfmetrics.registerFont(TTFont('SimplifiedArabic', simp_arabic_path))
            print("Registered Simplified Arabic font")

        # Try to register Arial font
        arial_path = "C:/Windows/Fonts/arial.ttf"
        if os.path.exists(arial_path):
            pdfmetrics.registerFont(TTFont('Arial', arial_path))
            print("Registered Arial font")

        return True
    except Exception as e:
        print(f"Error setting up Arabic fonts: {str(e)}")
        return False

# Function to fix Arabic text for proper display
def fix_arabic_text(text):
    """Fix Arabic text for proper display in PDF using arabic_reshaper and python-bidi.

    Args:
        text (str): Text that may contain Arabic characters

    Returns:
        str: Fixed text ready for PDF display
    """
    if not text:
        return ""

    try:
        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        # Apply bidirectional algorithm
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"Error fixing Arabic text: {str(e)}")
        return text  # Return original text if there's an error

def generate_customer_statement_pdf(file_path, customer, start_date, end_date, headers, rows, total_invoices, total_paid, balance):
    """Generate a PDF file for a customer statement.

    Args:
        file_path (str): Path to save the PDF file
        customer (Customer): Customer object
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Statement data rows
        total_invoices (str): Total invoices amount
        total_paid (str): Total paid amount
        balance (str): Current balance
    """
    # Set up Arabic fonts
    setup_arabic_fonts()

    # Create the PDF document
    doc = SimpleDocTemplate(
        file_path,
        pagesize=A4,
        rightMargin=1.5*cm,
        leftMargin=1.5*cm,
        topMargin=1.5*cm,
        bottomMargin=1.5*cm
    )

    # Container for PDF elements
    elements = []

    # Set up styles
    styles = getSampleStyleSheet()

    # Create custom styles for Arabic text
    arabic_title_style = ParagraphStyle(
        'ArabicTitle',
        parent=styles['Title'],
        fontName='Tahoma-Bold' if 'Tahoma-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
        fontSize=18,
        alignment=1,  # Center alignment
        leading=24,
        textColor=colors.darkblue
    )

    arabic_heading_style = ParagraphStyle(
        'ArabicHeading',
        parent=styles['Heading2'],
        fontName='Tahoma-Bold' if 'Tahoma-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold',
        fontSize=14,
        alignment=2,  # Right alignment
        leading=18,
        textColor=colors.darkblue
    )

    arabic_normal_style = ParagraphStyle(
        'ArabicNormal',
        parent=styles['Normal'],
        fontName='Tahoma' if 'Tahoma' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
        fontSize=12,
        alignment=2,  # Right alignment
        leading=16
    )

    # Add title
    elements.append(Paragraph(fix_arabic_text("كشف حساب العميل"), arabic_title_style))
    elements.append(Spacer(1, 0.5*cm))

    # Add customer information
    elements.append(Paragraph(fix_arabic_text(f"العميل: {customer.name}"), arabic_heading_style))
    elements.append(Spacer(1, 0.2*cm))

    elements.append(Paragraph(fix_arabic_text(f"الفترة: من {start_date} إلى {end_date}"), arabic_normal_style))
    elements.append(Paragraph(fix_arabic_text(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"), arabic_normal_style))

    if customer.email:
        elements.append(Paragraph(fix_arabic_text(f"البريد الإلكتروني: {customer.email}"), arabic_normal_style))

    if customer.phone:
        elements.append(Paragraph(fix_arabic_text(f"الهاتف: {customer.phone}"), arabic_normal_style))

    if customer.address:
        elements.append(Paragraph(fix_arabic_text(f"العنوان: {customer.address}"), arabic_normal_style))

    elements.append(Spacer(1, 0.5*cm))

    # Prepare table data
    # Fix Arabic text in headers
    fixed_headers = [fix_arabic_text(header) for header in headers]
    table_data = [fixed_headers]

    # Fix Arabic text in rows
    for row in rows:
        fixed_row = []
        for cell in row:
            # Only apply fix_arabic_text to text cells, not numeric values
            if isinstance(cell, str) and any('\u0600' <= c <= '\u06FF' for c in cell):
                fixed_row.append(fix_arabic_text(cell))
            else:
                fixed_row.append(cell)
        table_data.append(fixed_row)

    # Create table
    table = Table(table_data, repeatRows=1)

    # Style the table
    table.setStyle(TableStyle([
        # Header styling
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Tahoma-Bold' if 'Tahoma-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('TOPPADDING', (0, 0), (-1, 0), 12),

        # Data rows styling
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 1), (-1, -1), 'Tahoma' if 'Tahoma' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
        ('TOPPADDING', (0, 1), (-1, -1), 8),

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.grey),
        ('BOX', (0, 0), (-1, -1), 1.5, colors.black),
    ]))

    # Add alternating row colors
    for i in range(1, len(table_data), 2):
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, i), (-1, i), colors.lightgrey)
        ]))

    # Special styling for amount columns (assuming columns 4, 5, 6 are amount columns)
    table.setStyle(TableStyle([
        ('TEXTCOLOR', (4, 1), (4, -1), colors.darkred),  # Debit column
        ('TEXTCOLOR', (5, 1), (5, -1), colors.darkgreen),  # Credit column
        ('FONTNAME', (4, 1), (6, -1), 'Tahoma-Bold' if 'Tahoma-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
    ]))

    elements.append(table)
    elements.append(Spacer(1, 0.5*cm))

    # Add summary
    elements.append(Paragraph(fix_arabic_text(f"إجمالي الفواتير: {total_invoices}"), arabic_heading_style))
    elements.append(Paragraph(fix_arabic_text(f"إجمالي المدفوعات: {total_paid}"), arabic_heading_style))
    elements.append(Paragraph(fix_arabic_text(f"الرصيد الحالي: {balance}"), arabic_heading_style))

    # Add footer
    elements.append(Spacer(1, 1*cm))
    elements.append(Paragraph(fix_arabic_text(f"تم إنشاء هذا التقرير بواسطة تطبيق فوترها - {datetime.now().strftime('%Y-%m-%d %H:%M')}"), arabic_normal_style))

    # Build the PDF
    doc.build(elements)

    return file_path
