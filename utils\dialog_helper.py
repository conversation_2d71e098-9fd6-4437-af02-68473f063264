#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Dialog Helper for فوترها (Fawterha)
Provides enhanced dialog boxes with better contrast
"""

from PySide6.QtWidgets import QMessageBox
from PySide6.QtGui import QFont


def show_info_message(parent, title, message):
    """Show an information message dialog with better contrast.
    
    Args:
        parent: Parent widget
        title (str): Dialog title
        message (str): Dialog message
        
    Returns:
        int: Dialog result
    """
    msg_box = QMessageBox(parent)
    msg_box.setObjectName("infoDialog")
    msg_box.setWindowTitle(title)
    msg_box.setIcon(QMessageBox.Information)
    
    # Format message with better contrast
    formatted_message = f"<p style='color: #212121; font-size: 14pt;'>{message}</p>"
    msg_box.setText(formatted_message)
    
    # Set button styling
    ok_button = msg_box.addButton(QMessageBox.Ok)
    ok_button.setStyleSheet("""
        QPushButton {
            background-color: #1e88e5;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 100px;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #1976d2;
        }
        QPushButton:pressed {
            background-color: #0d47a1;
        }
    """)
    
    # Set minimum width for better readability
    msg_box.setMinimumWidth(400)
    
    return msg_box.exec()


def show_warning_message(parent, title, message):
    """Show a warning message dialog with better contrast.
    
    Args:
        parent: Parent widget
        title (str): Dialog title
        message (str): Dialog message
        
    Returns:
        int: Dialog result
    """
    msg_box = QMessageBox(parent)
    msg_box.setObjectName("warningDialog")
    msg_box.setWindowTitle(title)
    msg_box.setIcon(QMessageBox.Warning)
    
    # Format message with better contrast
    formatted_message = f"<p style='color: #212121; font-size: 14pt; font-weight: bold;'>{message}</p>"
    msg_box.setText(formatted_message)
    
    # Set button styling
    ok_button = msg_box.addButton(QMessageBox.Ok)
    ok_button.setStyleSheet("""
        QPushButton {
            background-color: #ff9800;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 100px;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #f57c00;
        }
        QPushButton:pressed {
            background-color: #e65100;
        }
    """)
    
    # Set minimum width for better readability
    msg_box.setMinimumWidth(400)
    
    return msg_box.exec()


def show_error_message(parent, title, message):
    """Show an error message dialog with better contrast.
    
    Args:
        parent: Parent widget
        title (str): Dialog title
        message (str): Dialog message
        
    Returns:
        int: Dialog result
    """
    msg_box = QMessageBox(parent)
    msg_box.setObjectName("errorDialog")
    msg_box.setWindowTitle(title)
    msg_box.setIcon(QMessageBox.Critical)
    
    # Format message with better contrast
    formatted_message = f"<p style='color: #b71c1c; font-size: 14pt; font-weight: bold;'>{message}</p>"
    msg_box.setText(formatted_message)
    
    # Set button styling
    ok_button = msg_box.addButton(QMessageBox.Ok)
    ok_button.setStyleSheet("""
        QPushButton {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 100px;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #d32f2f;
        }
        QPushButton:pressed {
            background-color: #b71c1c;
        }
    """)
    
    # Set minimum width for better readability
    msg_box.setMinimumWidth(400)
    
    return msg_box.exec()


def show_confirmation_message(parent, title, message):
    """Show a confirmation message dialog with better contrast.
    
    Args:
        parent: Parent widget
        title (str): Dialog title
        message (str): Dialog message
        
    Returns:
        bool: True if confirmed, False otherwise
    """
    msg_box = QMessageBox(parent)
    msg_box.setObjectName("confirmDialog")
    msg_box.setWindowTitle(title)
    msg_box.setIcon(QMessageBox.Question)
    
    # Format message with better contrast
    formatted_message = f"<p style='color: #212121; font-size: 14pt;'>{message}</p>"
    msg_box.setText(formatted_message)
    
    # Add buttons
    yes_button = msg_box.addButton(QMessageBox.Yes)
    yes_button.setText("نعم")
    yes_button.setStyleSheet("""
        QPushButton {
            background-color: #4caf50;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 100px;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #388e3c;
        }
        QPushButton:pressed {
            background-color: #2e7d32;
        }
    """)
    
    no_button = msg_box.addButton(QMessageBox.No)
    no_button.setText("لا")
    no_button.setStyleSheet("""
        QPushButton {
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-weight: bold;
            min-width: 100px;
            min-height: 30px;
        }
        QPushButton:hover {
            background-color: #d32f2f;
        }
        QPushButton:pressed {
            background-color: #b71c1c;
        }
    """)
    
    msg_box.setDefaultButton(QMessageBox.No)
    
    # Set minimum width for better readability
    msg_box.setMinimumWidth(400)
    
    return msg_box.exec() == QMessageBox.Yes
