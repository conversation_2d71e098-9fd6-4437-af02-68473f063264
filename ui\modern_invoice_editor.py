from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout, QLabel,
    QLineEdit, QDateEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QPushButton, QDoubleSpinBox, QTextEdit, QMessageBox, QFrame,
    QHeaderView, QAbstractItemView, QDialogButtonBox, QWidget, QSizePolicy,
    QSpacerItem, QScrollArea
)
from PySide6.QtCore import Qt, QDate, QMargins
from PySide6.QtGui import QColor, QPalette, QFont

from models.invoice_item import InvoiceItem
from ui.products_view import ProductsView
from utils.currency_helper import convert_currency
from ui.invoice_item_dialog import InvoiceItemDialog

class ModernInvoiceEditor(QDialog):
    """A modern, clean invoice editor dialog."""

    def __init__(self, parent=None, db_manager=None, invoice=None, items=None, view_only=False, currency_manager=None):
        """Initialize the invoice editor.

        Args:
            parent: Parent widget
            db_manager: Database manager
            invoice: Invoice object to edit
            items: List of invoice items
            view_only: Whether to show in view-only mode
            currency_manager: Currency manager
        """
        super().__init__(parent)

        # Store parameters
        self.db_manager = db_manager
        self.invoice = invoice
        self.view_only = view_only
        self.currency_manager = currency_manager
        self.items_list = []
        self.primary_currency = None
        self.current_currency = None

        # Set window properties
        self.setWindowTitle("فاتورة جديدة" if not invoice else f"فاتورة {invoice.invoice_number}")
        self.setMinimumSize(1000, 700)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Set up the UI
        self.setup_ui()

        # Load items if provided
        if items:
            for item_row in items:
                item = InvoiceItem.from_db_row(item_row)
                self.items_list.append(item)
                self.add_item_to_table(item)

        # Calculate initial totals
        self.calculate_totals()

        # Set view-only mode if specified
        if view_only:
            self.set_view_only_mode()

    def setup_ui(self):
        """Set up the user interface."""
        # Define color scheme
        self.colors = {
            'primary': '#1976D2',
            'primary_light': '#BBDEFB',
            'primary_dark': '#0D47A1',
            'accent': '#FF5722',
            'accent_light': '#FFCCBC',
            'success': '#4CAF50',
            'success_light': '#E8F5E9',
            'warning': '#FFC107',
            'warning_light': '#FFF8E1',
            'danger': '#F44336',
            'danger_light': '#FFEBEE',
            'text': '#212121',
            'text_secondary': '#757575',
            'divider': '#BDBDBD',
            'background': '#F5F5F5',
            'card': '#FFFFFF',
            'disabled': '#EEEEEE'
        }

        # Set window background
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.colors['background']};
            }}
        """)

        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Create header section
        header_widget = self.create_header_section()
        main_layout.addWidget(header_widget)

        # Create content section
        content_layout = QHBoxLayout()
        content_layout.setSpacing(15)

        # Create items section (left side, 70%)
        items_widget = self.create_items_section()
        content_layout.addWidget(items_widget, 70)

        # Create totals section (right side, 30%)
        totals_widget = self.create_totals_section()
        content_layout.addWidget(totals_widget, 30)

        main_layout.addLayout(content_layout, 1)

        # Create footer with action buttons
        footer_widget = self.create_footer_section()
        main_layout.addWidget(footer_widget)

    def create_header_section(self):
        """Create the invoice header section."""
        header_widget = QWidget()
        header_widget.setObjectName("headerWidget")
        header_widget.setStyleSheet(f"""
            #headerWidget {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
        """)

        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(15, 15, 15, 15)
        header_layout.setSpacing(20)

        # Left side - Invoice info
        invoice_info_layout = QFormLayout()
        invoice_info_layout.setLabelAlignment(Qt.AlignRight)
        invoice_info_layout.setFormAlignment(Qt.AlignLeft)
        invoice_info_layout.setSpacing(10)

        # Invoice number
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        self.invoice_number_edit.setStyleSheet(self.get_input_style())
        if self.invoice:
            self.invoice_number_edit.setText(self.invoice.invoice_number)

        invoice_number_label = QLabel("رقم الفاتورة:")
        invoice_number_label.setStyleSheet(self.get_label_style())

        invoice_info_layout.addRow(invoice_number_label, self.invoice_number_edit)

        # Status
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet(self.get_input_style())
        self.status_combo.addItem("مسودة", "draft")
        self.status_combo.addItem("منتظرة", "pending")
        self.status_combo.addItem("مدفوع جزئياً", "partially_paid")
        self.status_combo.addItem("مدفوعة", "paid")
        self.status_combo.addItem("ملغاة", "cancelled")
        if self.invoice and self.invoice.status:
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == self.invoice.status:
                    self.status_combo.setCurrentIndex(i)
                    break

        status_label = QLabel("الحالة:")
        status_label.setStyleSheet(self.get_label_style())

        invoice_info_layout.addRow(status_label, self.status_combo)

        # Right side - Customer and dates
        customer_dates_layout = QFormLayout()
        customer_dates_layout.setLabelAlignment(Qt.AlignRight)
        customer_dates_layout.setFormAlignment(Qt.AlignLeft)
        customer_dates_layout.setSpacing(10)

        # Customer selection
        self.customer_combo = QComboBox()
        self.customer_combo.setStyleSheet(self.get_input_style())
        self.load_customers()
        if self.invoice and self.invoice.customer_id:
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == self.invoice.customer_id:
                    self.customer_combo.setCurrentIndex(i)
                    break

        customer_label = QLabel("العميل:")
        customer_label.setStyleSheet(self.get_label_style())

        customer_dates_layout.addRow(customer_label, self.customer_combo)

        # Issue date
        self.issue_date_edit = QDateEdit()
        self.issue_date_edit.setCalendarPopup(True)
        self.issue_date_edit.setDate(QDate.currentDate())
        self.issue_date_edit.setStyleSheet(self.get_input_style())
        if self.invoice and self.invoice.issue_date:
            self.issue_date_edit.setDate(QDate.fromString(self.invoice.issue_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        issue_date_label = QLabel("تاريخ الإصدار:")
        issue_date_label.setStyleSheet(self.get_label_style())

        customer_dates_layout.addRow(issue_date_label, self.issue_date_edit)

        # Due date
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        self.due_date_edit.setStyleSheet(self.get_input_style())
        if self.invoice and self.invoice.due_date:
            self.due_date_edit.setDate(QDate.fromString(self.invoice.due_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        due_date_label = QLabel("تاريخ الاستحقاق:")
        due_date_label.setStyleSheet(self.get_label_style())

        customer_dates_layout.addRow(due_date_label, self.due_date_edit)

        # Add layouts to header
        header_layout.addLayout(invoice_info_layout, 1)
        header_layout.addLayout(customer_dates_layout, 2)

        return header_widget

    def create_items_section(self):
        """Create the invoice items section."""
        items_widget = QWidget()
        items_widget.setObjectName("itemsWidget")
        items_widget.setStyleSheet(f"""
            #itemsWidget {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
        """)

        items_layout = QVBoxLayout(items_widget)
        items_layout.setContentsMargins(15, 15, 15, 15)
        items_layout.setSpacing(10)

        # Section title
        title_label = QLabel("المنتجات والخدمات")
        title_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['primary']};
            padding-bottom: 5px;
            border-bottom: 2px solid {self.colors['primary_light']};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        items_layout.addWidget(title_label)

        # Action buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Add item button
        self.add_item_button = QPushButton("إضافة منتج/خدمة يدوياً")
        self.add_item_button.setStyleSheet(self.get_button_style('primary'))
        self.add_item_button.setMinimumHeight(40)
        self.add_item_button.clicked.connect(self.add_item)
        buttons_layout.addWidget(self.add_item_button)

        # Select product button
        self.select_product_button = QPushButton("اختيار من المنتجات والخدمات")
        self.select_product_button.setStyleSheet(self.get_button_style('accent'))
        self.select_product_button.setMinimumHeight(40)
        self.select_product_button.clicked.connect(self.select_product)
        buttons_layout.addWidget(self.select_product_button)

        items_layout.addLayout(buttons_layout)

        # Items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "الوصف", "الكمية", "سعر الوحدة", "الخصم", "الضريبة", "الإجمالي", ""
        ])

        # Configure table appearance
        self.items_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.colors['card']};
                alternate-background-color: {self.colors['background']};
                border: 1px solid {self.colors['divider']};
                gridline-color: {self.colors['divider']};
                selection-background-color: {self.colors['primary_light']};
                selection-color: {self.colors['text']};
                font-size: 11pt;
            }}
            QTableWidget::item {{
                padding: 5px;
                border-bottom: 1px solid {self.colors['divider']};
            }}
            QHeaderView::section {{
                background-color: {self.colors['primary']};
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 11pt;
            }}
        """)

        # Configure column sizes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 6):
            self.items_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)
        self.items_table.setColumnWidth(6, 120)

        # Configure table behavior
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setShowGrid(True)
        self.items_table.setAlternatingRowColors(True)

        items_layout.addWidget(self.items_table, 1)

        # Notes section
        notes_layout = QVBoxLayout()

        notes_label = QLabel("ملاحظات")
        notes_label.setStyleSheet(f"""
            font-size: 12pt;
            font-weight: bold;
            color: {self.colors['text']};
            margin-top: 10px;
        """)
        notes_layout.addWidget(notes_label)

        self.notes_edit = QTextEdit()
        self.notes_edit.setStyleSheet(f"""
            background-color: {self.colors['card']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
            padding: 5px;
            font-size: 11pt;
        """)
        self.notes_edit.setMaximumHeight(80)
        if self.invoice and self.invoice.notes:
            self.notes_edit.setText(self.invoice.notes)

        notes_layout.addWidget(self.notes_edit)
        items_layout.addLayout(notes_layout)

        return items_widget

    def create_totals_section(self):
        """Create the invoice totals section."""
        # Main container for totals section
        totals_widget = QWidget()
        totals_widget.setObjectName("totalsWidget")
        totals_widget.setMinimumWidth(350)  # Ensure minimum width for readability
        totals_widget.setStyleSheet(f"""
            #totalsWidget {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 2px solid #007bff;
                padding: 15px;
                box-sizing: border-box;
            }}
        """)

        # Main layout with proper spacing
        totals_layout = QVBoxLayout(totals_widget)
        totals_layout.setContentsMargins(15, 15, 15, 15)
        totals_layout.setSpacing(10)

        # Section title with clear styling
        title_label = QLabel("الإجماليات")
        title_label.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 20px;
            text-align: center;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        totals_layout.addWidget(title_label)

        # Create rows for each total item using HBoxLayout (similar to flex in CSS)
        # Row 1: Subtotal
        subtotal_row = QWidget()
        subtotal_layout = QHBoxLayout(subtotal_row)
        subtotal_layout.setContentsMargins(0, 0, 0, 0)
        subtotal_layout.setSpacing(10)

        subtotal_label = QLabel("الإجمالي الفرعي:")
        subtotal_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            text-align: right;
        """)
        subtotal_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.subtotal_label = QLineEdit("0.00")
        self.subtotal_label.setReadOnly(True)
        self.subtotal_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)
        self.subtotal_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        subtotal_layout.addWidget(subtotal_label, 1)  # flex: 1
        subtotal_layout.addWidget(self.subtotal_label, 1)  # flex: 1
        totals_layout.addWidget(subtotal_row)

        # Row 2: Discount
        discount_row = QWidget()
        discount_layout = QHBoxLayout(discount_row)
        discount_layout.setContentsMargins(0, 0, 0, 0)
        discount_layout.setSpacing(10)

        discount_label = QLabel("إجمالي الخصم:")
        discount_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            text-align: right;
        """)
        discount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(9999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        self.discount_spin.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)
        self.discount_spin.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        if self.invoice:
            self.discount_spin.setValue(self.invoice.discount)

        discount_layout.addWidget(discount_label, 1)  # flex: 1
        discount_layout.addWidget(self.discount_spin, 1)  # flex: 1
        totals_layout.addWidget(discount_row)

        # Row 3: Tax
        tax_row = QWidget()
        tax_layout = QHBoxLayout(tax_row)
        tax_layout.setContentsMargins(0, 0, 0, 0)
        tax_layout.setSpacing(10)

        tax_label = QLabel("إجمالي الضريبة:")
        tax_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            text-align: right;
        """)
        tax_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMinimum(0.0)
        self.tax_spin.setMaximum(9999999.99)
        self.tax_spin.setDecimals(2)
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        self.tax_spin.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)
        self.tax_spin.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        if self.invoice:
            self.tax_spin.setValue(self.invoice.tax)

        tax_layout.addWidget(tax_label, 1)  # flex: 1
        tax_layout.addWidget(self.tax_spin, 1)  # flex: 1
        totals_layout.addWidget(tax_row)

        # Row 4: Total - make it stand out
        total_row = QWidget()
        total_layout = QHBoxLayout(total_row)
        total_layout.setContentsMargins(0, 0, 0, 0)
        total_layout.setSpacing(10)

        total_label = QLabel("الإجمالي النهائي:")
        total_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: #212121;
            text-align: right;
        """)
        total_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.total_label = QLineEdit("0.00")
        self.total_label.setReadOnly(True)
        self.total_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)
        self.total_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        total_layout.addWidget(total_label, 1)  # flex: 1
        total_layout.addWidget(self.total_label, 1)  # flex: 1
        totals_layout.addWidget(total_row)

        # Add some space before currency
        spacer = QWidget()
        spacer.setFixedHeight(10)
        totals_layout.addWidget(spacer)

        # Row 5: Currency
        currency_row = QWidget()
        currency_layout = QHBoxLayout(currency_row)
        currency_layout.setContentsMargins(0, 0, 0, 0)
        currency_layout.setSpacing(10)

        currency_label = QLabel("العملة:")
        currency_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            text-align: right;
        """)
        currency_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.currency_combo = QComboBox()
        self.currency_combo.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)

        if self.currency_manager:
            self.load_currencies()
            self.currency_combo.currentIndexChanged.connect(self.on_currency_changed)

        currency_layout.addWidget(currency_label, 1)  # flex: 1
        currency_layout.addWidget(self.currency_combo, 1)  # flex: 1
        totals_layout.addWidget(currency_row)

        # Payment information section
        payment_container = QWidget()
        payment_container.setStyleSheet(f"""
            background-color: #FAFAFA;
            border-radius: 8px;
            border: 1px solid #CED4DA;
            margin-top: 20px;
        """)

        payment_layout = QVBoxLayout(payment_container)
        payment_layout.setContentsMargins(15, 15, 15, 15)
        payment_layout.setSpacing(10)

        # Payment section title
        payment_title = QLabel("معلومات الدفع")
        payment_title.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: #212121;
            padding: 5px 0;
            border-bottom: 1px solid #CED4DA;
            margin-bottom: 10px;
            text-align: center;
        """)
        payment_title.setAlignment(Qt.AlignCenter)
        payment_layout.addWidget(payment_title)

        # Paid amount
        paid_row = QWidget()
        paid_layout = QHBoxLayout(paid_row)
        paid_layout.setContentsMargins(0, 0, 0, 0)
        paid_layout.setSpacing(10)

        paid_label = QLabel("المدفوع:")
        paid_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            text-align: right;
        """)
        paid_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.amount_paid_spin = QDoubleSpinBox()
        self.amount_paid_spin.setMinimum(0.0)
        self.amount_paid_spin.setMaximum(9999999.99)
        self.amount_paid_spin.setDecimals(2)
        self.amount_paid_spin.valueChanged.connect(self.update_payment_info)
        self.amount_paid_spin.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)
        self.amount_paid_spin.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        if self.invoice:
            self.amount_paid_spin.setValue(self.invoice.amount_paid)

        paid_layout.addWidget(paid_label, 1)  # flex: 1
        paid_layout.addWidget(self.amount_paid_spin, 1)  # flex: 1
        payment_layout.addWidget(paid_row)

        # Due amount
        due_row = QWidget()
        due_layout = QHBoxLayout(due_row)
        due_layout.setContentsMargins(0, 0, 0, 0)
        due_layout.setSpacing(10)

        due_label = QLabel("المتبقي:")
        due_label.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            text-align: right;
        """)
        due_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.amount_due_spin = QDoubleSpinBox()
        self.amount_due_spin.setMinimum(0.0)
        self.amount_due_spin.setMaximum(9999999.99)
        self.amount_due_spin.setDecimals(2)
        self.amount_due_spin.valueChanged.connect(self.update_payment_from_due)
        self.amount_due_spin.setStyleSheet(f"""
            font-size: 14pt;
            color: #212121;
            background-color: #F8F9FA;
            border: 1px solid #CED4DA;
            border-radius: 4px;
            padding: 5px;
            text-align: right;
        """)
        self.amount_due_spin.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        if self.invoice and hasattr(self.invoice, 'amount_due'):
            self.amount_due_spin.setValue(self.invoice.amount_due)

        due_layout.addWidget(due_label, 1)  # flex: 1
        due_layout.addWidget(self.amount_due_spin, 1)  # flex: 1
        payment_layout.addWidget(due_row)

        totals_layout.addWidget(payment_container)

        # Add spacer to push everything to the top
        totals_layout.addStretch(1)

        return totals_widget

    def create_footer_section(self):
        """Create the footer section with action buttons."""
        footer_widget = QWidget()
        footer_widget.setObjectName("footerWidget")
        footer_widget.setStyleSheet(f"""
            #footerWidget {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
        """)

        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(15, 15, 15, 15)
        footer_layout.setSpacing(15)

        # Add spacer to push buttons to the right
        footer_layout.addStretch(1)

        # Cancel button
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(self.get_button_style('danger'))
        self.cancel_button.setMinimumSize(120, 45)
        self.cancel_button.clicked.connect(self.reject)
        footer_layout.addWidget(self.cancel_button)

        # Save button
        self.save_button = QPushButton("إنشاء فاتورة")
        self.save_button.setStyleSheet(self.get_button_style('success'))
        self.save_button.setMinimumSize(150, 45)
        self.save_button.clicked.connect(self.save_invoice)
        footer_layout.addWidget(self.save_button)

        return footer_widget

    def get_label_style(self, color=None, size=None):
        """Get the style for labels."""
        if not color:
            color = self.colors['text']
        if not size:
            size = "12pt"

        return f"""
            font-weight: bold;
            font-size: {size};
            color: {color};
            padding: 5px;
        """

    def get_input_style(self, color=None):
        """Get the style for input fields."""
        if not color:
            color = self.colors['text']

        return f"""
            background-color: {self.colors['card']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
            padding: 8px;
            font-size: 11pt;
            color: {color};
            min-height: 20px;
        """

    def get_button_style(self, style_type='primary'):
        """Get the style for buttons."""
        if style_type == 'primary':
            bg_color = self.colors['primary']
            hover_color = self.colors['primary_dark']
            text_color = 'white'
        elif style_type == 'success':
            bg_color = self.colors['success']
            hover_color = '#388E3C'  # Darker green
            text_color = 'white'
        elif style_type == 'danger':
            bg_color = self.colors['danger']
            hover_color = '#C62828'  # Darker red
            text_color = 'white'
        elif style_type == 'accent':
            bg_color = self.colors['accent']
            hover_color = '#E64A19'  # Darker orange
            text_color = 'white'
        else:
            bg_color = self.colors['background']
            hover_color = self.colors['divider']
            text_color = self.colors['text']

        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 11pt;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                padding: 9px 14px 7px 16px;
            }}
            QPushButton:disabled {{
                background-color: {self.colors['disabled']};
                color: {self.colors['text_secondary']};
            }}
        """

    def load_customers(self):
        """Load customers from the database."""
        if not self.db_manager:
            return

        query = "SELECT id, name FROM customers ORDER BY name"
        rows = self.db_manager.execute_query(query)

        self.customer_combo.clear()
        for row in rows:
            self.customer_combo.addItem(row['name'], row['id'])

    def load_currencies(self):
        """Load currencies from the database."""
        if not self.currency_manager:
            return

        currencies = self.currency_manager.get_active_currencies()

        self.currency_combo.clear()

        # Find primary currency
        for currency in currencies:
            if currency.is_primary:
                self.primary_currency = currency
                break

        # Add currencies to combo box
        for currency in currencies:
            self.currency_combo.addItem(f"{currency.name} ({currency.code})", currency.id)

            # Set current currency if this is the primary currency
            if currency.is_primary:
                self.current_currency = currency
                self.currency_combo.setCurrentIndex(self.currency_combo.count() - 1)

    def on_currency_changed(self, index):
        """Handle currency change."""
        if index < 0 or not self.currency_manager:
            return

        # Get the selected currency ID
        currency_id = self.currency_combo.itemData(index)

        # Get the currency object
        new_currency = self.currency_manager.get_currency_by_id(currency_id)

        if not new_currency or new_currency.id == self.current_currency.id:
            return

        # Store old currency for conversion
        old_currency = self.current_currency
        self.current_currency = new_currency

        # Update currency symbols in the UI
        currency_suffix = f" {new_currency.symbol}"

        # Update unit price suffix in item dialog
        for row in range(self.items_table.rowCount()):
            # Update unit price display
            unit_price_item = self.items_table.item(row, 2)
            if unit_price_item:
                try:
                    old_value = float(unit_price_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    unit_price_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

            # Update discount display
            discount_item = self.items_table.item(row, 3)
            if discount_item:
                try:
                    old_value = float(discount_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    discount_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

            # Update tax display
            tax_item = self.items_table.item(row, 4)
            if tax_item:
                try:
                    old_value = float(tax_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    tax_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

            # Update total display
            total_item = self.items_table.item(row, 5)
            if total_item:
                try:
                    old_value = float(total_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    total_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

        # Update items in the list
        for item in self.items_list:
            item.unit_price = convert_currency(item.unit_price, old_currency.exchange_rate, new_currency.exchange_rate)
            item.discount = convert_currency(item.discount, old_currency.exchange_rate, new_currency.exchange_rate)
            item.tax = convert_currency(item.tax, old_currency.exchange_rate, new_currency.exchange_rate)
            item.total = convert_currency(item.total, old_currency.exchange_rate, new_currency.exchange_rate)

        # Update discount and tax spinboxes
        self.discount_spin.setSuffix(currency_suffix)
        old_discount = self.discount_spin.value()
        new_discount = convert_currency(old_discount, old_currency.exchange_rate, new_currency.exchange_rate)
        self.discount_spin.setValue(new_discount)

        self.tax_spin.setSuffix(currency_suffix)
        old_tax = self.tax_spin.value()
        new_tax = convert_currency(old_tax, old_currency.exchange_rate, new_currency.exchange_rate)
        self.tax_spin.setValue(new_tax)

        # Update amount paid and amount due spinboxes
        self.amount_paid_spin.setSuffix(currency_suffix)
        old_amount_paid = self.amount_paid_spin.value()
        new_amount_paid = convert_currency(old_amount_paid, old_currency.exchange_rate, new_currency.exchange_rate)
        self.amount_paid_spin.setValue(new_amount_paid)

        self.amount_due_spin.setSuffix(currency_suffix)

        # Recalculate totals
        self.calculate_totals()

    def add_item(self):
        """Add a new item to the invoice manually."""
        dialog = InvoiceItemDialog(self)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف المنتج أو الخدمة")
                return

            # Create item object
            item = InvoiceItem(
                description=item_data['description'],
                quantity=item_data['quantity'],
                unit_price=item_data['unit_price'],
                discount=item_data['discount'],
                tax=item_data['tax'],
                total=item_data['total']
            )

            # Add to list and table
            self.items_list.append(item)
            self.add_item_to_table(item)

            # Recalculate totals
            self.calculate_totals()

    def select_product(self):
        """Select a product or service from the products list."""
        # Create products view in select mode
        products_dialog = QDialog(self)
        products_dialog.setWindowTitle("اختيار منتج أو خدمة")
        products_dialog.setMinimumSize(800, 600)

        # Set RTL layout direction
        products_dialog.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        layout = QVBoxLayout(products_dialog)

        # Create products view
        products_view = ProductsView(self.db_manager, select_mode=True)
        # Pass currency manager if available
        if hasattr(self, 'currency_manager') and self.currency_manager:
            products_view.currency_manager = self.currency_manager
        layout.addWidget(products_view)

        # Connect product selection signal
        products_view.product_selected.connect(lambda product: self.add_product_to_invoice(product, products_dialog))

        # Show dialog
        products_dialog.exec()

    def add_product_to_invoice(self, product, dialog=None):
        """Add a product to the invoice."""
        # Check inventory if this is a product (not a service) and inventory tracking is enabled
        if product.type == 'product' and product.track_inventory:
            # Check if we have enough stock
            from database.inventory_manager import InventoryManager
            inventory_manager = InventoryManager(self.db_manager)

            if not inventory_manager.check_product_availability(product.id, 1):
                QMessageBox.warning(
                    self,
                    "المخزون غير كافي",
                    f"المخزون غير كافي للمنتج '{product.name}'. الكمية المتاحة: {product.stock_quantity}."
                )
                # Don't close the dialog
                return

        # Get tax settings
        tax_query = "SELECT value FROM settings WHERE key = 'tax_rate'"
        tax_rows = self.db_manager.execute_query(tax_query)
        default_tax_rate = float(tax_rows[0]['value']) if tax_rows else 15.0

        # Calculate tax amount
        tax_amount = (product.price * product.tax_rate / 100) if product.tax_rate > 0 else (product.price * default_tax_rate / 100)

        # Create item object
        item = InvoiceItem(
            description=product.name,
            quantity=1.0,
            unit_price=product.price,
            discount=0.0,
            tax=tax_amount,
            total=product.price + tax_amount
        )

        # Store product ID for inventory tracking
        item.product_id = product.id

        # Add to list and table
        self.items_list.append(item)
        self.add_item_to_table(item)

        # Recalculate totals
        self.calculate_totals()

        # Close the dialog if provided
        if dialog:
            dialog.accept()

    def add_item_to_table(self, item):
        """Add an item to the table."""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Set item data
        self.items_table.setItem(row, 0, QTableWidgetItem(item.description))
        self.items_table.setItem(row, 1, QTableWidgetItem(f"{item.quantity:.2f}"))
        self.items_table.setItem(row, 2, QTableWidgetItem(f"{item.unit_price:.2f} {currency_symbol}"))
        self.items_table.setItem(row, 3, QTableWidgetItem(f"{item.discount:.2f} {currency_symbol}"))
        self.items_table.setItem(row, 4, QTableWidgetItem(f"{item.tax:.2f} {currency_symbol}"))
        self.items_table.setItem(row, 5, QTableWidgetItem(f"{item.total:.2f} {currency_symbol}"))

        # Add edit/delete buttons
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(5)

        # Edit button
        edit_button = QPushButton("تعديل")
        edit_button.setStyleSheet(self.get_button_style('primary'))
        edit_button.clicked.connect(lambda: self.edit_item(row))
        actions_layout.addWidget(edit_button)

        # Delete button
        delete_button = QPushButton("حذف")
        delete_button.setStyleSheet(self.get_button_style('danger'))
        delete_button.clicked.connect(lambda: self.delete_item(row))
        actions_layout.addWidget(delete_button)

        self.items_table.setCellWidget(row, 6, actions_widget)

    def edit_item(self, row):
        """Edit an item in the table."""
        if row < 0 or row >= len(self.items_list):
            return

        item = self.items_list[row]
        dialog = InvoiceItemDialog(self, item)

        if dialog.exec():
            item_data = dialog.get_item_data()

            # Update item object
            item.description = item_data['description']
            item.quantity = item_data['quantity']
            item.unit_price = item_data['unit_price']
            item.discount = item_data['discount']
            item.tax = item_data['tax']
            item.total = item_data['total']

            # Update table
            self.items_list[row] = item

            # Get currency symbol
            currency_symbol = "ج.م"  # Default
            if hasattr(self, 'current_currency') and self.current_currency:
                currency_symbol = self.current_currency.symbol

            # Update table cells
            self.items_table.item(row, 0).setText(item.description)
            self.items_table.item(row, 1).setText(f"{item.quantity:.2f}")
            self.items_table.item(row, 2).setText(f"{item.unit_price:.2f} {currency_symbol}")
            self.items_table.item(row, 3).setText(f"{item.discount:.2f} {currency_symbol}")
            self.items_table.item(row, 4).setText(f"{item.tax:.2f} {currency_symbol}")
            self.items_table.item(row, 5).setText(f"{item.total:.2f} {currency_symbol}")

            # Recalculate totals
            self.calculate_totals()

    def delete_item(self, row):
        """Delete an item from the table."""
        if row < 0 or row >= len(self.items_list):
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا العنصر؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from list and table
            self.items_list.pop(row)
            self.items_table.removeRow(row)

            # Recalculate totals
            self.calculate_totals()

    def calculate_totals(self):
        """Calculate invoice totals."""
        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Calculate subtotal
        subtotal = sum(item.total for item in self.items_list)

        # Get discount and tax
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()

        # Calculate total
        total = subtotal - discount + tax

        # Format numbers with thousand separators for better readability
        from utils.currency_helper import format_thousands
        formatted_subtotal = format_thousands(f"{subtotal:.2f}")
        formatted_total = format_thousands(f"{total:.2f}")

        # Update text fields with formatted values
        self.subtotal_label.setText(f"{formatted_subtotal} {currency_symbol}")
        self.total_label.setText(f"{formatted_total} {currency_symbol}")

        # Update amount due
        amount_paid = self.amount_paid_spin.value()
        amount_due = max(0, total - amount_paid)  # Ensure amount due is not negative
        self.amount_due_spin.blockSignals(True)  # Prevent recursive calls
        self.amount_due_spin.setValue(amount_due)
        self.amount_due_spin.blockSignals(False)

        # Update spinbox suffixes
        self.discount_spin.setSuffix(f" {currency_symbol}")
        self.tax_spin.setSuffix(f" {currency_symbol}")
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")
        self.amount_due_spin.setSuffix(f" {currency_symbol}")

        # Update status based on payment
        if amount_paid <= 0:
            self.status_combo.setCurrentIndex(self.status_combo.findData("draft"))
        elif amount_paid < total:
            self.status_combo.setCurrentIndex(self.status_combo.findData("partially_paid"))
        else:
            self.status_combo.setCurrentIndex(self.status_combo.findData("paid"))

    def update_payment_info(self):
        """Update payment information when amount paid changes."""
        # Get total - handle thousand separators
        total_text = self.total_label.text().split()[0].replace(',', '')
        try:
            total = float(total_text)
        except ValueError:
            total = 0.0

        # Get amount paid
        amount_paid = self.amount_paid_spin.value()

        # Calculate amount due (ensure it's not negative)
        amount_due = max(0, total - amount_paid)

        # Update amount due without triggering signals
        self.amount_due_spin.blockSignals(True)
        self.amount_due_spin.setValue(amount_due)
        self.amount_due_spin.blockSignals(False)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Update spinbox suffixes
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")
        self.amount_due_spin.setSuffix(f" {currency_symbol}")

        # Update status based on payment
        if amount_paid <= 0:
            self.status_combo.setCurrentIndex(self.status_combo.findData("draft"))
        elif amount_paid < total:
            self.status_combo.setCurrentIndex(self.status_combo.findData("partially_paid"))
        else:
            self.status_combo.setCurrentIndex(self.status_combo.findData("paid"))

    def update_payment_from_due(self):
        """Update payment information when amount due changes."""
        # Get total - handle thousand separators
        total_text = self.total_label.text().split()[0].replace(',', '')
        try:
            total = float(total_text)
        except ValueError:
            total = 0.0

        # Get amount due
        amount_due = self.amount_due_spin.value()

        # Calculate amount paid (ensure it's not negative)
        amount_paid = max(0, total - amount_due)

        # Update amount paid without triggering signals
        self.amount_paid_spin.blockSignals(True)
        self.amount_paid_spin.setValue(amount_paid)
        self.amount_paid_spin.blockSignals(False)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Update spinbox suffixes
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")
        self.amount_due_spin.setSuffix(f" {currency_symbol}")

        # Update status based on payment
        if amount_paid <= 0:
            self.status_combo.setCurrentIndex(self.status_combo.findData("draft"))
        elif amount_paid < total:
            self.status_combo.setCurrentIndex(self.status_combo.findData("partially_paid"))
        else:
            self.status_combo.setCurrentIndex(self.status_combo.findData("paid"))

        # If amount paid is greater than total, adjust amount due to zero
        if amount_paid > total:
            self.amount_due_spin.blockSignals(True)
            self.amount_due_spin.setValue(0)
            self.amount_due_spin.blockSignals(False)

    def set_view_only_mode(self):
        """Set the dialog to view-only mode."""
        self.customer_combo.setEnabled(False)
        self.issue_date_edit.setEnabled(False)
        self.due_date_edit.setEnabled(False)
        self.status_combo.setEnabled(False)
        self.add_item_button.setEnabled(False)
        self.select_product_button.setEnabled(False)
        self.notes_edit.setReadOnly(True)
        self.discount_spin.setReadOnly(True)
        self.tax_spin.setReadOnly(True)
        self.amount_paid_spin.setReadOnly(True)
        self.amount_due_spin.setReadOnly(True)

        # Hide edit/delete buttons in the items table
        for row in range(self.items_table.rowCount()):
            self.items_table.removeCellWidget(row, 6)
            self.items_table.setItem(row, 6, QTableWidgetItem(""))

        # Change save button to close
        self.save_button.setText("إغلاق")
        self.save_button.clicked.disconnect()
        self.save_button.clicked.connect(self.accept)

        # Hide cancel button
        self.cancel_button.hide()

    def save_invoice(self):
        """Save the invoice."""
        # Validate required fields
        if self.customer_combo.currentIndex() < 0:
            QMessageBox.warning(self, "خطأ", "يجب اختيار العميل")
            return

        if len(self.items_list) == 0:
            QMessageBox.warning(self, "خطأ", "يجب إضافة منتج أو خدمة واحدة على الأقل")
            return

        # Get invoice data
        customer_id = self.customer_combo.currentData()
        issue_date = self.issue_date_edit.date().toString("yyyy-MM-dd")
        due_date = self.due_date_edit.date().toString("yyyy-MM-dd")
        status = self.status_combo.currentData()
        notes = self.notes_edit.toPlainText()

        # Get totals
        subtotal_text = self.subtotal_label.text().split()[0]
        total_text = self.total_label.text().split()[0]

        try:
            subtotal = float(subtotal_text)
            total = float(total_text)
        except ValueError:
            QMessageBox.warning(self, "خطأ", "حدث خطأ في حساب الإجماليات")
            return

        discount = self.discount_spin.value()
        tax = self.tax_spin.value()
        amount_paid = self.amount_paid_spin.value()
        amount_due = self.amount_due_spin.value()

        # Get currency
        currency_id = None
        if self.currency_manager and self.current_currency:
            currency_id = self.current_currency.id

        # Create or update invoice
        if self.invoice:
            # Update existing invoice
            query = """
                UPDATE invoices
                SET customer_id = ?, issue_date = ?, due_date = ?, status = ?,
                    subtotal = ?, discount = ?, tax = ?, total = ?,
                    amount_paid = ?, amount_due = ?, notes = ?, currency_id = ?
                WHERE id = ?
            """
            params = (
                customer_id, issue_date, due_date, status,
                subtotal, discount, tax, total,
                amount_paid, amount_due, notes, currency_id,
                self.invoice.id
            )
            self.db_manager.execute_query(query, params)

            # Delete existing items
            self.db_manager.execute_query("DELETE FROM invoice_items WHERE invoice_id = ?", (self.invoice.id,))

            # Insert new items
            for item in self.items_list:
                query = """
                    INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, discount, tax, total, product_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    self.invoice.id, item.description, item.quantity, item.unit_price,
                    item.discount, item.tax, item.total, getattr(item, 'product_id', None)
                )
                self.db_manager.execute_query(query, params)

            QMessageBox.information(self, "نجاح", f"تم تحديث الفاتورة {self.invoice.invoice_number} بنجاح")
        else:
            # Generate invoice number
            invoice_number = self.generate_invoice_number()

            # Insert new invoice
            query = """
                INSERT INTO invoices (invoice_number, customer_id, issue_date, due_date, status,
                                     subtotal, discount, tax, total, amount_paid, amount_due, notes, currency_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                invoice_number, customer_id, issue_date, due_date, status,
                subtotal, discount, tax, total, amount_paid, amount_due, notes, currency_id
            )
            invoice_id = self.db_manager.execute_query(query, params, get_last_id=True)

            # Insert items
            for item in self.items_list:
                query = """
                    INSERT INTO invoice_items (invoice_id, description, quantity, unit_price, discount, tax, total, product_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    invoice_id, item.description, item.quantity, item.unit_price,
                    item.discount, item.tax, item.total, getattr(item, 'product_id', None)
                )
                self.db_manager.execute_query(query, params)

            # Update inventory if needed
            self.update_inventory(invoice_id)

            QMessageBox.information(self, "نجاح", f"تم إنشاء الفاتورة {invoice_number} بنجاح")

        self.accept()

    def generate_invoice_number(self):
        """Generate a unique invoice number."""
        # Get the last invoice number
        query = "SELECT invoice_number FROM invoices ORDER BY id DESC LIMIT 1"
        rows = self.db_manager.execute_query(query)

        if rows:
            last_number = rows[0]['invoice_number']
            # Extract numeric part
            try:
                prefix = ''.join(c for c in last_number if not c.isdigit())
                number = int(''.join(c for c in last_number if c.isdigit()))
                new_number = f"{prefix}{number + 1:04d}"
            except:
                # If parsing fails, use default
                new_number = "INV-1001"
        else:
            # No invoices yet
            new_number = "INV-1001"

        return new_number

    def update_inventory(self, invoice_id):
        """Update inventory for products in the invoice."""
        # Check if we need to update inventory
        for item in self.items_list:
            if hasattr(item, 'product_id') and item.product_id:
                # Get product details
                query = "SELECT type, track_inventory FROM products WHERE id = ?"
                rows = self.db_manager.execute_query(query, (item.product_id,))

                if rows and rows[0]['type'] == 'product' and rows[0]['track_inventory']:
                    # Update inventory
                    from database.inventory_manager import InventoryManager
                    inventory_manager = InventoryManager(self.db_manager)
                    inventory_manager.update_stock(item.product_id, -item.quantity, f"Invoice #{invoice_id}")
