#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Currencies View for فوترها (Fawterha)
Manages currency data display and editing
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFormLayout, QLineEdit, QDoubleSpinBox,
    QCheckBox, QDialogButtonBox
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QColor, QIcon

from models.currency import Currency
from ui.themes import THEMES
from utils.translation_manager import tr


def format_currency(amount, currency_code):
    """Format a currency amount.

    Args:
        amount (float): Amount to format
        currency_code (str): Currency code

    Returns:
        str: Formatted amount with currency symbol
    """
    return f"{amount:.2f} {currency_code}"


class CurrenciesView(QWidget):
    """Widget for managing currencies."""

    # Signal to notify when primary currency is changed
    currency_changed = Signal(str)

    def __init__(self, db_manager, currency_manager):
        """Initialize the currencies view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager

        # Get current theme from settings
        self.theme_key = "default"
        try:
            theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
            if theme_setting and theme_setting[0]['value']:
                self.theme_key = theme_setting[0]['value']
        except Exception as e:
            print(f"Error loading theme setting: {e}")
            self.theme_key = "default"

        # Get theme colors
        self.theme = THEMES.get(self.theme_key, THEMES["default"])

        # Create layout
        layout = QVBoxLayout(self)

        # Create header
        header_layout = QHBoxLayout()
        title_label = QLabel(tr("currency.management", "إدارة العملات"))
        title_label.setStyleSheet(f"font-size: 18pt; font-weight: bold; color: {self.theme['primary_dark']};")
        header_layout.addWidget(title_label)
        header_layout.addStretch()

        # Create buttons
        self.add_button = QPushButton(tr("currency.add_new_currency", "إضافة عملة جديدة"))
        self.add_button.setIcon(QIcon("resources/icons/add.png"))
        self.add_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["success"]};
                color: {self.theme["button_text"]};
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: {self.theme["border_radius"]};
            }}
            QPushButton:hover {{
                background-color: {self.theme["success_dark"]};
            }}
        """)
        self.add_button.clicked.connect(self.add_currency)
        header_layout.addWidget(self.add_button)

        layout.addLayout(header_layout)

        # Create table
        self.currencies_table = QTableWidget()
        self.currencies_table.setColumnCount(6)
        self.currencies_table.setHorizontalHeaderLabels([
            tr("currency.short_code", "الرمز"),
            tr("currency.name", "الاسم"),
            tr("currency.symbol", "الرمز"),
            tr("currency.exchange_rate", "سعر الصرف"),
            tr("currency.is_primary", "العملة الرئيسية"),
            tr("currency.is_active", "نشط")
        ])
        self.currencies_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.currencies_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.currencies_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.currencies_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.currencies_table.setAlternatingRowColors(True)
        self.currencies_table.verticalHeader().setDefaultSectionSize(60)  # Increase row height
        self.currencies_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.theme.get("table_bg", self.theme.get("panel_bg", "#ffffff"))};
                alternate-background-color: {self.theme.get("table_alternate_bg", self.theme.get("main_bg", "#f5f5f5"))};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                gridline-color: {self.theme["border_color"]};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.theme["border_color"]};
                min-height: 32px;
                margin: 1px;
                color: {self.theme["main_text"]};
            }}
            QHeaderView::section {{
                background-color: {self.theme["primary_dark"]};
                color: {self.theme["button_text"]};
                padding: 14px;
                font-weight: bold;
                font-size: 13pt;
                border: none;
            }}
        """)
        layout.addWidget(self.currencies_table)

        # Create actions layout
        actions_layout = QHBoxLayout()
        actions_layout.addStretch()

        self.set_primary_button = QPushButton(tr("currency.set_as_primary", "تعيين كعملة رئيسية"))
        self.set_primary_button.setIcon(QIcon("resources/icons/star.png"))
        self.set_primary_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["success"]};
                color: {self.theme["button_text"]};
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: {self.theme["border_radius"]};
            }}
            QPushButton:hover {{
                background-color: {self.theme["success_dark"]};
            }}
        """)
        self.set_primary_button.clicked.connect(self.set_as_primary)
        actions_layout.addWidget(self.set_primary_button)

        self.edit_button = QPushButton(tr("common.edit", "تعديل"))
        self.edit_button.setIcon(QIcon("resources/icons/edit.png"))
        self.edit_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["primary"]};
                color: {self.theme["button_text"]};
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: {self.theme["border_radius"]};
            }}
            QPushButton:hover {{
                background-color: {self.theme["primary_dark"]};
            }}
        """)
        self.edit_button.clicked.connect(self.edit_currency)
        actions_layout.addWidget(self.edit_button)

        self.delete_button = QPushButton(tr("common.delete", "حذف"))
        self.delete_button.setIcon(QIcon("resources/icons/delete.png"))
        self.delete_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["danger"]};
                color: {self.theme["button_text"]};
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: {self.theme["border_radius"]};
            }}
            QPushButton:hover {{
                background-color: {self.theme["danger_dark"]};
            }}
        """)
        self.delete_button.clicked.connect(self.delete_currency)
        actions_layout.addWidget(self.delete_button)

        layout.addLayout(actions_layout)

        # Load currencies
        self.load_currencies()

    def load_currencies(self):
        """Load currencies from the database."""
        currencies = self.currency_manager.get_all_currencies()

        self.currencies_table.setRowCount(0)

        for currency in currencies:
            self.add_currency_to_table(currency)

    def add_currency_to_table(self, currency):
        """Add a currency to the table.

        Args:
            currency (Currency): Currency to add
        """
        row_position = self.currencies_table.rowCount()
        self.currencies_table.insertRow(row_position)

        # Set currency data
        self.currencies_table.setItem(row_position, 0, QTableWidgetItem(currency.code))
        self.currencies_table.setItem(row_position, 1, QTableWidgetItem(currency.name))
        self.currencies_table.setItem(row_position, 2, QTableWidgetItem(currency.symbol))
        self.currencies_table.setItem(row_position, 3, QTableWidgetItem(f"{currency.exchange_rate:.4f}"))

        # Primary currency
        primary_item = QTableWidgetItem(tr("common.yes", "نعم") if currency.is_primary else tr("common.no", "لا"))
        primary_item.setTextAlignment(Qt.AlignCenter)
        if currency.is_primary:
            primary_item.setForeground(QColor("#4caf50"))  # Green
        self.currencies_table.setItem(row_position, 4, primary_item)

        # Active
        active_item = QTableWidgetItem(tr("common.yes", "نعم") if currency.is_active else tr("common.no", "لا"))
        active_item.setTextAlignment(Qt.AlignCenter)
        if not currency.is_active:
            active_item.setForeground(QColor("#f44336"))  # Red
        self.currencies_table.setItem(row_position, 5, active_item)

        # Store currency ID as hidden data
        self.currencies_table.item(row_position, 0).setData(Qt.UserRole, currency.id)

    def add_currency(self):
        """Add a new currency."""
        dialog = CurrencyDialog(self)
        if dialog.exec():
            currency_data = dialog.get_currency_data()

            # Create currency object
            currency = Currency(
                code=currency_data['code'],
                name=currency_data['name'],
                symbol=currency_data['symbol'],
                exchange_rate=currency_data['exchange_rate'],
                is_primary=currency_data['is_primary'],
                is_active=currency_data['is_active']
            )

            try:
                # Add to database
                currency_id = self.currency_manager.add_currency(currency)

                # Reload currencies
                self.load_currencies()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("currency.add_success", "تم إضافة العملة بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("currency.add_error", f"حدث خطأ أثناء إضافة العملة: {str(e)}"))

    def edit_currency(self):
        """Edit the selected currency."""
        selected_rows = self.currencies_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.select_to_edit", "الرجاء اختيار عملة للتعديل"))
            return

        # Get currency ID from the first cell of the selected row
        currency_id = self.currencies_table.item(selected_rows[0].row(), 0).data(Qt.UserRole)

        # Get currency from database
        currency = self.currency_manager.get_currency_by_id(currency_id)
        if not currency:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.not_found", "لم يتم العثور على العملة"))
            return

        dialog = CurrencyDialog(self, currency)
        if dialog.exec():
            currency_data = dialog.get_currency_data()

            # Update currency object
            currency.code = currency_data['code']
            currency.name = currency_data['name']
            currency.symbol = currency_data['symbol']
            currency.exchange_rate = currency_data['exchange_rate']
            currency.is_primary = currency_data['is_primary']
            currency.is_active = currency_data['is_active']

            try:
                # Update in database
                self.currency_manager.update_currency(currency)

                # Reload currencies
                self.load_currencies()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("currency.update_success", "تم تحديث العملة بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("currency.update_error", f"حدث خطأ أثناء تحديث العملة: {str(e)}"))

    def set_as_primary(self):
        """Set the selected currency as primary."""
        selected_rows = self.currencies_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.select_to_set_primary", "الرجاء اختيار عملة لتعيينها كعملة رئيسية"))
            return

        # Get currency ID from the first cell of the selected row
        currency_id = self.currencies_table.item(selected_rows[0].row(), 0).data(Qt.UserRole)

        # Get currency from database
        currency = self.currency_manager.get_currency_by_id(currency_id)
        if not currency:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.not_found", "لم يتم العثور على العملة"))
            return

        # Check if already primary
        if currency.is_primary:
            QMessageBox.information(self, tr("messages.info", "معلومات"), tr("currency.already_primary", f"العملة {currency.name} هي بالفعل العملة الرئيسية"))
            return

        # Confirm change
        reply = QMessageBox.question(
            self, tr("messages.confirm_change", "تأكيد التغيير"),
            tr("currency.confirm_set_primary", f"هل أنت متأكد من تعيين {currency.name} كعملة رئيسية؟\n\nسيتم إعادة حساب أسعار الصرف لجميع العملات الأخرى."),
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Set as primary
                currency.is_primary = True

                # Update in database
                result = self.currency_manager.update_currency(currency)

                if result:
                    # Reload currencies
                    self.load_currencies()

                    # Emit currency changed signal
                    self.currency_changed.emit(currency.code)

                    # Notify the main window to update currency display
                    if hasattr(self, 'main_window') and self.main_window:
                        if hasattr(self.main_window, 'update_currency_display'):
                            print(f"Notifying main window to update currency display to {currency.code}")
                            self.main_window.update_currency_display(currency)

                    QMessageBox.information(
                        self,
                        tr("currency.primary_set", "تم تعيين العملة الرئيسية"),
                        tr("currency.primary_set_success", f"تم تعيين {currency.name} ({currency.symbol}) كعملة رئيسية بنجاح.\n\n"
                        f"سيتم تحديث جميع أجزاء البرنامج لاستخدام العملة الجديدة.")
                    )
                else:
                    QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("currency.primary_set_failed", "فشل تعيين العملة الرئيسية"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("currency.primary_set_error", f"حدث خطأ أثناء تعيين العملة الرئيسية: {str(e)}"))

    def delete_currency(self):
        """Delete the selected currency."""
        selected_rows = self.currencies_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.select_to_delete", "الرجاء اختيار عملة للحذف"))
            return

        # Get currency ID from the first cell of the selected row
        currency_id = self.currencies_table.item(selected_rows[0].row(), 0).data(Qt.UserRole)

        # Get currency from database
        currency = self.currency_manager.get_currency_by_id(currency_id)
        if not currency:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.not_found", "لم يتم العثور على العملة"))
            return

        # Check if this is the primary currency
        if currency.is_primary:
            QMessageBox.warning(self, tr("messages.warning", "تحذير"), tr("currency.cannot_delete_primary", "لا يمكن حذف العملة الرئيسية"))
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self, tr("messages.confirm_delete", "تأكيد الحذف"),
            tr("currency.confirm_delete", f"هل أنت متأكد من حذف العملة {currency.name}؟"),
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # Delete from database
                self.currency_manager.delete_currency(currency_id)

                # Reload currencies
                self.load_currencies()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("currency.delete_success", "تم حذف العملة بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("currency.delete_error", f"حدث خطأ أثناء حذف العملة: {str(e)}"))


class CurrencyDialog(QDialog):
    """Dialog for adding or editing a currency."""

    def __init__(self, parent=None, currency=None):
        """Initialize the currency dialog.

        Args:
            parent: Parent widget
            currency (Currency, optional): Currency to edit. Defaults to None.
        """
        super().__init__(parent)

        self.currency = currency
        self.setWindowTitle(tr("currency.add_new_currency", "إضافة عملة جديدة") if not currency else tr("currency.edit", "تعديل العملة"))
        self.setMinimumWidth(400)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Get current theme from parent if available
        if hasattr(parent, 'theme'):
            self.theme = parent.theme
        else:
            # Default theme
            self.theme = THEMES["default"]

        # Set dialog style
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.theme["main_bg"]};
                color: {self.theme["main_text"]};
            }}
            QLabel {{
                color: {self.theme["main_text"]};
            }}
            QLineEdit, QDoubleSpinBox {{
                background-color: {self.theme.get("input_bg", "#ffffff")};
                color: {self.theme["input_text"]};
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                padding: 5px;
            }}
            QCheckBox {{
                color: {self.theme["main_text"]};
            }}
            QPushButton {{
                background-color: {self.theme["primary"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {self.theme["primary_dark"]};
            }}
        """)

        # Create layout
        layout = QVBoxLayout(self)

        # Create form
        form_layout = QFormLayout()

        # Code
        self.code_edit = QLineEdit()
        self.code_edit.setPlaceholderText(tr("currency.code_example", "مثال: USD"))
        self.code_edit.setMaxLength(3)
        form_layout.addRow(tr("currency.code_label", "رمز العملة:"), self.code_edit)

        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("currency.name_example", "مثال: الدولار الأمريكي"))
        form_layout.addRow(tr("currency.name_label", "اسم العملة:"), self.name_edit)

        # Symbol
        self.symbol_edit = QLineEdit()
        self.symbol_edit.setPlaceholderText(tr("currency.symbol_example", "مثال: $"))
        form_layout.addRow(tr("currency.symbol_label", "الرمز المختصر:"), self.symbol_edit)

        # Exchange rate
        self.rate_spin = QDoubleSpinBox()
        self.rate_spin.setRange(0.0001, 1000000)
        self.rate_spin.setDecimals(4)
        self.rate_spin.setSingleStep(0.1)
        self.rate_spin.setValue(1.0)
        form_layout.addRow(tr("currency.exchange_rate_label", "سعر الصرف:"), self.rate_spin)

        # Is primary
        self.primary_check = QCheckBox(tr("currency.is_primary_check", "هذه هي العملة الرئيسية"))
        form_layout.addRow("", self.primary_check)

        # Is active
        self.active_check = QCheckBox(tr("currency.is_active_check", "العملة نشطة"))
        self.active_check.setChecked(True)
        form_layout.addRow("", self.active_check)

        layout.addLayout(form_layout)

        # Add buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # Fill form if editing
        if currency:
            self.code_edit.setText(currency.code)
            self.name_edit.setText(currency.name)
            self.symbol_edit.setText(currency.symbol)
            self.rate_spin.setValue(currency.exchange_rate)
            self.primary_check.setChecked(currency.is_primary)
            self.active_check.setChecked(currency.is_active)

    def get_currency_data(self):
        """Get the currency data from the form.

        Returns:
            dict: Currency data
        """
        return {
            'code': self.code_edit.text().strip().upper(),
            'name': self.name_edit.text().strip(),
            'symbol': self.symbol_edit.text().strip(),
            'exchange_rate': self.rate_spin.value(),
            'is_primary': self.primary_check.isChecked(),
            'is_active': self.active_check.isChecked()
        }
