#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Alerts View for فوترها (Fawterha)
Provides a UI for viewing and managing inventory alerts
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QMessageBox, QTabWidget, QCheckBox
)
from PySide6.QtCore import Qt, QSize, QTimer
from PySide6.QtGui import QIcon, QFont, QColor, QAction

from database.enhanced_inventory_manager import EnhancedInventoryManager
from models.inventory_alert import InventoryAlert
from utils.translation_manager import tr


class InventoryAlertsView(QWidget):
    """Widget for viewing and managing inventory alerts."""

    def __init__(self, db_manager):
        """Initialize the inventory alerts view.

        Args:
            db_manager: Database manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.inventory_manager = EnhancedInventoryManager(db_manager)

        # Set up auto-refresh timer (every 5 minutes)
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.check_for_alerts)
        self.refresh_timer.start(300000)  # 5 minutes in milliseconds

        self.setup_ui()
        self.load_data()
        self.check_for_alerts()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Title
        title_label = QLabel(tr("inventory.alerts", "تنبيهات المخزون"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Description
        description_label = QLabel(tr("inventory.alerts_description", "عرض وإدارة تنبيهات المخزون المنخفض والمنتجات قريبة انتهاء الصلاحية"))
        description_label.setStyleSheet("font-size: 10pt;")
        main_layout.addWidget(description_label)

        # Toolbar
        toolbar_layout = QHBoxLayout()
        main_layout.addLayout(toolbar_layout)

        # Check for alerts button
        self.check_alerts_button = QPushButton(tr("inventory.check_alerts", "فحص التنبيهات"))
        self.check_alerts_button.setIcon(QIcon("resources/icons/refresh.png"))
        self.check_alerts_button.clicked.connect(self.check_for_alerts)
        toolbar_layout.addWidget(self.check_alerts_button)

        # Mark all as read button
        self.mark_all_read_button = QPushButton(tr("inventory.mark_all_read", "تعليم الكل كمقروء"))
        self.mark_all_read_button.setIcon(QIcon("resources/icons/check.png"))
        self.mark_all_read_button.clicked.connect(self.mark_all_as_read)
        toolbar_layout.addWidget(self.mark_all_read_button)

        toolbar_layout.addStretch()

        # Filter by type
        toolbar_layout.addWidget(QLabel(tr("inventory.filter_by_type", "تصفية حسب النوع:")))
        
        self.type_combo = QComboBox()
        self.type_combo.addItem(tr("inventory.all_types", "جميع الأنواع"), "all")
        self.type_combo.addItem(tr("inventory.low_stock", "مخزون منخفض"), InventoryAlert.TYPE_LOW_STOCK)
        self.type_combo.addItem(tr("inventory.expiry", "قرب انتهاء الصلاحية"), InventoryAlert.TYPE_EXPIRY)
        self.type_combo.addItem(tr("inventory.out_of_stock", "نفاد المخزون"), InventoryAlert.TYPE_OUT_OF_STOCK)
        self.type_combo.currentIndexChanged.connect(self.load_data)
        toolbar_layout.addWidget(self.type_combo)

        # Show read alerts checkbox
        self.show_read_check = QCheckBox(tr("inventory.show_read", "عرض التنبيهات المقروءة"))
        self.show_read_check.stateChanged.connect(self.load_data)
        toolbar_layout.addWidget(self.show_read_check)

        # Alerts table
        self.alerts_table = QTableWidget()
        self.alerts_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.alerts_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.alerts_table.setAlternatingRowColors(True)
        self.alerts_table.verticalHeader().setVisible(False)
        self.alerts_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.alerts_table.setColumnCount(6)
        self.alerts_table.setHorizontalHeaderLabels([
            tr("inventory.alert_type", "نوع التنبيه"),
            tr("inventory.product", "المنتج"),
            tr("inventory.warehouse", "المستودع"),
            tr("inventory.message", "الرسالة"),
            tr("inventory.date", "التاريخ"),
            tr("common.actions", "الإجراءات")
        ])
        main_layout.addWidget(self.alerts_table)

        # Status bar
        status_layout = QHBoxLayout()
        main_layout.addLayout(status_layout)

        self.status_label = QLabel("")
        status_layout.addWidget(self.status_label)

        status_layout.addStretch()

        self.auto_refresh_label = QLabel(tr("inventory.auto_refresh", "تحديث تلقائي كل 5 دقائق"))
        self.auto_refresh_label.setStyleSheet("color: gray; font-style: italic;")
        status_layout.addWidget(self.auto_refresh_label)

    def load_data(self):
        """Load alerts data."""
        # Clear the table
        self.alerts_table.setRowCount(0)

        # Get filter values
        alert_type = self.type_combo.currentData()
        if alert_type == "all":
            alert_type = None

        show_read = self.show_read_check.isChecked()
        is_read = None if show_read else False

        # Get alerts
        alerts = self.inventory_manager.get_alerts(is_read=is_read, alert_type=alert_type)

        # Populate the table
        self.alerts_table.setRowCount(len(alerts))
        for row, alert in enumerate(alerts):
            # Alert type
            type_text = ""
            if alert.alert_type == InventoryAlert.TYPE_LOW_STOCK:
                type_text = tr("inventory.low_stock", "مخزون منخفض")
            elif alert.alert_type == InventoryAlert.TYPE_EXPIRY:
                type_text = tr("inventory.expiry", "قرب انتهاء الصلاحية")
            elif alert.alert_type == InventoryAlert.TYPE_OUT_OF_STOCK:
                type_text = tr("inventory.out_of_stock", "نفاد المخزون")

            type_item = QTableWidgetItem(type_text)
            if alert.alert_type == InventoryAlert.TYPE_LOW_STOCK:
                type_item.setForeground(QColor("orange"))
            elif alert.alert_type == InventoryAlert.TYPE_EXPIRY:
                type_item.setForeground(QColor("red"))
            elif alert.alert_type == InventoryAlert.TYPE_OUT_OF_STOCK:
                type_item.setForeground(QColor("darkred"))

            self.alerts_table.setItem(row, 0, type_item)

            # Product
            # Get product name from the database
            query = """
            SELECT name FROM products WHERE id = ?
            """
            product_rows = self.db_manager.execute_query(query, (alert.product_id,))
            product_name = product_rows[0]['name'] if product_rows else f"منتج #{alert.product_id}"
            
            product_item = QTableWidgetItem(product_name)
            self.alerts_table.setItem(row, 1, product_item)

            # Warehouse
            warehouse_name = ""
            if alert.warehouse_id:
                # Get warehouse name from the database
                query = """
                SELECT name FROM warehouses WHERE id = ?
                """
                warehouse_rows = self.db_manager.execute_query(query, (alert.warehouse_id,))
                warehouse_name = warehouse_rows[0]['name'] if warehouse_rows else f"مستودع #{alert.warehouse_id}"
            
            warehouse_item = QTableWidgetItem(warehouse_name)
            self.alerts_table.setItem(row, 2, warehouse_item)

            # Message
            message_item = QTableWidgetItem(alert.message)
            if alert.is_read:
                message_item.setForeground(QColor("gray"))
            self.alerts_table.setItem(row, 3, message_item)

            # Date
            date_item = QTableWidgetItem(str(alert.created_at))
            self.alerts_table.setItem(row, 4, date_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)

            # Mark as read button
            if not alert.is_read:
                read_button = QPushButton(tr("inventory.mark_read", "تعليم كمقروء"))
                read_button.setProperty("alert_id", alert.id)
                read_button.clicked.connect(self.mark_as_read)
                actions_layout.addWidget(read_button)

            # View product button
            view_button = QPushButton(tr("inventory.view_product", "عرض المنتج"))
            view_button.setProperty("product_id", alert.product_id)
            view_button.clicked.connect(self.view_product)
            actions_layout.addWidget(view_button)

            # Delete button
            delete_button = QPushButton(tr("common.delete", "حذف"))
            delete_button.setProperty("alert_id", alert.id)
            delete_button.clicked.connect(self.delete_alert)
            actions_layout.addWidget(delete_button)

            self.alerts_table.setCellWidget(row, 5, actions_widget)

        # Update status label
        self.status_label.setText(tr("inventory.alerts_count", "عدد التنبيهات: {0}").format(len(alerts)))

    def check_for_alerts(self):
        """Check for new alerts."""
        # Check for low stock alerts
        low_stock_alerts = self.inventory_manager.check_low_stock_alerts()
        
        # Check for expiry alerts
        expiry_alerts = self.inventory_manager.check_expiry_alerts()
        
        # Reload data
        self.load_data()
        
        # Show notification if new alerts were created
        total_alerts = low_stock_alerts + expiry_alerts
        if total_alerts > 0:
            QMessageBox.information(
                self,
                tr("inventory.new_alerts", "تنبيهات جديدة"),
                tr("inventory.new_alerts_created", "تم إنشاء {0} تنبيه جديد").format(total_alerts)
            )

    def mark_as_read(self):
        """Mark an alert as read."""
        button = self.sender()
        if button:
            alert_id = button.property("alert_id")
            if self.inventory_manager.mark_alert_as_read(alert_id):
                self.load_data()

    def mark_all_as_read(self):
        """Mark all alerts as read."""
        # Get all unread alerts
        alerts = self.inventory_manager.get_alerts(is_read=False)
        
        # Mark each alert as read
        for alert in alerts:
            self.inventory_manager.mark_alert_as_read(alert.id)
            
        # Reload data
        self.load_data()
        
        # Show confirmation
        QMessageBox.information(
            self,
            tr("messages.success", "نجاح"),
            tr("inventory.all_marked_read", "تم تعليم جميع التنبيهات كمقروءة")
        )

    def view_product(self):
        """View product details."""
        button = self.sender()
        if button:
            product_id = button.property("product_id")
            # TODO: Implement product view
            QMessageBox.information(
                self,
                tr("inventory.product_details", "تفاصيل المنتج"),
                tr("inventory.feature_coming_soon", "هذه الميزة قادمة قريباً")
            )

    def delete_alert(self):
        """Delete an alert."""
        button = self.sender()
        if button:
            alert_id = button.property("alert_id")
            
            # Confirm deletion
            confirm = QMessageBox.question(
                self,
                tr("inventory.confirm_delete", "تأكيد الحذف"),
                tr("inventory.confirm_delete_alert", "هل أنت متأكد من حذف هذا التنبيه؟"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if confirm == QMessageBox.Yes:
                if self.inventory_manager.delete_alert(alert_id):
                    self.load_data()
                    
    def closeEvent(self, event):
        """Handle close event.
        
        Args:
            event: Close event
        """
        # Stop the refresh timer
        self.refresh_timer.stop()
        super().closeEvent(event)
