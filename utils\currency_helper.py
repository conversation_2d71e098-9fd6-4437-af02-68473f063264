#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Currency Helper for فوترها (Fawterha)
Provides currency-related utility functions
"""

def get_currency_symbol(currency_code):
    """Get the currency symbol for a currency code.

    Args:
        currency_code (str): Currency code

    Returns:
        str: Currency symbol
    """
    currency_symbols = {
        'SAR': "ر.س",
        'USD': "$",
        'EUR': "€",
        'GBP': "£",
        'AED': "د.إ",
        'KWD': "د.ك",
        'QAR': "ر.ق",
        'BHD': "د.ب",
        'OMR': "ر.ع",
        'EGP': "ج.م"
    }
    return currency_symbols.get(currency_code, currency_code)


def get_currency_name(currency_code):
    """Get the currency name for a currency code.

    Args:
        currency_code (str): Currency code

    Returns:
        str: Currency name
    """
    currency_names = {
        'SAR': "ريال سعودي",
        'USD': "دولار أمريكي",
        'EUR': "يورو",
        'GBP': "جنيه إسترليني",
        'AED': "درهم إماراتي",
        'KWD': "دينار كويتي",
        'QAR': "ريال قطري",
        'BHD': "دينار بحريني",
        'OMR': "ريال عماني",
        'EGP': "جنيه مصري"
    }
    return currency_names.get(currency_code, currency_code)


def format_currency(amount, currency_code, decimal_places=2, use_html=False):
    """Format an amount with the currency symbol.

    Args:
        amount (float): Amount to format
        currency_code (str): Currency code
        decimal_places (int, optional): Number of decimal places. Defaults to 2.
        use_html (bool, optional): Whether to return HTML formatted string. Defaults to False.

    Returns:
        str: Formatted amount with currency symbol
    """
    symbol = get_currency_symbol(currency_code)

    # Ensure amount is a float
    try:
        amount_float = float(amount)
    except (ValueError, TypeError):
        print(f"Warning: Invalid amount value: {amount}, defaulting to 0.0")
        amount_float = 0.0

    # Format with specified decimal places
    formatted_amount = f"{amount_float:.{decimal_places}f}"

    # Add thousand separators
    parts = formatted_amount.split('.')
    parts[0] = format_thousands(parts[0])
    formatted_amount = '.'.join(parts)

    # Return formatted amount with currency symbol
    if use_html:
        return f'<span class="currency-value">{formatted_amount}</span> <span class="currency-symbol">{symbol}</span>'
    else:
        return f"{formatted_amount} {symbol}"


def convert_currency(amount, from_rate, to_rate):
    """Convert an amount between currencies.

    Args:
        amount (float): Amount to convert
        from_rate (float): Exchange rate of source currency to primary currency (EGP)
        to_rate (float): Exchange rate of target currency to primary currency (EGP)

    Returns:
        float: Converted amount
    """
    # Ensure we have valid rates
    if from_rate <= 0 or to_rate <= 0:
        return amount

    # First convert to primary currency (EGP)
    # For example, if from_rate is 50.6 (USD to EGP), then 100 USD = 100 * 50.6 = 5060 EGP
    primary_amount = amount * from_rate

    # Then convert from primary to target currency
    # For example, if to_rate is 56.9 (EUR to EGP), then 5060 EGP / 56.9 = 88.93 EUR
    result = primary_amount / to_rate

    # Print debug information
    print(f"Converting {amount} with from_rate={from_rate} to_rate={to_rate}")
    print(f"Primary amount: {amount} * {from_rate} = {primary_amount}")
    print(f"Result: {primary_amount} / {to_rate} = {result}")

    return result


def format_thousands(value):
    """Format a number with thousand separators.

    Args:
        value (str): Number as string

    Returns:
        str: Formatted number with thousand separators
    """
    # Split by decimal point
    parts = value.split('.')

    # Format the integer part
    integer_part = parts[0]
    result = ""
    for i, char in enumerate(reversed(integer_part)):
        if i > 0 and i % 3 == 0:
            result = ',' + result
        result = char + result

    # Add decimal part if exists
    if len(parts) > 1:
        result += '.' + parts[1]

    return result


def get_styled_currency_label(amount, currency_code, is_total=False, is_positive=True):
    """Create a styled QLabel for currency display.

    Args:
        amount (float): Amount to display
        currency_code (str): Currency code
        is_total (bool, optional): Whether this is a total amount (larger font). Defaults to False.
        is_positive (bool, optional): Whether to use positive styling (green). Defaults to True.

    Returns:
        QLabel: Styled label with formatted currency
    """
    from PySide6.QtWidgets import QLabel
    from PySide6.QtCore import Qt

    # Format the amount with currency symbol
    formatted = format_currency(amount, currency_code)

    # Create the label
    label = QLabel(formatted)
    label.setAlignment(Qt.AlignCenter)  # Center alignment for better appearance

    # Apply styling based on parameters
    if is_total:
        # Total amount styling (larger, bolder)
        if is_positive:
            # Positive total (green)
            label.setStyleSheet("""
                font-size: 14pt;
                font-weight: bold;
                color: #2E7D32;
                background-color: #E8F5E9;
                border: 2px solid #1976D2;
                border-radius: 6px;
                padding: 10px 15px;
                margin: 4px 0;
                min-width: 150px;
                min-height: 20px;
            """)
        else:
            # Negative total (red)
            label.setStyleSheet("""
                font-size: 14pt;
                font-weight: bold;
                color: #C62828;
                background-color: #FFEBEE;
                border: 2px solid #1976D2;
                border-radius: 6px;
                padding: 10px 15px;
                margin: 4px 0;
                min-width: 150px;
                min-height: 20px;
            """)
    else:
        # Regular amount styling
        if is_positive:
            # Positive amount (dark text)
            label.setStyleSheet("""
                font-size: 12pt;
                color: #212121;
                background-color: #F5F5F5;
                border: 1px solid #1976D2;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px 0;
                min-width: 120px;
                min-height: 18px;
            """)
        else:
            # Negative amount (red)
            label.setStyleSheet("""
                font-size: 12pt;
                color: #C62828;
                background-color: #FFEBEE;
                border: 1px solid #1976D2;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 2px 0;
                min-width: 120px;
                min-height: 18px;
            """)

    return label


def get_totals_section_title():
    """Create a standardized title label for the totals section.

    Returns:
        QLabel: Styled label with the title "الإجماليات"
    """
    from PySide6.QtWidgets import QLabel
    from PySide6.QtCore import Qt

    # Create the title label
    title = QLabel("الإجماليات")
    title.setAlignment(Qt.AlignCenter)

    # Apply consistent styling
    title.setStyleSheet("""
        font-size: 20pt;
        font-weight: bold;
        color: white;
        background-color: #1976D2;
        border-bottom: 3px solid #0D47A1;
        border-radius: 8px 8px 0 0;
        padding: 12px;
        margin-bottom: 20px;
        min-height: 30px;
    """)

    return title


def get_totals_container_style():
    """Get the standardized style for the totals container.

    Returns:
        str: CSS style for the totals container
    """
    return """
        background-color: white;
        border: 3px solid #1976D2;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    """
