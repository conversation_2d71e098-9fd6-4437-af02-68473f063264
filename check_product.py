#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Check Product Script for فوترها (Fawterha)
Checks the product details in the database
"""

import os
import sqlite3

def check_product():
    """Check product details in the database."""
    # Get database path
    documents_path = os.path.expanduser("~/Documents/Fawterha")
    db_path = os.path.join(documents_path, "fawterha.db")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Get product details
        cursor.execute("SELECT * FROM products WHERE name = ?", ("ماوس",))
        product = cursor.fetchone()
        
        if product:
            print("Product found:")
            print(f"ID: {product['id']}")
            print(f"Name: {product['name']}")
            print(f"Description: {product['description']}")
            print(f"Price: {product['price']}")
            print(f"Type: {product['type']}")
            print(f"Track Inventory: {product['track_inventory']}")
            print(f"Stock Quantity: {product['stock_quantity']}")
            print(f"Min Stock Level: {product['min_stock_level']}")
        else:
            print("Product not found.")
        
    except Exception as e:
        print(f"Error checking product: {str(e)}")
    finally:
        # Close connection
        conn.close()

if __name__ == "__main__":
    check_product()
