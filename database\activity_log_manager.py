#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Activity Log Manager for فوترها (Fawterha)
Handles database operations for the activity log
"""

import sqlite3
from datetime import datetime
import json
import socket
import getpass

from models.activity_log import ActivityLog

class ActivityLogManager:
    """Manager for activity log database operations."""

    def __init__(self, db_manager):
        """Initialize the activity log manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    def add_log(self, activity_log):
        """Add a new activity log entry.

        Args:
            activity_log (ActivityLog): Activity log object to add

        Returns:
            int: ID of the new activity log entry
        """
        query = """
        INSERT INTO activity_logs (
            user_id, username, activity_type, entity_type, 
            entity_id, description, details, ip_address
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            activity_log.user_id,
            activity_log.username,
            activity_log.activity_type,
            activity_log.entity_type,
            activity_log.entity_id,
            activity_log.description,
            activity_log.details,
            activity_log.ip_address or self._get_ip_address()
        )
        return self.db_manager.execute_insert(query, params)

    def get_logs(self, limit=100, offset=0, user_id=None, activity_type=None, 
                entity_type=None, entity_id=None, start_date=None, end_date=None):
        """Get activity logs with optional filtering.

        Args:
            limit (int, optional): Maximum number of logs to return. Defaults to 100.
            offset (int, optional): Offset for pagination. Defaults to 0.
            user_id (int, optional): Filter by user ID. Defaults to None.
            activity_type (str, optional): Filter by activity type. Defaults to None.
            entity_type (str, optional): Filter by entity type. Defaults to None.
            entity_id (int, optional): Filter by entity ID. Defaults to None.
            start_date (str, optional): Filter by start date (YYYY-MM-DD). Defaults to None.
            end_date (str, optional): Filter by end date (YYYY-MM-DD). Defaults to None.

        Returns:
            list: List of ActivityLog objects
        """
        query = """
        SELECT * FROM activity_logs
        WHERE 1=1
        """
        params = []

        if user_id:
            query += " AND user_id = ?"
            params.append(user_id)

        if activity_type:
            query += " AND activity_type = ?"
            params.append(activity_type)

        if entity_type:
            query += " AND entity_type = ?"
            params.append(entity_type)

        if entity_id:
            query += " AND entity_id = ?"
            params.append(entity_id)

        if start_date:
            query += " AND date(created_at) >= date(?)"
            params.append(start_date)

        if end_date:
            query += " AND date(created_at) <= date(?)"
            params.append(end_date)

        query += " ORDER BY created_at DESC LIMIT ? OFFSET ?"
        params.append(limit)
        params.append(offset)

        rows = self.db_manager.execute_query(query, tuple(params))
        return [ActivityLog.from_db_row(row) for row in rows]

    def get_log_by_id(self, log_id):
        """Get an activity log by ID.

        Args:
            log_id (int): Activity log ID

        Returns:
            ActivityLog: ActivityLog object or None if not found
        """
        query = """
        SELECT * FROM activity_logs
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (log_id,))
        return ActivityLog.from_db_row(rows[0]) if rows else None

    def delete_old_logs(self, days=90):
        """Delete logs older than the specified number of days.

        Args:
            days (int, optional): Number of days to keep logs. Defaults to 90.

        Returns:
            int: Number of logs deleted
        """
        query = """
        DELETE FROM activity_logs
        WHERE date(created_at) < date('now', ?)
        """
        return self.db_manager.execute_update(query, (f"-{days} days",))

    def log_activity(self, user_id, username, activity_type, entity_type, 
                    entity_id=None, description=None, details=None):
        """Convenience method to log an activity.

        Args:
            user_id (int): User ID who performed the activity
            username (str): Username who performed the activity
            activity_type (str): Type of activity (login, create, update, etc)
            entity_type (str): Type of entity (user, product, invoice, etc)
            entity_id (int, optional): ID of the entity. Defaults to None.
            description (str, optional): Brief description of the activity. Defaults to None.
            details (dict, optional): Additional details as dictionary. Defaults to None.

        Returns:
            int: ID of the new activity log entry
        """
        # Convert details to JSON string if it's a dictionary
        details_str = None
        if details:
            if isinstance(details, dict):
                details_str = json.dumps(details)
            else:
                details_str = str(details)

        # Create activity log object
        activity_log = ActivityLog(
            user_id=user_id,
            username=username,
            activity_type=activity_type,
            entity_type=entity_type,
            entity_id=entity_id,
            description=description,
            details=details_str
        )

        # Add log to database
        return self.add_log(activity_log)

    def _get_ip_address(self):
        """Get the local IP address.

        Returns:
            str: IP address or 'unknown'
        """
        try:
            # Get hostname
            hostname = socket.gethostname()
            # Get IP address
            ip_address = socket.gethostbyname(hostname)
            return ip_address
        except:
            return 'unknown'

# Global instance
_activity_log_manager = None

def get_activity_log_manager(db_manager=None):
    """Get the global activity log manager instance.

    Args:
        db_manager: Database manager instance

    Returns:
        ActivityLogManager: Activity log manager instance
    """
    global _activity_log_manager
    if _activity_log_manager is None and db_manager is not None:
        _activity_log_manager = ActivityLogManager(db_manager)
    return _activity_log_manager
