#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Product Model for فوترها (Fawterha)
Represents a product or service in the system
"""

class Product:
    """Product model class."""

    def __init__(self, id=None, name="", description="", price=0.0,
                 tax_rate=0.0, type="product", stock_quantity=0, min_stock_level=0,
                 track_inventory=False, created_at=None, updated_at=None,
                 expiry_date=None, barcode=None, qr_code=None, warehouse_id=1,
                 category_id=None, category_name=None):
        """Initialize a product object.

        Args:
            id (int, optional): Product ID. Defaults to None.
            name (str, optional): Product name. Defaults to "".
            description (str, optional): Product description. Defaults to "".
            price (float, optional): Product price. Defaults to 0.0.
            tax_rate (float, optional): Product tax rate. Defaults to 0.0.
            type (str, optional): Product type (product or service). Defaults to "product".
            stock_quantity (int, optional): Current stock quantity. Defaults to 0.
            min_stock_level (int, optional): Minimum stock level. Defaults to 0.
            track_inventory (bool, optional): Whether to track inventory for this product. Defaults to False.
            created_at (str, optional): Creation timestamp. Defaults to None.
            updated_at (str, optional): Update timestamp. Defaults to None.
            expiry_date (str, optional): Expiry date for the product. Defaults to None.
            barcode (str, optional): Barcode for the product. Defaults to None.
            qr_code (str, optional): QR code for the product. Defaults to None.
            warehouse_id (int, optional): Default warehouse ID. Defaults to 1.
            category_id (int, optional): Category ID for the product. Defaults to None.
            category_name (str, optional): Category name for the product. Defaults to None.
        """
        self.id = id
        self.name = name
        self.description = description

        # Ensure price is a float
        try:
            self.price = float(price)
        except (ValueError, TypeError):
            self.price = 0.0

        # Ensure tax_rate is a float
        try:
            self.tax_rate = float(tax_rate)
        except (ValueError, TypeError):
            self.tax_rate = 0.0

        self.type = type

        # Ensure inventory values are integers
        try:
            self.stock_quantity = int(stock_quantity)
        except (ValueError, TypeError):
            self.stock_quantity = 0

        try:
            self.min_stock_level = int(min_stock_level)
        except (ValueError, TypeError):
            self.min_stock_level = 0

        # Ensure track_inventory is boolean
        if isinstance(track_inventory, bool):
            self.track_inventory = track_inventory
        elif isinstance(track_inventory, int):
            self.track_inventory = track_inventory != 0
        else:
            self.track_inventory = bool(track_inventory)

        self.created_at = created_at
        self.updated_at = updated_at
        self.expiry_date = expiry_date
        self.barcode = barcode
        self.qr_code = qr_code
        self.warehouse_id = warehouse_id
        self.category_id = category_id
        self.category_name = category_name

    @classmethod
    def from_db_row(cls, row):
        """Create a Product object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            Product: Product object
        """
        # Convert row to dict for easier access
        row_dict = dict(row)

        # Ensure price is a valid float
        try:
            price = float(row_dict.get('price', 0.0))
        except (ValueError, TypeError):
            print(f"Warning: Invalid price value in database: {row_dict.get('price')}, defaulting to 0.0")
            price = 0.0

        # Ensure tax_rate is a valid float
        try:
            tax_rate = float(row_dict.get('tax_rate', 0.0))
        except (ValueError, TypeError):
            print(f"Warning: Invalid tax_rate value in database: {row_dict.get('tax_rate')}, defaulting to 0.0")
            tax_rate = 0.0

        # Create the product object with all fields from the row
        product = cls(
            id=row_dict.get('id'),
            name=row_dict.get('name', ''),
            description=row_dict.get('description', ''),
            price=price,
            tax_rate=tax_rate,
            type=row_dict.get('type', 'product'),
            stock_quantity=row_dict.get('stock_quantity', 0),
            min_stock_level=row_dict.get('min_stock_level', 0),
            track_inventory=row_dict.get('track_inventory', 0),
            created_at=row_dict.get('created_at'),
            updated_at=row_dict.get('updated_at'),
            expiry_date=row_dict.get('expiry_date'),
            barcode=row_dict.get('barcode'),
            qr_code=row_dict.get('qr_code'),
            warehouse_id=row_dict.get('warehouse_id', 1),
            category_id=row_dict.get('category_id'),
            category_name=row_dict.get('category_name')
        )

        # Print debug info
        print(f"DEBUG - Product from DB: {product.name}, stock={product.stock_quantity}, min={product.min_stock_level}, track={product.track_inventory}, price={product.price}")

        return product

    def to_dict(self):
        """Convert the product object to a dictionary.

        Returns:
            dict: Dictionary representation of the product
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'price': self.price,
            'tax_rate': self.tax_rate,
            'type': self.type,
            'stock_quantity': self.stock_quantity,
            'min_stock_level': self.min_stock_level,
            'track_inventory': self.track_inventory,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'expiry_date': self.expiry_date,
            'barcode': self.barcode,
            'qr_code': self.qr_code,
            'warehouse_id': self.warehouse_id,
            'category_id': self.category_id,
            'category_name': self.category_name
        }

    def __str__(self):
        """Return a string representation of the product.

        Returns:
            str: String representation
        """
        return f"{self.name} ({self.price})"
