#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Warehouse Inventory Model for فوترها (Fawterha)
Represents inventory of a product in a specific warehouse
"""

from datetime import datetime

class WarehouseInventory:
    """Warehouse inventory model class."""

    def __init__(self, id=None, product_id=None, warehouse_id=None,
                 stock_quantity=0, min_stock_level=0,
                 created_at=None, updated_at=None):
        """Initialize a warehouse inventory object.

        Args:
            id (int, optional): Warehouse inventory ID. Defaults to None.
            product_id (int, optional): Product ID. Defaults to None.
            warehouse_id (int, optional): Warehouse ID. Defaults to None.
            stock_quantity (int, optional): Stock quantity. Defaults to 0.
            min_stock_level (int, optional): Minimum stock level. Defaults to 0.
            created_at (datetime, optional): Creation timestamp. Defaults to None.
            updated_at (datetime, optional): Update timestamp. Defaults to None.
        """
        self.id = id
        self.product_id = product_id
        self.warehouse_id = warehouse_id
        
        # Ensure stock_quantity is an integer
        try:
            self.stock_quantity = int(stock_quantity)
        except (ValueError, TypeError):
            self.stock_quantity = 0
            
        # Ensure min_stock_level is an integer
        try:
            self.min_stock_level = int(min_stock_level)
        except (ValueError, TypeError):
            self.min_stock_level = 0
            
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create a WarehouseInventory object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            WarehouseInventory: WarehouseInventory object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)

        # Create the warehouse inventory object with all fields from the row
        warehouse_inventory = cls(
            id=row_dict.get('id'),
            product_id=row_dict.get('product_id'),
            warehouse_id=row_dict.get('warehouse_id'),
            stock_quantity=row_dict.get('stock_quantity', 0),
            min_stock_level=row_dict.get('min_stock_level', 0),
            created_at=row_dict.get('created_at'),
            updated_at=row_dict.get('updated_at')
        )

        return warehouse_inventory

    def to_dict(self):
        """Convert the warehouse inventory object to a dictionary.

        Returns:
            dict: Dictionary representation of the warehouse inventory
        """
        return {
            'id': self.id,
            'product_id': self.product_id,
            'warehouse_id': self.warehouse_id,
            'stock_quantity': self.stock_quantity,
            'min_stock_level': self.min_stock_level,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def is_low_stock(self):
        """Check if the stock is below the minimum level.

        Returns:
            bool: True if stock is below minimum level, False otherwise
        """
        return self.stock_quantity <= self.min_stock_level
