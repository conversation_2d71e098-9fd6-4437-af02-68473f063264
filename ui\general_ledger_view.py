#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
General Ledger View for فوترها (Fawterha)
Displays the general ledger and allows for transaction management
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDateEdit, QLineEdit, QMessageBox, QDialog, QFormLayout,
    QTabWidget, QSplitter, QFrame, QGroupBox, QRadioButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QMenu, QToolBar,
    QSizePolicy, QScrollArea
)
from PySide6.QtCore import Qt, QDate, Signal, QSize
from PySide6.QtGui import QIcon, QColor, QFont, QAction

from datetime import datetime, timedelta
import os

from database.account_manager import AccountManager
from database.transaction_manager import TransactionManager
from database.accounting_period_manager import AccountingPeriodManager
from models.transaction import Transaction
from models.transaction_detail import TransactionDetail
from models.account import Account
from ui.transaction_dialog import TransactionDialog
from utils.currency_helper import format_currency
from utils.translation_manager import tr


class GeneralLedgerView(QWidget):
    """General ledger view widget."""

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the general ledger view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager

        # Initialize managers
        self.account_manager = AccountManager(db_manager)
        self.transaction_manager = TransactionManager(db_manager, self.account_manager)
        self.period_manager = AccountingPeriodManager(db_manager)

        # Set up UI
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Title
        title_label = QLabel(tr("accounting.general_ledger", "دفتر الأستاذ العام"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Toolbar
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        main_layout.addWidget(toolbar)

        # Add transaction button
        self.add_transaction_action = QAction(QIcon("resources/icons/add.png"), tr("accounting.add_transaction", "إضافة معاملة"), self)
        self.add_transaction_action.triggered.connect(self.add_transaction)
        toolbar.addAction(self.add_transaction_action)

        # Filter section
        filter_layout = QHBoxLayout()
        main_layout.addLayout(filter_layout)

        # Date range
        filter_layout.addWidget(QLabel(tr("accounting.date_range", "نطاق التاريخ:")))

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        filter_layout.addWidget(self.start_date_edit)

        filter_layout.addWidget(QLabel(tr("accounting.to", "إلى")))

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        filter_layout.addWidget(self.end_date_edit)

        # Account filter
        filter_layout.addWidget(QLabel(tr("accounting.account", "الحساب:")))

        self.account_combo = QComboBox()
        self.account_combo.setMinimumWidth(200)
        filter_layout.addWidget(self.account_combo)

        # Status filter
        filter_layout.addWidget(QLabel(tr("accounting.status", "الحالة:")))

        self.status_combo = QComboBox()
        self.status_combo.addItem(tr("accounting.all_statuses", "جميع الحالات"), "all")
        self.status_combo.addItem(tr("accounting.posted", "مرحّل"), Transaction.STATUS_POSTED)
        self.status_combo.addItem(tr("accounting.draft", "مسودة"), Transaction.STATUS_DRAFT)
        self.status_combo.addItem(tr("accounting.voided", "ملغي"), Transaction.STATUS_VOIDED)
        filter_layout.addWidget(self.status_combo)

        # Apply filter button
        self.apply_filter_button = QPushButton(tr("accounting.apply_filter", "تطبيق الفلتر"))
        self.apply_filter_button.clicked.connect(self.load_data)
        filter_layout.addWidget(self.apply_filter_button)

        # Splitter for tables
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter, 1)

        # Transactions table
        transactions_widget = QWidget()
        transactions_layout = QVBoxLayout(transactions_widget)
        transactions_layout.setContentsMargins(0, 0, 0, 0)

        transactions_label = QLabel(tr("accounting.transactions", "المعاملات"))
        transactions_label.setStyleSheet("font-weight: bold;")
        transactions_layout.addWidget(transactions_label)

        self.transactions_table = QTableWidget()
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transactions_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.verticalHeader().setVisible(False)
        self.transactions_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels([
            tr("accounting.date", "التاريخ"),
            tr("accounting.number", "الرقم"),
            tr("accounting.description", "الوصف"),
            tr("accounting.reference", "المرجع"),
            tr("accounting.amount", "المبلغ"),
            tr("accounting.status", "الحالة"),
            tr("accounting.actions", "الإجراءات")
        ])
        self.transactions_table.clicked.connect(self.on_transaction_clicked)
        transactions_layout.addWidget(self.transactions_table)

        splitter.addWidget(transactions_widget)

        # Transaction details table
        details_widget = QWidget()
        details_layout = QVBoxLayout(details_widget)
        details_layout.setContentsMargins(0, 0, 0, 0)

        self.details_label = QLabel(tr("accounting.transaction_details", "تفاصيل المعاملة"))
        self.details_label.setStyleSheet("font-weight: bold;")
        details_layout.addWidget(self.details_label)

        self.details_table = QTableWidget()
        self.details_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.details_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.details_table.setAlternatingRowColors(True)
        self.details_table.verticalHeader().setVisible(False)
        self.details_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.details_table.setColumnCount(4)
        self.details_table.setHorizontalHeaderLabels([
            tr("accounting.account", "الحساب"),
            tr("accounting.description", "الوصف"),
            tr("accounting.debit", "مدين"),
            tr("accounting.credit", "دائن")
        ])
        details_layout.addWidget(self.details_table)

        splitter.addWidget(details_widget)

        # Set splitter sizes
        splitter.setSizes([int(self.height() * 0.6), int(self.height() * 0.4)])

    def load_data(self):
        """Load data into the view."""
        self.load_accounts()
        self.load_transactions()
        self.clear_details()

    def load_accounts(self):
        """Load accounts into the account combo box."""
        self.account_combo.clear()
        self.account_combo.addItem(tr("accounting.all_accounts", "جميع الحسابات"), "all")

        accounts = self.account_manager.get_all_accounts()
        for account in accounts:
            self.account_combo.addItem(f"{account.code} - {account.name}", account.id)

    def load_transactions(self):
        """Load transactions into the transactions table."""
        # Clear the table
        self.transactions_table.setRowCount(0)

        # Get filter values
        start_date = self.start_date_edit.date().toPython()
        end_date = self.end_date_edit.date().toPython()
        account_id = self.account_combo.currentData()
        status = self.status_combo.currentData()

        # Get transactions
        transactions = self.transaction_manager.get_all_transactions(limit=100)

        # Filter transactions
        filtered_transactions = []
        for transaction in transactions:
            # Filter by date
            if transaction.transaction_date < start_date or transaction.transaction_date > end_date:
                continue

            # Filter by status
            if status != "all" and transaction.status != status:
                continue

            # Filter by account
            if account_id != "all":
                account_found = False
                for detail in transaction.details:
                    if detail.account_id == account_id:
                        account_found = True
                        break
                if not account_found:
                    continue

            filtered_transactions.append(transaction)

        # Populate the table
        self.transactions_table.setRowCount(len(filtered_transactions))
        for row, transaction in enumerate(filtered_transactions):
            # Date
            date_item = QTableWidgetItem(transaction.transaction_date.strftime("%Y-%m-%d"))
            self.transactions_table.setItem(row, 0, date_item)

            # Number
            number_item = QTableWidgetItem(transaction.transaction_number)
            self.transactions_table.setItem(row, 1, number_item)

            # Description
            description_item = QTableWidgetItem(transaction.description)
            self.transactions_table.setItem(row, 2, description_item)

            # Reference
            reference = ""
            if transaction.reference_type and transaction.reference_id:
                reference = f"{transaction.reference_type} #{transaction.reference_id}"
            reference_item = QTableWidgetItem(reference)
            self.transactions_table.setItem(row, 3, reference_item)

            # Amount
            amount_item = QTableWidgetItem(format_currency(transaction.amount))
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.transactions_table.setItem(row, 4, amount_item)

            # Status
            status_text = ""
            if transaction.status == Transaction.STATUS_POSTED:
                status_text = tr("accounting.posted", "مرحّل")
            elif transaction.status == Transaction.STATUS_DRAFT:
                status_text = tr("accounting.draft", "مسودة")
            elif transaction.status == Transaction.STATUS_VOIDED:
                status_text = tr("accounting.voided", "ملغي")
            status_item = QTableWidgetItem(status_text)
            self.transactions_table.setItem(row, 5, status_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)

            view_button = QPushButton(tr("common.view", "عرض"))
            view_button.setProperty("transaction_id", transaction.id)
            view_button.clicked.connect(self.view_transaction)
            actions_layout.addWidget(view_button)

            if transaction.status == Transaction.STATUS_DRAFT:
                edit_button = QPushButton(tr("common.edit", "تعديل"))
                edit_button.setProperty("transaction_id", transaction.id)
                edit_button.clicked.connect(self.edit_transaction)
                actions_layout.addWidget(edit_button)

                post_button = QPushButton(tr("accounting.post", "ترحيل"))
                post_button.setProperty("transaction_id", transaction.id)
                post_button.clicked.connect(self.post_transaction)
                actions_layout.addWidget(post_button)

            if transaction.status == Transaction.STATUS_POSTED:
                void_button = QPushButton(tr("accounting.void", "إلغاء"))
                void_button.setProperty("transaction_id", transaction.id)
                void_button.clicked.connect(self.void_transaction)
                actions_layout.addWidget(void_button)

            self.transactions_table.setCellWidget(row, 6, actions_widget)

        # Resize columns to content
        self.transactions_table.resizeColumnsToContents()

    def load_transaction_details(self, transaction_id):
        """Load transaction details into the details table.

        Args:
            transaction_id (int): Transaction ID
        """
        # Get the transaction
        transaction = self.transaction_manager.get_transaction_by_id(transaction_id)
        if not transaction:
            self.clear_details()
            return

        # Update details label
        self.details_label.setText(f"{tr('accounting.transaction_details', 'تفاصيل المعاملة')}: {transaction.transaction_number}")

        # Clear the table
        self.details_table.setRowCount(0)

        # Populate the table
        self.details_table.setRowCount(len(transaction.details))
        for row, detail in enumerate(transaction.details):
            # Get the account
            account = self.account_manager.get_account_by_id(detail.account_id)

            # Account
            account_text = f"{account.code} - {account.name}" if account else ""
            account_item = QTableWidgetItem(account_text)
            self.details_table.setItem(row, 0, account_item)

            # Description
            description_item = QTableWidgetItem(detail.description)
            self.details_table.setItem(row, 1, description_item)

            # Debit
            debit_item = QTableWidgetItem(format_currency(detail.debit) if detail.debit > 0 else "")
            debit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.details_table.setItem(row, 2, debit_item)

            # Credit
            credit_item = QTableWidgetItem(format_currency(detail.credit) if detail.credit > 0 else "")
            credit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.details_table.setItem(row, 3, credit_item)

        # Add totals row
        total_row = self.details_table.rowCount()
        self.details_table.insertRow(total_row)

        # Totals label
        totals_item = QTableWidgetItem(tr("accounting.totals", "الإجمالي"))
        totals_item.setFont(QFont("", -1, QFont.Bold))
        self.details_table.setItem(total_row, 1, totals_item)

        # Calculate totals
        total_debit = sum(detail.debit for detail in transaction.details)
        total_credit = sum(detail.credit for detail in transaction.details)

        # Total debit
        total_debit_item = QTableWidgetItem(format_currency(total_debit))
        total_debit_item.setFont(QFont("", -1, QFont.Bold))
        total_debit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.details_table.setItem(total_row, 2, total_debit_item)

        # Total credit
        total_credit_item = QTableWidgetItem(format_currency(total_credit))
        total_credit_item.setFont(QFont("", -1, QFont.Bold))
        total_credit_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.details_table.setItem(total_row, 3, total_credit_item)

        # Resize columns to content
        self.details_table.resizeColumnsToContents()

    def clear_details(self):
        """Clear the details table."""
        self.details_label.setText(tr("accounting.transaction_details", "تفاصيل المعاملة"))
        self.details_table.setRowCount(0)

    def on_transaction_clicked(self, index):
        """Handle transaction table click.

        Args:
            index: Index of the clicked item
        """
        if index.isValid():
            row = index.row()
            transaction_number = self.transactions_table.item(row, 1).text()
            transaction = self.transaction_manager.get_transaction_by_number(transaction_number)
            if transaction:
                self.load_transaction_details(transaction.id)

    def add_transaction(self):
        """Add a new transaction."""
        dialog = TransactionDialog(self.db_manager, self.account_manager, self.transaction_manager, self.currency_manager)
        if dialog.exec() == QDialog.Accepted:
            self.load_data()

    def view_transaction(self):
        """View a transaction."""
        button = self.sender()
        if button:
            transaction_id = button.property("transaction_id")
            dialog = TransactionDialog(self.db_manager, self.account_manager, self.transaction_manager, self.currency_manager, transaction_id=transaction_id, readonly=True)
            dialog.exec()

    def edit_transaction(self):
        """Edit a transaction."""
        button = self.sender()
        if button:
            transaction_id = button.property("transaction_id")
            dialog = TransactionDialog(self.db_manager, self.account_manager, self.transaction_manager, self.currency_manager, transaction_id=transaction_id)
            if dialog.exec() == QDialog.Accepted:
                self.load_data()

    def post_transaction(self):
        """Post a transaction."""
        button = self.sender()
        if button:
            transaction_id = button.property("transaction_id")

            # Confirm posting
            confirm = QMessageBox.question(
                self,
                tr("accounting.confirm_post", "تأكيد الترحيل"),
                tr("accounting.confirm_post_message", "هل أنت متأكد من ترحيل هذه المعاملة؟"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirm == QMessageBox.Yes:
                try:
                    self.transaction_manager.update_transaction_status(transaction_id, Transaction.STATUS_POSTED)
                    self.load_data()
                    QMessageBox.information(
                        self,
                        tr("accounting.post_success", "تم الترحيل"),
                        tr("accounting.post_success_message", "تم ترحيل المعاملة بنجاح")
                    )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        tr("accounting.post_error", "خطأ في الترحيل"),
                        f"{tr('accounting.post_error_message', 'حدث خطأ أثناء ترحيل المعاملة')}: {str(e)}"
                    )

    def void_transaction(self):
        """Void a transaction."""
        button = self.sender()
        if button:
            transaction_id = button.property("transaction_id")

            # Confirm voiding
            confirm = QMessageBox.question(
                self,
                tr("accounting.confirm_void", "تأكيد الإلغاء"),
                tr("accounting.confirm_void_message", "هل أنت متأكد من إلغاء هذه المعاملة؟ سيتم عكس تأثيرها على أرصدة الحسابات."),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirm == QMessageBox.Yes:
                try:
                    self.transaction_manager.void_transaction(transaction_id)
                    self.load_data()
                    QMessageBox.information(
                        self,
                        tr("accounting.void_success", "تم الإلغاء"),
                        tr("accounting.void_success_message", "تم إلغاء المعاملة بنجاح")
                    )
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        tr("accounting.void_error", "خطأ في الإلغاء"),
                        f"{tr('accounting.void_error_message', 'حدث خطأ أثناء إلغاء المعاملة')}: {str(e)}"
                    )
