/* Main application stylesheet for فوترها (Fawterha) */

/* Global styles */
QWidget {
    font-family: 'Segoe UI', 'Arial', sans-serif;
    font-size: 11pt;
    color: #333333;
}

QMainWindow {
    background-color: #f8f9fa;
}

/* Buttons */
QPushButton {
    background-color: #1e88e5;
    color: white;
    border: none;
    padding: 6px 16px;
    border-radius: 4px;
    min-height: 28px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #1976d2;
}

QPushButton:pressed {
    background-color: #0d47a1;
}

QPushButton:disabled {
    background-color: #e0e0e0;
    color: #9e9e9e;
}

/* Text inputs */
QLineEdit, QTextEdit, QSpinBox, QDoubleSpinBox, QDateEdit, QComboBox {
    border: 1px solid #bdbdbd;
    border-radius: 4px;
    padding: 5px;
    background-color: white;
    selection-background-color: #1e88e5;
    selection-color: white;
    color: #212121;
}

QLineEdit:focus, QTextEdit:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus, QComboBox:focus {
    border: 2px solid #1e88e5;
}

/* Labels */
QLabel {
    color: #212121;
}

/* Tables */
QTableWidget {
    border: 1px solid #bdbdbd;
    gridline-color: #e0e0e0;
    selection-background-color: #e3f2fd;
    selection-color: #212121;
    alternate-background-color: #f5f5f5;
}

QTableWidget::item {
    padding: 6px;
    border-bottom: 1px solid #f0f0f0;
}

QHeaderView::section {
    background-color: #0d47a1;
    color: white;
    padding: 8px;
    border: none;
    font-weight: bold;
}

/* Tabs */
QTabWidget::pane {
    border: 1px solid #bdbdbd;
    background-color: white;
    border-radius: 4px;
}

QTabBar::tab {
    background-color: #f5f5f5;
    color: #616161;
    border: 1px solid #bdbdbd;
    border-bottom-color: #bdbdbd;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 8px 16px;
    margin-right: 2px;
    font-weight: bold;
}

QTabBar::tab:selected {
    background-color: #1e88e5;
    color: white;
    border-bottom-color: #1e88e5;
}

QTabBar::tab:!selected {
    margin-top: 2px;
}

/* Group boxes */
QGroupBox {
    border: 1px solid #bdbdbd;
    border-radius: 6px;
    margin-top: 24px;
    font-weight: bold;
    color: #0d47a1;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 8px;
    background-color: #ffffff;
}

/* Status bar */
QStatusBar {
    background-color: #0d47a1;
    color: white;
    font-weight: bold;
    padding: 3px;
}

/* Toolbar */
QToolBar {
    background-color: #1e88e5;
    border-bottom: 1px solid #1976d2;
    spacing: 6px;
    padding: 4px;
}

QToolBar QToolButton {
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 5px;
    color: white;
}

QToolBar QToolButton:hover {
    background-color: #1976d2;
    border: 1px solid #1565c0;
}

QToolBar QToolButton:pressed {
    background-color: #0d47a1;
}

/* Scrollbars */
QScrollBar:vertical {
    border: none;
    background-color: #f5f5f5;
    width: 12px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #90caf9;
    min-height: 20px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background-color: #42a5f5;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background-color: #f5f5f5;
    height: 12px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #90caf9;
    min-width: 20px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #42a5f5;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* Invoice status colors */
QTableWidget QTableWidgetItem[status="draft"] {
    background-color: #eceff1;
    color: #546e7a;
}

QTableWidget QTableWidgetItem[status="pending"] {
    background-color: #fff8e1;
    color: #ff8f00;
}

QTableWidget QTableWidgetItem[status="paid"] {
    background-color: #e8f5e9;
    color: #2e7d32;
}

QTableWidget QTableWidgetItem[status="cancelled"] {
    background-color: #ffebee;
    color: #c62828;
}

/* Dialog buttons */
QDialogButtonBox QPushButton {
    min-width: 100px;
}

/* Form layout spacing */
QFormLayout {
    spacing: 12px;
}

/* Tooltips */
QToolTip {
    background-color: #424242;
    color: white;
    border: 1px solid #212121;
    padding: 5px;
    border-radius: 3px;
    opacity: 200;
}

/* Message boxes and dialogs */
QMessageBox, QDialog {
    background-color: #ffffff;
}

QMessageBox QLabel, QDialog QLabel {
    color: #212121;
    font-size: 12pt;
    font-weight: normal;
}

QMessageBox QPushButton, QDialog QPushButton {
    background-color: #1e88e5;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: bold;
    min-width: 100px;
    min-height: 30px;
}

QMessageBox QPushButton:hover, QDialog QPushButton:hover {
    background-color: #1976d2;
}

QMessageBox QPushButton:pressed, QDialog QPushButton:pressed {
    background-color: #0d47a1;
}

/* Dialog title bar */
QDialog QLabel#qt_msgbox_label {
    font-size: 13pt;
    font-weight: bold;
    color: #0d47a1;
}

/* About dialog and other information dialogs */
QMessageBox#aboutDialog {
    background-color: #f8f9fa;
}

QMessageBox#aboutDialog QLabel {
    color: #212121;
    font-size: 12pt;
}

QMessageBox#aboutDialog QLabel#qt_msgbox_label {
    font-size: 16pt;
    font-weight: bold;
    color: #0d47a1;
}

/* Warning and error dialogs */
QMessageBox#warningDialog, QMessageBox#errorDialog {
    background-color: #fff8e1;
}

QMessageBox#warningDialog QLabel, QMessageBox#errorDialog QLabel {
    color: #212121;
    font-size: 12pt;
    font-weight: bold;
}

QMessageBox#errorDialog {
    background-color: #ffebee;
}

QMessageBox#errorDialog QLabel {
    color: #b71c1c;
}
