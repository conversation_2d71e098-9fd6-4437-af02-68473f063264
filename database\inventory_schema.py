#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Schema for فوترها (Fawterha)
Defines the database tables for the enhanced inventory system
"""

def create_inventory_tables(connection):
    """Create inventory database tables if they don't exist.

    Args:
        connection: SQLite database connection
    """
    cursor = connection.cursor()

    # Add new columns to products table if they don't exist
    cursor.execute("PRAGMA table_info(products)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]

    # Add expiry_date column if it doesn't exist
    if 'expiry_date' not in column_names:
        cursor.execute('''
        ALTER TABLE products ADD COLUMN expiry_date DATE
        ''')

    # Add barcode column if it doesn't exist
    if 'barcode' not in column_names:
        cursor.execute('''
        ALTER TABLE products ADD COLUMN barcode TEXT
        ''')

    # Add qr_code column if it doesn't exist
    if 'qr_code' not in column_names:
        cursor.execute('''
        ALTER TABLE products ADD COLUMN qr_code TEXT
        ''')

    # Add warehouse_id column if it doesn't exist
    if 'warehouse_id' not in column_names:
        cursor.execute('''
        ALTER TABLE products ADD COLUMN warehouse_id INTEGER DEFAULT 1
        ''')

    # Create warehouses table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS warehouses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        location TEXT,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create warehouse_inventory table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS warehouse_inventory (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        warehouse_id INTEGER NOT NULL,
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE CASCADE,
        UNIQUE(product_id, warehouse_id)
    )
    ''')

    # Create inventory_alerts table if it doesn't exist
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS inventory_alerts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        warehouse_id INTEGER,
        alert_type TEXT NOT NULL,
        message TEXT NOT NULL,
        is_read INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE,
        FOREIGN KEY (warehouse_id) REFERENCES warehouses (id) ON DELETE SET NULL
    )
    ''')

    # Update inventory_transactions table to include warehouse_id if it doesn't exist
    cursor.execute("PRAGMA table_info(inventory_transactions)")
    columns = cursor.fetchall()
    column_names = [column[1] for column in columns]

    if 'warehouse_id' not in column_names:
        cursor.execute('''
        ALTER TABLE inventory_transactions ADD COLUMN warehouse_id INTEGER DEFAULT 1
        ''')

    # Insert default warehouse if it doesn't exist
    cursor.execute('''
    SELECT COUNT(*) FROM warehouses
    ''')
    warehouse_count = cursor.fetchone()[0]

    if warehouse_count == 0:
        cursor.execute('''
        INSERT INTO warehouses (name, location, description)
        VALUES ('المستودع الرئيسي', 'الموقع الرئيسي', 'المستودع الافتراضي')
        ''')

    # Commit the changes
    connection.commit()
