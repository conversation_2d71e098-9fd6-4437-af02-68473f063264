#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Manager for فوترها (Fawterha)
Handles database operations for the Point of Sale system
"""

import sqlite3
from datetime import datetime

from models.user import User
from models.pos_session import POSSession
from models.pos_transaction import POSTransaction
from models.invoice import Invoice
from models.product import Product
from models.pos_inventory import POSInventory
from models.pos_inventory_transaction import POSInventoryTransaction

class POSManager:
    """Manager for POS database operations."""

    def __init__(self, db_manager):
        """Initialize the POS manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    # User Management Methods

    def get_user_by_username(self, username):
        """Get a user by username.

        Args:
            username (str): Username

        Returns:
            User: User object or None if not found
        """
        query = """
        SELECT * FROM users
        WHERE username = ?
        """
        rows = self.db_manager.execute_query(query, (username,))
        return User.from_db_row(rows[0]) if rows else None

    def get_user_by_id(self, user_id):
        """Get a user by ID.

        Args:
            user_id (int): User ID

        Returns:
            User: User object or None if not found
        """
        query = """
        SELECT * FROM users
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (user_id,))
        return User.from_db_row(rows[0]) if rows else None

    def get_all_users(self):
        """Get all users.

        Returns:
            list: List of User objects
        """
        query = """
        SELECT * FROM users
        ORDER BY name
        """
        rows = self.db_manager.execute_query(query)
        return [User.from_db_row(row) for row in rows]

    def add_user(self, user):
        """Add a new user.

        Args:
            user (User): User object to add

        Returns:
            int: ID of the new user
        """
        query = """
        INSERT INTO users (username, password, name, role, is_active)
        VALUES (?, ?, ?, ?, ?)
        """
        params = (
            user.username,
            user.password,
            user.name,
            user.role,
            1 if user.is_active else 0
        )
        return self.db_manager.execute_insert(query, params)

    def update_user(self, user):
        """Update an existing user.

        Args:
            user (User): User object to update

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE users
        SET username = ?, name = ?, role = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            user.username,
            user.name,
            user.role,
            1 if user.is_active else 0,
            user.id
        )
        return self.db_manager.execute_update(query, params) > 0

    def update_user_password(self, user_id, new_password):
        """Update a user's password.

        Args:
            user_id (int): User ID
            new_password (str): New password

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE users
        SET password = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (new_password, user_id)
        return self.db_manager.execute_update(query, params) > 0



    def delete_user(self, user_id):
        """Delete a user.

        Args:
            user_id (int): User ID

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        DELETE FROM users
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (user_id,)) > 0

    def authenticate_user(self, username, password):
        """Authenticate a user.

        Args:
            username (str): Username
            password (str): Password

        Returns:
            User: User object if authentication is successful, None otherwise
        """
        query = """
        SELECT * FROM users
        WHERE username = ? AND password = ? AND is_active = 1
        """
        rows = self.db_manager.execute_query(query, (username, password))
        return User.from_db_row(rows[0]) if rows else None

    # Session Management Methods

    def get_session_by_id(self, session_id):
        """Get a session by ID.

        Args:
            session_id (int): Session ID

        Returns:
            POSSession: POSSession object or None if not found
        """
        query = """
        SELECT * FROM pos_sessions
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (session_id,))
        return POSSession.from_db_row(rows[0]) if rows else None

    def get_open_session_by_user(self, user_id):
        """Get an open session for a user.

        Args:
            user_id (int): User ID

        Returns:
            POSSession: POSSession object or None if not found
        """
        query = """
        SELECT * FROM pos_sessions
        WHERE user_id = ? AND status = ?
        ORDER BY start_time DESC
        LIMIT 1
        """
        rows = self.db_manager.execute_query(query, (user_id, POSSession.STATUS_OPEN))
        return POSSession.from_db_row(rows[0]) if rows else None

    def get_all_sessions(self, status=None):
        """Get all sessions.

        Args:
            status (str, optional): Filter by status. Defaults to None.

        Returns:
            list: List of POSSession objects
        """
        query = """
        SELECT s.*, u.name as user_name
        FROM pos_sessions s
        JOIN users u ON s.user_id = u.id
        """

        params = []
        if status:
            query += " WHERE s.status = ?"
            params.append(status)

        query += " ORDER BY s.start_time DESC"

        rows = self.db_manager.execute_query(query, tuple(params))
        sessions = []

        for row in rows:
            session = POSSession.from_db_row(row)
            # Add user name
            if 'user_name' in row:
                user = User(id=session.user_id, name=row['user_name'])
                session.user = user
            sessions.append(session)

        return sessions

    def open_session(self, session):
        """Open a new session.

        Args:
            session (POSSession): Session object to open

        Returns:
            int: ID of the new session
        """
        # Check if user already has an open session
        open_session = self.get_open_session_by_user(session.user_id)
        if open_session:
            return -1  # User already has an open session

        query = """
        INSERT INTO pos_sessions (user_id, starting_cash, status, notes)
        VALUES (?, ?, ?, ?)
        """
        params = (
            session.user_id,
            session.starting_cash,
            POSSession.STATUS_OPEN,
            session.notes
        )
        return self.db_manager.execute_insert(query, params)

    def close_session(self, session):
        """Close a session.

        Args:
            session (POSSession): Session object to close

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE pos_sessions
        SET end_time = CURRENT_TIMESTAMP, ending_cash = ?, status = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            session.ending_cash,
            POSSession.STATUS_CLOSED,
            session.notes,
            session.id
        )
        return self.db_manager.execute_update(query, params) > 0

    # Transaction Management Methods

    def get_transaction_by_id(self, transaction_id):
        """Get a transaction by ID.

        Args:
            transaction_id (int): Transaction ID

        Returns:
            POSTransaction: POSTransaction object or None if not found
        """
        query = """
        SELECT * FROM pos_transactions
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (transaction_id,))
        return POSTransaction.from_db_row(rows[0]) if rows else None

    def get_transactions_by_session(self, session_id):
        """Get transactions for a session.

        Args:
            session_id (int): Session ID

        Returns:
            list: List of POSTransaction objects
        """
        query = """
        SELECT * FROM pos_transactions
        WHERE session_id = ?
        ORDER BY created_at
        """
        rows = self.db_manager.execute_query(query, (session_id,))
        return [POSTransaction.from_db_row(row) for row in rows]

    def add_transaction(self, transaction):
        """Add a new transaction.

        Args:
            transaction (POSTransaction): Transaction object to add

        Returns:
            int: ID of the new transaction
        """
        query = """
        INSERT INTO pos_transactions (session_id, invoice_id, transaction_type, amount, payment_method, reference, notes)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            transaction.session_id,
            transaction.invoice_id,
            transaction.transaction_type,
            transaction.amount,
            transaction.payment_method,
            transaction.reference,
            transaction.notes
        )
        return self.db_manager.execute_insert(query, params)

    def get_session_summary(self, session_id):
        """Get a summary of a session.

        Args:
            session_id (int): Session ID

        Returns:
            dict: Session summary
        """
        # Get session
        session = self.get_session_by_id(session_id)
        if not session:
            return None

        # Get transactions
        transactions = self.get_transactions_by_session(session_id)
        session.transactions = transactions

        # Calculate totals
        total_sales = 0.0
        total_cash = 0.0
        total_card = 0.0
        total_other = 0.0

        for transaction in transactions:
            if transaction.transaction_type == POSTransaction.TYPE_SALE:
                total_sales += transaction.amount

                if transaction.payment_method == POSTransaction.METHOD_CASH:
                    total_cash += transaction.amount
                elif transaction.payment_method == POSTransaction.METHOD_CARD:
                    total_card += transaction.amount
                else:
                    total_other += transaction.amount
            elif transaction.transaction_type == POSTransaction.TYPE_REFUND:
                total_sales -= transaction.amount

                if transaction.payment_method == POSTransaction.METHOD_CASH:
                    total_cash -= transaction.amount
                elif transaction.payment_method == POSTransaction.METHOD_CARD:
                    total_card -= transaction.amount
                else:
                    total_other -= transaction.amount
            elif transaction.transaction_type == POSTransaction.TYPE_CASH_IN:
                total_cash += transaction.amount
            elif transaction.transaction_type == POSTransaction.TYPE_CASH_OUT:
                total_cash -= transaction.amount

        session.total_sales = total_sales
        session.total_cash = total_cash
        session.total_card = total_card
        session.total_other = total_other

        return session

    # Settings Methods

    def get_setting(self, key, default=None):
        """Get a POS setting.

        Args:
            key (str): Setting key
            default: Default value if setting is not found

        Returns:
            str: Setting value
        """
        query = """
        SELECT value FROM pos_settings
        WHERE key = ?
        """
        rows = self.db_manager.execute_query(query, (key,))
        return rows[0]['value'] if rows else default

    def set_setting(self, key, value):
        """Set a POS setting.

        Args:
            key (str): Setting key
            value (str): Setting value

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        INSERT OR REPLACE INTO pos_settings (key, value, updated_at)
        VALUES (?, ?, CURRENT_TIMESTAMP)
        """
        return self.db_manager.execute_update(query, (key, value)) > 0

    # Payment Methods

    def get_payment_methods(self, active_only=True):
        """Get all payment methods.

        Args:
            active_only (bool, optional): Whether to get only active payment methods. Defaults to True.

        Returns:
            list: List of payment method dictionaries
        """
        query = """
        SELECT * FROM pos_payment_methods
        """

        if active_only:
            query += " WHERE is_active = 1"

        query += " ORDER BY name"

        rows = self.db_manager.execute_query(query)
        return rows

    # POS Inventory Management Methods

    def get_pos_inventory_item(self, inventory_id):
        """Get a POS inventory item by ID.

        Args:
            inventory_id (int): Inventory ID

        Returns:
            POSInventory: POSInventory object or None if not found
        """
        query = """
        SELECT pi.*, p.name as product_name, p.barcode, NULL as category_name
        FROM pos_inventory pi
        JOIN products p ON pi.product_id = p.id
        WHERE pi.id = ?
        """
        rows = self.db_manager.execute_query(query, (inventory_id,))
        return POSInventory.from_db_row(rows[0]) if rows else None

    def get_pos_inventory_by_product(self, product_id):
        """Get a POS inventory item by product ID.

        Args:
            product_id (int): Product ID

        Returns:
            POSInventory: POSInventory object or None if not found
        """
        query = """
        SELECT pi.*, p.name as product_name, p.barcode, NULL as category_name
        FROM pos_inventory pi
        JOIN products p ON pi.product_id = p.id
        WHERE pi.product_id = ?
        """
        rows = self.db_manager.execute_query(query, (product_id,))
        return POSInventory.from_db_row(rows[0]) if rows else None

    def get_all_pos_inventory(self, include_zero_stock=True, category_id=None):
        """Get all POS inventory items.

        Args:
            include_zero_stock (bool, optional): Whether to include items with zero stock. Defaults to True.
            category_id (int, optional): Filter by category ID. Defaults to None.

        Returns:
            list: List of POSInventory objects
        """
        query = """
        SELECT pi.*, p.name as product_name, p.barcode, NULL as category_name
        FROM pos_inventory pi
        JOIN products p ON pi.product_id = p.id
        WHERE 1=1
        """

        params = []

        if not include_zero_stock:
            query += " AND pi.stock_quantity > 0"

        # Category filter removed as category_id column doesn't exist
        if category_id:
            # Just to maintain compatibility, but this won't filter anything
            pass

        query += " ORDER BY p.name"

        rows = self.db_manager.execute_query(query, tuple(params))
        return [POSInventory.from_db_row(row) for row in rows]

    def get_low_stock_pos_inventory(self):
        """Get POS inventory items with low stock.

        Returns:
            list: List of POSInventory objects with stock below min_stock_level
        """
        query = """
        SELECT pi.*, p.name as product_name, p.barcode, NULL as category_name
        FROM pos_inventory pi
        JOIN products p ON pi.product_id = p.id
        WHERE pi.stock_quantity <= pi.min_stock_level AND pi.min_stock_level > 0
        ORDER BY p.name
        """

        rows = self.db_manager.execute_query(query)
        return [POSInventory.from_db_row(row) for row in rows]

    def add_pos_inventory_item(self, inventory_item):
        """Add a new POS inventory item.

        Args:
            inventory_item (POSInventory): Inventory item to add

        Returns:
            int: ID of the new inventory item
        """
        # Check if product already exists in POS inventory
        existing = self.get_pos_inventory_by_product(inventory_item.product_id)
        if existing:
            return -1  # Product already exists in POS inventory

        query = """
        INSERT INTO pos_inventory (product_id, stock_quantity, min_stock_level, location, notes)
        VALUES (?, ?, ?, ?, ?)
        """
        params = (
            inventory_item.product_id,
            inventory_item.stock_quantity,
            inventory_item.min_stock_level,
            inventory_item.location,
            inventory_item.notes
        )

        inventory_id = self.db_manager.execute_insert(query, params)

        # Add initial transaction if stock is not zero
        if inventory_item.stock_quantity > 0:
            transaction = POSInventoryTransaction(
                pos_inventory_id=inventory_id,
                transaction_type=POSInventoryTransaction.TYPE_INITIAL,
                quantity=inventory_item.stock_quantity,
                reference_type='manual',
                notes='Initial stock'
            )
            self.add_pos_inventory_transaction(transaction)

        return inventory_id

    def update_pos_inventory_item(self, inventory_item):
        """Update an existing POS inventory item.

        Args:
            inventory_item (POSInventory): Inventory item to update

        Returns:
            bool: True if successful, False otherwise
        """
        # Get current stock to check if it changed
        current = self.get_pos_inventory_item(inventory_item.id)
        if not current:
            return False

        query = """
        UPDATE pos_inventory
        SET min_stock_level = ?, location = ?, notes = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            inventory_item.min_stock_level,
            inventory_item.location,
            inventory_item.notes,
            inventory_item.id
        )

        result = self.db_manager.execute_update(query, params) > 0

        # If stock quantity changed, add a transaction
        if current.stock_quantity != inventory_item.stock_quantity:
            # Calculate difference
            difference = inventory_item.stock_quantity - current.stock_quantity

            # Determine transaction type
            if difference > 0:
                transaction_type = POSInventoryTransaction.TYPE_ADJUSTMENT
            else:
                transaction_type = POSInventoryTransaction.TYPE_ADJUSTMENT

            # Add transaction
            transaction = POSInventoryTransaction(
                pos_inventory_id=inventory_item.id,
                transaction_type=transaction_type,
                quantity=abs(difference),
                reference_type='manual',
                notes='Manual adjustment'
            )
            self.add_pos_inventory_transaction(transaction)

            # Update stock quantity
            query = """
            UPDATE pos_inventory
            SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            self.db_manager.execute_update(query, (inventory_item.stock_quantity, inventory_item.id))

        return result

    def delete_pos_inventory_item(self, inventory_id):
        """Delete a POS inventory item.

        Args:
            inventory_id (int): Inventory ID

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        DELETE FROM pos_inventory
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (inventory_id,)) > 0

    def add_pos_inventory_transaction(self, transaction):
        """Add a new POS inventory transaction.

        Args:
            transaction (POSInventoryTransaction): Transaction to add

        Returns:
            int: ID of the new transaction
        """
        try:
            # Validate transaction data
            if not transaction.pos_inventory_id:
                print("Error: Missing pos_inventory_id in transaction")
                return -1

            if not transaction.transaction_type:
                print("Error: Missing transaction_type in transaction")
                return -1

            if transaction.quantity <= 0:
                print(f"Error: Invalid quantity in transaction: {transaction.quantity}")
                return -1

            # Ensure notes is not None
            if transaction.notes is None:
                transaction.notes = ""

            query = """
            INSERT INTO pos_inventory_transactions (pos_inventory_id, transaction_type, quantity, reference_type, reference_id, notes)
            VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (
                transaction.pos_inventory_id,
                transaction.transaction_type,
                transaction.quantity,
                transaction.reference_type,
                transaction.reference_id,
                transaction.notes
            )

            transaction_id = self.db_manager.execute_insert(query, params)

            # Update inventory stock based on transaction type
            inventory_item = self.get_pos_inventory_item(transaction.pos_inventory_id)
            if not inventory_item:
                print(f"Warning: Inventory item not found for ID: {transaction.pos_inventory_id}")
                return transaction_id

            new_stock = inventory_item.stock_quantity

            if transaction.transaction_type in [POSInventoryTransaction.TYPE_PURCHASE, POSInventoryTransaction.TYPE_RETURN, POSInventoryTransaction.TYPE_TRANSFER_IN, POSInventoryTransaction.TYPE_INITIAL]:
                new_stock += transaction.quantity
            elif transaction.transaction_type in [POSInventoryTransaction.TYPE_SALE, POSInventoryTransaction.TYPE_TRANSFER_OUT]:
                new_stock -= transaction.quantity
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_ADJUSTMENT:
                # For adjustments, the quantity is the absolute value of the change
                # The direction is determined by comparing with current stock
                if transaction.reference_type == 'increase':
                    new_stock += transaction.quantity
                elif transaction.reference_type == 'decrease':
                    new_stock -= transaction.quantity

            # Ensure stock doesn't go below zero
            new_stock = max(0, new_stock)

            # Update inventory stock
            query = """
            UPDATE pos_inventory
            SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            self.db_manager.execute_update(query, (new_stock, transaction.pos_inventory_id))

            return transaction_id
        except Exception as e:
            print(f"Error in add_pos_inventory_transaction: {str(e)}")
            return -1

    def get_pos_inventory_transactions(self, inventory_id=None, product_id=None, limit=100):
        """Get POS inventory transactions.

        Args:
            inventory_id (int, optional): Filter by inventory ID. Defaults to None.
            product_id (int, optional): Filter by product ID. Defaults to None.
            limit (int, optional): Maximum number of transactions to return. Defaults to 100.

        Returns:
            list: List of POSInventoryTransaction objects
        """
        query = """
        SELECT t.*, p.name as product_name
        FROM pos_inventory_transactions t
        JOIN pos_inventory i ON t.pos_inventory_id = i.id
        JOIN products p ON i.product_id = p.id
        WHERE 1=1
        """

        params = []

        if inventory_id:
            query += " AND t.pos_inventory_id = ?"
            params.append(inventory_id)

        if product_id:
            query += " AND i.product_id = ?"
            params.append(product_id)

        query += " ORDER BY t.created_at DESC LIMIT ?"
        params.append(limit)

        rows = self.db_manager.execute_query(query, tuple(params))
        return [POSInventoryTransaction.from_db_row(row) for row in rows]

    def sync_pos_inventory_from_main(self):
        """Sync POS inventory from main inventory.

        This adds products from the main inventory that don't exist in POS inventory.

        Returns:
            int: Number of products added to POS inventory
        """
        # Get products that are not in POS inventory
        query = """
        SELECT p.*
        FROM products p
        LEFT JOIN pos_inventory pi ON p.id = pi.product_id
        WHERE pi.id IS NULL AND p.type = 'product'
        """

        rows = self.db_manager.execute_query(query)
        count = 0

        for row in rows:
            product = Product.from_db_row(row)

            # Add to POS inventory with zero stock
            inventory_item = POSInventory(
                product_id=product.id,
                stock_quantity=0,
                min_stock_level=product.min_stock_level,
                notes=f"Synced from main inventory on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )

            if self.add_pos_inventory_item(inventory_item) > 0:
                count += 1

        return count

    def adjust_pos_inventory_for_sale(self, invoice_id, session_id=None):
        """Adjust POS inventory for a sale.

        Args:
            invoice_id (int): Invoice ID
            session_id (int, optional): Session ID. Not used currently.

        Returns:
            bool: True if successful, False otherwise
        """
        # Get invoice items
        query = """
        SELECT * FROM invoice_items
        WHERE invoice_id = ? AND is_product = 1
        """

        rows = self.db_manager.execute_query(query, (invoice_id,))
        if not rows:
            return True  # No products in invoice

        success = True

        for row in rows:
            product_id = row['product_id']
            quantity = row['quantity']

            # Get POS inventory item
            inventory_item = self.get_pos_inventory_by_product(product_id)
            if not inventory_item:
                # Product not in POS inventory, skip
                continue

            # Add transaction
            transaction = POSInventoryTransaction(
                pos_inventory_id=inventory_item.id,
                transaction_type=POSInventoryTransaction.TYPE_SALE,
                quantity=quantity,
                reference_type='invoice',
                reference_id=invoice_id,
                notes=f"Sale from invoice #{invoice_id}"
            )

            if self.add_pos_inventory_transaction(transaction) <= 0:
                success = False

        return success
