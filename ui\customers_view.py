#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Customers View for فوترها (Fawterha)
Manages customer data display and editing
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QLineEdit, QFormLayout,
    QDialog, QMessageBox, QHeaderView, QAbstractItemView
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont
from models.customer import Customer
from utils.dialog_helper import show_info_message, show_warning_message, show_error_message, show_confirmation_message
from ui.customer_statement_dialog import CustomerStatementDialog
from utils.translation_manager import tr


class CustomerDialog(QDialog):
    """Dialog for adding or editing a customer."""

    def __init__(self, parent=None, customer=None):
        """Initialize the customer dialog.

        Args:
            parent: Parent widget
            customer (Customer, optional): Customer to edit. Defaults to None.
        """
        super().__init__(parent)

        self.customer = customer
        self.setWindowTitle(tr("customers.add_new", "إضافة عميل جديد") if not customer else tr("customers.edit", "تعديل بيانات العميل"))
        self.setMinimumWidth(400)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        layout = QVBoxLayout(self)

        # Create form layout
        form_layout = QFormLayout()
        layout.addLayout(form_layout)

        # Add form fields
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("customers.enter_name", "أدخل اسم العميل"))
        form_layout.addRow(tr("customers.name", "الاسم:"), self.name_edit)

        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText(tr("customers.enter_email", "أدخل البريد الإلكتروني"))
        form_layout.addRow(tr("customers.email", "البريد الإلكتروني:"), self.email_edit)

        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText(tr("customers.enter_phone", "أدخل رقم الهاتف"))
        form_layout.addRow(tr("customers.phone", "الهاتف:"), self.phone_edit)

        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText(tr("customers.enter_address", "أدخل العنوان"))
        form_layout.addRow(tr("customers.address", "العنوان:"), self.address_edit)

        # Add buttons
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)

        self.save_button = QPushButton(tr("common.save", "حفظ"))
        self.save_button.clicked.connect(self.accept)
        button_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        # Fill form if editing an existing customer
        if customer:
            self.name_edit.setText(customer.name)
            self.email_edit.setText(customer.email)
            self.phone_edit.setText(customer.phone)
            self.address_edit.setText(customer.address)

    def get_customer_data(self):
        """Get the customer data from the form.

        Returns:
            dict: Customer data
        """
        return {
            'name': self.name_edit.text(),
            'email': self.email_edit.text(),
            'phone': self.phone_edit.text(),
            'address': self.address_edit.text()
        }


class CustomersView(QWidget):
    """Widget for managing customers."""

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the customers view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager

        # Create layout
        layout = QVBoxLayout(self)

        # Create toolbar
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)

        # Add new customer button with theme-aware styling
        self.add_button = QPushButton(tr("customers.add_new", "إضافة عميل جديد"))
        self.add_button.setProperty("style", "primary")  # Set property for theme-aware styling
        self.add_button.setMinimumHeight(40)
        self.add_button.clicked.connect(self.create_new_customer)
        toolbar_layout.addWidget(self.add_button)

        # Add search field with theme-aware styling
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("customers.search_customer", "بحث عن عميل"))
        self.search_edit.setMinimumHeight(40)
        self.search_edit.textChanged.connect(self.filter_customers)
        toolbar_layout.addWidget(self.search_edit)

        # Create customers table with improved styling
        self.customers_table = QTableWidget()
        self.customers_table.setColumnCount(6)
        self.customers_table.setHorizontalHeaderLabels([
            tr("customers.name", "الاسم"),
            tr("customers.email", "البريد الإلكتروني"),
            tr("customers.phone", "الهاتف"),
            tr("customers.address", "العنوان"),
            "",
            ""
        ])
        self.customers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.customers_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.customers_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Stretch)
        self.customers_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)
        self.customers_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Fixed)
        self.customers_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)
        self.customers_table.setColumnWidth(4, 150)
        self.customers_table.setColumnWidth(5, 150)
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.customers_table.setAlternatingRowColors(True)
        self.customers_table.verticalHeader().setVisible(False)
        self.customers_table.setShowGrid(True)
        self.customers_table.verticalHeader().setDefaultSectionSize(60)  # Increase row height
        layout.addWidget(self.customers_table)

        # Load customers
        self.load_customers()

    def load_customers(self):
        """Load customers from the database."""
        query = "SELECT * FROM customers ORDER BY name"
        rows = self.db_manager.execute_query(query)

        self.customers_table.setRowCount(0)

        for row in rows:
            customer = Customer.from_db_row(row)
            self.add_customer_to_table(customer)

    def add_customer_to_table(self, customer):
        """Add a customer to the table.

        Args:
            customer (Customer): Customer to add
        """
        row_position = self.customers_table.rowCount()
        self.customers_table.insertRow(row_position)

        # Set customer data
        self.customers_table.setItem(row_position, 0, QTableWidgetItem(customer.name))
        self.customers_table.setItem(row_position, 1, QTableWidgetItem(customer.email))
        self.customers_table.setItem(row_position, 2, QTableWidgetItem(customer.phone))
        self.customers_table.setItem(row_position, 3, QTableWidgetItem(customer.address))

        # Add edit button with theme-aware styling
        edit_button = QPushButton(tr("common.edit", "تعديل"))
        edit_button.setProperty("style", "success")  # Set property for theme-aware styling
        edit_button.setMinimumHeight(32)
        edit_button.setMinimumWidth(110)
        edit_button.clicked.connect(lambda: self.edit_customer(customer.id))
        self.customers_table.setCellWidget(row_position, 4, edit_button)

        # Add statement button with theme-aware styling
        statement_button = QPushButton(tr("customers.view_account_statement", "كشف حساب"))
        statement_button.setProperty("style", "info")  # Set property for theme-aware styling
        statement_button.setMinimumHeight(32)
        statement_button.setMinimumWidth(110)
        statement_button.clicked.connect(lambda: self.show_customer_statement(customer.id))
        self.customers_table.setCellWidget(row_position, 5, statement_button)

        # Store customer ID in the first column item
        self.customers_table.item(row_position, 0).setData(Qt.UserRole, customer.id)

    def create_new_customer(self):
        """Create a new customer."""
        dialog = CustomerDialog(self)
        if dialog.exec():
            customer_data = dialog.get_customer_data()

            # Validate data
            if not customer_data['name']:
                QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("customers.name_required", "يجب إدخال اسم العميل"))
                return

            # Insert customer into database
            query = """
            INSERT INTO customers (name, email, phone, address)
            VALUES (?, ?, ?, ?)
            """
            params = (
                customer_data['name'],
                customer_data['email'],
                customer_data['phone'],
                customer_data['address']
            )

            try:
                customer_id = self.db_manager.execute_insert(query, params)

                # Create customer object
                customer = Customer(
                    id=customer_id,
                    name=customer_data['name'],
                    email=customer_data['email'],
                    phone=customer_data['phone'],
                    address=customer_data['address']
                )

                # Add to table
                self.add_customer_to_table(customer)

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("customers.add_success", "تم إضافة العميل بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("customers.add_error", f"حدث خطأ أثناء إضافة العميل: {str(e)}"))

    def edit_customer(self, customer_id):
        """Edit a customer.

        Args:
            customer_id (int): Customer ID
        """
        # Get customer from database
        query = "SELECT * FROM customers WHERE id = ?"
        rows = self.db_manager.execute_query(query, (customer_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("customers.customer_not_found", "لم يتم العثور على العميل"))
            return

        customer = Customer.from_db_row(rows[0])

        # Show edit dialog
        dialog = CustomerDialog(self, customer)
        if dialog.exec():
            customer_data = dialog.get_customer_data()

            # Validate data
            if not customer_data['name']:
                QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("customers.name_required", "يجب إدخال اسم العميل"))
                return

            # Update customer in database
            query = """
            UPDATE customers
            SET name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            params = (
                customer_data['name'],
                customer_data['email'],
                customer_data['phone'],
                customer_data['address'],
                customer_id
            )

            try:
                self.db_manager.execute_update(query, params)

                # Reload customers
                self.load_customers()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("customers.update_success", "تم تحديث بيانات العميل بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("customers.update_error", f"حدث خطأ أثناء تحديث بيانات العميل: {str(e)}"))

    def filter_customers(self, text):
        """Filter customers by search text.

        Args:
            text (str): Search text
        """
        for row in range(self.customers_table.rowCount()):
            show_row = False

            for col in range(4):  # Check first 4 columns
                item = self.customers_table.item(row, col)
                if item and text.lower() in item.text().lower():
                    show_row = True
                    break

            self.customers_table.setRowHidden(row, not show_row)

    def show_customer_statement(self, customer_id):
        """Show customer account statement.

        Args:
            customer_id (int): Customer ID
        """
        try:
            # Create and show the customer statement dialog
            dialog = CustomerStatementDialog(
                parent=self,
                db_manager=self.db_manager,
                customer_id=customer_id,
                currency_manager=self.currency_manager
            )
            dialog.exec()
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("customers.statement_error", f"حدث خطأ أثناء عرض كشف حساب العميل: {str(e)}"))
