#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Reset Script for فوترها (Fawterha)
This script deletes the existing database and recreates it with the schema
"""

import os
import sqlite3
import sys
import pathlib

# Add the parent directory to sys.path to allow importing modules
current_dir = pathlib.Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from database.db_manager import DatabaseManager
from database.schema import create_tables

def reset_database():
    """Reset the database by deleting it and recreating it."""
    # Get the database path
    db_manager = DatabaseManager()
    db_path = db_manager.db_path

    print(f"Resetting database at: {db_path}")

    # Close any existing connections
    db_manager.close()

    # Delete the database file if it exists
    if os.path.exists(db_path):
        try:
            os.remove(db_path)
            print("Existing database deleted.")
        except Exception as e:
            print(f"Error deleting database: {e}")
            return False

    # Create a new database with the schema
    try:
        # Create a new connection
        connection = sqlite3.connect(db_path)

        # Create tables
        create_tables(connection)

        # Close the connection
        connection.close()

        print("Database reset successfully.")
        return True
    except Exception as e:
        print(f"Error creating new database: {e}")
        return False

if __name__ == "__main__":
    # Ask for confirmation
    confirm = input("This will delete all data in the database. Are you sure? (y/n): ")

    if confirm.lower() == 'y':
        success = reset_database()
        if success:
            print("Database has been reset. You can now restart the application.")
            sys.exit(0)
        else:
            print("Failed to reset database.")
            sys.exit(1)
    else:
        print("Database reset cancelled.")
        sys.exit(0)
