#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Transaction Manager for فوترها (Fawterha)
Handles database operations for financial transactions
"""

from models.transaction import Transaction
from models.transaction_detail import TransactionDetail
from models.account import Account


class TransactionManager:
    """Manager for transaction database operations."""

    def __init__(self, db_manager, account_manager=None):
        """Initialize the transaction manager.

        Args:
            db_manager: Database manager instance
            account_manager: Account manager instance
        """
        self.db_manager = db_manager
        self.account_manager = account_manager

    def get_all_transactions(self, limit=100):
        """Get all transactions.

        Args:
            limit (int, optional): Maximum number of transactions to return

        Returns:
            list: List of Transaction objects
        """
        query = """
        SELECT id, transaction_number, transaction_date, description, reference_type, reference_id,
               amount, currency_id, status, created_by, created_at, updated_at
        FROM transactions
        ORDER BY transaction_date DESC, id DESC
        LIMIT ?
        """
        rows = self.db_manager.execute_query(query, (limit,))
        transactions = []

        for row in rows:
            transaction = Transaction.from_db_row(row)
            transaction.details = self.get_transaction_details(transaction.id)
            transactions.append(transaction)

        return transactions

    def get_transaction_by_id(self, transaction_id):
        """Get a transaction by ID.

        Args:
            transaction_id (int): Transaction ID

        Returns:
            Transaction: Transaction object or None if not found
        """
        query = """
        SELECT id, transaction_number, transaction_date, description, reference_type, reference_id,
               amount, currency_id, status, created_by, created_at, updated_at
        FROM transactions
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (transaction_id,))
        if not rows:
            return None

        transaction = Transaction.from_db_row(rows[0])
        transaction.details = self.get_transaction_details(transaction_id)
        return transaction

    def get_transaction_by_number(self, transaction_number):
        """Get a transaction by number.

        Args:
            transaction_number (str): Transaction number

        Returns:
            Transaction: Transaction object or None if not found
        """
        query = """
        SELECT id, transaction_number, transaction_date, description, reference_type, reference_id,
               amount, currency_id, status, created_by, created_at, updated_at
        FROM transactions
        WHERE transaction_number = ?
        """
        rows = self.db_manager.execute_query(query, (transaction_number,))
        if not rows:
            return None

        transaction = Transaction.from_db_row(rows[0])
        transaction.details = self.get_transaction_details(transaction.id)
        return transaction

    def get_transactions_by_reference(self, reference_type, reference_id):
        """Get transactions by reference.

        Args:
            reference_type (str): Reference type
            reference_id (int): Reference ID

        Returns:
            list: List of Transaction objects
        """
        query = """
        SELECT id, transaction_number, transaction_date, description, reference_type, reference_id,
               amount, currency_id, status, created_by, created_at, updated_at
        FROM transactions
        WHERE reference_type = ? AND reference_id = ?
        ORDER BY transaction_date DESC, id DESC
        """
        rows = self.db_manager.execute_query(query, (reference_type, reference_id))
        transactions = []

        for row in rows:
            transaction = Transaction.from_db_row(row)
            transaction.details = self.get_transaction_details(transaction.id)
            transactions.append(transaction)

        return transactions

    def get_transaction_details(self, transaction_id):
        """Get details for a transaction.

        Args:
            transaction_id (int): Transaction ID

        Returns:
            list: List of TransactionDetail objects
        """
        query = """
        SELECT id, transaction_id, account_id, debit, credit, description, created_at
        FROM transaction_details
        WHERE transaction_id = ?
        ORDER BY id
        """
        rows = self.db_manager.execute_query(query, (transaction_id,))
        return [TransactionDetail.from_db_row(row) for row in rows]

    def add_transaction(self, transaction):
        """Add a new transaction.

        Args:
            transaction (Transaction): Transaction object to add

        Returns:
            int: ID of the new transaction
        """
        # Validate transaction
        if not transaction.details:
            raise ValueError("Transaction must have at least one detail")

        if not transaction.is_balanced():
            raise ValueError("Transaction must be balanced (debits = credits)")

        # Start a transaction
        conn = self.db_manager.connect()
        cursor = conn.cursor()

        try:
            # Begin transaction
            conn.execute("BEGIN TRANSACTION")

            # Insert transaction
            query = """
            INSERT INTO transactions (
                transaction_number, transaction_date, description, reference_type, reference_id,
                amount, currency_id, status, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                transaction.transaction_number,
                transaction.transaction_date,
                transaction.description,
                transaction.reference_type,
                transaction.reference_id,
                transaction.amount,
                transaction.currency_id,
                transaction.status,
                transaction.created_by
            )
            cursor.execute(query, params)
            transaction_id = cursor.lastrowid

            # Insert details
            for detail in transaction.details:
                detail.transaction_id = transaction_id
                detail_query = """
                INSERT INTO transaction_details (
                    transaction_id, account_id, debit, credit, description
                ) VALUES (?, ?, ?, ?, ?)
                """
                cursor.execute(detail_query, (
                    detail.transaction_id,
                    detail.account_id,
                    detail.debit,
                    detail.credit,
                    detail.description
                ))

                # Update account balance
                if self.account_manager:
                    account = self.account_manager.get_account_by_id(detail.account_id)
                    if account:
                        # For debit-normal accounts (assets, expenses), debits increase the balance
                        if account.is_debit_account():
                            new_balance = account.balance + detail.debit - detail.credit
                        # For credit-normal accounts (liabilities, equity, revenue), credits increase the balance
                        else:
                            new_balance = account.balance - detail.debit + detail.credit

                        self.account_manager.update_account_balance(account.id, new_balance)

            # Commit transaction
            conn.execute("COMMIT")
            return transaction_id
        except Exception as e:
            # Rollback transaction on error
            conn.execute("ROLLBACK")
            raise e
        finally:
            self.db_manager.close()

    def update_transaction_status(self, transaction_id, new_status):
        """Update a transaction's status.

        Args:
            transaction_id (int): Transaction ID
            new_status (str): New status

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE transactions
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (new_status, transaction_id)
        return self.db_manager.execute_update(query, params) > 0

    def void_transaction(self, transaction_id):
        """Void a transaction.

        Args:
            transaction_id (int): Transaction ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Get the transaction
        transaction = self.get_transaction_by_id(transaction_id)
        if not transaction:
            return False

        # Start a transaction
        conn = self.db_manager.connect()
        cursor = conn.cursor()

        try:
            # Begin transaction
            conn.execute("BEGIN TRANSACTION")

            # Update transaction status
            status_query = """
            UPDATE transactions
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            cursor.execute(status_query, (Transaction.STATUS_VOIDED, transaction_id))

            # Reverse account balances
            if self.account_manager:
                for detail in transaction.details:
                    account = self.account_manager.get_account_by_id(detail.account_id)
                    if account:
                        # For debit-normal accounts (assets, expenses), reverse the effect
                        if account.is_debit_account():
                            new_balance = account.balance - detail.debit + detail.credit
                        # For credit-normal accounts (liabilities, equity, revenue), reverse the effect
                        else:
                            new_balance = account.balance + detail.debit - detail.credit

                        self.account_manager.update_account_balance(account.id, new_balance)

            # Commit transaction
            conn.execute("COMMIT")
            return True
        except Exception as e:
            # Rollback transaction on error
            conn.execute("ROLLBACK")
            raise e
        finally:
            self.db_manager.close()

    def delete_transaction(self, transaction_id):
        """Delete a transaction.

        Args:
            transaction_id (int): Transaction ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Get the transaction
        transaction = self.get_transaction_by_id(transaction_id)
        if not transaction:
            return False

        # Only draft transactions can be deleted
        if transaction.status != Transaction.STATUS_DRAFT:
            return False

        # Start a transaction
        conn = self.db_manager.connect()
        cursor = conn.cursor()

        try:
            # Begin transaction
            conn.execute("BEGIN TRANSACTION")

            # Delete transaction details
            details_query = """
            DELETE FROM transaction_details WHERE transaction_id = ?
            """
            cursor.execute(details_query, (transaction_id,))

            # Delete transaction
            transaction_query = """
            DELETE FROM transactions WHERE id = ?
            """
            cursor.execute(transaction_query, (transaction_id,))

            # Commit transaction
            conn.execute("COMMIT")
            return True
        except Exception as e:
            # Rollback transaction on error
            conn.execute("ROLLBACK")
            raise e
        finally:
            self.db_manager.close()

    def get_next_transaction_number(self, prefix="TRX-"):
        """Get the next transaction number.

        Args:
            prefix (str, optional): Transaction number prefix

        Returns:
            str: Next transaction number
        """
        query = """
        SELECT MAX(CAST(SUBSTR(transaction_number, ?) AS INTEGER)) AS max_number
        FROM transactions
        WHERE transaction_number LIKE ?
        """
        prefix_len = len(prefix) + 1
        rows = self.db_manager.execute_query(query, (prefix_len, f"{prefix}%"))

        if rows and rows[0]['max_number'] is not None:
            next_number = int(rows[0]['max_number']) + 1
        else:
            next_number = 1001

        return f"{prefix}{next_number}"
