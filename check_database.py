#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Check Database Script for فوترها (Fawterha)
Checks the database tables and values
"""

import os
import sqlite3

def check_database():
    """Check database tables and values."""
    # Get database path
    documents_path = os.path.expanduser("~/Documents/Fawterha")
    db_path = os.path.join(documents_path, "fawterha.db")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    try:
        # Check products table schema
        print("=== Products Table Schema ===")
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        for column in columns:
            print(f"{column['name']} ({column['type']})")
        
        # Check products table data
        print("\n=== Products Table Data ===")
        cursor.execute("SELECT * FROM products")
        products = cursor.fetchall()
        for product in products:
            print(f"ID: {product['id']}")
            print(f"Name: {product['name']}")
            print(f"Type: {product['type']}")
            print(f"Price: {product['price']}")
            print(f"Stock Quantity: {product['stock_quantity']}")
            print(f"Min Stock Level: {product['min_stock_level']}")
            print(f"Track Inventory: {product['track_inventory']}")
            print("---")
        
        # Check inventory_transactions table schema
        print("\n=== Inventory Transactions Table Schema ===")
        cursor.execute("PRAGMA table_info(inventory_transactions)")
        columns = cursor.fetchall()
        for column in columns:
            print(f"{column['name']} ({column['type']})")
        
        # Check inventory_transactions table data
        print("\n=== Inventory Transactions Table Data ===")
        cursor.execute("SELECT * FROM inventory_transactions")
        transactions = cursor.fetchall()
        for transaction in transactions:
            print(f"ID: {transaction['id']}")
            print(f"Product ID: {transaction['product_id']}")
            print(f"Type: {transaction['transaction_type']}")
            print(f"Quantity: {transaction['quantity']}")
            print(f"Created At: {transaction['created_at']}")
            print("---")
        
    except Exception as e:
        print(f"Error checking database: {str(e)}")
    finally:
        # Close connection
        conn.close()

if __name__ == "__main__":
    check_database()
