#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Update Script for فوترها (Fawterha)
Updates the database schema to add inventory fields
"""

import os
import sqlite3

def update_database():
    """Update the database schema to add inventory fields."""
    # Get database path
    documents_path = os.path.expanduser("~/Documents/Fawterha")
    db_path = os.path.join(documents_path, "fawterha.db")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Start transaction
        conn.execute("BEGIN TRANSACTION")
        
        # Check if products table has stock_quantity column
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # Add inventory columns to products table if they don't exist
        if 'stock_quantity' not in column_names:
            print("Adding stock_quantity column to products table...")
            cursor.execute("ALTER TABLE products ADD COLUMN stock_quantity INTEGER DEFAULT 0")
        
        if 'min_stock_level' not in column_names:
            print("Adding min_stock_level column to products table...")
            cursor.execute("ALTER TABLE products ADD COLUMN min_stock_level INTEGER DEFAULT 0")
        
        if 'track_inventory' not in column_names:
            print("Adding track_inventory column to products table...")
            cursor.execute("ALTER TABLE products ADD COLUMN track_inventory INTEGER DEFAULT 0")
        
        # Check if inventory_transactions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='inventory_transactions'")
        if not cursor.fetchone():
            print("Creating inventory_transactions table...")
            cursor.execute('''
            CREATE TABLE inventory_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                transaction_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                reference_type TEXT,
                reference_id INTEGER,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
            ''')
        
        # Check if invoice_items table has product_id column
        cursor.execute("PRAGMA table_info(invoice_items)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        # Add product columns to invoice_items table if they don't exist
        if 'product_id' not in column_names:
            print("Adding product_id column to invoice_items table...")
            cursor.execute("ALTER TABLE invoice_items ADD COLUMN product_id INTEGER")
        
        if 'is_product' not in column_names:
            print("Adding is_product column to invoice_items table...")
            cursor.execute("ALTER TABLE invoice_items ADD COLUMN is_product INTEGER DEFAULT 0")
        
        if 'track_inventory' not in column_names:
            print("Adding track_inventory column to invoice_items table...")
            cursor.execute("ALTER TABLE invoice_items ADD COLUMN track_inventory INTEGER DEFAULT 0")
        
        # Commit transaction
        conn.commit()
        print("Database updated successfully!")
        
    except Exception as e:
        # Rollback transaction on error
        conn.rollback()
        print(f"Error updating database: {str(e)}")
    finally:
        # Close connection
        conn.close()

if __name__ == "__main__":
    update_database()
