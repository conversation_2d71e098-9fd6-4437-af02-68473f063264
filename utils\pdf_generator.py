#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF Generator for فوترها (Fawterha)
Generates PDF files for invoices and reports
"""

import os
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont

# Import Arabic text handling libraries
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    print("Warning: arabic_reshaper or python-bidi not installed. Arabic text may not display correctly.")
    print("To install: pip install arabic-reshaper python-bidi")
    ARABIC_SUPPORT = False


def reshape_arabic_text(text):
    """Reshape Arabic text for proper display in PDF.

    Args:
        text (str): Text that may contain Arabic characters

    Returns:
        str: Reshaped text ready for PDF display
    """
    if not text:
        return ""

    if not ARABIC_SUPPORT:
        return text

    try:
        # Reshape Arabic text
        reshaped_text = arabic_reshaper.reshape(text)
        # Apply bidirectional algorithm with RTL base direction
        bidi_text = get_display(reshaped_text)
        return bidi_text
    except Exception as e:
        print(f"Error reshaping Arabic text: {str(e)}")
        return text


def setup_arabic_fonts():
    """Set up Arabic fonts for ReportLab."""
    # Check if fonts are already registered
    if 'Arabic' not in pdfmetrics.getRegisteredFontNames():
        # Ensure fonts directory exists
        fonts_dir = "resources/fonts"
        if not os.path.exists(fonts_dir):
            try:
                os.makedirs(fonts_dir, exist_ok=True)
                print(f"Created fonts directory: {fonts_dir}")
            except Exception as e:
                print(f"Failed to create fonts directory: {str(e)}")

        # Define a list of potential Arabic fonts to try
        font_search_list = [
            # Custom fonts in resources folder
            {
                "path": "resources/fonts/Amiri-Regular.ttf",
                "name": "Arabic",
                "message": "Using Amiri font for Arabic text"
            },
            {
                "path": "resources/fonts/Amiri-Bold.ttf",
                "name": "Arabic-Bold",
                "message": "Using Amiri Bold font for Arabic text"
            },
            {
                "path": "resources/fonts/Almarai-Regular.ttf",
                "name": "Arabic",
                "message": "Using Almarai font for Arabic text"
            },
            {
                "path": "resources/fonts/Almarai-Bold.ttf",
                "name": "Arabic-Bold",
                "message": "Using Almarai Bold font for Arabic text"
            },
            # Windows Arabic fonts
            {
                "path": "C:/Windows/Fonts/arabtype.ttf",
                "name": "Arabic",
                "message": "Using Arabic Typesetting font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/simpo.ttf",
                "name": "Arabic",
                "message": "Using Simplified Arabic font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/simpbdo.ttf",
                "name": "Arabic-Bold",
                "message": "Using Simplified Arabic Bold font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/tahoma.ttf",
                "name": "Arabic",
                "message": "Using Tahoma font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/tahomabd.ttf",
                "name": "Arabic-Bold",
                "message": "Using Tahoma Bold font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/arial.ttf",
                "name": "Arabic",
                "message": "Using Arial font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/arialbd.ttf",
                "name": "Arabic-Bold",
                "message": "Using Arial Bold font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/segoeui.ttf",
                "name": "Arabic",
                "message": "Using Segoe UI font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/segoeuib.ttf",
                "name": "Arabic-Bold",
                "message": "Using Segoe UI Bold font for Arabic text"
            },
            # Additional Windows fonts with Arabic support
            {
                "path": "C:/Windows/Fonts/calibri.ttf",
                "name": "Arabic",
                "message": "Using Calibri font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/calibrib.ttf",
                "name": "Arabic-Bold",
                "message": "Using Calibri Bold font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/timesi.ttf",
                "name": "Arabic",
                "message": "Using Times New Roman Italic font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/timesbi.ttf",
                "name": "Arabic-Bold",
                "message": "Using Times New Roman Bold Italic font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/times.ttf",
                "name": "Arabic",
                "message": "Using Times New Roman font for Arabic text"
            },
            {
                "path": "C:/Windows/Fonts/timesbd.ttf",
                "name": "Arabic-Bold",
                "message": "Using Times New Roman Bold font for Arabic text"
            },
            # macOS fonts
            {
                "path": "/Library/Fonts/Arial Unicode.ttf",
                "name": "Arabic",
                "message": "Using Arial Unicode font for Arabic text"
            },
            {
                "path": "/System/Library/Fonts/ArialHB.ttc",
                "name": "Arabic",
                "message": "Using Arial Hebrew font for Arabic text"
            },
            # Linux fonts
            {
                "path": "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                "name": "Arabic",
                "message": "Using DejaVu Sans font for Arabic text"
            },
            {
                "path": "/usr/share/fonts/truetype/kacst/KacstBook.ttf",
                "name": "Arabic",
                "message": "Using KacstBook font for Arabic text"
            },
            {
                "path": "/usr/share/fonts/truetype/kacst/KacstOne.ttf",
                "name": "Arabic",
                "message": "Using KacstOne font for Arabic text"
            }
        ]

        # Try to register fonts from the list
        font_registered = False
        for font_info in font_search_list:
            if os.path.exists(font_info["path"]):
                try:
                    pdfmetrics.registerFont(TTFont(font_info["name"], font_info["path"]))
                    print(f"{font_info['message']} from: {font_info['path']}")
                    font_registered = True
                    if font_info["name"] == "Arabic":
                        break  # Stop after finding a regular font
                except Exception as e:
                    print(f"Failed to register font from {font_info['path']}: {str(e)}")
                    continue

        # If no suitable font found, show a message
        if not font_registered:
            print("لم يتم العثور على خط عربي مناسب. يرجى تنزيل خط Almarai أو Amiri من:")
            print("https://fonts.google.com/specimen/Almarai")
            print("https://fonts.google.com/specimen/Amiri")
            print("وضع الملفات في المجلد resources/fonts")
            print("النص العربي قد لا يظهر بشكل صحيح.")


def generate_invoice_pdf(file_path, invoice, customer, items, company_info):
    """Generate a PDF file for an invoice.

    Args:
        file_path (str): Path to save the PDF file
        invoice (Invoice): Invoice object
        customer (Customer): Customer object
        items (list): List of invoice items
        company_info (dict): Company information
    """
    setup_arabic_fonts()

    # Create the PDF document
    doc = SimpleDocTemplate(
        file_path,
        pagesize=A4,
        rightMargin=30,
        leftMargin=30,
        topMargin=30,
        bottomMargin=30
    )

    # Create styles
    styles = getSampleStyleSheet()

    # Add custom styles for Arabic text
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
    arabic_bold_font = 'Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

    styles.add(ParagraphStyle(
        name='RightAlign',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12
    ))

    styles.add(ParagraphStyle(
        name='ArabicHeading1',
        parent=styles['Heading1'],
        alignment=2,  # right alignment
        fontName=arabic_bold_font,
        fontSize=18,
        textColor=colors.darkblue
    ))

    styles.add(ParagraphStyle(
        name='ArabicHeading2',
        parent=styles['Heading2'],
        alignment=2,  # right alignment
        fontName=arabic_bold_font,
        fontSize=14,
        textColor=colors.darkblue
    ))

    styles.add(ParagraphStyle(
        name='ArabicHeading3',
        parent=styles['Heading3'],
        alignment=2,  # right alignment
        fontName=arabic_bold_font,
        fontSize=12,
        textColor=colors.darkblue
    ))

    styles.add(ParagraphStyle(
        name='ArabicNormal',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12,
        leading=16  # line spacing
    ))

    # Create content elements
    elements = []

    # Add company logo if available
    if company_info.get('company_logo'):
        try:
            logo_path = company_info['company_logo']
            if os.path.exists(logo_path):
                from reportlab.platypus import Image
                img = Image(logo_path, width=150, height=70)
                elements.append(img)
        except:
            pass

    # Add company information with reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(company_info.get('company_name', 'Hadou Design')), styles['ArabicHeading1']))
    elements.append(Spacer(1, 20))

    # Add invoice header with reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(f"INV-{invoice.invoice_number} :رقم الفاتورة"), styles['ArabicHeading2']))
    elements.append(Paragraph(reshape_arabic_text(f"تاريخ الإصدار: {invoice.issue_date.strftime('%d-%m-%Y')}"), styles['ArabicNormal']))
    if invoice.due_date:
        elements.append(Paragraph(reshape_arabic_text(f"تاريخ الاستحقاق: {invoice.due_date.strftime('%d-%m-%Y')}"), styles['ArabicNormal']))
    elements.append(Spacer(1, 10))

    # Add customer information with reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(":معلومات العميل"), styles['ArabicHeading3']))
    if customer:
        elements.append(Paragraph(reshape_arabic_text(f"الاسم: {customer.name}"), styles['ArabicNormal']))
        if hasattr(customer, 'email') and customer.email:
            elements.append(Paragraph(reshape_arabic_text(f"البريد الإلكتروني: {customer.email}"), styles['ArabicNormal']))
        if hasattr(customer, 'phone') and customer.phone:
            elements.append(Paragraph(reshape_arabic_text(f"الهاتف: {customer.phone}"), styles['ArabicNormal']))
        if hasattr(customer, 'address') and customer.address:
            elements.append(Paragraph(reshape_arabic_text(f"العنوان: {customer.address}"), styles['ArabicNormal']))
    else:
        elements.append(Paragraph(reshape_arabic_text("عميل افتراضي"), styles['ArabicNormal']))
    elements.append(Spacer(1, 20))

    # Add items table with reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(":المنتجات والخدمات"), styles['ArabicHeading3']))

    # Create table data with reshaped Arabic text
    reshaped_headers = [reshape_arabic_text("الإجمالي"), reshape_arabic_text("الضريبة"),
                        reshape_arabic_text("الخصم"), reshape_arabic_text("سعر الوحدة"),
                        reshape_arabic_text("كمية"), reshape_arabic_text("وصف")]
    table_data = [reshaped_headers]

    for item in items:
        table_data.append([
            reshape_arabic_text(f"{item.total:.2f}"),
            reshape_arabic_text(f"{item.tax:.2f}"),
            reshape_arabic_text(f"{item.discount:.2f}"),
            reshape_arabic_text(f"{item.unit_price:.2f}"),
            reshape_arabic_text(str(item.quantity)),
            reshape_arabic_text(item.description)
        ])

    # Create table
    table = Table(table_data, repeatRows=1)

    # Style the table with improved formatting for Arabic text
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
    arabic_bold_font = 'Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

    table.setStyle(TableStyle([
        # Header styling
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), arabic_bold_font),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('TOPPADDING', (0, 0), (-1, 0), 12),
        ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),  # Vertical alignment for header

        # Data rows styling
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),  # Center align all columns for better Arabic display
        ('FONTNAME', (0, 1), (-1, -1), arabic_font),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
        ('TOPPADDING', (0, 1), (-1, -1), 8),
        ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),  # Vertical alignment for data cells

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.grey),
        ('BOX', (0, 0), (-1, -1), 1.5, colors.black),
    ]))

    # Add alternating row colors
    for i in range(1, len(table_data), 2):
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, i), (-1, i), colors.lightgrey)
        ]))

    # Special styling for amount column
    table.setStyle(TableStyle([
        ('TEXTCOLOR', (5, 1), (5, -1), colors.darkgreen),
        ('FONTNAME', (5, 1), (5, -1), arabic_bold_font),
        ('FONTSIZE', (5, 1), (5, -1), 11),  # Slightly larger font for total column
    ]))

    elements.append(table)
    elements.append(Spacer(1, 10))

    # Get currency symbol
    from utils.currency_helper import get_currency_symbol
    currency_symbol = get_currency_symbol(company_info.get('currency', 'EGP'))

    # Add totals with reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(f"المجموع الفرعي: {currency_symbol} {invoice.subtotal:.2f}"), styles['RightAlign']))
    elements.append(Paragraph(reshape_arabic_text(f"الخصم: {currency_symbol} {invoice.discount:.2f}"), styles['RightAlign']))
    elements.append(Paragraph(reshape_arabic_text(f"الضريبة: {currency_symbol} {invoice.tax:.2f}"), styles['RightAlign']))

    # Add total with special styling and reshaped Arabic text
    total_style = ParagraphStyle(
        name='Total',
        parent=styles['RightAlign'],
        textColor=colors.navy,
        fontSize=14,
        fontName='Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'
    )
    elements.append(Paragraph(reshape_arabic_text(f"الإجمالي: {currency_symbol} {invoice.total:.2f}"), total_style))

    # Add paid and due amounts if available
    if hasattr(invoice, 'amount_paid') and invoice.amount_paid is not None:
        elements.append(Paragraph(reshape_arabic_text(f"المدفوع: {currency_symbol} {invoice.amount_paid:.2f}"), styles['RightAlign']))
    if hasattr(invoice, 'amount_due') and invoice.amount_due is not None:
        elements.append(Paragraph(reshape_arabic_text(f"المتبقي: {currency_symbol} {invoice.amount_due:.2f}"), styles['RightAlign']))

    # Add notes if available with reshaped Arabic text
    if invoice.notes:
        elements.append(Spacer(1, 20))
        elements.append(Paragraph(reshape_arabic_text(":ملاحظات"), styles['ArabicHeading3']))
        elements.append(Paragraph(reshape_arabic_text(invoice.notes), styles['ArabicNormal']))

    # Add footer
    elements.append(Spacer(1, 30))
    footer_text = company_info.get('footer_text', f"Created from POS")
    elements.append(Paragraph(reshape_arabic_text(footer_text), styles['ArabicNormal']))

    # Build the PDF
    doc.build(elements)


def generate_report_pdf(file_path, report_type, start_date, end_date, headers, rows, total_sales, invoice_count):
    """Generate a PDF file for a report.

    Args:
        file_path (str): Path to save the PDF file
        report_type (str): Report type
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Report data rows
        total_sales (str): Total sales amount
        invoice_count (str): Invoice count
    """
    setup_arabic_fonts()

    # Create the PDF document
    doc = SimpleDocTemplate(
        file_path,
        pagesize=A4,
        rightMargin=30,
        leftMargin=30,
        topMargin=30,
        bottomMargin=30
    )

    # Create styles
    styles = getSampleStyleSheet()

    # Add custom styles for Arabic text
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'

    styles.add(ParagraphStyle(
        name='RightAlign',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12
    ))

    styles.add(ParagraphStyle(
        name='ArabicHeading1',
        parent=styles['Heading1'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=18,
        textColor=colors.darkblue
    ))

    styles.add(ParagraphStyle(
        name='ArabicNormal',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12,
        leading=16  # line spacing
    ))

    styles.add(ParagraphStyle(
        name='ArabicSummary',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=14,
        textColor=colors.darkgreen,
        leading=20  # line spacing
    ))

    # Create content elements
    elements = []

    # Add report header with improved styling and reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(f"تقرير {report_type}"), styles['ArabicHeading1']))
    elements.append(Spacer(1, 10))
    elements.append(Paragraph(reshape_arabic_text(f"الفترة: من {start_date} إلى {end_date}"), styles['ArabicNormal']))
    elements.append(Paragraph(reshape_arabic_text(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"), styles['ArabicNormal']))
    elements.append(Spacer(1, 20))

    # Create table data with reshaped Arabic text
    reshaped_headers = [reshape_arabic_text(header) for header in headers]
    table_data = [reshaped_headers]

    # Reshape all text in rows
    reshaped_rows = []
    for row in rows:
        reshaped_row = [reshape_arabic_text(str(cell)) for cell in row]
        reshaped_rows.append(reshaped_row)

    table_data.extend(reshaped_rows)

    # Create table with improved styling
    col_widths = [doc.width/3.0] * len(headers)  # Equal column widths
    table = Table(table_data, repeatRows=1, colWidths=col_widths)

    # Style the table with better colors and improved formatting for Arabic text
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
    arabic_bold_font = 'Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

    table.setStyle(TableStyle([
        # Header styling
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), arabic_bold_font),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('TOPPADDING', (0, 0), (-1, 0), 12),
        ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),  # Vertical alignment for header

        # Data rows styling
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),  # Center align all columns for better Arabic display
        ('FONTNAME', (0, 1), (-1, -1), arabic_font),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
        ('TOPPADDING', (0, 1), (-1, -1), 8),
        ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),  # Vertical alignment for data cells

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.grey),
        ('BOX', (0, 0), (-1, -1), 1.5, colors.black),

        # Special styling for amount column
        ('TEXTCOLOR', (2, 1), (2, -1), colors.darkgreen),
        ('FONTNAME', (2, 1), (2, -1), arabic_bold_font),
    ]))

    # Add alternating row colors
    for i in range(1, len(table_data), 2):
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, i), (-1, i), colors.lightgrey)
        ]))

    elements.append(table)
    elements.append(Spacer(1, 20))

    # Add summary with improved styling and reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(f"إجمالي المبيعات: {total_sales}"), styles['ArabicSummary']))
    elements.append(Paragraph(reshape_arabic_text(f"عدد الفواتير: {invoice_count}"), styles['ArabicSummary']))

    # Build the PDF
    doc.build(elements)


def generate_customer_statement_pdf(file_path, customer, start_date, end_date, headers, rows, total_invoices, total_paid, balance):
    """Generate a PDF file for a customer statement.

    Args:
        file_path (str): Path to save the PDF file
        customer (Customer): Customer object
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Statement data rows
        total_invoices (str): Total invoices amount
        total_paid (str): Total paid amount
        balance (str): Current balance
    """
    setup_arabic_fonts()

    # Create the PDF document
    doc = SimpleDocTemplate(
        file_path,
        pagesize=A4,
        rightMargin=30,
        leftMargin=30,
        topMargin=30,
        bottomMargin=30
    )

    # Create styles
    styles = getSampleStyleSheet()

    # Add custom styles for Arabic text
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'

    styles.add(ParagraphStyle(
        name='RightAlign',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12
    ))

    styles.add(ParagraphStyle(
        name='ArabicHeading1',
        parent=styles['Heading1'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=18,
        textColor=colors.darkblue
    ))

    styles.add(ParagraphStyle(
        name='ArabicHeading2',
        parent=styles['Heading2'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=14,
        textColor=colors.darkblue
    ))

    styles.add(ParagraphStyle(
        name='ArabicNormal',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12,
        leading=16  # line spacing
    ))

    styles.add(ParagraphStyle(
        name='ArabicSummary',
        parent=styles['Normal'],
        alignment=2,  # right alignment
        fontName=arabic_font,
        fontSize=12,
        leading=16,
        fontWeight='bold'
    ))

    # Create content elements
    elements = []

    # Add statement header with reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text("كشف حساب العميل"), styles['ArabicHeading1']))
    elements.append(Spacer(1, 10))
    elements.append(Paragraph(reshape_arabic_text(f"العميل: {customer.name}"), styles['ArabicHeading2']))
    elements.append(Paragraph(reshape_arabic_text(f"الفترة: من {start_date} إلى {end_date}"), styles['ArabicNormal']))
    elements.append(Paragraph(reshape_arabic_text(f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"), styles['ArabicNormal']))

    # Add customer details if available
    if customer.email or customer.phone or customer.address:
        elements.append(Spacer(1, 10))
        if customer.email:
            elements.append(Paragraph(reshape_arabic_text(f"البريد الإلكتروني: {customer.email}"), styles['ArabicNormal']))
        if customer.phone:
            elements.append(Paragraph(reshape_arabic_text(f"الهاتف: {customer.phone}"), styles['ArabicNormal']))
        if customer.address:
            elements.append(Paragraph(reshape_arabic_text(f"العنوان: {customer.address}"), styles['ArabicNormal']))

    elements.append(Spacer(1, 20))

    # Create table data with reshaped Arabic text
    reshaped_headers = [reshape_arabic_text(header) for header in headers]
    table_data = [reshaped_headers]

    # Reshape all text in rows
    reshaped_rows = []
    for row in rows:
        reshaped_row = [reshape_arabic_text(str(cell)) for cell in row]
        reshaped_rows.append(reshaped_row)

    table_data.extend(reshaped_rows)

    # Create table with improved styling
    col_widths = [doc.width/len(headers)] * len(headers)  # Equal column widths
    table = Table(table_data, repeatRows=1, colWidths=col_widths)

    # Apply table styles with improved formatting for Arabic text
    arabic_font = 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica'
    arabic_bold_font = 'Arabic-Bold' if 'Arabic-Bold' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'

    table.setStyle(TableStyle([
        # Header styles
        ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), arabic_bold_font),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 10),
        ('TOPPADDING', (0, 0), (-1, 0), 10),
        ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),  # Vertical alignment for header

        # Cell styles
        ('ALIGN', (0, 1), (-1, -1), 'CENTER'),  # Center align all data cells for better Arabic display
        ('FONTNAME', (0, 1), (-1, -1), arabic_font),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
        ('BOTTOMPADDING', (0, 1), (-1, -1), 8),
        ('TOPPADDING', (0, 1), (-1, -1), 8),
        ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),  # Vertical alignment for data cells

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.grey),
        ('BOX', (0, 0), (-1, -1), 1.5, colors.black),

        # Special styling for amount columns (better visibility)
        ('FONTNAME', (4, 1), (6, -1), arabic_bold_font),
        ('TEXTCOLOR', (4, 1), (6, -1), colors.darkblue),
    ]))

    # Add alternating row colors
    for i in range(1, len(table_data), 2):
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, i), (-1, i), colors.lightgrey)
        ]))

    elements.append(table)
    elements.append(Spacer(1, 20))

    # Add summary with improved styling and reshaped Arabic text
    elements.append(Paragraph(reshape_arabic_text(f"إجمالي الفواتير: {total_invoices}"), styles['ArabicSummary']))
    elements.append(Paragraph(reshape_arabic_text(f"إجمالي المدفوعات: {total_paid}"), styles['ArabicSummary']))
    elements.append(Paragraph(reshape_arabic_text(f"الرصيد الحالي: {balance}"), styles['ArabicSummary']))

    # Add footer with reshaped Arabic text
    elements.append(Spacer(1, 30))
    elements.append(Paragraph(reshape_arabic_text(f"تم إنشاء هذا التقرير بواسطة تطبيق فوترها - {datetime.now().strftime('%Y-%m-%d %H:%M')}"), styles['ArabicNormal']))

    # Build the PDF
    doc.build(elements)