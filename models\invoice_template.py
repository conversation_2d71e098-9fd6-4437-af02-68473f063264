#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Template Model for فوترها (Fawterha)
Represents an invoice template in the system
"""

class InvoiceTemplate:
    """Invoice template model class."""
    
    def __init__(self, id=None, name="", description="", is_default=False,
                 header_color="#0d47a1", text_color="#212121", accent_color="#1e88e5",
                 font_family="Arial", font_size=12, show_logo=True, show_header=True,
                 show_footer=True, footer_text="", created_at=None, updated_at=None):
        """Initialize an invoice template object.
        
        Args:
            id (int, optional): Template ID. Defaults to None.
            name (str, optional): Template name. Defaults to "".
            description (str, optional): Template description. Defaults to "".
            is_default (bool, optional): Whether this is the default template. Defaults to False.
            header_color (str, optional): Header color in hex. Defaults to "#0d47a1".
            text_color (str, optional): Text color in hex. Defaults to "#212121".
            accent_color (str, optional): Accent color in hex. Defaults to "#1e88e5".
            font_family (str, optional): Font family. Defaults to "Arial".
            font_size (int, optional): Font size. Defaults to 12.
            show_logo (bool, optional): Whether to show the logo. Defaults to True.
            show_header (bool, optional): Whether to show the header. Defaults to True.
            show_footer (bool, optional): Whether to show the footer. Defaults to True.
            footer_text (str, optional): Footer text. Defaults to "".
            created_at (str, optional): Creation timestamp. Defaults to None.
            updated_at (str, optional): Update timestamp. Defaults to None.
        """
        self.id = id
        self.name = name
        self.description = description
        self.is_default = is_default
        self.header_color = header_color
        self.text_color = text_color
        self.accent_color = accent_color
        self.font_family = font_family
        self.font_size = font_size
        self.show_logo = show_logo
        self.show_header = show_header
        self.show_footer = show_footer
        self.footer_text = footer_text
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_db_row(cls, row):
        """Create an InvoiceTemplate object from a database row.
        
        Args:
            row: Database row (sqlite3.Row)
            
        Returns:
            InvoiceTemplate: InvoiceTemplate object
        """
        return cls(
            id=row['id'],
            name=row['name'],
            description=row['description'],
            is_default=bool(row['is_default']),
            header_color=row['header_color'],
            text_color=row['text_color'],
            accent_color=row['accent_color'],
            font_family=row['font_family'],
            font_size=row['font_size'],
            show_logo=bool(row['show_logo']),
            show_header=bool(row['show_header']),
            show_footer=bool(row['show_footer']),
            footer_text=row['footer_text'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
    
    def to_dict(self):
        """Convert the template object to a dictionary.
        
        Returns:
            dict: Dictionary representation of the template
        """
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'is_default': self.is_default,
            'header_color': self.header_color,
            'text_color': self.text_color,
            'accent_color': self.accent_color,
            'font_family': self.font_family,
            'font_size': self.font_size,
            'show_logo': self.show_logo,
            'show_header': self.show_header,
            'show_footer': self.show_footer,
            'footer_text': self.footer_text,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def get_css_style(self):
        """Get the CSS style for this template.
        
        Returns:
            str: CSS style string
        """
        return f"""
            body {{
                font-family: {self.font_family}, 'Segoe UI', 'Arial', sans-serif;
                font-size: {self.font_size}pt;
                color: {self.text_color};
                background-color: white;
                margin: 0;
                padding: 0;
            }}
            
            .invoice-header {{
                background-color: {self.header_color};
                color: white;
                padding: 20px;
                margin-bottom: 20px;
            }}
            
            .invoice-title {{
                font-size: 28pt;
                font-weight: bold;
                color: {self.accent_color};
                text-align: center;
                margin: 20px 0;
            }}
            
            .section-title {{
                font-size: 16pt;
                font-weight: bold;
                color: {self.accent_color};
                margin-top: 20px;
                margin-bottom: 10px;
            }}
            
            .invoice-details, .customer-details {{
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                background-color: #f5f5f5;
                padding: 20px;
                margin-bottom: 20px;
            }}
            
            .items-table {{
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }}
            
            .items-table th {{
                background-color: {self.accent_color};
                color: white;
                padding: 10px;
                text-align: center;
                font-weight: bold;
            }}
            
            .items-table td {{
                padding: 8px;
                border-bottom: 1px solid #e0e0e0;
            }}
            
            .items-table tr:nth-child(even) {{
                background-color: #f5f5f5;
            }}
            
            .totals {{
                text-align: right;
                margin-top: 20px;
            }}
            
            .total-row {{
                margin: 5px 0;
            }}
            
            .grand-total {{
                font-size: 14pt;
                font-weight: bold;
                color: {self.accent_color};
            }}
            
            .notes {{
                border: 2px solid #bdbdbd;
                border-radius: 8px;
                background-color: #f5f5f5;
                padding: 20px;
                margin-top: 20px;
                margin-bottom: 20px;
            }}
            
            .footer {{
                text-align: center;
                color: #757575;
                font-size: 10pt;
                margin-top: 40px;
                padding-top: 10px;
                border-top: 1px solid #e0e0e0;
            }}
        """
    
    def __str__(self):
        """Return a string representation of the template.
        
        Returns:
            str: String representation
        """
        return f"{self.name} ({'افتراضي' if self.is_default else 'مخصص'})"
