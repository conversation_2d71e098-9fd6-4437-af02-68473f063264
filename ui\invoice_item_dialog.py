from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel,
    QLineEdit, QDoubleSpinBox, QPushButton, QMessageBox
)
from PySide6.QtCore import Qt
from utils.translation_manager import tr

class InvoiceItemDialog(QDialog):
    """Dialog for adding or editing invoice items."""

    def __init__(self, parent=None, item=None):
        """Initialize the dialog.

        Args:
            parent: Parent widget
            item: InvoiceItem object to edit
        """
        super().__init__(parent)

        # Store parameters
        self.item = item
        self.parent = parent

        # Set window properties
        self.setWindowTitle(tr("invoice_items.add_new", "إضافة منتج/خدمة") if not item else tr("invoice_items.edit", "تعديل منتج/خدمة"))
        self.setMinimumWidth(500)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Set up the UI
        self.setup_ui()

        # Load item data if provided
        if item:
            self.load_item_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Get color scheme from parent if available
        self.colors = getattr(self.parent, 'colors', {
            'primary': '#1976D2',
            'primary_light': '#BBDEFB',
            'primary_dark': '#0D47A1',
            'accent': '#FF5722',
            'text': '#212121',
            'divider': '#BDBDBD',
            'background': '#F5F5F5',
            'card': '#FFFFFF',
            'danger': '#F44336'
        })

        # Set window background
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.colors.get('background', '#F5F5F5')};
            }}
        """)

        # Create main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Create form layout
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFormAlignment(Qt.AlignLeft)
        form_layout.setSpacing(10)

        # Description field
        description_label = QLabel(tr("common.description", "الوصف:"))
        description_label.setStyleSheet(self.get_label_style())

        self.description_edit = QLineEdit()
        self.description_edit.setStyleSheet(self.get_input_style())
        self.description_edit.setPlaceholderText(tr("invoice_items.enter_description", "أدخل وصف المنتج أو الخدمة"))

        form_layout.addRow(description_label, self.description_edit)

        # Quantity field
        quantity_label = QLabel(tr("invoice_items.quantity", "الكمية:"))
        quantity_label.setStyleSheet(self.get_label_style())

        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setMinimum(0.01)
        self.quantity_spin.setMaximum(9999.99)
        self.quantity_spin.setValue(1.0)
        self.quantity_spin.setDecimals(2)
        self.quantity_spin.setStyleSheet(self.get_input_style())
        self.quantity_spin.valueChanged.connect(self.calculate_total)

        form_layout.addRow(quantity_label, self.quantity_spin)

        # Unit price field
        unit_price_label = QLabel(tr("invoice_items.unit_price", "سعر الوحدة:"))
        unit_price_label.setStyleSheet(self.get_label_style())

        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMinimum(0.0)
        self.unit_price_spin.setMaximum(9999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setStyleSheet(self.get_input_style())
        self.unit_price_spin.valueChanged.connect(self.calculate_total)

        # Get currency symbol from parent
        currency_symbol = "ج.م"  # Default
        if hasattr(self.parent, 'current_currency') and self.parent.current_currency:
            currency_symbol = self.parent.current_currency.symbol

        self.unit_price_spin.setSuffix(f" {currency_symbol}")

        form_layout.addRow(unit_price_label, self.unit_price_spin)

        # Discount field
        discount_label = QLabel(tr("invoice_items.discount", "الخصم:"))
        discount_label.setStyleSheet(self.get_label_style())

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(9999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.setStyleSheet(self.get_input_style())
        self.discount_spin.valueChanged.connect(self.calculate_total)
        self.discount_spin.setSuffix(f" {currency_symbol}")

        form_layout.addRow(discount_label, self.discount_spin)

        # Tax field
        tax_label = QLabel(tr("invoice_items.tax", "الضريبة:"))
        tax_label.setStyleSheet(self.get_label_style())

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMinimum(0.0)
        self.tax_spin.setMaximum(9999999.99)
        self.tax_spin.setDecimals(2)
        self.tax_spin.setStyleSheet(self.get_input_style())
        self.tax_spin.valueChanged.connect(self.calculate_total)
        self.tax_spin.setSuffix(f" {currency_symbol}")

        form_layout.addRow(tax_label, self.tax_spin)

        # Total field
        total_label = QLabel(tr("invoice_items.total", "الإجمالي:"))
        total_label.setStyleSheet(self.get_label_style(color=self.colors.get('primary', '#1976D2')))

        self.total_spin = QDoubleSpinBox()
        self.total_spin.setMinimum(0.0)
        self.total_spin.setMaximum(9999999.99)
        self.total_spin.setDecimals(2)
        self.total_spin.setReadOnly(True)
        self.total_spin.setStyleSheet(self.get_input_style(color=self.colors.get('primary', '#1976D2')))
        self.total_spin.setSuffix(f" {currency_symbol}")

        form_layout.addRow(total_label, self.total_spin)

        main_layout.addLayout(form_layout)

        # Add buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Add spacer to push buttons to the right
        buttons_layout.addStretch(1)

        # Cancel button
        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.setStyleSheet(self.get_button_style('danger'))
        self.cancel_button.setMinimumSize(100, 40)
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        # Save button
        self.save_button = QPushButton(tr("common.save", "حفظ"))
        self.save_button.setStyleSheet(self.get_button_style('primary'))
        self.save_button.setMinimumSize(100, 40)
        self.save_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.save_button)

        main_layout.addLayout(buttons_layout)

        # Calculate initial total
        self.calculate_total()

    def get_label_style(self, color=None):
        """Get the style for labels."""
        if not color:
            color = self.colors.get('text', '#212121')

        return f"""
            font-weight: bold;
            font-size: 12pt;
            color: {color};
            padding: 5px;
        """

    def get_input_style(self, color=None):
        """Get the style for input fields."""
        if not color:
            color = self.colors.get('text', '#212121')

        return f"""
            background-color: {self.colors.get('card', '#FFFFFF')};
            border: 1px solid {self.colors.get('divider', '#BDBDBD')};
            border-radius: 4px;
            padding: 8px;
            font-size: 11pt;
            color: {color};
            min-height: 20px;
        """

    def get_button_style(self, style_type='primary'):
        """Get the style for buttons."""
        if style_type == 'primary':
            bg_color = self.colors.get('primary', '#1976D2')
            hover_color = self.colors.get('primary_dark', '#0D47A1')
            text_color = 'white'
        elif style_type == 'danger':
            bg_color = self.colors.get('danger', '#F44336')
            hover_color = '#C62828'  # Darker red
            text_color = 'white'
        else:
            bg_color = self.colors.get('background', '#F5F5F5')
            hover_color = self.colors.get('divider', '#BDBDBD')
            text_color = self.colors.get('text', '#212121')

        return f"""
            QPushButton {{
                background-color: {bg_color};
                color: {text_color};
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 11pt;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:pressed {{
                background-color: {hover_color};
                padding: 9px 14px 7px 16px;
            }}
        """

    def load_item_data(self):
        """Load data from the item."""
        if not self.item:
            return

        self.description_edit.setText(self.item.description)
        self.quantity_spin.setValue(self.item.quantity)
        self.unit_price_spin.setValue(self.item.unit_price)
        self.discount_spin.setValue(self.item.discount)
        self.tax_spin.setValue(self.item.tax)
        self.total_spin.setValue(self.item.total)

    def calculate_total(self):
        """Calculate the total based on quantity, unit price, discount, and tax."""
        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()

        # Calculate subtotal
        subtotal = quantity * unit_price

        # Calculate total
        total = subtotal - discount + tax

        # Update total field
        self.total_spin.setValue(total)

    def get_item_data(self):
        """Get the item data from the form.

        Returns:
            dict: Item data
        """
        return {
            'description': self.description_edit.text(),
            'quantity': self.quantity_spin.value(),
            'unit_price': self.unit_price_spin.value(),
            'discount': self.discount_spin.value(),
            'tax': self.tax_spin.value(),
            'total': self.total_spin.value()
        }