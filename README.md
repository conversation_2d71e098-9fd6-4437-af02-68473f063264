# فوترها - نظام إدارة الفواتير ونقاط البيع

تطبيق شامل موجه للمشاريع الصغيرة والشركات التي تحتاج إلى نظام متكامل لإدارة الفواتير وحسابات العملاء ونقاط البيع، مع واجهة احترافية وسهلة الاستخدام، قاعدة بيانات داخلية وخالية من الحاجة لأي استضافة خارجية.

## حقوق الملكية
حقوق الملكية لشركة "Hadou Design"

## الميزات الأساسية

### إنشاء الفواتير
- إمكانية إنشاء فواتير جديدة بسرعة
- إضافة تفاصيل الفاتورة مثل: اسم العميل، المنتجات أو الخدمات، الكمية، السعر، الخصومات
- حساب إجمالي الفاتورة بشكل تلقائي

### إدارة العملاء
- إضافة وتحرير بيانات العملاء
- تخزين بيانات العميل مثل الاسم، البريد الإلكتروني، رقم الهاتف، العنوان
- إمكانية البحث والتصفية بين العملاء

### قاعدة بيانات داخلية (SQLite)
- قاعدة بيانات محلية تخزن جميع المعلومات بشكل آمن
- يمكن حفظ واسترجاع بيانات الفواتير والعملاء بسرعة

### التقارير والإحصائيات
- عرض تقارير شهرية أو سنوية للمبيعات
- إمكانية تصدير التقارير إلى صيغة PDF أو Excel

### إعدادات التطبيق
- تخصيص العملة واللغة
- تخصيص الشعار والمظهر العام للواجهة لتتناسب مع هوية العمل

### نظام نقاط البيع (POS)
- واجهة نقاط بيع سريعة وسهلة الاستخدام
- دعم الطابعات الحرارية
- دعم قارئات الباركود
- إدارة الكاشير والمستخدمين
- تكامل مع نظام المخزون والفواتير

## متطلبات النظام
- Python 3.6 أو أحدث
- المكتبات المطلوبة موجودة في ملف requirements.txt

## التثبيت

1. قم بتثبيت Python من [الموقع الرسمي](https://www.python.org/downloads/)
2. قم بتنزيل أو استنساخ هذا المستودع
3. افتح موجه الأوامر (Command Prompt) أو Terminal في مجلد المشروع
4. قم بتثبيت المكتبات المطلوبة:

```
pip install -r requirements.txt
```

5. قم بتشغيل التطبيق:

```
python main.py
```

## هيكل المشروع

```
فوترها/
├── main.py                  # نقطة بداية التطبيق
├── requirements.txt         # المكتبات المطلوبة
├── database/                # مجلد قاعدة البيانات
│   ├── db_manager.py        # إدارة الاتصال بقاعدة البيانات
│   ├── schema.py            # مخطط قاعدة البيانات
│   ├── pos_schema.py        # مخطط قاعدة بيانات نظام نقاط البيع
│   └── pos_manager.py       # إدارة عمليات نظام نقاط البيع
├── models/                  # مجلد النماذج
│   ├── customer.py          # نموذج العميل
│   ├── invoice.py           # نموذج الفاتورة
│   ├── invoice_item.py      # نموذج عنصر الفاتورة
│   ├── user.py              # نموذج المستخدم
│   ├── pos_session.py       # نموذج جلسة نقاط البيع
│   └── pos_transaction.py   # نموذج معاملة نقاط البيع
├── ui/                      # مجلد واجهة المستخدم
│   ├── main_window.py       # النافذة الرئيسية
│   ├── customers_view.py    # واجهة إدارة العملاء
│   ├── invoices_view.py     # واجهة إدارة الفواتير
│   ├── invoice_editor.py    # محرر الفواتير
│   ├── reports_view.py      # واجهة التقارير
│   ├── settings_view.py     # واجهة الإعدادات
│   ├── pos_view.py          # واجهة نظام نقاط البيع
│   ├── pos_login_dialog.py  # واجهة تسجيل الدخول لنظام نقاط البيع
│   └── pos_session_dialog.py # واجهة جلسات نظام نقاط البيع
├── utils/                   # مجلد الأدوات المساعدة
│   ├── pdf_generator.py     # إنشاء ملفات PDF
│   └── excel_exporter.py    # تصدير إلى Excel
└── resources/               # مجلد الموارد
    ├── styles.qss           # أنماط CSS للتطبيق
    └── icons/               # أيقونات التطبيق
```

## المساهمة
نرحب بالمساهمات لتحسين هذا التطبيق. يرجى إرسال طلبات السحب (Pull Requests) أو فتح مشكلة (Issue) لمناقشة التغييرات المقترحة.

## الترخيص
جميع الحقوق محفوظة لشركة Hadou Design
