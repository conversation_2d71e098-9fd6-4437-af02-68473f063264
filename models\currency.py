#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Currency Model for فوترها (Fawterha)
Represents a currency in the system
"""

from datetime import datetime


class Currency:
    """Currency model class."""

    def __init__(self, id=None, code="", name="", symbol="", exchange_rate=1.0,
                 is_primary=False, is_active=True, created_at=None, updated_at=None):
        """Initialize a currency.

        Args:
            id (int, optional): Currency ID
            code (str): Currency code (e.g., USD, EUR)
            name (str): Currency name
            symbol (str): Currency symbol
            exchange_rate (float): Exchange rate relative to primary currency
            is_primary (bool): Whether this is the primary currency
            is_active (bool): Whether this currency is active
            created_at (datetime, optional): Creation timestamp
            updated_at (datetime, optional): Last update timestamp
        """
        self.id = id
        self.code = code
        self.name = name
        self.symbol = symbol
        self.exchange_rate = exchange_rate
        self.is_primary = is_primary
        self.is_active = is_active
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create a Currency object from a database row.

        Args:
            row (tuple or dict): Database row containing currency data

        Returns:
            Currency: Currency object
        """
        # Handle both tuple and dictionary formats
        if isinstance(row, dict):
            return cls(
                id=row.get('id'),
                code=row.get('code', ''),
                name=row.get('name', ''),
                symbol=row.get('symbol', ''),
                exchange_rate=row.get('exchange_rate', 1.0),
                is_primary=bool(row.get('is_primary', False)),
                is_active=bool(row.get('is_active', True)),
                created_at=row.get('created_at'),
                updated_at=row.get('updated_at')
            )
        else:
            # Handle tuple format (indexed access)
            try:
                return cls(
                    id=row[0],
                    code=row[1],
                    name=row[2],
                    symbol=row[3],
                    exchange_rate=row[4],
                    is_primary=bool(row[5]),
                    is_active=bool(row[6]),
                    created_at=row[7],
                    updated_at=row[8]
                )
            except (IndexError, TypeError) as e:
                print(f"Error creating Currency from row: {e}")
                print(f"Row data: {row}")
                # Return a default currency object
                return cls(
                    id=None,
                    code="EGP",
                    name="الجنيه المصري",
                    symbol="ج.م",
                    exchange_rate=1.0,
                    is_primary=True,
                    is_active=True
                )

    def to_dict(self):
        """Convert the currency to a dictionary.

        Returns:
            dict: Dictionary representation of the currency
        """
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'symbol': self.symbol,
            'exchange_rate': self.exchange_rate,
            'is_primary': self.is_primary,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def __str__(self):
        """Return a string representation of the currency.

        Returns:
            str: String representation
        """
        return f"{self.name} ({self.code})"

    def convert_to_primary(self, amount):
        """Convert an amount from this currency to the primary currency.

        Args:
            amount (float): Amount in this currency

        Returns:
            float: Amount in primary currency
        """
        if self.is_primary:
            return amount

        # For example, if exchange_rate is 30.9 for USD to EGP (primary),
        # then 100 USD = 100 * 30.9 = 3090 EGP
        result = amount * self.exchange_rate
        print(f"Converting {amount} {self.code} to primary: {amount} * {self.exchange_rate} = {result}")
        return result

    def convert_from_primary(self, amount):
        """Convert an amount from primary currency to this currency.

        Args:
            amount (float): Amount in primary currency

        Returns:
            float: Amount in this currency
        """
        if self.is_primary:
            return amount

        # For example, if exchange_rate is 50.0 for USD to EGP (primary),
        # then 5000 EGP = 5000 / 50.0 = 100 USD
        result = amount / self.exchange_rate
        print(f"Converting {amount} EGP to {self.code}: {amount} / {self.exchange_rate} = {result}")
        return result
