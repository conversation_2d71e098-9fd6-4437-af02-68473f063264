#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Professional Invoice Editor for فوترها (Fawterha)
A clean, responsive, and well-organized invoice editor with perfect alignment and spacing
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel,
    QLineEdit, QDateEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QPushButton, QDoubleSpinBox, QTextEdit, QMessageBox, QFrame,
    QHeaderView, QAbstractItemView, QWidget, QScrollArea,
    QSizePolicy
)
from PySide6.QtCore import Qt, QDate

from models.invoice_item import InvoiceItem
from ui.products_view import ProductsView
from utils.currency_helper import convert_currency, format_thousands
from ui.invoice_item_dialog import InvoiceItemDialog

class ProfessionalInvoiceEditor(QDialog):
    """A professional, responsive invoice editor with perfect alignment and spacing."""

    def __init__(self, parent=None, db_manager=None, invoice=None, items=None, view_only=False, currency_manager=None):
        """Initialize the invoice editor.

        Args:
            parent: Parent widget
            db_manager: Database manager
            invoice: Invoice object to edit
            items: List of invoice items
            view_only: Whether to show in view-only mode
            currency_manager: Currency manager
        """
        super().__init__(parent)

        # Store parameters
        self.db_manager = db_manager
        self.invoice = invoice
        self.view_only = view_only
        self.currency_manager = currency_manager
        self.items_list = []
        self.primary_currency = None
        self.current_currency = None

        # Set window properties
        self.setWindowTitle("فاتورة جديدة" if not invoice else f"فاتورة {invoice.invoice_number}")
        self.setMinimumSize(1200, 800)  # Set minimum size but allow resizing
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        self.resize(1400, 900)  # Default size

        # Define color scheme
        self.colors = {
            'primary': '#1976D2',
            'primary_light': '#BBDEFB',
            'primary_dark': '#0D47A1',
            'accent': '#FF5722',
            'success': '#4CAF50',
            'danger': '#F44336',
            'text': '#212121',
            'text_secondary': '#757575',
            'divider': '#BDBDBD',
            'background': '#F5F5F5',
            'card': '#FFFFFF',
            'card_alt': '#F9F9F9',
            'highlight': '#FFF8E1'
        }

        # Set window background
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.colors['background']};
            }}
        """)

        # Set up the UI
        self.setup_ui()

        # Load items if provided
        if items:
            for item_row in items:
                item = InvoiceItem.from_db_row(item_row)
                self.items_list.append(item)
                self.add_item_to_table(item)

        # Calculate initial totals
        self.calculate_totals()

        # Set view-only mode if specified
        if view_only:
            self.set_view_only_mode()

    def setup_ui(self):
        """Set up the user interface with responsive layout and proper spacing."""
        # Create main layout with proper spacing
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Create a scroll area to ensure all content is accessible regardless of window size
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # Create a container widget for the scroll area
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(0, 0, 0, 0)
        scroll_layout.setSpacing(20)

        # Create header section (invoice info and customer)
        header_widget = self.create_header_section()
        scroll_layout.addWidget(header_widget)

        # Create products and services section
        items_widget = self.create_items_section()
        scroll_layout.addWidget(items_widget)

        # Create summary section
        summary_widget = self.create_summary_section()
        scroll_layout.addWidget(summary_widget)

        # Create notes section
        notes_widget = self.create_notes_section()
        scroll_layout.addWidget(notes_widget)

        # Set the scroll content and add to main layout
        scroll_area.setWidget(scroll_content)
        main_layout.addWidget(scroll_area)

        # Create footer with action buttons
        footer_widget = self.create_footer_section()
        main_layout.addWidget(footer_widget)

    def create_header_section(self):
        """Create the invoice header section with responsive layout."""
        # Create a container with frame
        header_widget = QFrame()
        header_widget.setFrameShape(QFrame.StyledPanel)
        header_widget.setStyleSheet(f"""
            QFrame {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
            QLabel {{
                font-weight: bold;
                color: {self.colors['text']};
            }}
        """)

        # Create layout
        header_layout = QVBoxLayout(header_widget)
        header_layout.setContentsMargins(15, 15, 15, 15)
        header_layout.setSpacing(15)

        # Add section title
        title_label = QLabel("معلومات الفاتورة")
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            color: {self.colors['primary_dark']};
            padding-bottom: 5px;
            border-bottom: 2px solid {self.colors['primary']};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        header_layout.addWidget(title_label)

        # Create form layout in a grid for responsive alignment
        form_layout = QGridLayout()
        form_layout.setHorizontalSpacing(20)
        form_layout.setVerticalSpacing(15)

        # Invoice Number
        invoice_number_label = QLabel("رقم الفاتورة:")
        invoice_number_label.setStyleSheet("font-size: 12pt;")
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        self.invoice_number_edit.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        if self.invoice:
            self.invoice_number_edit.setText(self.invoice.invoice_number)
        form_layout.addWidget(invoice_number_label, 0, 0)
        form_layout.addWidget(self.invoice_number_edit, 0, 1)

        # Customer
        customer_label = QLabel("العميل:")
        customer_label.setStyleSheet("font-size: 12pt;")
        self.customer_combo = QComboBox()
        self.customer_combo.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        self.load_customers()
        if self.invoice and self.invoice.customer_id:
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == self.invoice.customer_id:
                    self.customer_combo.setCurrentIndex(i)
                    break
        form_layout.addWidget(customer_label, 0, 2)
        form_layout.addWidget(self.customer_combo, 0, 3)

        # Issue Date
        issue_date_label = QLabel("تاريخ الإصدار:")
        issue_date_label.setStyleSheet("font-size: 12pt;")
        self.issue_date_edit = QDateEdit()
        self.issue_date_edit.setCalendarPopup(True)
        self.issue_date_edit.setDate(QDate.currentDate())
        self.issue_date_edit.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        if self.invoice and self.invoice.issue_date:
            self.issue_date_edit.setDate(QDate.fromString(self.invoice.issue_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        form_layout.addWidget(issue_date_label, 1, 0)
        form_layout.addWidget(self.issue_date_edit, 1, 1)

        # Due Date
        due_date_label = QLabel("تاريخ الاستحقاق:")
        due_date_label.setStyleSheet("font-size: 12pt;")
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        self.due_date_edit.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        if self.invoice and self.invoice.due_date:
            self.due_date_edit.setDate(QDate.fromString(self.invoice.due_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))
        form_layout.addWidget(due_date_label, 1, 2)
        form_layout.addWidget(self.due_date_edit, 1, 3)

        # Status
        status_label = QLabel("الحالة:")
        status_label.setStyleSheet("font-size: 12pt;")
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        self.status_combo.addItem("مسودة", "draft")
        self.status_combo.addItem("منتظرة", "pending")
        self.status_combo.addItem("مدفوع جزئياً", "partially_paid")
        self.status_combo.addItem("مدفوعة", "paid")
        self.status_combo.addItem("ملغاة", "cancelled")
        if self.invoice and self.invoice.status:
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == self.invoice.status:
                    self.status_combo.setCurrentIndex(i)
                    break
        form_layout.addWidget(status_label, 2, 0)
        form_layout.addWidget(self.status_combo, 2, 1)

        # Currency
        currency_label = QLabel("العملة:")
        currency_label.setStyleSheet("font-size: 12pt;")
        self.currency_combo = QComboBox()
        self.currency_combo.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        if self.currency_manager:
            self.load_currencies()
            self.currency_combo.currentIndexChanged.connect(self.on_currency_changed)
        form_layout.addWidget(currency_label, 2, 2)
        form_layout.addWidget(self.currency_combo, 2, 3)

        # Set column stretch to make the form responsive
        form_layout.setColumnStretch(0, 1)  # Label
        form_layout.setColumnStretch(1, 2)  # Field
        form_layout.setColumnStretch(2, 1)  # Label
        form_layout.setColumnStretch(3, 2)  # Field

        header_layout.addLayout(form_layout)

        return header_widget

    def create_items_section(self):
        """Create the products and services section with responsive table."""
        # Create a container with frame
        items_widget = QFrame()
        items_widget.setFrameShape(QFrame.StyledPanel)
        items_widget.setStyleSheet(f"""
            QFrame {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
            QLabel {{
                font-weight: bold;
                color: {self.colors['text']};
            }}
        """)

        # Create layout
        items_layout = QVBoxLayout(items_widget)
        items_layout.setContentsMargins(15, 15, 15, 15)
        items_layout.setSpacing(15)

        # Add section title
        title_label = QLabel("المنتجات والخدمات")
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            color: {self.colors['primary_dark']};
            padding-bottom: 5px;
            border-bottom: 2px solid {self.colors['primary']};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        items_layout.addWidget(title_label)

        # Create buttons layout
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # Add item button
        self.add_item_button = QPushButton("إضافة منتج/خدمة يدوياً")
        self.add_item_button.setStyleSheet(f"""
            background-color: {self.colors['primary']};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            font-size: 12pt;
            font-weight: bold;
        """)
        self.add_item_button.clicked.connect(self.add_item)
        buttons_layout.addWidget(self.add_item_button)

        # Select product button
        self.select_product_button = QPushButton("اختيار من المنتجات والخدمات")
        self.select_product_button.setStyleSheet(f"""
            background-color: {self.colors['accent']};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 15px;
            font-size: 12pt;
            font-weight: bold;
        """)
        self.select_product_button.clicked.connect(self.select_product)
        buttons_layout.addWidget(self.select_product_button)

        items_layout.addLayout(buttons_layout)

        # Create items table
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "الوصف", "الكمية", "سعر الوحدة", "الخصم", "الضريبة", "الإجمالي", ""
        ])

        # Configure table appearance
        self.items_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.colors['card']};
                alternate-background-color: {self.colors['background']};
                border: 1px solid {self.colors['divider']};
                gridline-color: {self.colors['divider']};
                selection-background-color: {self.colors['primary_light']};
                selection-color: {self.colors['text']};
                font-size: 12pt;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['divider']};
            }}
            QHeaderView::section {{
                background-color: {self.colors['primary']};
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 12pt;
            }}
        """)

        # Configure column sizes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 6):
            self.items_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)
        self.items_table.setColumnWidth(6, 120)  # Column for buttons

        # Configure table behavior
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setShowGrid(True)
        self.items_table.setAlternatingRowColors(True)

        # Set size policy to make table responsive
        self.items_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.items_table.setMinimumHeight(200)

        items_layout.addWidget(self.items_table)

        return items_widget

    def create_summary_section(self):
        """Create the invoice summary section with perfect alignment and no overlapping."""
        # Create a container with frame
        summary_widget = QFrame()
        summary_widget.setFrameShape(QFrame.StyledPanel)
        summary_widget.setStyleSheet(f"""
            QFrame {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
            QLabel {{
                font-weight: bold;
                color: {self.colors['text']};
            }}
        """)

        # Create layout
        summary_layout = QVBoxLayout(summary_widget)
        summary_layout.setContentsMargins(15, 15, 15, 15)
        summary_layout.setSpacing(15)

        # Add section title
        title_label = QLabel("ملخص الفاتورة")
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            color: {self.colors['primary_dark']};
            padding-bottom: 5px;
            border-bottom: 2px solid {self.colors['primary']};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        summary_layout.addWidget(title_label)

        # Create a horizontal layout to organize the summary section
        h_layout = QHBoxLayout()
        h_layout.setSpacing(20)

        # Left side - Totals
        totals_frame = QFrame()
        totals_frame.setFrameShape(QFrame.StyledPanel)
        totals_frame.setStyleSheet(f"""
            background-color: {self.colors['card_alt']};
            border-radius: 6px;
            border: 1px solid {self.colors['divider']};
            padding: 10px;
        """)

        # Create grid layout for perfect alignment
        totals_layout = QGridLayout(totals_frame)
        totals_layout.setContentsMargins(10, 10, 10, 10)
        totals_layout.setHorizontalSpacing(15)
        totals_layout.setVerticalSpacing(10)

        # Row 0: Subtotal
        subtotal_label = QLabel("الإجمالي الفرعي:")
        subtotal_label.setStyleSheet("font-size: 12pt;")
        subtotal_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.subtotal_value = QLineEdit("0.00")
        self.subtotal_value.setReadOnly(True)
        self.subtotal_value.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)

        totals_layout.addWidget(subtotal_label, 0, 0)
        totals_layout.addWidget(self.subtotal_value, 0, 1)

        # Row 1: Discount
        discount_label = QLabel("إجمالي الخصم:")
        discount_label.setStyleSheet("font-size: 12pt;")
        discount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(9999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.setButtonSymbols(QDoubleSpinBox.NoButtons)
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        self.discount_spin.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)

        totals_layout.addWidget(discount_label, 1, 0)
        totals_layout.addWidget(self.discount_spin, 1, 1)

        # Row 2: Tax
        tax_label = QLabel("إجمالي الضريبة:")
        tax_label.setStyleSheet("font-size: 12pt;")
        tax_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMinimum(0.0)
        self.tax_spin.setMaximum(9999999.99)
        self.tax_spin.setDecimals(2)
        self.tax_spin.setButtonSymbols(QDoubleSpinBox.NoButtons)
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        self.tax_spin.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)

        totals_layout.addWidget(tax_label, 2, 0)
        totals_layout.addWidget(self.tax_spin, 2, 1)

        # Row 3: Separator
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setStyleSheet(f"background-color: {self.colors['divider']};")

        totals_layout.addWidget(separator, 3, 0, 1, 2)

        # Row 4: Final Total (highlighted)
        total_label = QLabel("الإجمالي النهائي:")
        total_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['primary_dark']};
        """)
        total_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.total_value = QLineEdit("0.00")
        self.total_value.setReadOnly(True)
        self.total_value.setStyleSheet(f"""
            padding: 8px;
            font-size: 14pt;
            font-weight: bold;
            background-color: {self.colors['highlight']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['primary_dark']};
        """)

        totals_layout.addWidget(total_label, 4, 0)
        totals_layout.addWidget(self.total_value, 4, 1)

        # Set column stretch to ensure proper alignment
        totals_layout.setColumnStretch(0, 1)  # Label column
        totals_layout.setColumnStretch(1, 2)  # Value column

        # Right side - Payment
        payment_frame = QFrame()
        payment_frame.setFrameShape(QFrame.StyledPanel)
        payment_frame.setStyleSheet(f"""
            background-color: {self.colors['card_alt']};
            border-radius: 6px;
            border: 1px solid {self.colors['divider']};
            padding: 10px;
        """)

        # Create grid layout for perfect alignment
        payment_layout = QGridLayout(payment_frame)
        payment_layout.setContentsMargins(10, 10, 10, 10)
        payment_layout.setHorizontalSpacing(15)
        payment_layout.setVerticalSpacing(10)

        # Payment title
        payment_title = QLabel("معلومات الدفع")
        payment_title.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['primary_dark']};
            padding-bottom: 5px;
            border-bottom: 1px solid {self.colors['primary']};
        """)
        payment_title.setAlignment(Qt.AlignCenter)

        payment_layout.addWidget(payment_title, 0, 0, 1, 2)

        # Row 1: Paid Amount
        paid_label = QLabel("المدفوع:")
        paid_label.setStyleSheet("font-size: 12pt;")
        paid_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.amount_paid_spin = QDoubleSpinBox()
        self.amount_paid_spin.setMinimum(0.0)
        self.amount_paid_spin.setMaximum(9999999.99)
        self.amount_paid_spin.setDecimals(2)
        self.amount_paid_spin.setButtonSymbols(QDoubleSpinBox.NoButtons)
        self.amount_paid_spin.valueChanged.connect(self.calculate_totals)
        self.amount_paid_spin.setStyleSheet(f"""
            padding: 8px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)

        payment_layout.addWidget(paid_label, 1, 0)
        payment_layout.addWidget(self.amount_paid_spin, 1, 1)

        # Row 2: Due Amount (highlighted)
        due_label = QLabel("المتبقي:")
        due_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['primary_dark']};
        """)
        due_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.amount_due_spin = QDoubleSpinBox()
        self.amount_due_spin.setMinimum(0.0)
        self.amount_due_spin.setMaximum(9999999.99)
        self.amount_due_spin.setDecimals(2)
        self.amount_due_spin.setReadOnly(True)
        self.amount_due_spin.setButtonSymbols(QDoubleSpinBox.NoButtons)
        self.amount_due_spin.setStyleSheet(f"""
            padding: 8px;
            font-size: 14pt;
            font-weight: bold;
            background-color: {self.colors['highlight']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['primary_dark']};
        """)

        payment_layout.addWidget(due_label, 2, 0)
        payment_layout.addWidget(self.amount_due_spin, 2, 1)

        # Set column stretch to ensure proper alignment
        payment_layout.setColumnStretch(0, 1)  # Label column
        payment_layout.setColumnStretch(1, 2)  # Value column

        # Add frames to horizontal layout with responsive sizing
        h_layout.addWidget(totals_frame, 3)  # 60% width
        h_layout.addWidget(payment_frame, 2)  # 40% width

        summary_layout.addLayout(h_layout)

        return summary_widget

    def create_notes_section(self):
        """Create the notes section."""
        # Create a container with frame
        notes_widget = QFrame()
        notes_widget.setFrameShape(QFrame.StyledPanel)
        notes_widget.setStyleSheet(f"""
            QFrame {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
            QLabel {{
                font-weight: bold;
                color: {self.colors['text']};
            }}
        """)

        # Create layout
        notes_layout = QVBoxLayout(notes_widget)
        notes_layout.setContentsMargins(15, 15, 15, 15)
        notes_layout.setSpacing(10)

        # Add section title
        title_label = QLabel("ملاحظات")
        title_label.setStyleSheet(f"""
            font-size: 16pt;
            color: {self.colors['primary_dark']};
            padding-bottom: 5px;
            border-bottom: 2px solid {self.colors['primary']};
        """)
        title_label.setAlignment(Qt.AlignCenter)
        notes_layout.addWidget(title_label)

        # Add notes text edit
        self.notes_edit = QTextEdit()
        self.notes_edit.setStyleSheet(f"""
            padding: 10px;
            font-size: 12pt;
            background-color: {self.colors['background']};
            border: 1px solid {self.colors['divider']};
            border-radius: 4px;
        """)
        self.notes_edit.setMinimumHeight(100)
        self.notes_edit.setMaximumHeight(150)

        if self.invoice and self.invoice.notes:
            self.notes_edit.setText(self.invoice.notes)

        notes_layout.addWidget(self.notes_edit)

        return notes_widget

    def create_footer_section(self):
        """Create the footer section with action buttons."""
        # Create a container with frame
        footer_widget = QFrame()
        footer_widget.setFrameShape(QFrame.StyledPanel)
        footer_widget.setStyleSheet(f"""
            QFrame {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 1px solid {self.colors['divider']};
            }}
        """)

        # Create layout
        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(15, 15, 15, 15)
        footer_layout.setSpacing(15)

        # Add spacer to push buttons to the right
        footer_layout.addStretch(1)

        # Cancel button
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet(f"""
            background-color: {self.colors['danger']};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 14pt;
            font-weight: bold;
        """)
        self.cancel_button.setMinimumWidth(150)
        self.cancel_button.clicked.connect(self.reject)

        # Create invoice button
        self.save_button = QPushButton("إنشاء فاتورة")
        self.save_button.setStyleSheet(f"""
            background-color: {self.colors['success']};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 10px 20px;
            font-size: 14pt;
            font-weight: bold;
        """)
        self.save_button.setMinimumWidth(150)
        self.save_button.clicked.connect(self.save_invoice)

        footer_layout.addWidget(self.cancel_button)
        footer_layout.addWidget(self.save_button)

        return footer_widget

    def load_customers(self):
        """Load customers from the database."""
        if not self.db_manager:
            return

        self.customer_combo.clear()

        query = "SELECT id, name FROM customers ORDER BY name"
        rows = self.db_manager.execute_query(query)

        for row in rows:
            self.customer_combo.addItem(row['name'], row['id'])

    def load_currencies(self):
        """Load currencies from the currency manager."""
        if not self.currency_manager:
            return

        self.currency_combo.clear()

        # Get primary currency
        self.primary_currency = self.currency_manager.get_primary_currency()

        # Get all currencies
        currencies = self.currency_manager.get_all_currencies()

        for currency in currencies:
            self.currency_combo.addItem(f"{currency.name} ({currency.symbol})", currency.id)

            # Set current currency
            if self.invoice and self.invoice.currency_id == currency.id:
                self.current_currency = currency
                self.currency_combo.setCurrentIndex(self.currency_combo.count() - 1)
            elif not self.invoice and not self.current_currency and currency.id == self.primary_currency.id:
                self.current_currency = currency
                self.currency_combo.setCurrentIndex(self.currency_combo.count() - 1)



    def on_currency_changed(self, index):
        """Handle currency change."""
        if index < 0 or not self.currency_manager:
            return

        # Get the selected currency ID
        currency_id = self.currency_combo.itemData(index)

        # Get the currency object
        new_currency = self.currency_manager.get_currency_by_id(currency_id)

        if not new_currency or (self.current_currency and new_currency.id == self.current_currency.id):
            return

        # Store old currency for conversion
        old_currency = self.current_currency
        self.current_currency = new_currency

        # Update currency symbols in the UI
        currency_suffix = f" {new_currency.symbol}"

        # Update items in the list
        if old_currency:
            for item in self.items_list:
                # If this is a product with original price stored, recalculate from primary currency
                if hasattr(item, 'product_id') and item.product_id and hasattr(item, 'original_price'):
                    # Get primary currency
                    primary_currency = self.currency_manager.get_primary_currency()

                    if primary_currency:
                        # The original_price is already in primary currency (EGP)
                        # We need to convert it to the new currency
                        item.unit_price = item.original_price / new_currency.exchange_rate

                        # Recalculate total based on new unit price
                        item.total = item.quantity * item.unit_price - item.discount + item.tax
                else:
                    # For non-product items or items without original price, convert directly
                    item.unit_price = convert_currency(item.unit_price, old_currency.exchange_rate, new_currency.exchange_rate)
                    item.discount = convert_currency(item.discount, old_currency.exchange_rate, new_currency.exchange_rate)
                    item.tax = convert_currency(item.tax, old_currency.exchange_rate, new_currency.exchange_rate)
                    item.total = convert_currency(item.total, old_currency.exchange_rate, new_currency.exchange_rate)

            # Update table
            self.items_table.setRowCount(0)
            for item in self.items_list:
                self.add_item_to_table(item)

        # Update spinbox suffixes
        self.discount_spin.setSuffix(currency_suffix)
        self.tax_spin.setSuffix(currency_suffix)
        self.amount_paid_spin.setSuffix(currency_suffix)
        self.amount_due_spin.setSuffix(currency_suffix)

        # Recalculate totals
        self.calculate_totals()

    def calculate_totals(self):
        """Calculate invoice totals."""
        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Calculate subtotal
        subtotal = sum(item.total for item in self.items_list)

        # Get discount and tax
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()

        # Calculate total
        total = subtotal - discount + tax

        # Format numbers with thousand separators
        formatted_subtotal = format_thousands(f"{subtotal:.2f}")
        formatted_total = format_thousands(f"{total:.2f}")

        # Update labels with formatted values
        self.subtotal_value.setText(f"{formatted_subtotal} {currency_symbol}")
        self.total_value.setText(f"{formatted_total} {currency_symbol}")

        # Update amount due
        amount_paid = self.amount_paid_spin.value()
        amount_due = max(0, total - amount_paid)  # Ensure amount due is not negative

        # Update amount due without triggering signals
        self.amount_due_spin.blockSignals(True)
        self.amount_due_spin.setValue(amount_due)
        self.amount_due_spin.blockSignals(False)

        # Update status based on payment
        if amount_paid <= 0:
            self.status_combo.setCurrentIndex(self.status_combo.findData("draft"))
        elif amount_paid < total:
            self.status_combo.setCurrentIndex(self.status_combo.findData("partially_paid"))
        else:
            self.status_combo.setCurrentIndex(self.status_combo.findData("paid"))

    def add_item(self):
        """Add a new item to the invoice manually."""
        dialog = InvoiceItemDialog(self)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف المنتج أو الخدمة")
                return

            # Create item object
            item = InvoiceItem(
                description=item_data['description'],
                quantity=item_data['quantity'],
                unit_price=item_data['unit_price'],
                discount=item_data['discount'],
                tax=item_data['tax'],
                total=item_data['total']
            )

            # Store original price in primary currency for reference if we have currency manager
            if hasattr(self, 'currency_manager') and self.currency_manager and self.current_currency:
                # Get primary currency
                primary_currency = self.currency_manager.get_primary_currency()

                if primary_currency and not self.current_currency.is_primary:
                    # Convert to primary currency (EGP)
                    # If current currency is USD with rate 50.6, and unit_price is 10 USD
                    # Then original_price in EGP = 10 * 50.6 = 506 EGP
                    item.original_price = item_data['unit_price'] * self.current_currency.exchange_rate
                else:
                    # Already in primary currency
                    item.original_price = item_data['unit_price']

            # Add to list and table
            self.items_list.append(item)
            self.add_item_to_table(item)

            # Recalculate totals
            self.calculate_totals()

    def select_product(self):
        """Select a product or service from the products list."""
        # Create products view in select mode
        products_dialog = QDialog(self)
        products_dialog.setWindowTitle("اختيار منتج أو خدمة")
        products_dialog.setMinimumSize(800, 600)

        # Set RTL layout direction
        products_dialog.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        layout = QVBoxLayout(products_dialog)

        # Create products view
        products_view = ProductsView(self.db_manager, select_mode=True)
        # Pass currency manager if available
        if hasattr(self, 'currency_manager') and self.currency_manager:
            products_view.currency_manager = self.currency_manager
        # Connect the product_selected signal to a local slot
        products_view.product_selected.connect(self.on_product_selected)
        layout.addWidget(products_view)

        # Store the products_view reference to access it later
        self.products_view = products_view

        # Create button box
        button_layout = QHBoxLayout()
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(products_dialog.reject)
        button_layout.addWidget(cancel_button)

        select_button = QPushButton("اختيار")
        select_button.clicked.connect(products_dialog.accept)
        button_layout.addWidget(select_button)

        layout.addLayout(button_layout)

        # Store the selected product temporarily
        self.selected_product_temp = None

        # Show dialog
        if products_dialog.exec():
            # Check if a product was selected via the signal
            if self.selected_product_temp:
                selected_product = self.selected_product_temp
                self.selected_product_temp = None  # Clear the temporary storage

                # Get product price in current currency
                product_price = selected_product.price

                # Convert product price to current currency if needed
                if hasattr(self, 'currency_manager') and self.currency_manager and self.current_currency:
                    # Get primary currency
                    primary_currency = self.currency_manager.get_primary_currency()

                    if primary_currency and not self.current_currency.is_primary:
                        # The product_price is already in primary currency (EGP)
                        # We need to convert it to the current currency
                        product_price = product_price / self.current_currency.exchange_rate

                # Create item from product with converted price
                item = InvoiceItem(
                    description=selected_product.name,
                    quantity=1,
                    unit_price=product_price,
                    discount=0,
                    tax=0,
                    total=product_price
                )

                # Add product_id and is_product attributes
                item.product_id = selected_product.id
                item.is_product = selected_product.type == "product"
                item.track_inventory = selected_product.track_inventory if hasattr(selected_product, 'track_inventory') else False

                # Store original price in primary currency for reference
                item.original_price = selected_product.price

                # Add to list and table
                self.items_list.append(item)
                self.add_item_to_table(item)

                # Recalculate totals
                self.calculate_totals()

    def on_product_selected(self, product):
        """Handle product selection from the products view.

        Args:
            product (Product): The selected product
        """
        # Store the selected product
        self.selected_product_temp = product

        # Close the dialog automatically when a product is selected
        if hasattr(self, 'products_view') and self.products_view:
            # Find the parent dialog
            parent_dialog = self.sender().parent()
            while parent_dialog and not isinstance(parent_dialog, QDialog):
                parent_dialog = parent_dialog.parent()

            # Accept the dialog if found
            if parent_dialog and isinstance(parent_dialog, QDialog):
                parent_dialog.accept()

    def add_item_to_table(self, item):
        """Add an item to the table."""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Format with thousand separators
        formatted_unit_price = format_thousands(f"{item.unit_price:.2f}")
        formatted_discount = format_thousands(f"{item.discount:.2f}")
        formatted_tax = format_thousands(f"{item.tax:.2f}")
        formatted_total = format_thousands(f"{item.total:.2f}")

        # Set item data
        self.items_table.setItem(row, 0, QTableWidgetItem(item.description))
        self.items_table.setItem(row, 1, QTableWidgetItem(f"{item.quantity:.2f}"))
        self.items_table.setItem(row, 2, QTableWidgetItem(f"{formatted_unit_price} {currency_symbol}"))
        self.items_table.setItem(row, 3, QTableWidgetItem(f"{formatted_discount} {currency_symbol}"))
        self.items_table.setItem(row, 4, QTableWidgetItem(f"{formatted_tax} {currency_symbol}"))
        self.items_table.setItem(row, 5, QTableWidgetItem(f"{formatted_total} {currency_symbol}"))

        # Create action buttons
        actions_widget = QWidget()
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(2, 2, 2, 2)
        actions_layout.setSpacing(5)

        # Edit button
        edit_button = QPushButton("تعديل")
        edit_button.setStyleSheet(f"""
            background-color: {self.colors['primary']};
            color: white;
            border: none;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 10pt;
        """)
        edit_button.clicked.connect(lambda _, r=row: self.edit_item(r))

        # Delete button
        delete_button = QPushButton("حذف")
        delete_button.setStyleSheet(f"""
            background-color: {self.colors['danger']};
            color: white;
            border: none;
            border-radius: 3px;
            padding: 3px 8px;
            font-size: 10pt;
        """)
        delete_button.clicked.connect(lambda _, r=row: self.delete_item(r))

        actions_layout.addWidget(edit_button)
        actions_layout.addWidget(delete_button)

        self.items_table.setCellWidget(row, 6, actions_widget)

    def edit_item(self, row):
        """Edit an item in the invoice."""
        if row < 0 or row >= len(self.items_list):
            return

        item = self.items_list[row]
        dialog = InvoiceItemDialog(self, item)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف المنتج أو الخدمة")
                return

            # Update item
            item.description = item_data['description']
            item.quantity = item_data['quantity']
            item.unit_price = item_data['unit_price']
            item.discount = item_data['discount']
            item.tax = item_data['tax']
            item.total = item_data['total']

            # Update original price in primary currency for reference if we have currency manager
            if hasattr(self, 'currency_manager') and self.currency_manager and self.current_currency:
                # Get primary currency
                primary_currency = self.currency_manager.get_primary_currency()

                if primary_currency and not self.current_currency.is_primary:
                    # Convert to primary currency (EGP)
                    # If current currency is USD with rate 50.6, and unit_price is 10 USD
                    # Then original_price in EGP = 10 * 50.6 = 506 EGP
                    item.original_price = item_data['unit_price'] * self.current_currency.exchange_rate
                else:
                    # Already in primary currency
                    item.original_price = item_data['unit_price']

            # Update table
            self.items_table.removeRow(row)
            self.items_table.insertRow(row)
            self.add_item_to_table(item)

            # Recalculate totals
            self.calculate_totals()

    def delete_item(self, row):
        """Delete an item from the invoice."""
        if row < 0 or row >= len(self.items_list):
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا العنصر؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from list
            self.items_list.pop(row)

            # Remove from table
            self.items_table.removeRow(row)

            # Recalculate totals
            self.calculate_totals()

    def save_invoice(self):
        """Save the invoice to the database."""
        # Validate required fields
        if self.customer_combo.currentIndex() < 0:
            QMessageBox.warning(self, "خطأ", "يجب اختيار العميل")
            return

        if len(self.items_list) == 0:
            QMessageBox.warning(self, "خطأ", "يجب إضافة منتج أو خدمة واحدة على الأقل")
            return

        try:
            # Get form data
            customer_id = self.customer_combo.currentData()
            issue_date = self.issue_date_edit.date().toString("yyyy-MM-dd")
            due_date = self.due_date_edit.date().toString("yyyy-MM-dd")
            status = self.status_combo.currentData()
            notes = self.notes_edit.toPlainText()

            # Get totals
            subtotal = sum(item.total for item in self.items_list)
            discount = self.discount_spin.value()
            tax = self.tax_spin.value()
            total = subtotal - discount + tax
            amount_paid = self.amount_paid_spin.value()
            amount_due = self.amount_due_spin.value()

            # Get currency
            currency_id = None
            if self.currency_manager and self.current_currency:
                currency_id = self.current_currency.id

            try:
                if self.invoice and self.invoice.id:
                    # Update existing invoice
                    update_query = """
                    UPDATE invoices
                    SET customer_id = ?, issue_date = ?, due_date = ?, status = ?,
                        subtotal = ?, discount = ?, tax = ?, total = ?,
                        amount_paid = ?, amount_due = ?, notes = ?, currency_id = ?
                    WHERE id = ?
                    """
                    params = (
                        customer_id, issue_date, due_date, status,
                        subtotal, discount, tax, total,
                        amount_paid, amount_due, notes, currency_id,
                        self.invoice.id
                    )
                    self.db_manager.execute_update(update_query, params)

                    # Delete existing items
                    delete_query = "DELETE FROM invoice_items WHERE invoice_id = ?"
                    self.db_manager.execute_update(delete_query, (self.invoice.id,))

                    invoice_id = self.invoice.id
                    invoice_number = self.invoice.invoice_number
                else:
                    # Generate invoice number
                    settings_query = "SELECT value FROM settings WHERE key = ?"
                    prefix_row = self.db_manager.execute_query(settings_query, ("invoice_prefix",))
                    number_row = self.db_manager.execute_query(settings_query, ("next_invoice_number",))

                    prefix = prefix_row[0]['value'] if prefix_row else "INV-"
                    next_number = number_row[0]['value'] if number_row else "1001"

                    invoice_number = f"{prefix}{next_number}"

                    # Insert new invoice
                    insert_query = """
                    INSERT INTO invoices (
                        invoice_number, customer_id, issue_date, due_date, status,
                        subtotal, discount, tax, total, amount_paid, amount_due, notes, currency_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    params = (
                        invoice_number, customer_id, issue_date, due_date, status,
                        subtotal, discount, tax, total, amount_paid, amount_due, notes, currency_id
                    )
                    invoice_id = self.db_manager.execute_insert(insert_query, params)

                    # Increment next invoice number
                    next_number_int = int(next_number) + 1
                    update_query = "UPDATE settings SET value = ? WHERE key = ?"
                    self.db_manager.execute_update(update_query, (str(next_number_int), "next_invoice_number"))

                # Insert items
                for item in self.items_list:
                    item_query = """
                    INSERT INTO invoice_items (
                        invoice_id, description, quantity, unit_price,
                        discount, tax, total, product_id
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    product_id = getattr(item, 'product_id', None)
                    self.db_manager.execute_insert(item_query, (
                        invoice_id, item.description, item.quantity, item.unit_price,
                        item.discount, item.tax, item.total, product_id
                    ))

                # Update inventory if needed
                if hasattr(self, 'update_inventory'):
                    self.update_inventory(invoice_id)

                QMessageBox.information(
                    self,
                    "نجاح",
                    f"تم {'تحديث' if self.invoice and self.invoice.id else 'إنشاء'} الفاتورة {invoice_number} بنجاح"
                )
                self.accept()
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة: {str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ: {str(e)}")

    def update_inventory(self, invoice_id):
        """Update inventory for products in the invoice.

        Args:
            invoice_id (int): ID of the invoice
        """
        try:
            # Check if we need to update inventory
            for item in self.items_list:
                if hasattr(item, 'product_id') and item.product_id and hasattr(item, 'is_product') and item.is_product:
                    # Get product details
                    query = "SELECT type, track_inventory FROM products WHERE id = ?"
                    rows = self.db_manager.execute_query(query, (item.product_id,))

                    if rows and rows[0]['type'] == 'product' and rows[0]['track_inventory']:
                        # Update inventory using the safe helper function
                        from utils.inventory_helper import update_product_stock_safely
                        success = update_product_stock_safely(
                            self.db_manager,
                            item.product_id,
                            -item.quantity,
                            f"Invoice #{invoice_id}",
                            self
                        )
                        print(f"Updated inventory for product ID: {item.product_id}, Quantity: {-item.quantity}, Success: {success}")
        except Exception as e:
            print(f"Error updating inventory: {str(e)}")
            # Don't raise the exception to avoid breaking the invoice creation process
            # Just log it for debugging

    def set_view_only_mode(self):
        """Set the dialog to view-only mode."""
        # Change window title
        self.setWindowTitle(f"عرض الفاتورة {self.invoice.invoice_number}")

        # Disable all editable fields
        self.customer_combo.setEnabled(False)
        self.issue_date_edit.setEnabled(False)
        self.due_date_edit.setEnabled(False)
        self.status_combo.setEnabled(False)
        self.currency_combo.setEnabled(False)
        self.discount_spin.setEnabled(False)
        self.tax_spin.setEnabled(False)
        self.amount_paid_spin.setEnabled(False)
        self.notes_edit.setEnabled(False)

        # Hide action buttons
        self.add_item_button.setVisible(False)
        self.select_product_button.setVisible(False)

        # Change footer buttons
        self.save_button.setText("إغلاق")
        self.save_button.clicked.disconnect()
        self.save_button.clicked.connect(self.accept)
        self.cancel_button.setVisible(False)
