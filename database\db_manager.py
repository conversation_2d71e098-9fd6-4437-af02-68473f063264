#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for فوترها (Fawterha)
Handles database connections and operations
"""

import os
import sqlite3
import time
import random
from database.schema import create_tables


class DatabaseManager:
    """Manages database connections and operations."""

    def __init__(self, db_path=None):
        """Initialize the database manager.

        Args:
            db_path (str, optional): Path to the database file. Defaults to None,
                                    which will use the default path.
        """
        if db_path is None:
            # Create database in user's documents folder
            documents_path = os.path.expanduser("~/Documents/Fawterha")
            os.makedirs(documents_path, exist_ok=True)
            self.db_path = os.path.join(documents_path, "fawterha.db")
        else:
            self.db_path = db_path

        self.connection = None

    def connect(self, max_attempts=5):
        """Connect to the database with retry mechanism.

        Args:
            max_attempts (int): Maximum number of connection attempts

        Returns:
            sqlite3.Connection: Database connection

        Raises:
            sqlite3.Error: If connection fails after max_attempts
        """
        attempt = 0
        last_error = None

        while attempt < max_attempts:
            try:
                # Close any existing connection first
                self.close()

                # Add timeout and set to 120 seconds to avoid database locked errors
                # Add additional pragmas to help with concurrency issues
                self.connection = sqlite3.connect(
                    self.db_path,
                    timeout=120.0,
                    isolation_level=None  # This enables autocommit mode
                )

                # Enable foreign keys
                self.connection.execute("PRAGMA foreign_keys = ON")

                # Set journal mode to WAL for better concurrency
                self.connection.execute("PRAGMA journal_mode = WAL")

                # Set busy timeout to 120000 milliseconds (120 seconds)
                self.connection.execute("PRAGMA busy_timeout = 120000")

                # Set locking mode to EXCLUSIVE to prevent other connections from writing
                self.connection.execute("PRAGMA locking_mode = NORMAL")

                # Return dictionary-like objects for rows
                self.connection.row_factory = sqlite3.Row

                return self.connection

            except sqlite3.Error as e:
                last_error = e
                attempt += 1

                if "database is locked" in str(e):
                    # If database is locked, wait with exponential backoff
                    wait_time = (2 ** attempt) * (0.1 + 0.1 * random.random())
                    print(f"Database locked, retrying in {wait_time:.2f} seconds (attempt {attempt}/{max_attempts})")
                    time.sleep(wait_time)
                else:
                    # For other errors, raise immediately
                    raise e

        # If we've exhausted all attempts, raise the last error
        raise last_error or sqlite3.Error("Failed to connect to database after multiple attempts")

    def close(self):
        """Close the database connection safely."""
        if self.connection:
            try:
                # Make sure any pending transactions are committed
                self.connection.commit()
                # Close the connection
                self.connection.close()
            except sqlite3.Error as e:
                print(f"Error closing database connection: {e}")
            finally:
                # Always set connection to None
                self.connection = None

    def setup_database(self):
        """Set up the database if it doesn't exist."""
        conn = self.connect()
        create_tables(conn)
        self.close()

    def execute_query(self, query, parameters=None, max_attempts=5):
        """Execute a query and return the results with enhanced retry mechanism.

        Args:
            query (str): SQL query to execute
            parameters (tuple, optional): Parameters for the query. Defaults to None.
            max_attempts (int): Maximum number of query execution attempts

        Returns:
            list: List of rows returned by the query

        Raises:
            sqlite3.Error: If query execution fails after max_attempts
        """
        attempt = 0
        last_error = None
        conn = None

        while attempt < max_attempts:
            try:
                # Ensure we have a fresh connection
                conn = self.connect()
                cursor = conn.cursor()

                if parameters:
                    cursor.execute(query, parameters)
                else:
                    cursor.execute(query)

                result = cursor.fetchall()

                # Make a copy of the results before closing the connection
                # This prevents "Cannot operate on a closed database" errors
                result_copy = [dict(row) for row in result]

                conn.commit()
                self.close()

                return result_copy

            except sqlite3.Error as e:
                last_error = e
                attempt += 1

                # Always try to rollback on error
                try:
                    if conn:
                        conn.rollback()
                except Exception as rollback_error:
                    print(f"Rollback error: {rollback_error}")

                error_msg = str(e).lower()
                if ("database is locked" in error_msg or
                    "database disk image is malformed" in error_msg or
                    "no such table" in error_msg or
                    "closed database" in error_msg) and attempt < max_attempts:
                    # If database is locked or corrupted, wait with exponential backoff
                    wait_time = (2 ** attempt) * (0.1 + 0.1 * random.random())
                    print(f"Database error during query, retrying in {wait_time:.2f} seconds (attempt {attempt}/{max_attempts}): {e}")
                    time.sleep(wait_time)

                    # Try to repair the database if it seems corrupted
                    if "malformed" in error_msg or "no such table" in error_msg:
                        self.repair_database()
                else:
                    # For other errors or last attempt, raise
                    if attempt >= max_attempts:
                        print(f"Query failed after {max_attempts} attempts: {query}")
                    raise e
            finally:
                # Always ensure the connection is closed
                try:
                    self.close()
                except Exception as close_error:
                    print(f"Error closing connection: {close_error}")

        # If we've exhausted all attempts, raise the last error
        raise last_error or sqlite3.Error("Failed to execute query after multiple attempts")

    def execute_insert(self, query, parameters=None, max_attempts=5):
        """Execute an insert query and return the last row id with enhanced retry mechanism.

        Args:
            query (str): SQL query to execute
            parameters (tuple, optional): Parameters for the query. Defaults to None.
            max_attempts (int): Maximum number of query execution attempts

        Returns:
            int: ID of the last inserted row

        Raises:
            sqlite3.Error: If query execution fails after max_attempts
        """
        attempt = 0
        last_error = None
        conn = None

        while attempt < max_attempts:
            try:
                # Ensure we have a fresh connection
                conn = self.connect()
                cursor = conn.cursor()

                if parameters:
                    cursor.execute(query, parameters)
                else:
                    cursor.execute(query)

                last_id = cursor.lastrowid
                conn.commit()
                self.close()

                return last_id

            except sqlite3.Error as e:
                last_error = e
                attempt += 1

                # Always try to rollback on error
                try:
                    if conn:
                        conn.rollback()
                except Exception as rollback_error:
                    print(f"Rollback error: {rollback_error}")

                error_msg = str(e).lower()
                if ("database is locked" in error_msg or
                    "database disk image is malformed" in error_msg or
                    "no such table" in error_msg or
                    "closed database" in error_msg) and attempt < max_attempts:
                    # If database is locked or corrupted, wait with exponential backoff
                    wait_time = (2 ** attempt) * (0.1 + 0.1 * random.random())
                    print(f"Database error during insert, retrying in {wait_time:.2f} seconds (attempt {attempt}/{max_attempts}): {e}")
                    time.sleep(wait_time)

                    # Try to repair the database if it seems corrupted
                    if "malformed" in error_msg or "no such table" in error_msg:
                        self.repair_database()
                else:
                    # For other errors or last attempt, raise
                    if attempt >= max_attempts:
                        print(f"Insert failed after {max_attempts} attempts: {query}")
                    raise e
            finally:
                # Always ensure the connection is closed
                try:
                    self.close()
                except Exception as close_error:
                    print(f"Error closing connection: {close_error}")

        # If we've exhausted all attempts, raise the last error
        raise last_error or sqlite3.Error("Failed to execute insert after multiple attempts")

    def execute_update(self, query, parameters=None, max_attempts=5):
        """Execute an update query and return the number of affected rows with enhanced retry mechanism.

        Args:
            query (str): SQL query to execute
            parameters (tuple, optional): Parameters for the query. Defaults to None.
            max_attempts (int): Maximum number of query execution attempts

        Returns:
            int: Number of affected rows

        Raises:
            sqlite3.Error: If query execution fails after max_attempts
        """
        attempt = 0
        last_error = None
        conn = None

        while attempt < max_attempts:
            try:
                # Ensure we have a fresh connection
                conn = self.connect()
                cursor = conn.cursor()

                if parameters:
                    cursor.execute(query, parameters)
                else:
                    cursor.execute(query)

                affected_rows = cursor.rowcount
                conn.commit()
                self.close()

                return affected_rows

            except sqlite3.Error as e:
                last_error = e
                attempt += 1

                # Always try to rollback on error
                try:
                    if conn:
                        conn.rollback()
                except Exception as rollback_error:
                    print(f"Rollback error: {rollback_error}")

                error_msg = str(e).lower()
                if ("database is locked" in error_msg or
                    "database disk image is malformed" in error_msg or
                    "no such table" in error_msg or
                    "closed database" in error_msg) and attempt < max_attempts:
                    # If database is locked or corrupted, wait with exponential backoff
                    wait_time = (2 ** attempt) * (0.1 + 0.1 * random.random())
                    print(f"Database error during update, retrying in {wait_time:.2f} seconds (attempt {attempt}/{max_attempts}): {e}")
                    time.sleep(wait_time)

                    # Try to repair the database if it seems corrupted
                    if "malformed" in error_msg or "no such table" in error_msg:
                        self.repair_database()
                else:
                    # For other errors or last attempt, raise
                    if attempt >= max_attempts:
                        print(f"Update failed after {max_attempts} attempts: {query}")
                    raise e
            finally:
                # Always ensure the connection is closed
                try:
                    self.close()
                except Exception as close_error:
                    print(f"Error closing connection: {close_error}")

        # If we've exhausted all attempts, raise the last error
        raise last_error or sqlite3.Error("Failed to execute update after multiple attempts")

    def repair_database(self):
        """Attempt to repair the database if it's corrupted or locked.
        Uses multiple strategies to fix common database issues.

        Returns:
            bool: True if repair was successful, False otherwise
        """
        try:
            print("Attempting to repair database...")

            # First try to close any existing connections
            self.close()

            # Wait a moment to ensure all connections are properly closed
            time.sleep(1)

            # Create a new connection with a very long timeout
            temp_conn = sqlite3.connect(self.db_path, timeout=300.0)

            try:
                # Run integrity check
                cursor = temp_conn.cursor()
                cursor.execute("PRAGMA integrity_check")
                integrity_result = cursor.fetchone()[0]

                if integrity_result != "ok":
                    print(f"Database integrity check failed: {integrity_result}")

                    # Try to recover with vacuum
                    print("Running VACUUM to rebuild the database...")
                    cursor.execute("VACUUM")
                    temp_conn.commit()

                    # Check integrity again
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]

                    if integrity_result != "ok":
                        print(f"Database still corrupted after VACUUM: {integrity_result}")

                        # If still corrupted, try more aggressive repair
                        print("Attempting more aggressive repair...")

                        # Reset journal mode to DELETE (more compatible)
                        cursor.execute("PRAGMA journal_mode = DELETE")

                        # Run integrity check again
                        cursor.execute("PRAGMA integrity_check")

                        # Try to fix specific tables if needed
                        try:
                            # Check if we can access the settings table
                            cursor.execute("SELECT COUNT(*) FROM settings")
                        except sqlite3.Error:
                            print("Settings table may be corrupted, attempting to recreate...")
                            try:
                                # Try to recreate the settings table
                                from database.schema import create_tables
                                create_tables(temp_conn)
                            except Exception as table_error:
                                print(f"Error recreating tables: {table_error}")

                # Reset journal mode
                cursor.execute("PRAGMA journal_mode = DELETE")
                cursor.execute("PRAGMA journal_mode = WAL")

                # Reset locking mode
                cursor.execute("PRAGMA locking_mode = NORMAL")

                # Set busy timeout
                cursor.execute("PRAGMA busy_timeout = 120000")

                # Optimize
                cursor.execute("PRAGMA optimize")

                # Commit all changes
                temp_conn.commit()

            finally:
                # Always close the temporary connection
                temp_conn.close()

            print("Database repair completed")

            # Try to setup the database again
            try:
                self.setup_database()
            except Exception as setup_error:
                print(f"Error setting up database after repair: {setup_error}")

            return True

        except Exception as e:
            print(f"Error repairing database: {e}")
            return False
