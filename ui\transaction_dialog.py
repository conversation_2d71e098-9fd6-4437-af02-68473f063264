#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Transaction Dialog for فوترها (Fawterha)
Dialog for adding and editing financial transactions
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDateEdit, QLineEdit, QMessageBox, QFormLayout, QDialogButtonBox,
    QTabWidget, QSplitter, QFrame, QGroupBox, QRadioButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QMenu, QToolBar,
    QSizePolicy, QWidget, QScrollArea
)
from PySide6.QtCore import Qt, QDate, Signal, QSize
from PySide6.QtGui import QIcon, QColor, QFont, QAction

from datetime import datetime
import os

from models.transaction import Transaction
from models.transaction_detail import TransactionDetail
from models.account import Account
from utils.currency_helper import format_currency
from utils.translation_manager import tr


class TransactionDetailWidget(QWidget):
    """Widget for a transaction detail line."""

    removed = Signal(object)  # Signal emitted when the detail is removed

    def __init__(self, account_manager, parent=None, detail=None, readonly=False):
        """Initialize the transaction detail widget.

        Args:
            account_manager: Account manager instance
            parent: Parent widget
            detail: TransactionDetail object to edit
            readonly: Whether the widget is read-only
        """
        super().__init__(parent)

        self.account_manager = account_manager
        self.detail = detail or TransactionDetail()
        self.readonly = readonly

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(5)

        # Account combo
        self.account_combo = QComboBox()
        self.account_combo.setMinimumWidth(200)
        self.account_combo.setEnabled(not self.readonly)
        layout.addWidget(self.account_combo, 3)

        # Description
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText(tr("accounting.description", "الوصف"))
        self.description_edit.setReadOnly(self.readonly)
        layout.addWidget(self.description_edit, 3)

        # Debit
        self.debit_spin = QDoubleSpinBox()
        self.debit_spin.setMinimum(0)
        self.debit_spin.setMaximum(9999999)
        self.debit_spin.setDecimals(2)
        self.debit_spin.setPrefix("")
        self.debit_spin.setSuffix("")
        self.debit_spin.setReadOnly(self.readonly)
        self.debit_spin.valueChanged.connect(self.on_debit_changed)
        layout.addWidget(self.debit_spin, 2)

        # Credit
        self.credit_spin = QDoubleSpinBox()
        self.credit_spin.setMinimum(0)
        self.credit_spin.setMaximum(9999999)
        self.credit_spin.setDecimals(2)
        self.credit_spin.setPrefix("")
        self.credit_spin.setSuffix("")
        self.credit_spin.setReadOnly(self.readonly)
        self.credit_spin.valueChanged.connect(self.on_credit_changed)
        layout.addWidget(self.credit_spin, 2)

        # Remove button
        if not self.readonly:
            self.remove_button = QPushButton(tr("common.remove", "حذف"))
            self.remove_button.clicked.connect(self.on_remove_clicked)
            layout.addWidget(self.remove_button)

        # Load accounts
        self.load_accounts()

    def load_accounts(self):
        """Load accounts into the account combo box."""
        self.account_combo.clear()

        accounts = self.account_manager.get_all_accounts()
        for account in accounts:
            self.account_combo.addItem(f"{account.code} - {account.name}", account.id)

    def load_data(self):
        """Load data into the widget."""
        if self.detail:
            # Set account
            if self.detail.account_id:
                index = self.account_combo.findData(self.detail.account_id)
                if index >= 0:
                    self.account_combo.setCurrentIndex(index)

            # Set description
            self.description_edit.setText(self.detail.description)

            # Set debit and credit
            self.debit_spin.setValue(self.detail.debit)
            self.credit_spin.setValue(self.detail.credit)

    def get_detail(self):
        """Get the transaction detail from the widget.

        Returns:
            TransactionDetail: Transaction detail object
        """
        detail = TransactionDetail()
        detail.id = self.detail.id
        detail.transaction_id = self.detail.transaction_id
        detail.account_id = self.account_combo.currentData()
        detail.description = self.description_edit.text()
        detail.debit = self.debit_spin.value()
        detail.credit = self.credit_spin.value()
        return detail

    def on_debit_changed(self, value):
        """Handle debit value change.

        Args:
            value: New debit value
        """
        if value > 0:
            self.credit_spin.setValue(0)

    def on_credit_changed(self, value):
        """Handle credit value change.

        Args:
            value: New credit value
        """
        if value > 0:
            self.debit_spin.setValue(0)

    def on_remove_clicked(self):
        """Handle remove button click."""
        self.removed.emit(self)


class TransactionDialog(QDialog):
    """Dialog for adding and editing financial transactions."""

    def __init__(self, db_manager, account_manager, transaction_manager, currency_manager=None, transaction_id=None, readonly=False):
        """Initialize the transaction dialog.

        Args:
            db_manager: Database manager instance
            account_manager: Account manager instance
            transaction_manager: Transaction manager instance
            currency_manager: Currency manager instance
            transaction_id: Transaction ID to edit
            readonly: Whether the dialog is read-only
        """
        super().__init__()

        self.db_manager = db_manager
        self.account_manager = account_manager
        self.transaction_manager = transaction_manager
        self.currency_manager = currency_manager
        self.transaction_id = transaction_id
        self.readonly = readonly
        self.transaction = None
        self.detail_widgets = []

        # Load transaction if editing
        if transaction_id:
            self.transaction = self.transaction_manager.get_transaction_by_id(transaction_id)
            if not self.transaction:
                QMessageBox.critical(
                    self,
                    tr("accounting.error", "خطأ"),
                    tr("accounting.transaction_not_found", "لم يتم العثور على المعاملة")
                )
                self.reject()
                return

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        title = tr("accounting.view_transaction", "عرض معاملة") if self.readonly else (
            tr("accounting.edit_transaction", "تعديل معاملة") if self.transaction_id else
            tr("accounting.add_transaction", "إضافة معاملة")
        )
        self.setWindowTitle(title)
        self.resize(800, 600)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Form layout for header
        form_layout = QFormLayout()
        main_layout.addLayout(form_layout)

        # Transaction number
        self.number_edit = QLineEdit()
        self.number_edit.setReadOnly(self.readonly or self.transaction_id is not None)
        form_layout.addRow(tr("accounting.transaction_number", "رقم المعاملة:"), self.number_edit)

        # Transaction date
        self.date_edit = QDateEdit()
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setReadOnly(self.readonly)
        form_layout.addRow(tr("accounting.transaction_date", "تاريخ المعاملة:"), self.date_edit)

        # Description
        self.description_edit = QLineEdit()
        self.description_edit.setReadOnly(self.readonly)
        form_layout.addRow(tr("accounting.description", "الوصف:"), self.description_edit)

        # Reference
        reference_layout = QHBoxLayout()

        self.reference_type_combo = QComboBox()
        self.reference_type_combo.addItem(tr("accounting.none", "بدون"), "")
        self.reference_type_combo.addItem(tr("accounting.invoice", "فاتورة"), Transaction.REF_TYPE_INVOICE)
        self.reference_type_combo.addItem(tr("accounting.payment", "دفعة"), Transaction.REF_TYPE_PAYMENT)
        self.reference_type_combo.addItem(tr("accounting.expense", "مصروف"), Transaction.REF_TYPE_EXPENSE)
        self.reference_type_combo.addItem(tr("accounting.manual", "يدوي"), Transaction.REF_TYPE_MANUAL)
        self.reference_type_combo.setEnabled(not self.readonly)
        reference_layout.addWidget(self.reference_type_combo)

        self.reference_id_edit = QLineEdit()
        self.reference_id_edit.setReadOnly(self.readonly)
        reference_layout.addWidget(self.reference_id_edit)

        form_layout.addRow(tr("accounting.reference", "المرجع:"), reference_layout)

        # Status
        self.status_combo = QComboBox()
        self.status_combo.addItem(tr("accounting.draft", "مسودة"), Transaction.STATUS_DRAFT)
        self.status_combo.addItem(tr("accounting.posted", "مرحّل"), Transaction.STATUS_POSTED)
        self.status_combo.addItem(tr("accounting.voided", "ملغي"), Transaction.STATUS_VOIDED)
        self.status_combo.setEnabled(not self.readonly)
        form_layout.addRow(tr("accounting.status", "الحالة:"), self.status_combo)

        # Details section
        details_group = QGroupBox(tr("accounting.transaction_details", "تفاصيل المعاملة"))
        main_layout.addWidget(details_group, 1)

        details_layout = QVBoxLayout(details_group)

        # Details header
        header_layout = QHBoxLayout()
        details_layout.addLayout(header_layout)

        header_layout.addWidget(QLabel(tr("accounting.account", "الحساب")), 3)
        header_layout.addWidget(QLabel(tr("accounting.description", "الوصف")), 3)
        header_layout.addWidget(QLabel(tr("accounting.debit", "مدين")), 2)
        header_layout.addWidget(QLabel(tr("accounting.credit", "دائن")), 2)
        if not self.readonly:
            header_layout.addWidget(QLabel(""), 1)  # Placeholder for remove button

        # Details container
        self.details_container = QWidget()
        self.details_layout = QVBoxLayout(self.details_container)
        self.details_layout.setContentsMargins(0, 0, 0, 0)
        self.details_layout.setSpacing(5)

        # Scroll area for details
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.details_container)
        details_layout.addWidget(scroll_area)

        # Add detail button
        if not self.readonly:
            self.add_detail_button = QPushButton(tr("accounting.add_detail", "إضافة تفاصيل"))
            self.add_detail_button.clicked.connect(self.add_detail)
            details_layout.addWidget(self.add_detail_button)

        # Totals
        totals_layout = QHBoxLayout()
        main_layout.addLayout(totals_layout)

        totals_layout.addStretch()

        self.total_debit_label = QLabel("0.00")
        self.total_debit_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(QLabel(tr("accounting.total_debit", "إجمالي المدين:")))
        totals_layout.addWidget(self.total_debit_label)

        totals_layout.addSpacing(20)

        self.total_credit_label = QLabel("0.00")
        self.total_credit_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(QLabel(tr("accounting.total_credit", "إجمالي الدائن:")))
        totals_layout.addWidget(self.total_credit_label)

        totals_layout.addSpacing(20)

        self.difference_label = QLabel("0.00")
        self.difference_label.setStyleSheet("font-weight: bold;")
        totals_layout.addWidget(QLabel(tr("accounting.difference", "الفرق:")))
        totals_layout.addWidget(self.difference_label)

        # Buttons
        button_box = QDialogButtonBox()
        main_layout.addWidget(button_box)

        if self.readonly:
            button_box.addButton(QDialogButtonBox.Close)
            button_box.rejected.connect(self.reject)
        else:
            button_box.addButton(QDialogButtonBox.Save)
            button_box.addButton(QDialogButtonBox.Cancel)
            button_box.accepted.connect(self.accept)
            button_box.rejected.connect(self.reject)

    def load_data(self):
        """Load data into the dialog."""
        if self.transaction:
            # Set transaction data
            self.number_edit.setText(self.transaction.transaction_number)
            self.date_edit.setDate(QDate.fromString(str(self.transaction.transaction_date), "yyyy-MM-dd"))
            self.description_edit.setText(self.transaction.description)

            # Set reference
            if self.transaction.reference_type:
                index = self.reference_type_combo.findData(self.transaction.reference_type)
                if index >= 0:
                    self.reference_type_combo.setCurrentIndex(index)

            if self.transaction.reference_id:
                self.reference_id_edit.setText(str(self.transaction.reference_id))

            # Set status
            index = self.status_combo.findData(self.transaction.status)
            if index >= 0:
                self.status_combo.setCurrentIndex(index)

            # Add details
            for detail in self.transaction.details:
                self.add_detail(detail)
        else:
            # Set default values for new transaction
            next_number = self.transaction_manager.get_next_transaction_number()
            self.number_edit.setText(next_number)

            # Add empty detail
            self.add_detail()

        # Update totals
        self.update_totals()

    def add_detail(self, detail=None):
        """Add a detail line to the transaction.

        Args:
            detail: TransactionDetail object to add
        """
        detail_widget = TransactionDetailWidget(self.account_manager, self, detail, self.readonly)
        detail_widget.removed.connect(self.remove_detail)
        self.details_layout.addWidget(detail_widget)
        self.detail_widgets.append(detail_widget)

        # Update totals
        self.update_totals()

    def remove_detail(self, widget):
        """Remove a detail line from the transaction.

        Args:
            widget: TransactionDetailWidget to remove
        """
        if widget in self.detail_widgets:
            self.details_layout.removeWidget(widget)
            self.detail_widgets.remove(widget)
            widget.deleteLater()

            # Update totals
            self.update_totals()

    def update_totals(self):
        """Update the totals display."""
        total_debit = sum(widget.debit_spin.value() for widget in self.detail_widgets)
        total_credit = sum(widget.credit_spin.value() for widget in self.detail_widgets)
        difference = total_debit - total_credit

        self.total_debit_label.setText(format_currency(total_debit))
        self.total_credit_label.setText(format_currency(total_credit))
        self.difference_label.setText(format_currency(abs(difference)))

        # Highlight difference if not zero
        if abs(difference) > 0.001:
            self.difference_label.setStyleSheet("font-weight: bold; color: red;")
        else:
            self.difference_label.setStyleSheet("font-weight: bold; color: green;")

    def accept(self):
        """Handle dialog acceptance."""
        # Validate form
        if not self.validate():
            return

        try:
            # Create transaction object
            transaction = Transaction()
            if self.transaction:
                transaction.id = self.transaction.id

            transaction.transaction_number = self.number_edit.text()
            transaction.transaction_date = self.date_edit.date().toPython()
            transaction.description = self.description_edit.text()

            reference_type = self.reference_type_combo.currentData()
            if reference_type:
                transaction.reference_type = reference_type
                reference_id = self.reference_id_edit.text()
                if reference_id:
                    transaction.reference_id = int(reference_id)

            transaction.status = self.status_combo.currentData()

            # Get details
            transaction.details = [widget.get_detail() for widget in self.detail_widgets]

            # Calculate amount (sum of debits or credits)
            transaction.amount = sum(detail.debit for detail in transaction.details)

            # Save transaction
            if transaction.id:
                # TODO: Implement update_transaction in TransactionManager
                QMessageBox.information(
                    self,
                    tr("accounting.not_implemented", "غير مكتمل"),
                    tr("accounting.update_not_implemented", "تحديث المعاملات غير مكتمل بعد")
                )
            else:
                self.transaction_manager.add_transaction(transaction)

            # Close dialog
            super().accept()
        except Exception as e:
            QMessageBox.critical(
                self,
                tr("accounting.error", "خطأ"),
                f"{tr('accounting.save_error', 'حدث خطأ أثناء حفظ المعاملة')}: {str(e)}"
            )

    def validate(self):
        """Validate the form.

        Returns:
            bool: True if valid, False otherwise
        """
        # Check transaction number
        if not self.number_edit.text():
            QMessageBox.warning(
                self,
                tr("accounting.validation_error", "خطأ في التحقق"),
                tr("accounting.transaction_number_required", "رقم المعاملة مطلوب")
            )
            self.number_edit.setFocus()
            return False

        # Check description
        if not self.description_edit.text():
            QMessageBox.warning(
                self,
                tr("accounting.validation_error", "خطأ في التحقق"),
                tr("accounting.description_required", "الوصف مطلوب")
            )
            self.description_edit.setFocus()
            return False

        # Check details
        if not self.detail_widgets:
            QMessageBox.warning(
                self,
                tr("accounting.validation_error", "خطأ في التحقق"),
                tr("accounting.details_required", "يجب إضافة تفاصيل للمعاملة")
            )
            return False

        # Check if debits equal credits
        total_debit = sum(widget.debit_spin.value() for widget in self.detail_widgets)
        total_credit = sum(widget.credit_spin.value() for widget in self.detail_widgets)

        if abs(total_debit - total_credit) > 0.001:
            QMessageBox.warning(
                self,
                tr("accounting.validation_error", "خطأ في التحقق"),
                tr("accounting.debits_credits_must_equal", "يجب أن يتساوى إجمالي المدين مع إجمالي الدائن")
            )
            return False

        return True
