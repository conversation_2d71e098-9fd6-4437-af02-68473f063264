#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Accounts Payable and Receivable View for فوترها (Fawterha)
Displays accounts payable and receivable information
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDateEdit, QLineEdit, QMessageBox, QTabWidget,
    QSplitter, QFrame, QGroupBox, QRadioButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QMenu, QToolBar,
    QSizePolicy
)
from PySide6.QtCore import Qt, QDate, Signal, QSize
from PySide6.QtGui import QIcon, QColor, QFont, QAction

from datetime import datetime, timedelta
import os

from database.account_manager import AccountManager
from database.transaction_manager import TransactionManager
from models.transaction import Transaction
from models.account import Account
from ui.transaction_dialog import TransactionDialog
from utils.currency_helper import format_currency
from utils.translation_manager import tr


class AccountsPayableReceivableView(QWidget):
    """Accounts payable and receivable view widget."""

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the accounts payable and receivable view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager

        # Initialize managers
        self.account_manager = AccountManager(db_manager)
        self.transaction_manager = TransactionManager(db_manager, self.account_manager)

        # Set up UI
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Title
        title_label = QLabel(tr("accounting.accounts_payable_receivable", "الذمم الدائنة والمدينة"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Accounts receivable tab
        self.receivable_widget = QWidget()
        self.tab_widget.addTab(self.receivable_widget, tr("accounting.accounts_receivable", "الذمم المدينة"))
        self.setup_receivable_tab()

        # Accounts payable tab
        self.payable_widget = QWidget()
        self.tab_widget.addTab(self.payable_widget, tr("accounting.accounts_payable", "الذمم الدائنة"))
        self.setup_payable_tab()

        # Connect tab changed signal
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def setup_receivable_tab(self):
        """Set up the accounts receivable tab."""
        # Layout
        layout = QVBoxLayout(self.receivable_widget)
        layout.setContentsMargins(0, 10, 0, 0)
        layout.setSpacing(10)

        # Toolbar
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        layout.addWidget(toolbar)

        # Add payment button
        self.add_payment_action = QAction(QIcon("resources/icons/payment.png"), tr("accounting.add_payment", "إضافة دفعة"), self)
        self.add_payment_action.triggered.connect(self.add_customer_payment)
        toolbar.addAction(self.add_payment_action)

        # Filter section
        filter_layout = QHBoxLayout()
        layout.addLayout(filter_layout)

        # Date range
        filter_layout.addWidget(QLabel(tr("accounting.date_range", "نطاق التاريخ:")))

        self.receivable_start_date_edit = QDateEdit()
        self.receivable_start_date_edit.setCalendarPopup(True)
        self.receivable_start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        filter_layout.addWidget(self.receivable_start_date_edit)

        filter_layout.addWidget(QLabel(tr("accounting.to", "إلى")))

        self.receivable_end_date_edit = QDateEdit()
        self.receivable_end_date_edit.setCalendarPopup(True)
        self.receivable_end_date_edit.setDate(QDate.currentDate())
        filter_layout.addWidget(self.receivable_end_date_edit)

        # Customer filter
        filter_layout.addWidget(QLabel(tr("accounting.customer", "العميل:")))

        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumWidth(200)
        filter_layout.addWidget(self.customer_combo)

        # Apply filter button
        self.receivable_filter_button = QPushButton(tr("accounting.apply_filter", "تطبيق الفلتر"))
        self.receivable_filter_button.clicked.connect(self.load_receivables)
        filter_layout.addWidget(self.receivable_filter_button)

        # Accounts receivable table
        self.receivable_table = QTableWidget()
        self.receivable_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.receivable_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.receivable_table.setAlternatingRowColors(True)
        self.receivable_table.verticalHeader().setVisible(False)
        self.receivable_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.receivable_table.setColumnCount(7)
        self.receivable_table.setHorizontalHeaderLabels([
            tr("accounting.invoice_number", "رقم الفاتورة"),
            tr("accounting.customer", "العميل"),
            tr("accounting.date", "التاريخ"),
            tr("accounting.due_date", "تاريخ الاستحقاق"),
            tr("accounting.amount", "المبلغ"),
            tr("accounting.paid", "المدفوع"),
            tr("accounting.balance", "الرصيد")
        ])
        layout.addWidget(self.receivable_table)

        # Summary section
        summary_layout = QHBoxLayout()
        layout.addLayout(summary_layout)

        summary_layout.addStretch()

        self.receivable_total_label = QLabel("0.00")
        self.receivable_total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(QLabel(tr("accounting.total_receivable", "إجمالي الذمم المدينة:")))
        summary_layout.addWidget(self.receivable_total_label)

    def setup_payable_tab(self):
        """Set up the accounts payable tab."""
        # Layout
        layout = QVBoxLayout(self.payable_widget)
        layout.setContentsMargins(0, 10, 0, 0)
        layout.setSpacing(10)

        # Toolbar
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        layout.addWidget(toolbar)

        # Add payment button
        self.add_vendor_payment_action = QAction(QIcon("resources/icons/payment.png"), tr("accounting.add_payment", "إضافة دفعة"), self)
        self.add_vendor_payment_action.triggered.connect(self.add_vendor_payment)
        toolbar.addAction(self.add_vendor_payment_action)

        # Filter section
        filter_layout = QHBoxLayout()
        layout.addLayout(filter_layout)

        # Date range
        filter_layout.addWidget(QLabel(tr("accounting.date_range", "نطاق التاريخ:")))

        self.payable_start_date_edit = QDateEdit()
        self.payable_start_date_edit.setCalendarPopup(True)
        self.payable_start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        filter_layout.addWidget(self.payable_start_date_edit)

        filter_layout.addWidget(QLabel(tr("accounting.to", "إلى")))

        self.payable_end_date_edit = QDateEdit()
        self.payable_end_date_edit.setCalendarPopup(True)
        self.payable_end_date_edit.setDate(QDate.currentDate())
        filter_layout.addWidget(self.payable_end_date_edit)

        # Vendor filter
        filter_layout.addWidget(QLabel(tr("accounting.vendor", "المورد:")))

        self.vendor_combo = QComboBox()
        self.vendor_combo.setMinimumWidth(200)
        filter_layout.addWidget(self.vendor_combo)

        # Apply filter button
        self.payable_filter_button = QPushButton(tr("accounting.apply_filter", "تطبيق الفلتر"))
        self.payable_filter_button.clicked.connect(self.load_payables)
        filter_layout.addWidget(self.payable_filter_button)

        # Accounts payable table
        self.payable_table = QTableWidget()
        self.payable_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.payable_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.payable_table.setAlternatingRowColors(True)
        self.payable_table.verticalHeader().setVisible(False)
        self.payable_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.payable_table.setColumnCount(7)
        self.payable_table.setHorizontalHeaderLabels([
            tr("accounting.invoice_number", "رقم الفاتورة"),
            tr("accounting.vendor", "المورد"),
            tr("accounting.date", "التاريخ"),
            tr("accounting.due_date", "تاريخ الاستحقاق"),
            tr("accounting.amount", "المبلغ"),
            tr("accounting.paid", "المدفوع"),
            tr("accounting.balance", "الرصيد")
        ])
        layout.addWidget(self.payable_table)

        # Summary section
        summary_layout = QHBoxLayout()
        layout.addLayout(summary_layout)

        summary_layout.addStretch()

        self.payable_total_label = QLabel("0.00")
        self.payable_total_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(QLabel(tr("accounting.total_payable", "إجمالي الذمم الدائنة:")))
        summary_layout.addWidget(self.payable_total_label)

    def load_data(self):
        """Load data into the view."""
        self.load_customers()
        self.load_vendors()
        self.load_receivables()
        self.load_payables()

    def load_customers(self):
        """Load customers into the customer combo box."""
        self.customer_combo.clear()
        self.customer_combo.addItem(tr("accounting.all_customers", "جميع العملاء"), "all")

        # TODO: Load customers from database
        # For now, we'll add some dummy data
        self.customer_combo.addItem("عميل 1", 1)
        self.customer_combo.addItem("عميل 2", 2)
        self.customer_combo.addItem("عميل 3", 3)

    def load_vendors(self):
        """Load vendors into the vendor combo box."""
        self.vendor_combo.clear()
        self.vendor_combo.addItem(tr("accounting.all_vendors", "جميع الموردين"), "all")

        # TODO: Load vendors from database
        # For now, we'll add some dummy data
        self.vendor_combo.addItem("مورد 1", 1)
        self.vendor_combo.addItem("مورد 2", 2)
        self.vendor_combo.addItem("مورد 3", 3)

    def load_receivables(self):
        """Load accounts receivable data."""
        # Clear the table
        self.receivable_table.setRowCount(0)

        # TODO: Load accounts receivable from database
        # For now, we'll add some dummy data
        data = [
            {"invoice": "INV-1001", "customer": "عميل 1", "date": "2023-01-15", "due_date": "2023-02-15", "amount": 1000, "paid": 500, "balance": 500},
            {"invoice": "INV-1002", "customer": "عميل 2", "date": "2023-01-20", "due_date": "2023-02-20", "amount": 1500, "paid": 0, "balance": 1500},
            {"invoice": "INV-1003", "customer": "عميل 1", "date": "2023-02-05", "due_date": "2023-03-05", "amount": 800, "paid": 800, "balance": 0}
        ]

        # Filter by customer
        customer_id = self.customer_combo.currentData()
        if customer_id != "all":
            data = [item for item in data if item["customer"] == f"عميل {customer_id}"]

        # Populate the table
        self.receivable_table.setRowCount(len(data))
        total_receivable = 0

        for row, item in enumerate(data):
            # Invoice number
            invoice_item = QTableWidgetItem(item["invoice"])
            self.receivable_table.setItem(row, 0, invoice_item)

            # Customer
            customer_item = QTableWidgetItem(item["customer"])
            self.receivable_table.setItem(row, 1, customer_item)

            # Date
            date_item = QTableWidgetItem(item["date"])
            self.receivable_table.setItem(row, 2, date_item)

            # Due date
            due_date_item = QTableWidgetItem(item["due_date"])
            self.receivable_table.setItem(row, 3, due_date_item)

            # Amount
            amount_item = QTableWidgetItem(format_currency(item["amount"], "EGP"))
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.receivable_table.setItem(row, 4, amount_item)

            # Paid
            paid_item = QTableWidgetItem(format_currency(item["paid"], "EGP"))
            paid_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.receivable_table.setItem(row, 5, paid_item)

            # Balance
            balance_item = QTableWidgetItem(format_currency(item["balance"], "EGP"))
            balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.receivable_table.setItem(row, 6, balance_item)

            # Add to total
            total_receivable += item["balance"]

        # Update total
        self.receivable_total_label.setText(format_currency(total_receivable, "EGP"))

        # Resize columns to content
        self.receivable_table.resizeColumnsToContents()

    def load_payables(self):
        """Load accounts payable data."""
        # Clear the table
        self.payable_table.setRowCount(0)

        # TODO: Load accounts payable from database
        # For now, we'll add some dummy data
        data = [
            {"invoice": "VINV-1001", "vendor": "مورد 1", "date": "2023-01-10", "due_date": "2023-02-10", "amount": 2000, "paid": 1000, "balance": 1000},
            {"invoice": "VINV-1002", "vendor": "مورد 2", "date": "2023-01-25", "due_date": "2023-02-25", "amount": 1200, "paid": 0, "balance": 1200},
            {"invoice": "VINV-1003", "vendor": "مورد 1", "date": "2023-02-01", "due_date": "2023-03-01", "amount": 500, "paid": 500, "balance": 0}
        ]

        # Filter by vendor
        vendor_id = self.vendor_combo.currentData()
        if vendor_id != "all":
            data = [item for item in data if item["vendor"] == f"مورد {vendor_id}"]

        # Populate the table
        self.payable_table.setRowCount(len(data))
        total_payable = 0

        for row, item in enumerate(data):
            # Invoice number
            invoice_item = QTableWidgetItem(item["invoice"])
            self.payable_table.setItem(row, 0, invoice_item)

            # Vendor
            vendor_item = QTableWidgetItem(item["vendor"])
            self.payable_table.setItem(row, 1, vendor_item)

            # Date
            date_item = QTableWidgetItem(item["date"])
            self.payable_table.setItem(row, 2, date_item)

            # Due date
            due_date_item = QTableWidgetItem(item["due_date"])
            self.payable_table.setItem(row, 3, due_date_item)

            # Amount
            amount_item = QTableWidgetItem(format_currency(item["amount"], "EGP"))
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.payable_table.setItem(row, 4, amount_item)

            # Paid
            paid_item = QTableWidgetItem(format_currency(item["paid"], "EGP"))
            paid_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.payable_table.setItem(row, 5, paid_item)

            # Balance
            balance_item = QTableWidgetItem(format_currency(item["balance"], "EGP"))
            balance_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            self.payable_table.setItem(row, 6, balance_item)

            # Add to total
            total_payable += item["balance"]

        # Update total
        self.payable_total_label.setText(format_currency(total_payable, "EGP"))

        # Resize columns to content
        self.payable_table.resizeColumnsToContents()

    def on_tab_changed(self, index):
        """Handle tab changed event.

        Args:
            index (int): Index of the selected tab
        """
        if index == 0:
            self.load_receivables()
        else:
            self.load_payables()

    def add_customer_payment(self):
        """Add a customer payment."""
        QMessageBox.information(
            self,
            tr("accounting.not_implemented", "غير مكتمل"),
            tr("accounting.feature_not_implemented", "هذه الميزة غير مكتملة بعد")
        )

    def add_vendor_payment(self):
        """Add a vendor payment."""
        QMessageBox.information(
            self,
            tr("accounting.not_implemented", "غير مكتمل"),
            tr("accounting.feature_not_implemented", "هذه الميزة غير مكتملة بعد")
        )
