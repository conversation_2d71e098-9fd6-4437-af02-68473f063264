#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Templates View for فوترها (Fawterha)
Manages invoice templates
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QHeaderView, QAbstractItemView,
    QMessageBox, QDialog, QFrame
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QColor, QIcon

from models.invoice_template import InvoiceTemplate
from ui.template_dialog import TemplateDialog
from ui.themes import THEMES
from utils.translation_manager import tr


class TemplatesView(QWidget):
    """Widget for managing invoice templates."""

    def __init__(self, db_manager):
        """Initialize the templates view.

        Args:
            db_manager: Database manager instance
        """
        super().__init__()

        self.db_manager = db_manager

        # Get current theme from settings
        self.theme_key = "default"
        try:
            theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
            if theme_setting and theme_setting[0]['value']:
                self.theme_key = theme_setting[0]['value']
        except Exception as e:
            print(f"Error loading theme setting: {e}")
            self.theme_key = "default"

        # Get theme colors
        self.theme = THEMES.get(self.theme_key, THEMES["default"])

        # Create layout
        layout = QVBoxLayout(self)

        # Create header
        header = QFrame()
        header.setStyleSheet(f"""
            background-color: {self.theme.get("panel_bg", self.theme["main_bg"])};
            border-radius: {self.theme["border_radius"]};
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid {self.theme["border_color"]};
        """)
        header_layout = QVBoxLayout(header)

        title = QLabel(tr("templates.title", "قوالب الفواتير"))
        title.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: {self.theme["primary_dark"]};
        """)
        header_layout.addWidget(title)

        description = QLabel(tr("templates.description", "قم بإنشاء وتخصيص قوالب الفواتير لتناسب احتياجات عملك. يمكنك تغيير الألوان والخطوط وإعدادات العرض لكل قالب."))
        description.setWordWrap(True)
        description.setStyleSheet(f"""
            font-size: 11pt;
            color: {self.theme["main_text"]};
        """)
        header_layout.addWidget(description)

        layout.addWidget(header)

        # Create toolbar
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)

        # Add new template button
        self.add_button = QPushButton(tr("templates.add_new", "إضافة قالب جديد"))
        self.add_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["primary"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12pt;
            }}
            QPushButton:hover {{
                background-color: {self.theme["primary_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["primary_dark"]};
            }}
        """)
        self.add_button.setMinimumHeight(40)
        self.add_button.clicked.connect(self.create_new_template)
        toolbar_layout.addWidget(self.add_button)

        toolbar_layout.addStretch()

        # Create templates table
        self.templates_table = QTableWidget()
        self.templates_table.setColumnCount(5)
        self.templates_table.setHorizontalHeaderLabels([
            tr("templates.name", "اسم القالب"),
            tr("common.description", "الوصف"),
            tr("templates.is_default", "افتراضي"),
            tr("templates.creation_date", "تاريخ الإنشاء"),
            ""
        ])
        self.templates_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.templates_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.templates_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.templates_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.templates_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Fixed)
        self.templates_table.setColumnWidth(4, 180)
        self.templates_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.templates_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.templates_table.setAlternatingRowColors(True)
        self.templates_table.verticalHeader().setVisible(False)
        self.templates_table.setShowGrid(True)
        self.templates_table.verticalHeader().setDefaultSectionSize(60)  # Increase row height
        self.templates_table.setStyleSheet(f"""
            QTableWidget {{
                border: 1px solid {self.theme["border_color"]};
                border-radius: {self.theme["border_radius"]};
                background-color: {self.theme.get("table_bg", self.theme.get("panel_bg", "#ffffff"))};
                gridline-color: {self.theme["border_color"]};
                font-size: 11pt;
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.theme["border_color"]};
                min-height: 40px;
                margin: 2px;
                color: {self.theme["main_text"]};
            }}
            QTableWidget::item:selected {{
                background-color: {self.theme["primary_light"]};
                color: {self.theme["main_text"]};
            }}
            QHeaderView::section {{
                background-color: {self.theme["primary_dark"]};
                color: {self.theme["button_text"]};
                padding: 10px;
                border: none;
                font-weight: bold;
                font-size: 12pt;
            }}
            QTableWidget::item:alternate {{
                background-color: {self.theme.get("table_alternate_bg", self.theme.get("main_bg", "#f5f5f5"))};
            }}
        """)
        layout.addWidget(self.templates_table)

        # Load templates
        self.load_templates()

    def load_templates(self):
        """Load templates from the database."""
        query = "SELECT * FROM invoice_templates ORDER BY is_default DESC, name"
        rows = self.db_manager.execute_query(query)

        self.templates_table.setRowCount(0)

        for row in rows:
            template = InvoiceTemplate.from_db_row(row)
            self.add_template_to_table(template)

    def add_template_to_table(self, template):
        """Add a template to the table.

        Args:
            template (InvoiceTemplate): Template to add
        """
        row_position = self.templates_table.rowCount()
        self.templates_table.insertRow(row_position)

        # Set template data
        name_item = QTableWidgetItem(template.name)
        name_item.setData(Qt.UserRole, template.id)
        self.templates_table.setItem(row_position, 0, name_item)

        description_item = QTableWidgetItem(template.description)
        self.templates_table.setItem(row_position, 1, description_item)

        default_item = QTableWidgetItem("✓" if template.is_default else "")
        default_item.setTextAlignment(Qt.AlignCenter)
        if template.is_default:
            default_item.setForeground(QColor("#4caf50"))  # Green
            font = default_item.font()
            font.setBold(True)
            default_item.setFont(font)
        self.templates_table.setItem(row_position, 2, default_item)

        created_at_item = QTableWidgetItem(template.created_at.split(" ")[0] if template.created_at else "")
        created_at_item.setTextAlignment(Qt.AlignCenter)
        self.templates_table.setItem(row_position, 3, created_at_item)

        # Add buttons container
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(5, 5, 5, 5)
        buttons_layout.setSpacing(10)

        # Add edit button
        edit_button = QPushButton(tr("common.edit", "تعديل"))
        edit_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["primary"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 6px 12px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 32px;
                min-width: 90px;
                max-width: 100px;
                margin: 3px;
            }}
            QPushButton:hover {{
                background-color: {self.theme["primary_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["primary_dark"]};
            }}
        """)
        edit_button.clicked.connect(lambda: self.edit_template(template.id))
        buttons_layout.addWidget(edit_button)

        # Add delete button
        delete_button = QPushButton(tr("common.delete", "حذف"))
        delete_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {self.theme["danger"]};
                color: {self.theme["button_text"]};
                border: none;
                border-radius: {self.theme["border_radius"]};
                padding: 6px 12px;
                font-weight: bold;
                font-size: 11pt;
                min-height: 32px;
                min-width: 90px;
                max-width: 100px;
                margin: 3px;
            }}
            QPushButton:hover {{
                background-color: {self.theme["danger_dark"]};
            }}
            QPushButton:pressed {{
                background-color: {self.theme["danger_dark"]};
            }}
        """)
        delete_button.clicked.connect(lambda: self.delete_template(template.id))
        if template.is_default:
            delete_button.setEnabled(False)
            delete_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {self.theme.get("disabled_bg", "#bdbdbd")};
                    color: {self.theme.get("disabled_text", "#ffffff")};
                    border: none;
                    border-radius: {self.theme["border_radius"]};
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 11pt;
                    min-height: 32px;
                    min-width: 90px;
                    max-width: 100px;
                    margin: 3px;
                }}
            """)
        buttons_layout.addWidget(delete_button)

        self.templates_table.setCellWidget(row_position, 4, buttons_widget)

    def create_new_template(self):
        """Create a new template."""
        dialog = TemplateDialog(self, None, self.db_manager)
        if dialog.exec():
            template_data = dialog.get_template_data()

            # Validate data
            if not template_data['name']:
                QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("templates.name_required", "يجب إدخال اسم القالب"))
                return

            # If this is the default template, update other templates
            if template_data['is_default']:
                self.db_manager.execute_update(
                    "UPDATE invoice_templates SET is_default = 0"
                )

            # Insert template into database
            query = """
            INSERT INTO invoice_templates (
                name, description, is_default,
                header_color, text_color, accent_color,
                font_family, font_size, show_logo,
                show_header, show_footer, footer_text
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                template_data['name'],
                template_data['description'],
                1 if template_data['is_default'] else 0,
                template_data['header_color'],
                template_data['text_color'],
                template_data['accent_color'],
                template_data['font_family'],
                template_data['font_size'],
                1 if template_data['show_logo'] else 0,
                1 if template_data['show_header'] else 0,
                1 if template_data['show_footer'] else 0,
                template_data['footer_text']
            )

            try:
                template_id = self.db_manager.execute_insert(query, params)

                # If this is the default template, update the settings
                if template_data['is_default']:
                    self.db_manager.execute_update(
                        "UPDATE settings SET value = ? WHERE key = 'default_template_id'",
                        (str(template_id),)
                    )

                # Reload templates
                self.load_templates()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("templates.add_success", "تم إضافة القالب بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("templates.add_error", f"حدث خطأ أثناء إضافة القالب: {str(e)}"))

    def edit_template(self, template_id):
        """Edit a template.

        Args:
            template_id (int): Template ID
        """
        # Get template from database
        query = "SELECT * FROM invoice_templates WHERE id = ?"
        rows = self.db_manager.execute_query(query, (template_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("templates.not_found", "لم يتم العثور على القالب"))
            return

        template = InvoiceTemplate.from_db_row(rows[0])

        # Show edit dialog
        dialog = TemplateDialog(self, template, self.db_manager)
        if dialog.exec():
            template_data = dialog.get_template_data()

            # Validate data
            if not template_data['name']:
                QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("templates.name_required", "يجب إدخال اسم القالب"))
                return

            # If this is the default template, update other templates
            if template_data['is_default'] and not template.is_default:
                self.db_manager.execute_update(
                    "UPDATE invoice_templates SET is_default = 0"
                )

            # Update template in database
            query = """
            UPDATE invoice_templates
            SET name = ?, description = ?, is_default = ?,
                header_color = ?, text_color = ?, accent_color = ?,
                font_family = ?, font_size = ?, show_logo = ?,
                show_header = ?, show_footer = ?, footer_text = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            params = (
                template_data['name'],
                template_data['description'],
                1 if template_data['is_default'] else 0,
                template_data['header_color'],
                template_data['text_color'],
                template_data['accent_color'],
                template_data['font_family'],
                template_data['font_size'],
                1 if template_data['show_logo'] else 0,
                1 if template_data['show_header'] else 0,
                1 if template_data['show_footer'] else 0,
                template_data['footer_text'],
                template_id
            )

            try:
                self.db_manager.execute_update(query, params)

                # If this is the default template, update the settings
                if template_data['is_default']:
                    self.db_manager.execute_update(
                        "UPDATE settings SET value = ? WHERE key = 'default_template_id'",
                        (str(template_id),)
                    )

                # Reload templates
                self.load_templates()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("templates.update_success", "تم تحديث القالب بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("templates.update_error", f"حدث خطأ أثناء تحديث القالب: {str(e)}"))

    def delete_template(self, template_id):
        """Delete a template.

        Args:
            template_id (int): Template ID
        """
        # Get template from database
        query = "SELECT * FROM invoice_templates WHERE id = ?"
        rows = self.db_manager.execute_query(query, (template_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("templates.not_found", "لم يتم العثور على القالب"))
            return

        template = InvoiceTemplate.from_db_row(rows[0])

        # Cannot delete default template
        if template.is_default:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("templates.cannot_delete_default", "لا يمكن حذف القالب الافتراضي"))
            return

        # Confirm deletion
        confirm = QMessageBox.question(
            self,
            tr("messages.confirm_delete", "تأكيد الحذف"),
            tr("templates.confirm_delete", f"هل أنت متأكد من حذف القالب '{template.name}'؟"),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # Delete template from database
                self.db_manager.execute_update(
                    "DELETE FROM invoice_templates WHERE id = ?",
                    (template_id,)
                )

                # Reload templates
                self.load_templates()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("templates.delete_success", "تم حذف القالب بنجاح"))
            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("templates.delete_error", f"حدث خطأ أثناء حذف القالب: {str(e)}"))
