#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Update Inventory Script for فوترها (Fawterha)
Updates the inventory values for a product
"""

import os
import sqlite3

def update_inventory():
    """Update inventory values for a product."""
    # Get database path
    documents_path = os.path.expanduser("~/Documents/Fawterha")
    db_path = os.path.join(documents_path, "fawterha.db")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Start transaction
        conn.execute("BEGIN TRANSACTION")
        
        # Update product
        cursor.execute("""
        UPDATE products
        SET stock_quantity = 20,
            min_stock_level = 5,
            track_inventory = 1
        WHERE id = 3
        """)
        
        # Commit transaction
        conn.commit()
        print("Inventory updated successfully!")
        
    except Exception as e:
        # Rollback transaction on error
        conn.rollback()
        print(f"Error updating inventory: {str(e)}")
    finally:
        # Close connection
        conn.close()

if __name__ == "__main__":
    update_inventory()
