#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Themes for فوترها (Fawterha)
Different visual themes for the application
"""

# Default theme (current style)
DEFAULT_THEME = {
    "name": "الافتراضي",
    "description": "النمط الافتراضي للتطبيق",
    # Main colors
    "main_bg": "#f5f5f5",
    "main_text": "#212121",
    "primary": "#1976d2",
    "primary_dark": "#0d47a1",
    "primary_light": "#bbdefb",
    "secondary": "#03a9f4",
    "secondary_dark": "#0288d1",
    "secondary_light": "#b3e5fc",
    "accent": "#ff4081",
    "success": "#2e7d32",
    "success_dark": "#1b5e20",
    "danger": "#e53935",
    "danger_dark": "#c62828",
    "warning": "#ff9800",
    "warning_dark": "#f57c00",
    "button_text": "#ffffff",

    # Input fields
    "input_bg": "#ffffff",
    "input_border": "#bdbdbd",
    "input_focus": "#1976d2",
    "input_text": "#212121",
    "input_placeholder": "#9e9e9e",
    "input_disabled_bg": "#f5f5f5",
    "input_disabled_text": "#9e9e9e",

    # Tables
    "table_header_bg": "#0d47a1",
    "table_header_text": "#ffffff",
    "table_alternate_bg": "#f5f5f5",
    "table_border": "#e0e0e0",
    "table_selection_bg": "#e3f2fd",
    "table_selection_text": "#212121",
    "table_hover_bg": "#e1f5fe",
    "table_text": "#212121",

    # Gradients
    "main_bg_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f5f5f5, stop:1 #e0e0e0)",
    "button_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1e88e5, stop:1 #0d47a1)",
    "button_hover_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1976d2, stop:1 #0d47a1)",
    "button_pressed_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0d47a1, stop:1 #0a367a)",
    "success_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #43a047, stop:1 #2e7d32)",
    "danger_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f44336, stop:1 #c62828)",
    "header_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1976d2, stop:1 #0d47a1)",
    "toolbar_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1565c0, stop:1 #0d47a1)",
    "menu_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #ffffff, stop:1 #f5f5f5)",
    "dialog_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ffffff, stop:1 #f5f5f5)",

    # Borders and effects
    "border_radius": "6px",
    "border_width": "1px",
    "border_color": "#bdbdbd",
    "focus_border_color": "#1976d2",
    "focus_border_width": "2px",

    # Tabs and panels
    "tab_active_bg": "#1976d2",
    "tab_active_text": "#ffffff",
    "tab_inactive_bg": "#f5f5f5",
    "tab_inactive_text": "#616161",
    "tab_hover_bg": "#e3f2fd",
    "panel_bg": "#ffffff",
    "panel_border": "#e0e0e0",

    # Scrollbars
    "scrollbar_bg": "#f5f5f5",
    "scrollbar_handle": "#bdbdbd",
    "scrollbar_handle_hover": "#9e9e9e",

    # Dialogs and popups
    "dialog_bg": "#ffffff",
    "dialog_border": "#bdbdbd",
    "dialog_title_bg": "#1976d2",
    "dialog_title_text": "#ffffff",

    # Menus
    "menu_bg": "#ffffff",
    "menu_text": "#212121",
    "menu_hover_bg": "#e3f2fd",
    "menu_hover_text": "#1976d2",
    "menu_border": "#e0e0e0",

    # Misc
    "tooltip_bg": "#616161",
    "tooltip_text": "#ffffff",
    "separator": "#e0e0e0",
    "disabled_opacity": "0.6",

    # Font settings
    "font_family": "'Almarai', 'Arial', sans-serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Dark theme - Enhanced contrast
DARK_THEME = {
    "name": "الداكن",
    "description": "نمط داكن بتباين عالي",
    # Main colors
    "main_bg": "#121212",  # Very dark gray
    "main_text": "#ffffff",  # Pure white for maximum contrast
    "primary": "#2196f3",  # Bright blue
    "primary_dark": "#1976d2",  # Darker blue
    "primary_light": "#4dabf5",  # Light blue
    "secondary": "#03dac6",  # Bright teal
    "secondary_dark": "#018786",  # Darker teal
    "secondary_light": "#b2ebf2",  # Light teal
    "accent": "#bb86fc",  # Bright purple
    "success": "#4caf50",  # Bright green
    "success_dark": "#388e3c",  # Darker green
    "danger": "#f44336",  # Bright red
    "danger_dark": "#d32f2f",  # Darker red
    "warning": "#ff9800",  # Bright orange
    "warning_dark": "#f57c00",  # Darker orange
    "button_text": "#ffffff",  # Pure white for button text

    # Input fields - Improved contrast
    "input_bg": "#1e1e1e",  # Dark gray
    "input_border": "#4285f4",  # Bright blue border for visibility
    "input_focus": "#2196f3",  # Bright blue focus
    "input_text": "#ffffff",  # Pure white text for maximum contrast
    "input_placeholder": "#b0b0b0",  # Lighter gray for better visibility
    "input_disabled_bg": "#121212",  # Very dark gray when disabled
    "input_disabled_text": "#909090",  # Medium gray for disabled text

    # Tables - Enhanced dark theme (One Dark inspired) with improved contrast
    "table_header_bg": "#21252b",  # Dark header (One Dark inspired)
    "table_header_text": "#ffffff",  # Pure white text for maximum contrast
    "table_alternate_bg": "#282c34",  # Dark background (One Dark inspired)
    "table_border": "#3b4048",  # Subtle border for better appearance
    "table_selection_bg": "#528bff",  # Brighter blue selection for better visibility (One Dark inspired)
    "table_selection_text": "#ffffff",  # Pure white text for maximum contrast
    "table_hover_bg": "#2c313a",  # Slightly lighter on hover (One Dark inspired)
    "table_text": "#ffffff",  # Pure white text for maximum contrast and readability

    # Gradients - Enhanced for better visibility
    "main_bg_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #121212, stop:1 #1e1e1e)",
    "button_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2196f3, stop:1 #0d47a1)",
    "button_hover_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #42a5f5, stop:1 #1976d2)",
    "button_pressed_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #0d47a1, stop:1 #0a367a)",
    "success_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #4caf50, stop:1 #2e7d32)",
    "danger_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #f44336, stop:1 #c62828)",
    "header_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #2196f3, stop:1 #0d47a1)",
    "toolbar_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1565c0, stop:1 #0a0a0a)",
    "menu_gradient": "qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #1e1e1e, stop:1 #121212)",
    "dialog_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e1e1e, stop:1 #212121)",

    # Borders and effects - Enhanced for better visibility
    "border_radius": "6px",
    "border_width": "1px",
    "border_color": "#4285f4",  # Bright blue border for visibility
    "focus_border_color": "#2196f3",  # Bright blue focus
    "focus_border_width": "2px",

    # Tabs and panels - Improved contrast
    "tab_active_bg": "#2196f3",  # Bright blue active tab
    "tab_active_text": "#ffffff",  # Pure white text for maximum contrast
    "tab_inactive_bg": "#1e1e1e",  # Dark gray inactive tab
    "tab_inactive_text": "#ffffff",  # Pure white text for maximum contrast
    "tab_hover_bg": "#2979ff",  # Brighter blue on hover for better visibility
    "panel_bg": "#1e1e1e",  # Dark gray panel
    "panel_border": "#4285f4",  # Bright blue border for visibility

    # Scrollbars - Enhanced for better visibility
    "scrollbar_bg": "#1e1e1e",  # Dark gray background
    "scrollbar_handle": "#4285f4",  # Bright blue handle for visibility
    "scrollbar_handle_hover": "#64b5f6",  # Lighter blue on hover

    # Dialogs and popups - Improved contrast
    "dialog_bg": "#1e1e1e",  # Dark gray background
    "dialog_border": "#4285f4",  # Bright blue border for visibility
    "dialog_title_bg": "#1976d2",  # Darker blue title
    "dialog_title_text": "#ffffff",  # Pure white text for maximum contrast

    # Menus - Improved contrast
    "menu_bg": "#1e1e1e",  # Dark gray background
    "menu_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_hover_bg": "#2979ff",  # Brighter blue on hover for better visibility
    "menu_hover_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_border": "#4285f4",  # Bright blue border for visibility

    # Misc - Enhanced for better visibility
    "tooltip_bg": "#1976d2",  # Darker blue background
    "tooltip_text": "#ffffff",  # Pure white text for maximum contrast
    "separator": "#4285f4",  # Bright blue separator for visibility
    "disabled_opacity": "0.5",  # 50% opacity when disabled

    # Font settings
    "font_family": "'Almarai', 'Arial', sans-serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Dreamy theme - Dark fantasy UI with glowing elements
DREAMY_THEME = {
    "name": "Dreamy",
    "description": "نمط خيالي داكن مع عناصر متوهجة",
    # Main colors
    "main_bg": "#0e0f1a",  # Deep midnight navy
    "main_text": "#ffffff",  # Pure white for maximum contrast
    "primary": "#865aff",  # Vibrant purple
    "primary_dark": "#6a3ecf",  # Darker purple
    "primary_light": "#d0a5ff",  # Light lavender
    "secondary": "#5abef5",  # Soft cyan
    "secondary_dark": "#3a8fc0",  # Darker cyan
    "secondary_light": "#a5e0ff",  # Light cyan
    "accent": "#ff9de2",  # Soft pink
    "success": "#5ae0aa",  # Dreamy teal
    "success_dark": "#3aaa80",  # Darker teal
    "danger": "#ff6b9d",  # Soft red/pink
    "danger_dark": "#d84a7a",  # Darker red/pink
    "warning": "#ffcf54",  # Soft amber
    "warning_dark": "#e0aa30",  # Darker amber
    "button_text": "#ffffff",  # Pure white for button text

    # Input fields
    "input_bg": "#1a1a2b",  # Dark translucent background
    "input_border": "#b48add",  # Soft purple border
    "input_focus": "#d0a5ff",  # Bright purple for focus
    "input_text": "#ffffff",  # White text for contrast
    "input_placeholder": "#a095c3",  # Soft lavender for placeholder
    "input_disabled_bg": "#151525",  # Darker background when disabled
    "input_disabled_text": "#6a6a8a",  # Muted text when disabled

    # Tables - Enhanced for dreamy dark fantasy look (Dracula inspired) with improved contrast
    "table_header_bg": "#282a36",  # Dark header (Dracula inspired)
    "table_header_text": "#ffffff",  # Pure white text for maximum contrast
    "table_alternate_bg": "#1e1f29",  # Dark background (Dracula inspired)
    "table_border": "#44475a",  # Subtle border (Dracula inspired)
    "table_selection_bg": "#bd93f9",  # Brighter purple selection for better visibility (Dracula inspired)
    "table_selection_text": "#ffffff",  # Pure white text for maximum contrast
    "table_hover_bg": "#313442",  # Slightly lighter on hover (Dracula inspired)
    "table_text": "#ffffff",  # Pure white text for maximum contrast and readability

    # Gradients - Dreamy aurora-like gradients
    "main_bg_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0e0f1a, stop:0.3 #141429, stop:0.7 #1a1a33, stop:1 #1e1e38)",
    "button_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #865aff, stop:1 #d0a5ff)",
    "button_hover_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9a6aff, stop:1 #e0b5ff)",
    "button_pressed_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6a3ecf, stop:1 #b48add)",
    "success_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5ae0aa, stop:1 #3aaa80)",
    "danger_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff6b9d, stop:1 #d84a7a)",
    "header_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2c2c45, stop:1 #3d3d5c)",
    "toolbar_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1e1e38, stop:1 #2c2c45)",
    "menu_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1a1a2b, stop:1 #2c2c45)",
    "dialog_gradient": "qradial-gradient(circle, #2c2c45 0%, #1a1a2b 100%)",

    # Borders and effects - Soft glowing borders
    "border_radius": "12px",
    "border_width": "1px",
    "border_color": "#b48add",
    "focus_border_color": "#d0a5ff",
    "focus_border_width": "2px",

    # Tabs and panels
    "tab_active_bg": "#4a3a80",  # Purple active tab
    "tab_active_text": "#ffffff",  # White text for contrast
    "tab_inactive_bg": "#1c1c2d",  # Dark inactive tab
    "tab_inactive_text": "#a095c3",  # Soft purple text
    "tab_hover_bg": "#2c2c45",  # Slightly lighter on hover
    "panel_bg": "#1a1a2b",  # Dark panel background
    "panel_border": "#3d3d5c",  # Subtle border

    # Scrollbars
    "scrollbar_bg": "#0e0f1a",  # Dark scrollbar background
    "scrollbar_handle": "#4a3a80",  # Purple handle
    "scrollbar_handle_hover": "#6a3ecf",  # Brighter on hover

    # Dialogs and popups - Improved contrast
    "dialog_bg": "#1a1a2b",  # Dark dialog background
    "dialog_border": "#d0a5ff",  # Brighter purple border for better visibility
    "dialog_title_bg": "#4a3a80",  # Brighter purple-blue title for better visibility
    "dialog_title_text": "#ffffff",  # Pure white text for maximum contrast

    # Menus - Improved contrast
    "menu_bg": "#1a1a2b",  # Dark menu background
    "menu_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_hover_bg": "#4a3a80",  # Brighter purple on hover for better visibility
    "menu_hover_text": "#ffffff",  # White text on hover
    "menu_border": "#b48add",  # Brighter purple border for better visibility

    # Misc
    "tooltip_bg": "#4a3a80",  # Purple tooltip
    "tooltip_text": "#ffffff",  # White text for contrast
    "separator": "#3d3d5c",  # Subtle separator
    "disabled_opacity": "0.6",  # Slightly transparent when disabled

    # Font settings - Rounded and elegant
    "font_family": "'Almarai', 'Arial', sans-serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Space theme - Enhanced contrast
SPACE_THEME = {
    "name": "Space",
    "description": "نمط فضائي بألوان كونية وتباين عالي",
    # Main colors
    "main_bg": "#0d1b38",  # Darker blue for better contrast
    "main_text": "#ffffff",  # Pure white for maximum contrast
    "primary": "#7e57c2",  # Bright purple
    "primary_dark": "#5e35b1",  # Darker purple
    "primary_light": "#b39ddb",  # Lighter purple for better visibility
    "secondary": "#448aff",  # Bright blue
    "secondary_dark": "#2979ff",  # Darker blue
    "secondary_light": "#82b1ff",  # Light blue
    "accent": "#64ffda",  # Bright teal
    "success": "#00e676",  # Brighter green for better visibility
    "success_dark": "#00c853",  # Darker green
    "danger": "#ff5252",  # Bright red
    "danger_dark": "#ff1744",  # Darker red
    "warning": "#ffab40",  # Bright orange
    "warning_dark": "#ff9100",  # Darker orange
    "button_text": "#ffffff",  # Pure white for button text

    # Input fields - Improved contrast
    "input_bg": "#1a237e",  # Dark blue background
    "input_border": "#7e57c2",  # Bright purple border for visibility
    "input_focus": "#b39ddb",  # Light purple focus for better visibility
    "input_text": "#ffffff",  # Pure white text for maximum contrast
    "input_placeholder": "#b0bec5",  # Light blue-gray for better visibility
    "input_disabled_bg": "#0d1b38",  # Darker blue when disabled
    "input_disabled_text": "#9fa8da",  # Light purple-blue for better visibility

    # Tables - Enhanced cosmic style (Material Palenight inspired)
    "table_header_bg": "#2f3347",  # Dark blue header (Material Palenight inspired)
    "table_header_text": "#ffffff",  # Pure white text for maximum contrast
    "table_alternate_bg": "#1b1e2b",  # Very dark blue background (Material Palenight inspired)
    "table_border": "#3b4252",  # Medium blue border for subtle cosmic effect
    "table_selection_bg": "#7e57c2",  # Purple selection (Material Palenight inspired)
    "table_selection_text": "#ffffff",  # Pure white text for maximum contrast
    "table_hover_bg": "#2a2d3e",  # Medium blue on hover (Material Palenight inspired)
    "table_text": "#a6accd",  # Light blue-gray text for better readability (Material Palenight inspired)

    # Gradients - Enhanced for better visibility
    "main_bg_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #0d1b38, stop:0.3 #1a237e, stop:0.7 #283593, stop:1 #303f9f)",
    "button_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9575cd, stop:1 #5e35b1)",
    "button_hover_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #b39ddb, stop:1 #7e57c2)",
    "button_pressed_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5e35b1, stop:1 #4527a0)",
    "success_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #00e676, stop:1 #00c853)",
    "danger_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff5252, stop:1 #d50000)",
    "header_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7e57c2, stop:1 #5e35b1)",
    "toolbar_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5e35b1, stop:1 #3949ab)",
    "menu_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #3949ab, stop:1 #1a237e)",
    "dialog_gradient": "qradial-gradient(circle, #303f9f 0%, #1a237e 100%)",

    # Borders and effects - Enhanced for better visibility
    "border_radius": "8px",
    "border_width": "1px",
    "border_color": "#7e57c2",  # Bright purple border for visibility
    "focus_border_color": "#b39ddb",  # Light purple focus for better visibility
    "focus_border_width": "2px",

    # Tabs and panels - Improved contrast
    "tab_active_bg": "#7e57c2",  # Bright purple active tab
    "tab_active_text": "#ffffff",  # Pure white text for maximum contrast
    "tab_inactive_bg": "#1a237e",  # Dark blue inactive tab
    "tab_inactive_text": "#b0bec5",  # Light blue-gray for better visibility
    "tab_hover_bg": "#3949ab",  # Medium blue on hover
    "panel_bg": "#1a237e",  # Dark blue panel
    "panel_border": "#7e57c2",  # Bright purple border for visibility

    # Scrollbars - Enhanced for better visibility
    "scrollbar_bg": "#0d1b38",  # Darker blue background
    "scrollbar_handle": "#7e57c2",  # Bright purple handle for visibility
    "scrollbar_handle_hover": "#b39ddb",  # Light purple on hover

    # Dialogs and popups - Improved contrast
    "dialog_bg": "#1a237e",  # Dark blue background
    "dialog_border": "#7e57c2",  # Bright purple border for visibility
    "dialog_title_bg": "#5e35b1",  # Dark purple title
    "dialog_title_text": "#ffffff",  # Pure white text for maximum contrast

    # Menus - Improved contrast
    "menu_bg": "#1a237e",  # Dark blue background
    "menu_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_hover_bg": "#3949ab",  # Medium blue on hover
    "menu_hover_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_border": "#7e57c2",  # Bright purple border for visibility

    # Misc - Enhanced for better visibility
    "tooltip_bg": "#5e35b1",  # Dark purple background
    "tooltip_text": "#ffffff",  # Pure white text for maximum contrast
    "separator": "#7e57c2",  # Bright purple separator for visibility
    "disabled_opacity": "0.5",  # 50% opacity when disabled

    # Font settings
    "font_family": "'Almarai', 'Arial', sans-serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Galaxy theme - Enhanced contrast
GALAXY_THEME = {
    "name": "Galaxy",
    "description": "نمط مجرة بألوان كونية عميقة وتباين عالي",
    # Main colors
    "main_bg": "#0a0e21",  # Very dark blue-black
    "main_text": "#ffffff",  # Pure white for maximum contrast
    "primary": "#aa00ff",  # Vibrant purple
    "primary_dark": "#7b1fa2",  # Darker purple
    "primary_light": "#e040fb",  # Light purple for better visibility
    "secondary": "#00b0ff",  # Bright blue
    "secondary_dark": "#0091ea",  # Darker blue
    "secondary_light": "#80d8ff",  # Light blue
    "accent": "#d500f9",  # Bright magenta
    "success": "#00e676",  # Bright green
    "success_dark": "#00c853",  # Darker green
    "danger": "#ff1744",  # Bright red
    "danger_dark": "#d50000",  # Darker red
    "warning": "#ffab00",  # Bright amber
    "warning_dark": "#ff8f00",  # Darker amber
    "button_text": "#ffffff",  # Pure white for button text

    # Input fields - Improved contrast
    "input_bg": "#1a1f33",  # Dark blue-purple background
    "input_border": "#aa00ff",  # Vibrant purple border for visibility
    "input_focus": "#e040fb",  # Light purple focus for better visibility
    "input_text": "#ffffff",  # Pure white text for maximum contrast
    "input_placeholder": "#b0bec5",  # Light blue-gray for better visibility
    "input_disabled_bg": "#0a0e21",  # Very dark blue-black when disabled
    "input_disabled_text": "#9e9e9e",  # Medium gray for better visibility

    # Tables - Enhanced galaxy style (Monokai Pro inspired) with improved contrast
    "table_header_bg": "#2d2a2e",  # Dark header (Monokai Pro inspired)
    "table_header_text": "#ffffff",  # Pure white text for maximum contrast
    "table_alternate_bg": "#221f22",  # Very dark background (Monokai Pro inspired)
    "table_border": "#403e41",  # Subtle border (Monokai Pro inspired)
    "table_selection_bg": "#78dce8",  # Cyan selection (Monokai Pro inspired)
    "table_selection_text": "#000000",  # Black text for maximum contrast on selection
    "table_hover_bg": "#363438",  # Slightly lighter on hover (Monokai Pro inspired)
    "table_text": "#ffffff",  # Pure white text for maximum contrast and readability

    # Gradients - Enhanced for better visibility
    "main_bg_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #000428, stop:0.3 #0a0e21, stop:0.7 #0d1b38, stop:1 #004e92)",
    "button_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #d500f9, stop:1 #aa00ff)",
    "button_hover_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e040fb, stop:1 #d500f9)",
    "button_pressed_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #aa00ff, stop:1 #7b1fa2)",
    "success_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #00e676, stop:1 #00c853)",
    "danger_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #ff1744, stop:1 #d50000)",
    "header_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #d500f9, stop:1 #aa00ff)",
    "toolbar_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #aa00ff, stop:1 #7b1fa2)",
    "menu_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #311b92, stop:1 #0a0e21)",
    "dialog_gradient": "qradial-gradient(circle, #1a1f33 0%, #0a0e21 100%)",

    # Borders and effects - Enhanced for better visibility
    "border_radius": "8px",
    "border_width": "1px",
    "border_color": "#aa00ff",  # Vibrant purple border for visibility
    "focus_border_color": "#e040fb",  # Light purple focus for better visibility
    "focus_border_width": "2px",

    # Tabs and panels - Improved contrast
    "tab_active_bg": "#aa00ff",  # Vibrant purple active tab
    "tab_active_text": "#ffffff",  # Pure white text for maximum contrast
    "tab_inactive_bg": "#1a1f33",  # Dark blue-purple inactive tab
    "tab_inactive_text": "#b0bec5",  # Light blue-gray for better visibility
    "tab_hover_bg": "#311b92",  # Deep indigo on hover
    "panel_bg": "#1a1f33",  # Dark blue-purple panel
    "panel_border": "#aa00ff",  # Vibrant purple border for visibility

    # Scrollbars - Enhanced for better visibility
    "scrollbar_bg": "#0a0e21",  # Very dark blue-black background
    "scrollbar_handle": "#aa00ff",  # Vibrant purple handle for visibility
    "scrollbar_handle_hover": "#e040fb",  # Light purple on hover

    # Dialogs and popups - Improved contrast
    "dialog_bg": "#1a1f33",  # Dark blue-purple background
    "dialog_border": "#e040fb",  # Brighter purple border for better visibility
    "dialog_title_bg": "#9c27b0",  # Brighter purple title for better visibility
    "dialog_title_text": "#ffffff",  # Pure white text for maximum contrast

    # Menus - Improved contrast
    "menu_bg": "#1a1f33",  # Dark blue-purple background
    "menu_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_hover_bg": "#4527a0",  # Brighter indigo on hover for better visibility
    "menu_hover_text": "#ffffff",  # Pure white text for maximum contrast
    "menu_border": "#e040fb",  # Brighter purple border for better visibility

    # Misc - Enhanced for better visibility
    "tooltip_bg": "#7b1fa2",  # Dark purple background
    "tooltip_text": "#ffffff",  # Pure white text for maximum contrast
    "separator": "#aa00ff",  # Vibrant purple separator for visibility
    "disabled_opacity": "0.5",  # 50% opacity when disabled

    # Font settings
    "font_family": "'Almarai', 'Arial', sans-serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Mono theme - Enhanced contrast
MONO_THEME = {
    "name": "Mono",
    "description": "نمط أحادي اللون بسيط وأنيق بتباين عالي",
    # Main colors - Improved contrast with pure black and white
    "main_bg": "#ffffff",  # Pure white background
    "main_text": "#000000",  # Pure black text for maximum contrast
    "primary": "#212121",  # Very dark gray (almost black)
    "primary_dark": "#000000",  # Pure black
    "primary_light": "#424242",  # Dark gray
    "secondary": "#424242",  # Dark gray
    "secondary_dark": "#212121",  # Very dark gray
    "secondary_light": "#616161",  # Medium gray
    "accent": "#424242",  # Dark gray
    "success": "#212121",  # Very dark gray
    "success_dark": "#000000",  # Pure black
    "danger": "#212121",  # Very dark gray
    "danger_dark": "#000000",  # Pure black
    "warning": "#424242",  # Dark gray
    "warning_dark": "#212121",  # Very dark gray
    "button_text": "#ffffff",  # Pure white for button text

    # Input fields - Improved contrast
    "input_bg": "#ffffff",  # Pure white background
    "input_border": "#000000",  # Pure black border for maximum contrast
    "input_focus": "#000000",  # Pure black focus for maximum contrast
    "input_text": "#000000",  # Pure black text for maximum contrast
    "input_placeholder": "#757575",  # Medium gray for better visibility
    "input_disabled_bg": "#f5f5f5",  # Light gray when disabled
    "input_disabled_text": "#757575",  # Medium gray for better visibility

    # Tables - Improved contrast with dark mono style (GitHub Dark inspired)
    "table_header_bg": "#161b22",  # Dark header (GitHub Dark inspired)
    "table_header_text": "#ffffff",  # Pure white text for maximum contrast
    "table_alternate_bg": "#0d1117",  # Very dark background (GitHub Dark inspired)
    "table_border": "#30363d",  # Dark gray border (GitHub Dark inspired)
    "table_selection_bg": "#388bfd",  # Blue selection (GitHub Dark inspired)
    "table_selection_text": "#ffffff",  # Pure white text for maximum contrast
    "table_hover_bg": "#21262d",  # Medium gray on hover (GitHub Dark inspired)
    "table_text": "#ffffff",  # Pure white text for maximum contrast and readability

    # Gradients - No gradients for mono theme
    "main_bg_gradient": None,
    "button_gradient": None,
    "button_hover_gradient": None,
    "button_pressed_gradient": None,
    "success_gradient": None,
    "danger_gradient": None,
    "header_gradient": None,
    "toolbar_gradient": None,
    "menu_gradient": None,
    "dialog_gradient": None,

    # Borders and effects - Sharp edges for mono theme
    "border_radius": "0px",
    "border_width": "1px",
    "border_color": "#000000",  # Pure black border for maximum contrast
    "focus_border_color": "#000000",  # Pure black focus for maximum contrast
    "focus_border_width": "2px",

    # Tabs and panels - Improved contrast
    "tab_active_bg": "#000000",  # Pure black active tab for maximum contrast
    "tab_active_text": "#ffffff",  # Pure white text for maximum contrast
    "tab_inactive_bg": "#f5f5f5",  # Light gray inactive tab
    "tab_inactive_text": "#000000",  # Pure black text for maximum contrast
    "tab_hover_bg": "#e0e0e0",  # Light gray on hover
    "panel_bg": "#ffffff",  # Pure white panel
    "panel_border": "#000000",  # Pure black border for maximum contrast

    # Scrollbars - Improved contrast
    "scrollbar_bg": "#f5f5f5",  # Light gray background
    "scrollbar_handle": "#212121",  # Very dark gray handle for better visibility
    "scrollbar_handle_hover": "#000000",  # Pure black on hover for maximum contrast

    # Dialogs and popups - Improved contrast
    "dialog_bg": "#ffffff",  # Pure white background
    "dialog_border": "#000000",  # Pure black border for maximum contrast
    "dialog_title_bg": "#000000",  # Pure black title for maximum contrast
    "dialog_title_text": "#ffffff",  # Pure white text for maximum contrast

    # Menus - Improved contrast
    "menu_bg": "#ffffff",  # Pure white background
    "menu_text": "#000000",  # Pure black text for maximum contrast
    "menu_hover_bg": "#cccccc",  # Darker gray on hover for better visibility
    "menu_hover_text": "#000000",  # Pure black text for maximum contrast
    "menu_border": "#000000",  # Pure black border for maximum contrast

    # Misc - Improved contrast
    "tooltip_bg": "#000000",  # Pure black background for maximum contrast
    "tooltip_text": "#ffffff",  # Pure white text for maximum contrast
    "separator": "#000000",  # Pure black separator for maximum contrast
    "disabled_opacity": "0.5",  # 50% opacity when disabled

    # Font settings
    "font_family": "'Almarai', 'Arial', sans-serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Vintage theme - Enhanced contrast
VINTAGE_THEME = {
    "name": "Vintage",
    "description": "نمط كلاسيكي بألوان تراثية وتباين عالي",
    # Main colors - Improved contrast
    "main_bg": "#f9f3e6",  # Warm beige background
    "main_text": "#3e2723",  # Very dark brown text for maximum contrast
    "primary": "#6d4c41",  # Dark brown
    "primary_dark": "#5d4037",  # Darker brown
    "primary_light": "#8d6e63",  # Light brown
    "secondary": "#558b2f",  # Dark green
    "secondary_dark": "#33691e",  # Darker green
    "secondary_light": "#7cb342",  # Light green
    "accent": "#a1887f",  # Medium brown
    "success": "#33691e",  # Dark green
    "success_dark": "#1b5e20",  # Darker green
    "danger": "#b71c1c",  # Dark red
    "danger_dark": "#7f0000",  # Darker red
    "warning": "#e65100",  # Dark orange
    "warning_dark": "#bf360c",  # Darker orange
    "button_text": "#ffffff",  # Pure white for button text

    # Input fields - Improved contrast
    "input_bg": "#fff8e1",  # Light cream background
    "input_border": "#8d6e63",  # Medium brown border for better visibility
    "input_focus": "#6d4c41",  # Dark brown focus for better visibility
    "input_text": "#3e2723",  # Very dark brown text for maximum contrast
    "input_placeholder": "#8d6e63",  # Medium brown for better visibility
    "input_disabled_bg": "#f5f0e6",  # Light beige when disabled
    "input_disabled_text": "#a1887f",  # Medium brown for better visibility

    # Tables - Dark vintage style (Gruvbox Dark inspired)
    "table_header_bg": "#3c3836",  # Dark brown header (Gruvbox Dark inspired)
    "table_header_text": "#fbf1c7",  # Light cream text for maximum contrast (Gruvbox Dark inspired)
    "table_alternate_bg": "#282828",  # Dark background (Gruvbox Dark inspired)
    "table_border": "#504945",  # Medium brown border (Gruvbox Dark inspired)
    "table_selection_bg": "#83a598",  # Blue-gray selection (Gruvbox Dark inspired)
    "table_selection_text": "#282828",  # Dark text for contrast on selection
    "table_hover_bg": "#32302f",  # Slightly lighter on hover (Gruvbox Dark inspired)
    "table_text": "#ebdbb2",  # Light beige text for better readability (Gruvbox Dark inspired)

    # Gradients - Enhanced for better visibility
    "main_bg_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f9f3e6, stop:0.5 #f5f0e6, stop:1 #f1e8d9)",
    "button_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #8d6e63, stop:1 #5d4037)",
    "button_hover_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #a1887f, stop:1 #6d4c41)",
    "button_pressed_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5d4037, stop:1 #3e2723)",
    "success_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #558b2f, stop:1 #33691e)",
    "danger_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #c62828, stop:1 #b71c1c)",
    "header_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6d4c41, stop:1 #5d4037)",
    "toolbar_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6d4c41, stop:1 #3e2723)",
    "menu_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #fff8e1, stop:1 #f5f0e6)",
    "dialog_gradient": "qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #f9f3e6, stop:1 #efebe9)",

    # Borders and effects - Enhanced for better visibility
    "border_radius": "8px",
    "border_width": "1px",
    "border_color": "#8d6e63",  # Medium brown border for better visibility
    "focus_border_color": "#5d4037",  # Very dark brown focus for better visibility
    "focus_border_width": "2px",

    # Tabs and panels - Improved contrast
    "tab_active_bg": "#5d4037",  # Very dark brown active tab for better contrast
    "tab_active_text": "#ffffff",  # Pure white text for maximum contrast
    "tab_inactive_bg": "#f5f0e6",  # Light beige inactive tab
    "tab_inactive_text": "#6d4c41",  # Dark brown text for better visibility
    "tab_hover_bg": "#efebe9",  # Light gray-beige on hover
    "panel_bg": "#fff8e1",  # Light cream panel
    "panel_border": "#8d6e63",  # Medium brown border for better visibility

    # Scrollbars - Enhanced for better visibility
    "scrollbar_bg": "#f9f3e6",  # Warm beige background
    "scrollbar_handle": "#8d6e63",  # Medium brown handle for better visibility
    "scrollbar_handle_hover": "#6d4c41",  # Dark brown on hover

    # Dialogs and popups - Improved contrast
    "dialog_bg": "#fff8e1",  # Light cream background
    "dialog_border": "#8d6e63",  # Medium brown border for better visibility
    "dialog_title_bg": "#5d4037",  # Very dark brown title for better contrast
    "dialog_title_text": "#ffffff",  # Pure white text for maximum contrast

    # Menus - Improved contrast
    "menu_bg": "#fff8e1",  # Light cream background
    "menu_text": "#3e2723",  # Very dark brown text for maximum contrast
    "menu_hover_bg": "#efebe9",  # Light gray-beige on hover
    "menu_hover_text": "#5d4037",  # Very dark brown text for better visibility
    "menu_border": "#8d6e63",  # Medium brown border for better visibility

    # Misc - Enhanced for better visibility
    "tooltip_bg": "#5d4037",  # Very dark brown background for better contrast
    "tooltip_text": "#ffffff",  # Pure white text for maximum contrast
    "separator": "#8d6e63",  # Medium brown separator for better visibility
    "disabled_opacity": "0.6",  # 60% opacity when disabled

    # Font settings - Keep the serif font for vintage feel
    "font_family": "'Almarai', 'Times New Roman', serif",
    "font_size_small": "10pt",
    "font_size_normal": "12pt",
    "font_size_large": "14pt",
    "font_size_xlarge": "16pt",
    "font_weight_normal": "normal",
    "font_weight_bold": "bold"
}

# Dictionary of all themes
THEMES = {
    "default": DEFAULT_THEME,
    "dark": DARK_THEME,
    "dreamy": DREAMY_THEME,
    "space": SPACE_THEME,
    "galaxy": GALAXY_THEME,
    "mono": MONO_THEME,
    "vintage": VINTAGE_THEME,
}


def get_theme_style(theme_key):
    """Generate CSS style for the selected theme.

    Args:
        theme_key (str): Key of the theme to use

    Returns:
        str: CSS style for the theme
    """
    if theme_key not in THEMES:
        theme_key = "default"

    theme = THEMES[theme_key]

    # Special handling for gradient backgrounds
    main_bg_style = f"background-color: {theme['main_bg']};"
    if "main_bg_gradient" in theme and theme["main_bg_gradient"]:
        main_bg_style = f"background: {theme['main_bg_gradient']};"

    button_bg_style = f"background-color: {theme['primary']};"
    if "button_gradient" in theme and theme["button_gradient"]:
        button_bg_style = f"background: {theme['button_gradient']};"

    # Define toolbar background based on theme
    toolbar_bg_style = f"background-color: {theme['primary_dark']};"
    if "toolbar_gradient" in theme and theme["toolbar_gradient"]:
        toolbar_bg_style = f"background: {theme['toolbar_gradient']};"
    elif "main_bg_gradient" in theme and theme["main_bg_gradient"]:
        # If no specific toolbar gradient but has main gradient, use a darker version
        toolbar_bg_style = f"background-color: {theme['primary_dark']};"

    button_hover_style = f"background-color: {theme['primary_dark']};"
    if "button_hover_gradient" in theme and theme["button_hover_gradient"]:
        button_hover_style = f"background: {theme['button_hover_gradient']};"

    button_pressed_style = f"background-color: {theme['primary_dark']};"
    if "button_pressed_gradient" in theme and theme["button_pressed_gradient"]:
        button_pressed_style = f"background: {theme['button_pressed_gradient']};"

    success_button_bg_style = f"background-color: {theme['success']};"
    if "success_gradient" in theme and theme["success_gradient"]:
        success_button_bg_style = f"background: {theme['success_gradient']};"

    danger_button_bg_style = f"background-color: {theme['danger']};"
    if "danger_gradient" in theme and theme["danger_gradient"]:
        danger_button_bg_style = f"background: {theme['danger_gradient']};"

    header_bg_style = f"background-color: {theme['table_header_bg']};"
    if "header_gradient" in theme and theme["header_gradient"]:
        header_bg_style = f"background: {theme['header_gradient']};"

    toolbar_bg_style = f"background-color: {theme['primary_dark']};"
    if "toolbar_gradient" in theme and theme["toolbar_gradient"]:
        toolbar_bg_style = f"background: {theme['toolbar_gradient']};"

    menu_bg_style = f"background-color: {theme['menu_bg']};"
    if "menu_gradient" in theme and theme["menu_gradient"]:
        menu_bg_style = f"background: {theme['menu_gradient']};"

    dialog_bg_style = f"background-color: {theme['dialog_bg']};"
    if "dialog_gradient" in theme and theme["dialog_gradient"]:
        dialog_bg_style = f"background: {theme['dialog_gradient']};"

    return f"""
    /* Main Application Style */
    QWidget {{
        {main_bg_style}
        color: {theme["main_text"]};
        font-family: {theme["font_family"]};
    }}

    /* Labels */
    QLabel {{
        color: {theme["main_text"]};
        font-size: {theme["font_size_normal"]};
        margin: 4px;
    }}

    /* Main Window */
    QMainWindow {{
        {main_bg_style}
        color: {theme["main_text"]};
    }}

    /* Central Widget */
    QMainWindow::centralWidget {{
        {main_bg_style}
        color: {theme["main_text"]};
    }}

    /* Input Fields */
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {{
        background-color: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
        border: 1px solid {theme["input_border"]};
        border-radius: 8px;
        padding: 10px;
        min-height: 40px;
        font-size: 12pt;
        selection-background-color: {theme["primary_light"]};
        selection-color: {theme["main_text"]};
        color: {theme["main_text"]};
    }}

    QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {{
        border: 2px solid {theme["input_focus"]};
    }}

    /* Buttons */
    QPushButton {{
        {button_bg_style}
        color: white;
        border: none;
        border-radius: {theme["border_radius"]};
        padding: 12px 24px;
        font-weight: {theme["font_weight_bold"]};
        font-size: {theme["font_size_normal"]};
        min-width: 130px;
        min-height: 45px;
    }}

    QPushButton:hover {{
        {button_hover_style}
    }}

    QPushButton:pressed {{
        {button_pressed_style}
    }}

    QPushButton:disabled {{
        background-color: {theme["input_border"]};
        color: {theme["main_text"]};
        opacity: 0.7;
    }}

    /* Success Buttons */
    QPushButton[style="success"] {{
        {success_button_bg_style}
        color: white;
    }}

    QPushButton[style="success"]:hover {{
        background-color: {theme["success_dark"]};
    }}

    /* Danger Buttons */
    QPushButton[style="danger"] {{
        {danger_button_bg_style}
        color: white;
    }}

    QPushButton[style="danger"]:hover {{
        background-color: {theme["danger_dark"]};
    }}

    /* Tables - Enhanced for better contrast in dark themes */
    QTableWidget {{
        {f"background-color: white; alternate-background-color: #f5f5f5;" if theme_key == "default" else f"""
        background-color: {
            "#0a0e21" if theme_key == "galaxy" else
            "#0e0f1a" if theme_key == "dreamy" else
            "#0d1b38" if theme_key == "space" else
            "#121212" if theme_key == "dark" else
            theme["table_alternate_bg"]
        };
        alternate-background-color: {
            "#0a0e21" if theme_key == "galaxy" else
            "#0e0f1a" if theme_key == "dreamy" else
            "#0d1b38" if theme_key == "space" else
            "#121212" if theme_key == "dark" else
            theme["table_alternate_bg"]
        };
        """}
        border: 1px solid {theme["table_border"]};
        border-radius: 6px;
        gridline-color: {theme["table_border"]};
        font-size: 12pt;
        color: {theme["table_text"]};
        selection-background-color: {theme["table_selection_bg"]};
        selection-color: {theme["table_selection_text"]};
    }}

    QTableWidget::item {{
        padding: 10px;
        border-bottom: 1px solid {theme["table_border"]};
        {f"background-color: white;" if theme_key == "default" else f"""background-color: {
            "#0a0e21" if theme_key == "galaxy" else
            "#0e0f1a" if theme_key == "dreamy" else
            "#0d1b38" if theme_key == "space" else
            "#121212" if theme_key == "dark" else
            theme["table_alternate_bg"]
        };"""}
        color: {theme["table_text"]};
    }}

    QTableWidget::item:alternate {{
        {f"background-color: #f5f5f5;" if theme_key == "default" else f"""
        background-color: {
            # For Galaxy theme - deep space gradient effect
            "#0a0e21" if theme_key == "galaxy" else
            # For Dreamy theme - slightly lighter dreamy background
            "#0e0f1a" if theme_key == "dreamy" else
            # For Space theme - deep space blue
            "#0d1b38" if theme_key == "space" else
            # For Dark theme - slightly lighter background
            "#121212" if theme_key == "dark" else
            # For Mono theme - slightly darker background for contrast
            "#0a0a0a" if theme_key == "mono" else
            # For Vintage theme - slightly darker vintage background
            "#2c2418" if theme_key == "vintage" else
            # Default fallback - slightly lighter version of the base color
            "rgba(" + str(min(int(theme["table_alternate_bg"][1:3], 16) + 15, 255)) + ", " +
            str(min(int(theme["table_alternate_bg"][3:5], 16) + 15, 255)) + ", " +
            str(min(int(theme["table_alternate_bg"][5:7], 16) + 15, 255)) + ", 255)"
        };
        """}
    }}

    QTableWidget::item:hover {{
        background-color: {theme["table_hover_bg"]};
        border: 1px solid {theme["primary_light"]};
        color: {theme["table_text"]};
    }}

    /* Table Corner Button */
    QTableWidget QTableCornerButton::section {{
        {f"background-color: #0d47a1;" if theme_key == "default" else f"""
        background-color: {theme["table_header_bg"]};
        {
            # Galaxy theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #aa00ff, stop:1 #7b1fa2);" if theme_key == "galaxy" else
            # Dreamy theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4a3a80, stop:1 #2c2c45);" if theme_key == "dreamy" else
            # Space theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7e57c2, stop:1 #5e35b1);" if theme_key == "space" else
            # Dark theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2196f3, stop:1 #1976d2);" if theme_key == "dark" else
            ""
        }
        """}
        border: none;
    }}

    /* Empty table cells */
    QTableWidget::item:empty {{
        background-color: {theme["table_alternate_bg"]};
    }}

    QTableWidget::item:selected {{
        background-color: {theme["table_selection_bg"]};
        color: {theme["table_selection_text"]};
        border: 1px solid {theme["primary"]};
    }}

    /* Buttons inside tables */
    QTableWidget QPushButton {{
        {f"background-color: #1976d2;" if theme_key == "default" else f"""
        background-color: {theme["primary"]};
        {
            # Galaxy theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #d500f9, stop:1 #aa00ff);" if theme_key == "galaxy" else
            # Dreamy theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #865aff, stop:1 #6a3ecf);" if theme_key == "dreamy" else
            # Space theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7e57c2, stop:1 #5e35b1);" if theme_key == "space" else
            # Dark theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2196f3, stop:1 #1976d2);" if theme_key == "dark" else
            # Vintage theme - add gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #8d6e63, stop:1 #5d4037);" if theme_key == "vintage" else
            ""
        }
        """}
        color: {theme["table_header_text"]};
        border: none;
        border-radius: {theme["border_radius"]};
        padding: 6px 12px;
        font-weight: bold;
        min-width: 80px;
        min-height: 30px;
    }}

    QTableWidget QPushButton:hover {{
        {f"background-color: #0d47a1;" if theme_key == "default" else f"""
        background-color: {theme["primary_dark"]};
        {
            # Galaxy theme - add hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #e040fb, stop:1 #d500f9);" if theme_key == "galaxy" else
            # Dreamy theme - add hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9a6aff, stop:1 #865aff);" if theme_key == "dreamy" else
            # Space theme - add hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9575cd, stop:1 #7e57c2);" if theme_key == "space" else
            # Dark theme - add hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #42a5f5, stop:1 #2196f3);" if theme_key == "dark" else
            # Vintage theme - add hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #a1887f, stop:1 #8d6e63);" if theme_key == "vintage" else
            ""
        }
        """}
    }}

    QTableWidget QPushButton:pressed {{
        background-color: {theme["primary_light"] if theme_key != "default" else "#bbdefb"};
    }}

    QHeaderView {{
        {f"background-color: #0d47a1;" if theme_key == "default" else f"""
        background-color: {theme["table_header_bg"]};
        {
            # Galaxy theme - add header gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7b1fa2, stop:1 #4a148c);" if theme_key == "galaxy" else
            # Dreamy theme - add header gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4a3a80, stop:1 #2c2c45);" if theme_key == "dreamy" else
            # Space theme - add header gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5e35b1, stop:1 #3949ab);" if theme_key == "space" else
            # Dark theme - add header gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1976d2, stop:1 #0d47a1);" if theme_key == "dark" else
            # Vintage theme - add header gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5d4037, stop:1 #3e2723);" if theme_key == "vintage" else
            ""
        }
        """}
        border: none;
    }}

    QHeaderView::section {{
        {f"background-color: #0d47a1;" if theme_key == "default" else f"""
        background-color: {theme["table_header_bg"]};
        {
            # Galaxy theme - add header section gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7b1fa2, stop:1 #4a148c);" if theme_key == "galaxy" else
            # Dreamy theme - add header section gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #4a3a80, stop:1 #2c2c45);" if theme_key == "dreamy" else
            # Space theme - add header section gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5e35b1, stop:1 #3949ab);" if theme_key == "space" else
            # Dark theme - add header section gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #1976d2, stop:1 #0d47a1);" if theme_key == "dark" else
            # Vintage theme - add header section gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #5d4037, stop:1 #3e2723);" if theme_key == "vintage" else
            ""
        }
        """}
        color: {theme["table_header_text"]};
        font-weight: bold;
        padding: 12px;
        border: none;
        border-bottom: 2px solid {theme["primary"]};
        font-size: 13pt;
        border-right: 1px solid {theme["table_border"]};
    }}

    QHeaderView::section:hover {{
        {f"background-color: #0d47a1;" if theme_key == "default" else f"""
        background-color: {theme["primary_dark"]};
        {
            # Galaxy theme - add header hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #9c27b0, stop:1 #7b1fa2);" if theme_key == "galaxy" else
            # Dreamy theme - add header hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6a3ecf, stop:1 #4a3a80);" if theme_key == "dreamy" else
            # Space theme - add header hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #7e57c2, stop:1 #5e35b1);" if theme_key == "space" else
            # Dark theme - add header hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #2196f3, stop:1 #1976d2);" if theme_key == "dark" else
            # Vintage theme - add header hover gradient
            "background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 #6d4c41, stop:1 #5d4037);" if theme_key == "vintage" else
            ""
        }
        """}
        color: {theme["table_header_text"]};
    }}

    QHeaderView::section:last {{
        border-right: none;
    }}

    /* Table scrollbars - themed to match the table style */
    QTableWidget QScrollBar:vertical {{
        background-color: {theme["table_alternate_bg"] if theme_key != "default" else "#f5f5f5"};
        width: 14px;
        margin: 15px 0 15px 0;
        border-radius: {0 if theme_key == "mono" else 7}px;
        {f"border-left: 1px solid {theme['table_border']};" if theme_key != "default" else ""}
    }}

    QTableWidget QScrollBar::handle:vertical {{
        background-color: {theme["scrollbar_handle"] if theme_key != "default" else "#bdbdbd"};
        min-height: 30px;
        border-radius: {0 if theme_key == "mono" else 7}px;
        margin: 2px;
    }}

    QTableWidget QScrollBar::handle:vertical:hover {{
        background-color: {theme["scrollbar_handle_hover"] if theme_key != "default" else "#9e9e9e"};
    }}

    QTableWidget QScrollBar::add-page:vertical, QTableWidget QScrollBar::sub-page:vertical {{
        background: none;
    }}

    QTableWidget QScrollBar::up-arrow:vertical, QTableWidget QScrollBar::down-arrow:vertical {{
        background: none;
        height: 0px;
        width: 0px;
    }}

    QTableWidget QScrollBar:horizontal {{
        background-color: {theme["table_alternate_bg"] if theme_key != "default" else "#f5f5f5"};
        height: 14px;
        margin: 0 15px 0 15px;
        border-radius: {0 if theme_key == "mono" else 7}px;
        {f"border-top: 1px solid {theme['table_border']};" if theme_key != "default" else ""}
    }}

    QTableWidget QScrollBar::handle:horizontal {{
        background-color: {theme["scrollbar_handle"] if theme_key != "default" else "#bdbdbd"};
        min-width: 30px;
        border-radius: {0 if theme_key == "mono" else 7}px;
        margin: 2px;
    }}

    QTableWidget QScrollBar::handle:horizontal:hover {{
        background-color: {theme["scrollbar_handle_hover"] if theme_key != "default" else "#9e9e9e"};
    }}

    /* Tabs */
    QTabWidget::pane {{
        border: 1px solid {theme["input_border"]};
        border-radius: 8px;
        background-color: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
    }}

    QTabBar::tab {{
        background-color: {theme["table_alternate_bg"]};
        color: {
            "#ffffff" if theme_key == "mono" or theme_key == "vintage" else
            theme["main_text"]
        };
        border: 1px solid {theme["input_border"]};
        border-bottom-color: {theme["input_border"]};
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        padding: 10px 20px;
        margin-right: 2px;
        font-weight: bold;
        font-size: 12pt;
    }}

    QTabBar::tab:selected {{
        background-color: {theme["primary"]};
        color: white;
        border-bottom-color: {theme["primary"]};
    }}

    QTabBar::tab:!selected {{
        margin-top: 2px;
        {f"""
        background-color: {
            "#212121" if theme_key == "mono" else
            "#5d4037" if theme_key == "vintage" else
            theme["table_alternate_bg"]
        };
        """ if theme_key == "mono" or theme_key == "vintage" else ""}
    }}

    /* Toolbar */
    QToolBar {{
        {toolbar_bg_style}
        border: none;
        spacing: 10px;
        padding: 5px;
    }}

    /* Main Toolbar with specific styling */
    QToolBar#mainToolBar {{
        {toolbar_bg_style}
        border: none;
        spacing: 10px;
        padding: 5px;
    }}

    QToolBar#mainToolBar QToolButton {{
        background-color: transparent;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-weight: bold;
        font-size: 12pt;
    }}

    QToolBar#mainToolBar QToolButton:hover {{
        background-color: {theme["primary"]};
    }}

    QToolBar#mainToolBar QToolButton:pressed {{
        background-color: {theme["primary_light"]};
    }}

    QToolBar#mainToolBar::separator {{
        background-color: {theme["primary_light"]};
        width: 2px;
        margin: 5px 10px;
    }}

    /* Theme selector in toolbar */
    QLabel#themeLabel {{
        color: white;
        font-weight: bold;
        font-size: 12pt;
    }}

    QComboBox#themeComboBox {{
        background-color: white;
        color: {theme["primary_dark"]};
        border: none;
        border-radius: 4px;
        padding: 5px;
        font-weight: bold;
        font-size: 12pt;
        min-height: 30px;
        text-align: center;
    }}

    QComboBox#themeComboBox QLineEdit {{
        background-color: white;
        color: {theme["primary_dark"]};
        border: none;
        font-weight: bold;
        font-size: 12pt;
        text-align: center;
        selection-background-color: transparent;
        selection-color: {theme["primary_dark"]};
    }}

    QComboBox#themeComboBox::drop-down {{
        subcontrol-origin: padding;
        subcontrol-position: top right;
        width: 30px;
        border-left: 1px solid {theme["primary_light"]};
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }}

    QComboBox#themeComboBox::down-arrow {{
        image: url(resources/icons/dropdown.png);
        width: 16px;
        height: 16px;
    }}

    QComboBox#themeComboBox QAbstractItemView {{
        background-color: white;
        color: {theme["primary_dark"]};
        selection-background-color: {theme["primary_light"]};
        selection-color: {theme["primary_dark"]};
        border: 1px solid {theme["primary_light"]};
        border-radius: 4px;
    }}

    QToolButton {{
        background-color: transparent;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 16px;
        font-weight: bold;
        font-size: 12pt;
    }}

    QToolButton:hover {{
        background-color: {theme["primary"]};
    }}

    QToolButton:pressed {{
        background-color: {theme["primary_light"]};
    }}

    /* Group Boxes */
    QGroupBox {{
        border: 2px solid {theme["primary"]};
        border-radius: 8px;
        margin-top: 20px;
        font-weight: bold;
        color: {theme["primary"]};
        background-color: {
            theme["main_bg"] if theme_key in ["dark", "dreamy", "space", "galaxy"] else
            theme["input_bg"]
        };
        padding: 15px;
    }}

    QGroupBox::title {{
        subcontrol-origin: margin;
        subcontrol-position: top center;
        padding: 0 10px;
        color: {theme["primary"]};
        font-size: 14pt;
    }}

    /* Scrollbars */
    QScrollBar:vertical {{
        border: none;
        background-color: {theme["scrollbar_bg"]};
        width: 12px;
        margin: 15px 0 15px 0;
        border-radius: 6px;
    }}

    QScrollBar::handle:vertical {{
        background-color: {theme["scrollbar_handle"]};
        min-height: 30px;
        border-radius: 6px;
    }}

    QScrollBar::handle:vertical:hover {{
        background-color: {theme["scrollbar_handle_hover"]};
    }}

    QScrollBar::sub-line:vertical, QScrollBar::add-line:vertical {{
        border: none;
        background: none;
        height: 15px;
        subcontrol-position: top;
        subcontrol-origin: margin;
    }}

    QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {{
        background: none;
        height: 0px;
        width: 0px;
    }}

    QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
        background: none;
    }}

    QScrollBar:horizontal {{
        border: none;
        background-color: {theme["scrollbar_bg"]};
        height: 12px;
        margin: 0 15px 0 15px;
        border-radius: 6px;
    }}

    QScrollBar::handle:horizontal {{
        background-color: {theme["scrollbar_handle"]};
        min-width: 30px;
        border-radius: 6px;
    }}

    QScrollBar::handle:horizontal:hover {{
        background-color: {theme["scrollbar_handle_hover"]};
    }}

    QScrollBar::sub-line:horizontal, QScrollBar::add-line:horizontal {{
        border: none;
        background: none;
        width: 15px;
        subcontrol-position: left;
        subcontrol-origin: margin;
    }}

    QScrollBar::left-arrow:horizontal, QScrollBar::right-arrow:horizontal {{
        background: none;
        height: 0px;
        width: 0px;
    }}

    QScrollBar::add-page:horizontal, QScrollBar::sub-page:horizontal {{
        background: none;
    }}

    /* Table Scrollbars - Specifically styled for tables with improved contrast */
    QTableWidget QScrollBar:vertical {{
        background-color: {theme["table_header_bg"]};
        width: 14px;
        margin: 15px 0 15px 0;
        border-radius: 0px;
        border-left: 1px solid {theme["table_border"]};
    }}

    QTableWidget QScrollBar::handle:vertical {{
        background-color: {theme["scrollbar_handle"]};
        min-height: 30px;
        border-radius: 7px;
        margin: 2px;
    }}

    QTableWidget QScrollBar::handle:vertical:hover {{
        background-color: {theme["scrollbar_handle_hover"]};
    }}

    QTableWidget QScrollBar::add-page:vertical, QTableWidget QScrollBar::sub-page:vertical {{
        background: none;
    }}

    QTableWidget QScrollBar::up-arrow:vertical, QTableWidget QScrollBar::down-arrow:vertical {{
        background: none;
        height: 0px;
        width: 0px;
    }}

    QTableWidget QScrollBar:horizontal {{
        background-color: {theme["table_header_bg"]};
        height: 14px;
        margin: 0 15px 0 15px;
        border-radius: 0px;
        border-top: 1px solid {theme["table_border"]};
    }}

    QTableWidget QScrollBar::handle:horizontal {{
        background-color: {theme["scrollbar_handle"]};
        min-width: 30px;
        border-radius: 7px;
        margin: 2px;
    }}

    QTableWidget QScrollBar::handle:horizontal:hover {{
        background-color: {theme["scrollbar_handle_hover"]};
    }}

    QTableWidget QScrollBar::add-page:horizontal, QTableWidget QScrollBar::sub-page:horizontal {{
        background: none;
    }}

    QTableWidget QScrollBar::left-arrow:horizontal, QTableWidget QScrollBar::right-arrow:horizontal {{
        background: none;
        height: 0px;
        width: 0px;
    }}

    /* Status Bar */
    QStatusBar {{
        background-color: {theme["primary_dark"]};
        color: white;
        font-weight: bold;
        padding: 5px;
    }}

    /* Message Boxes and Dialogs */
    QMessageBox, QDialog {{
        {dialog_bg_style}
        border: 2px solid {theme["primary"]};
        border-radius: {theme["border_radius"]};
    }}

    QMessageBox QLabel, QDialog QLabel {{
        color: {theme["main_text"]};
        font-size: {theme["font_size_normal"]};
    }}

    QMessageBox QPushButton, QDialog QPushButton {{
        min-width: 100px;
        min-height: 35px;
    }}

    /* Menus and Menu Items */
    QMenu {{
        {menu_bg_style}
        color: {theme["menu_text"]};
        border: 1px solid {theme["menu_border"]};
        border-radius: {theme["border_radius"]};
        padding: 5px;
    }}

    QMenu::item {{
        background-color: transparent;
        padding: 8px 20px;
        border-radius: {theme["border_radius"]};
    }}

    QMenu::item:selected {{
        background-color: {theme["menu_hover_bg"]};
        color: {theme["menu_hover_text"]};
    }}

    QMenuBar {{
        {menu_bg_style}
        color: {theme["menu_text"]};
    }}

    QMenuBar::item {{
        background-color: transparent;
        padding: 8px 12px;
    }}

    QMenuBar::item:selected {{
        background-color: {theme["menu_hover_bg"]};
        color: {theme["menu_hover_text"]};
    }}

    /* Tooltips */
    QToolTip {{
        background-color: {theme["tooltip_bg"]};
        color: {theme["tooltip_text"]};
        border: 1px solid {theme["border_color"]};
        border-radius: {theme["border_radius"]};
        padding: 5px;
        opacity: 220;
    }}

    /* Progress Bars */
    QProgressBar {{
        border: 1px solid {theme["border_color"]};
        border-radius: {theme["border_radius"]};
        background-color: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
        text-align: center;
        color: {theme["main_text"]};
        font-weight: bold;
    }}

    QProgressBar::chunk {{
        background-color: {theme["primary"]};
        width: 10px;
        margin: 0.5px;
    }}

    /* Sliders */
    QSlider::groove:horizontal {{
        border: 1px solid {theme["border_color"]};
        height: 8px;
        background: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
        margin: 2px 0;
        border-radius: 4px;
    }}

    QSlider::handle:horizontal {{
        background: {theme["primary"]};
        border: 1px solid {theme["primary_dark"]};
        width: 18px;
        margin: -2px 0;
        border-radius: 9px;
    }}

    QSlider::handle:horizontal:hover {{
        background: {theme["primary_dark"]};
    }}

    /* Calendar Widget */
    QCalendarWidget {{
        {main_bg_style}
        color: {theme["main_text"]};
    }}

    QCalendarWidget QToolButton {{
        color: {theme["main_text"]};
        background-color: transparent;
        border: none;
        border-radius: {theme["border_radius"]};
        padding: 6px;
    }}

    QCalendarWidget QToolButton:hover {{
        background-color: {theme["tab_hover_bg"]};
    }}

    QCalendarWidget QMenu {{
        {menu_bg_style}
        color: {theme["menu_text"]};
    }}

    QCalendarWidget QSpinBox {{
        background-color: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
        color: {theme["main_text"]};
        border: 1px solid {theme["input_border"]};
        border-radius: {theme["border_radius"]};
        padding: 3px;
    }}

    QCalendarWidget QAbstractItemView:enabled {{
        color: {theme["main_text"]};
        background-color: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
        selection-background-color: {theme["primary"]};
        selection-color: white;
    }}

    QCalendarWidget QAbstractItemView:disabled {{
        color: {theme["input_disabled_text"]};
    }}

    /* Dock Widgets */
    QDockWidget {{
        {main_bg_style}
        color: {theme["main_text"]};
        border: 1px solid {theme["border_color"]};
    }}

    QDockWidget::title {{
        {header_bg_style}
        color: white;
        padding: 6px;
        text-align: center;
    }}

    /* Tree View and List View */
    QTreeView, QListView {{
        {main_bg_style}
        color: {theme["main_text"]};
        border: 1px solid {theme["border_color"]};
        border-radius: {theme["border_radius"]};
        outline: none;
    }}

    QTreeView::item, QListView::item {{
        padding: 5px;
        border-bottom: 1px solid {theme["table_border"]};
    }}

    QTreeView::item:selected, QListView::item:selected {{
        background-color: {theme["table_selection_bg"]};
        color: {theme["table_selection_text"]};
    }}

    QTreeView::item:hover, QListView::item:hover {{
        background-color: {theme["table_hover_bg"]};
    }}

    /* Splitter */
    QSplitter::handle {{
        background-color: {theme["border_color"]};
    }}

    QSplitter::handle:horizontal {{
        width: 4px;
    }}

    QSplitter::handle:vertical {{
        height: 4px;
    }}

    QSplitter::handle:hover {{
        background-color: {theme["primary"]};
    }}

    /* Check Boxes and Radio Buttons */
    QCheckBox, QRadioButton {{
        color: {theme["main_text"]};
        spacing: 8px;
    }}

    QCheckBox::indicator, QRadioButton::indicator {{
        width: 18px;
        height: 18px;
        border: 1px solid {theme["input_border"]};
        border-radius: 3px;
        background-color: {
            "#1a1a2b" if theme_key == "dreamy" else
            "#1a237e" if theme_key == "space" else
            "#1a1f33" if theme_key == "galaxy" else
            "#1e1e1e" if theme_key == "dark" else
            theme["input_bg"]
        };
    }}

    QCheckBox::indicator:checked, QRadioButton::indicator:checked {{
        background-color: {theme["primary"]};
        border: 1px solid {theme["primary"]};
        image: url(resources/icons/check.png);
    }}

    QCheckBox::indicator:hover, QRadioButton::indicator:hover {{
        border: 1px solid {theme["primary"]};
    }}

    QRadioButton::indicator {{
        border-radius: 9px;
    }}
    """
