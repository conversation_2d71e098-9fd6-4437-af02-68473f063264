#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Session Dialog for فوترها (Fawterha)
Provides a dialog for opening and closing POS sessions
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QMessageBox, QFormLayout, QDialogButtonBox,
    QGroupBox, QFrame, QDoubleSpinBox, QTextEdit
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon, QFont

from database.pos_manager import POSManager
from models.pos_session import POSSession
from utils.translation_manager import tr
from utils.currency_helper import format_currency


class POSSessionDialog(QDialog):
    """Dialog for opening or closing a POS session."""

    # Signal emitted when a session is opened or closed
    session_updated = Signal(POSSession)

    def __init__(self, db_manager, user, session=None, parent=None):
        """Initialize the POS session dialog.

        Args:
            db_manager: Database manager instance
            user: User object
            session (POSSession, optional): Existing session for closing. Defaults to None.
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.pos_manager = POSManager(db_manager)
        self.user = user
        self.session = session
        self.is_opening = session is None

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        if self.is_opening:
            self.setWindowTitle(tr("pos.open_session", "فتح جلسة نقاط بيع جديدة"))
        else:
            self.setWindowTitle(tr("pos.close_session", "إغلاق جلسة نقاط البيع"))
        
        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self.setWindowIcon(QIcon("resources/icons/pos.png"))
        self.setLayoutDirection(Qt.RightToLeft)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Title
        title_label = QLabel(
            tr("pos.open_session", "فتح جلسة نقاط بيع جديدة") if self.is_opening else 
            tr("pos.close_session", "إغلاق جلسة نقاط البيع")
        )
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #0d47a1;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Session form
        session_group = QGroupBox(
            tr("pos.session_details", "تفاصيل الجلسة") if self.is_opening else 
            tr("pos.session_summary", "ملخص الجلسة")
        )
        session_layout = QFormLayout(session_group)
        session_layout.setContentsMargins(20, 20, 20, 20)
        session_layout.setSpacing(15)

        # User
        user_label = QLabel(self.user.name)
        user_label.setStyleSheet("font-weight: bold;")
        session_layout.addRow(tr("pos.cashier", "الكاشير:"), user_label)

        if self.is_opening:
            # Starting cash
            self.starting_cash_spin = QDoubleSpinBox()
            self.starting_cash_spin.setMinimum(0)
            self.starting_cash_spin.setMaximum(1000000)
            self.starting_cash_spin.setDecimals(2)
            self.starting_cash_spin.setSingleStep(10)
            self.starting_cash_spin.setValue(0)
            self.starting_cash_spin.setMinimumHeight(40)
            session_layout.addRow(tr("pos.starting_cash", "النقدية الافتتاحية:"), self.starting_cash_spin)

            # Notes
            self.notes_edit = QTextEdit()
            self.notes_edit.setPlaceholderText(tr("pos.notes_placeholder", "ملاحظات إضافية..."))
            self.notes_edit.setMaximumHeight(100)
            session_layout.addRow(tr("pos.notes", "ملاحظات:"), self.notes_edit)
        else:
            # Session details
            start_time_label = QLabel(str(self.session.start_time))
            session_layout.addRow(tr("pos.start_time", "وقت البدء:"), start_time_label)

            # Starting cash
            starting_cash_label = QLabel(format_currency(self.session.starting_cash, "EGP"))
            starting_cash_label.setStyleSheet("font-weight: bold;")
            session_layout.addRow(tr("pos.starting_cash", "النقدية الافتتاحية:"), starting_cash_label)

            # Total sales
            self.total_sales_label = QLabel(format_currency(0, "EGP"))
            self.total_sales_label.setStyleSheet("font-weight: bold;")
            session_layout.addRow(tr("pos.total_sales", "إجمالي المبيعات:"), self.total_sales_label)

            # Cash payments
            self.cash_payments_label = QLabel(format_currency(0, "EGP"))
            session_layout.addRow(tr("pos.cash_payments", "المدفوعات النقدية:"), self.cash_payments_label)

            # Card payments
            self.card_payments_label = QLabel(format_currency(0, "EGP"))
            session_layout.addRow(tr("pos.card_payments", "مدفوعات البطاقات:"), self.card_payments_label)

            # Other payments
            self.other_payments_label = QLabel(format_currency(0, "EGP"))
            session_layout.addRow(tr("pos.other_payments", "مدفوعات أخرى:"), self.other_payments_label)

            # Expected cash
            self.expected_cash_label = QLabel(format_currency(0, "EGP"))
            self.expected_cash_label.setStyleSheet("font-weight: bold;")
            session_layout.addRow(tr("pos.expected_cash", "النقدية المتوقعة:"), self.expected_cash_label)

            # Ending cash
            self.ending_cash_spin = QDoubleSpinBox()
            self.ending_cash_spin.setMinimum(0)
            self.ending_cash_spin.setMaximum(1000000)
            self.ending_cash_spin.setDecimals(2)
            self.ending_cash_spin.setSingleStep(10)
            self.ending_cash_spin.setValue(0)
            self.ending_cash_spin.setMinimumHeight(40)
            session_layout.addRow(tr("pos.ending_cash", "النقدية النهائية:"), self.ending_cash_spin)

            # Cash difference
            self.cash_difference_label = QLabel(format_currency(0, "EGP"))
            session_layout.addRow(tr("pos.cash_difference", "الفرق:"), self.cash_difference_label)

            # Notes
            self.notes_edit = QTextEdit()
            self.notes_edit.setPlaceholderText(tr("pos.closing_notes_placeholder", "ملاحظات الإغلاق..."))
            self.notes_edit.setMaximumHeight(100)
            session_layout.addRow(tr("pos.notes", "ملاحظات:"), self.notes_edit)

            # Connect ending cash spin to update difference
            self.ending_cash_spin.valueChanged.connect(self.update_cash_difference)

        main_layout.addWidget(session_group)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(10)

        # Cancel button
        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.setMinimumHeight(40)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        # Save button
        self.save_button = QPushButton(
            tr("pos.open_session", "فتح الجلسة") if self.is_opening else 
            tr("pos.close_session", "إغلاق الجلسة")
        )
        self.save_button.setMinimumHeight(40)
        self.save_button.setDefault(True)
        self.save_button.clicked.connect(self.save_session)
        button_layout.addWidget(self.save_button)

        main_layout.addLayout(button_layout)

    def load_data(self):
        """Load data for the dialog."""
        if not self.is_opening and self.session:
            # Load session summary
            session_summary = self.pos_manager.get_session_summary(self.session.id)
            if session_summary:
                self.total_sales_label.setText(format_currency(session_summary.total_sales, "EGP"))
                self.cash_payments_label.setText(format_currency(session_summary.total_cash, "EGP"))
                self.card_payments_label.setText(format_currency(session_summary.total_card, "EGP"))
                self.other_payments_label.setText(format_currency(session_summary.total_other, "EGP"))
                
                # Calculate expected cash
                expected_cash = self.session.starting_cash + session_summary.total_cash
                self.expected_cash_label.setText(format_currency(expected_cash, "EGP"))
                
                # Set ending cash to expected cash
                self.ending_cash_spin.setValue(expected_cash)
                
                # Update cash difference
                self.update_cash_difference()

    def update_cash_difference(self):
        """Update the cash difference label."""
        if not self.is_opening and self.session:
            # Get expected cash
            expected_cash_text = self.expected_cash_label.text()
            expected_cash = float(expected_cash_text.replace("ج.م", "").strip())
            
            # Get ending cash
            ending_cash = self.ending_cash_spin.value()
            
            # Calculate difference
            difference = ending_cash - expected_cash
            
            # Update label
            self.cash_difference_label.setText(format_currency(difference, "EGP"))
            
            # Set color based on difference
            if difference < 0:
                self.cash_difference_label.setStyleSheet("color: red; font-weight: bold;")
            elif difference > 0:
                self.cash_difference_label.setStyleSheet("color: green; font-weight: bold;")
            else:
                self.cash_difference_label.setStyleSheet("color: black;")

    def save_session(self):
        """Save the session."""
        if self.is_opening:
            # Get starting cash
            starting_cash = self.starting_cash_spin.value()
            
            # Get notes
            notes = self.notes_edit.toPlainText()
            
            # Create session
            session = POSSession(
                user_id=self.user.id,
                starting_cash=starting_cash,
                notes=notes
            )
            
            # Open session
            session_id = self.pos_manager.open_session(session)
            
            if session_id > 0:
                # Get the new session
                session = self.pos_manager.get_session_by_id(session_id)
                
                # Emit signal
                self.session_updated.emit(session)
                
                # Show success message
                QMessageBox.information(
                    self,
                    tr("pos.session_opened", "تم فتح الجلسة"),
                    tr("pos.session_opened_message", "تم فتح جلسة نقاط البيع بنجاح")
                )
                
                # Accept dialog
                self.accept()
            elif session_id == -1:
                # User already has an open session
                QMessageBox.warning(
                    self,
                    tr("pos.session_error", "خطأ في الجلسة"),
                    tr("pos.already_open_session", "لديك بالفعل جلسة مفتوحة. يرجى إغلاق الجلسة الحالية أولاً")
                )
            else:
                # Error opening session
                QMessageBox.critical(
                    self,
                    tr("pos.session_error", "خطأ في الجلسة"),
                    tr("pos.error_opening_session", "حدث خطأ أثناء فتح الجلسة")
                )
        else:
            # Get ending cash
            ending_cash = self.ending_cash_spin.value()
            
            # Get notes
            notes = self.notes_edit.toPlainText()
            
            # Update session
            self.session.ending_cash = ending_cash
            self.session.notes = notes
            
            # Close session
            if self.pos_manager.close_session(self.session):
                # Get the updated session
                session = self.pos_manager.get_session_by_id(self.session.id)
                
                # Emit signal
                self.session_updated.emit(session)
                
                # Show success message
                QMessageBox.information(
                    self,
                    tr("pos.session_closed", "تم إغلاق الجلسة"),
                    tr("pos.session_closed_message", "تم إغلاق جلسة نقاط البيع بنجاح")
                )
                
                # Accept dialog
                self.accept()
            else:
                # Error closing session
                QMessageBox.critical(
                    self,
                    tr("pos.session_error", "خطأ في الجلسة"),
                    tr("pos.error_closing_session", "حدث خطأ أثناء إغلاق الجلسة")
                )
