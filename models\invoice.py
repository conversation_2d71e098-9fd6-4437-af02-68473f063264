#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Model for فوترها (Fawterha)
Represents an invoice in the system
"""

from datetime import datetime

class Invoice:
    """Invoice model class."""

    # Status constants
    STATUS_DRAFT = "draft"
    STATUS_PENDING = "pending"
    STATUS_PAID = "paid"
    STATUS_PARTIALLY_PAID = "partially_paid"
    STATUS_CANCELLED = "cancelled"

    # Payment status constants
    PAYMENT_STATUS_UNPAID = "unpaid"
    PAYMENT_STATUS_PARTIALLY_PAID = "partially_paid"
    PAYMENT_STATUS_PAID = "paid"
    PAYMENT_STATUS_OVERPAID = "overpaid"

    def __init__(self, id=None, invoice_number="", customer_id=None,
                 issue_date=None, due_date=None, currency_id=None, subtotal=0.0, discount=0.0,
                 tax=0.0, total=0.0, amount_paid=0.0, amount_due=0.0, notes="", status="draft",
                 created_at=None, updated_at=None, items=None):
        """Initialize an invoice object.

        Args:
            id (int, optional): Invoice ID. Defaults to None.
            invoice_number (str, optional): Invoice number. Defaults to "".
            customer_id (int, optional): Customer ID. Defaults to None.
            issue_date (datetime, optional): Issue date. Defaults to None.
            due_date (datetime, optional): Due date. Defaults to None.
            currency_id (int, optional): Currency ID. Defaults to None.
            subtotal (float, optional): Subtotal amount. Defaults to 0.0.
            discount (float, optional): Discount amount. Defaults to 0.0.
            tax (float, optional): Tax amount. Defaults to 0.0.
            total (float, optional): Total amount. Defaults to 0.0.
            amount_paid (float, optional): Amount paid. Defaults to 0.0.
            amount_due (float, optional): Amount due. Defaults to 0.0.
            notes (str, optional): Notes. Defaults to "".
            status (str, optional): Status. Defaults to "draft".
            created_at (str, optional): Creation timestamp. Defaults to None.
            updated_at (str, optional): Update timestamp. Defaults to None.
            items (list, optional): List of invoice items. Defaults to None.
        """
        self.id = id
        self.invoice_number = invoice_number
        self.customer_id = customer_id
        self.issue_date = issue_date or datetime.now().date()
        self.due_date = due_date
        self.currency_id = currency_id
        self.subtotal = subtotal
        self.discount = discount
        self.tax = tax
        self.total = total
        self.amount_paid = amount_paid
        self.amount_due = amount_due if amount_due > 0 else total - amount_paid
        self.notes = notes
        self.status = status
        self.created_at = created_at
        self.updated_at = updated_at
        self.items = items or []

        # Objects to be populated later
        self.customer = None
        self.currency = None

    def update_payment_status(self):
        """Update the invoice status based on payment amount."""
        if self.amount_paid <= 0:
            if self.status == "paid" or self.status == "partially_paid":
                self.status = "pending"
        elif self.amount_paid >= self.total:
            self.status = "paid"
            self.amount_paid = self.total  # Ensure we don't overpay
            self.amount_due = 0
        elif self.amount_paid > 0 and self.amount_paid < self.total:
            self.status = "partially_paid"
            self.amount_due = self.total - self.amount_paid

    @classmethod
    def from_db_row(cls, row):
        """Create an Invoice object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            Invoice: Invoice object
        """
        # Get amount_paid and amount_due with fallback for older database versions
        amount_paid = row['amount_paid'] if 'amount_paid' in row.keys() else 0.0
        amount_due = row['amount_due'] if 'amount_due' in row.keys() else row['total'] - amount_paid

        return cls(
            id=row['id'],
            invoice_number=row['invoice_number'],
            customer_id=row['customer_id'],
            issue_date=datetime.strptime(row['issue_date'], '%Y-%m-%d').date() if row['issue_date'] else None,
            due_date=datetime.strptime(row['due_date'], '%Y-%m-%d').date() if row['due_date'] else None,
            currency_id=row['currency_id'] if 'currency_id' in row.keys() else None,
            subtotal=row['subtotal'],
            discount=row['discount'],
            tax=row['tax'],
            total=row['total'],
            amount_paid=amount_paid,
            amount_due=amount_due,
            notes=row['notes'],
            status=row['status'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )

    def calculate_totals(self):
        """Calculate invoice totals based on items."""
        self.subtotal = sum(item.total for item in self.items)
        self.total = self.subtotal - self.discount + self.tax

    def to_dict(self):
        """Convert the invoice object to a dictionary.

        Returns:
            dict: Dictionary representation of the invoice
        """
        result = {
            'id': self.id,
            'invoice_number': self.invoice_number,
            'customer_id': self.customer_id,
            'issue_date': self.issue_date.strftime('%Y-%m-%d') if self.issue_date else None,
            'due_date': self.due_date.strftime('%Y-%m-%d') if self.due_date else None,
            'currency_id': self.currency_id,
            'subtotal': self.subtotal,
            'discount': self.discount,
            'tax': self.tax,
            'total': self.total,
            'notes': self.notes,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'items': [item.to_dict() for item in self.items]
        }

        # Add currency information if available
        if self.currency:
            result['currency'] = {
                'code': self.currency.code,
                'name': self.currency.name,
                'symbol': self.currency.symbol,
                'exchange_rate': self.currency.exchange_rate,
                'is_primary': self.currency.is_primary
            }

        return result

    def __str__(self):
        """Return a string representation of the invoice.

        Returns:
            str: String representation
        """
        return f"Invoice #{self.invoice_number} - {self.total}"
