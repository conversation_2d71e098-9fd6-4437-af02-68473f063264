#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Transaction Model for فوترها (Fawterha)
Represents a point of sale transaction
"""

from datetime import datetime

class POSTransaction:
    """POS Transaction model class."""

    # Transaction types
    TYPE_SALE = 'sale'
    TYPE_REFUND = 'refund'
    TYPE_CASH_IN = 'cash_in'
    TYPE_CASH_OUT = 'cash_out'
    
    # Payment methods
    METHOD_CASH = 'cash'
    METHOD_CARD = 'card'
    METHOD_BANK_TRANSFER = 'bank_transfer'

    def __init__(self, id=None, session_id=None, invoice_id=None,
                 transaction_type=TYPE_SALE, amount=0.0, payment_method=METHOD_CASH,
                 reference="", notes="", created_at=None):
        """Initialize a POS transaction object.

        Args:
            id (int, optional): Transaction ID. Defaults to None.
            session_id (int, optional): Session ID. Defaults to None.
            invoice_id (int, optional): Invoice ID. Defaults to None.
            transaction_type (str, optional): Transaction type. Defaults to TYPE_SALE.
            amount (float, optional): Transaction amount. Defaults to 0.0.
            payment_method (str, optional): Payment method. Defaults to METHOD_CASH.
            reference (str, optional): Transaction reference. Defaults to "".
            notes (str, optional): Transaction notes. Defaults to "".
            created_at (datetime, optional): Creation timestamp. Defaults to None.
        """
        self.id = id
        self.session_id = session_id
        self.invoice_id = invoice_id
        self.transaction_type = transaction_type
        self.amount = amount
        self.payment_method = payment_method
        self.reference = reference
        self.notes = notes
        self.created_at = created_at or datetime.now()
        
        # Additional properties
        self.invoice = None
        self.session = None

    @classmethod
    def from_db_row(cls, row):
        """Create a POSTransaction object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            POSTransaction: POSTransaction object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)

        # Create the transaction object with all fields from the row
        transaction = cls(
            id=row_dict.get('id'),
            session_id=row_dict.get('session_id'),
            invoice_id=row_dict.get('invoice_id'),
            transaction_type=row_dict.get('transaction_type', cls.TYPE_SALE),
            amount=float(row_dict.get('amount', 0.0)),
            payment_method=row_dict.get('payment_method', cls.METHOD_CASH),
            reference=row_dict.get('reference', ''),
            notes=row_dict.get('notes', ''),
            created_at=row_dict.get('created_at')
        )

        return transaction

    def to_dict(self):
        """Convert the transaction object to a dictionary.

        Returns:
            dict: Dictionary representation of the transaction
        """
        return {
            'id': self.id,
            'session_id': self.session_id,
            'invoice_id': self.invoice_id,
            'transaction_type': self.transaction_type,
            'amount': self.amount,
            'payment_method': self.payment_method,
            'reference': self.reference,
            'notes': self.notes,
            'created_at': self.created_at
        }

    def is_sale(self):
        """Check if the transaction is a sale.

        Returns:
            bool: True if the transaction is a sale, False otherwise
        """
        return self.transaction_type == self.TYPE_SALE

    def is_refund(self):
        """Check if the transaction is a refund.

        Returns:
            bool: True if the transaction is a refund, False otherwise
        """
        return self.transaction_type == self.TYPE_REFUND

    def is_cash_transaction(self):
        """Check if the transaction is a cash transaction.

        Returns:
            bool: True if the transaction is a cash transaction, False otherwise
        """
        return self.payment_method == self.METHOD_CASH

    def __str__(self):
        """Return a string representation of the transaction.

        Returns:
            str: String representation
        """
        return f"{self.transaction_type.capitalize()} #{self.id} ({self.amount})"
