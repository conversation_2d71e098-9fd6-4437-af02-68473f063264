#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Update Database Structure Script for فوترها (Fawterha)
Adds currency_id column to invoices table
"""

import os
import sqlite3

def update_database_structure():
    """Update database structure by adding currency_id column to invoices table."""
    # Get database path
    documents_path = os.path.expanduser("~/Documents/Fawterha")
    db_path = os.path.join(documents_path, "fawterha.db")

    print(f"Using database at: {db_path}")

    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    try:
        # Start transaction
        conn.execute("BEGIN TRANSACTION")

        # Check columns in invoices table
        cursor.execute("PRAGMA table_info(invoices)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        print(f"Current columns in invoices table: {column_names}")

        # Add currency_id column if it doesn't exist
        if 'currency_id' not in column_names:
            print("Adding currency_id column to invoices table...")
            cursor.execute("ALTER TABLE invoices ADD COLUMN currency_id INTEGER DEFAULT 1")
            print("Column added successfully!")

        # Add amount_paid column if it doesn't exist
        if 'amount_paid' not in column_names:
            print("Adding amount_paid column to invoices table...")
            cursor.execute("ALTER TABLE invoices ADD COLUMN amount_paid REAL DEFAULT 0.0")
            print("Column added successfully!")

        # Add amount_due column if it doesn't exist
        if 'amount_due' not in column_names:
            print("Adding amount_due column to invoices table...")
            cursor.execute("ALTER TABLE invoices ADD COLUMN amount_due REAL DEFAULT 0.0")
            print("Column added successfully!")
        else:
            print("currency_id column already exists in invoices table.")

        # Check if currencies table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='currencies'")
        if not cursor.fetchone():
            print("Creating currencies table...")
            cursor.execute("""
            CREATE TABLE currencies (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                code TEXT NOT NULL,
                symbol TEXT NOT NULL,
                is_default INTEGER DEFAULT 0,
                exchange_rate REAL DEFAULT 1.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)

            # Insert default currencies
            print("Adding default currencies...")
            currencies = [
                (1, "جنيه مصري", "EGP", "ج.م", 1, 1.0),
                (2, "دولار أمريكي", "USD", "$", 0, 30.9),
                (3, "يورو", "EUR", "€", 0, 33.5)
            ]

            cursor.executemany("""
            INSERT INTO currencies (id, name, code, symbol, is_default, exchange_rate)
            VALUES (?, ?, ?, ?, ?, ?)
            """, currencies)

            print("Currencies added successfully!")
        else:
            print("Currencies table already exists.")

        # Commit transaction
        conn.commit()
        print("Database structure updated successfully!")

    except Exception as e:
        # Rollback transaction on error
        conn.rollback()
        print(f"Error updating database structure: {str(e)}")
    finally:
        # Close connection
        conn.close()

if __name__ == "__main__":
    update_database_structure()
