#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS View for فوترها (Fawterha)
Provides the main Point of Sale interface
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDateEdit, QLineEdit, QMessageBox, QTabWidget,
    QSplitter, QFrame, QGroupBox, QRadioButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QMenu, QToolBar,
    QSizePolicy, QScrollArea, QGridLayout, QStackedWidget,
    QFormLayout, QDialogButtonBox, QDialog, QInputDialog
)
from PySide6.QtCore import Qt, QDate, Signal, QSize, QTimer, QThread, QObject
from PySide6.QtGui import QIcon, QColor, QFont, QAction, QPixmap
from PySide6.QtWidgets import QProgressDialog

from database.pos_manager import POSManager
from database.invoice_manager import InvoiceManager
from models.user import User
from models.pos_session import POSSession
from models.pos_transaction import POSTransaction
from models.invoice import Invoice
from models.invoice_item import InvoiceItem
from models.product import Product
from models.customer import Customer
from ui.pos_login_dialog import POSLoginDialog
from ui.pos_session_dialog import POSSessionDialog
from utils.translation_manager import tr, get_translation_manager
from utils.currency_helper import format_currency
from utils.inventory_helper import get_inventory_manager, update_product_stock_safely
from utils.pos_inventory_helper import update_pos_inventory, check_pos_inventory
from ui.pos_inventory_helper import initialize_pos_inventory_view
from ui.pos_inventory_report import POSInventoryReportDialog
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
import tempfile
import os
from datetime import datetime


class EmailWorker(QObject):
    """Worker class for sending emails in a separate thread."""

    # Define signals
    finished = Signal()
    error = Signal(str)
    success = Signal(str)

    def __init__(self, smtp_server, smtp_port, smtp_username, smtp_password,
                 smtp_use_tls, smtp_from_email, smtp_from_name, recipient,
                 invoice, invoice_items, customer, company_info, temp_pdf_path=None):
        """Initialize the worker with email settings.

        Args:
            smtp_server (str): SMTP server address
            smtp_port (int): SMTP server port
            smtp_username (str): SMTP username
            smtp_password (str): SMTP password
            smtp_use_tls (bool): Whether to use TLS
            smtp_from_email (str): Sender email address
            smtp_from_name (str): Sender name
            recipient (str): Recipient email address
            invoice: Invoice object
            invoice_items: List of invoice items
            customer: Customer object
            company_info: Dictionary with company information
            temp_pdf_path: No longer used as we don't attach PDFs. Kept for compatibility.
        """
        super().__init__()
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.smtp_username = smtp_username
        self.smtp_password = smtp_password
        self.smtp_use_tls = smtp_use_tls
        self.smtp_from_email = smtp_from_email
        self.smtp_from_name = smtp_from_name
        self.recipient = recipient
        self.invoice = invoice
        self.invoice_items = invoice_items
        self.customer = customer
        self.company_info = company_info
        # We don't use PDF attachments anymore, but keep the parameter for compatibility
        self.temp_pdf_path = None
        self.should_delete_pdf = False

    def send_email(self):
        """Send an email with invoice details in HTML format (no PDF attachment)."""
        try:
            print(f"EmailWorker: Starting to send email to {self.recipient}")
            print(f"EmailWorker: SMTP Server: {self.smtp_server}, Port: {self.smtp_port}")
            print(f"EmailWorker: Username: {self.smtp_username}, Use TLS: {self.smtp_use_tls}")

            # Create email message
            msg = MIMEMultipart()
            # Fix the From header format to comply with RFC 5322
            if self.smtp_from_name:
                # Use proper format for From header with name and email
                from email.utils import formataddr
                msg['From'] = formataddr((self.smtp_from_name, self.smtp_from_email))
            else:
                # Just use the email address if no name is provided
                msg['From'] = self.smtp_from_email
            msg['To'] = self.recipient
            # Make sure the subject is properly set with a clear title
            invoice_number = getattr(self.invoice, 'invoice_number', '')
            msg['Subject'] = f"فاتورة رقم {invoice_number} - {self.smtp_from_name}"

            # Create HTML content
            html_content = self._create_html_content()

            # Attach HTML content to email
            msg.attach(MIMEText(html_content, 'html'))

            # We're not attaching PDF files anymore as per user request
            print("EmailWorker: Not attaching PDF file as per user request")

            print("EmailWorker: Creating SMTP connection...")
            # Create SMTP connection
            try:
                # Check if we should use SSL instead of TLS
                if int(self.smtp_port) == 465:
                    print("EmailWorker: Using SMTP_SSL for port 465")
                    server = smtplib.SMTP_SSL(self.smtp_server, int(self.smtp_port), timeout=10)
                else:
                    print("EmailWorker: Using standard SMTP")
                    server = smtplib.SMTP(self.smtp_server, int(self.smtp_port), timeout=10)
                print("EmailWorker: SMTP connection established")
            except Exception as conn_error:
                error_msg = f"فشل الاتصال بخادم SMTP: {str(conn_error)}"
                print(f"EmailWorker: Connection error - {error_msg}")

                # Add more detailed error information
                if "timed out" in str(conn_error):
                    error_msg += "\n\nتأكد من صحة عنوان الخادم والمنفذ وتأكد من اتصالك بالإنترنت."
                elif "getaddrinfo failed" in str(conn_error):
                    error_msg += "\n\nتعذر العثور على خادم SMTP. تأكد من صحة عنوان الخادم."
                elif "refused" in str(conn_error):
                    error_msg += "\n\nرفض الخادم الاتصال. تأكد من صحة رقم المنفذ."

                self.error.emit(error_msg)
                self._cleanup_pdf()
                self.finished.emit()
                return

            try:
                server.ehlo()
                print("EmailWorker: EHLO command sent")

                # Use TLS if enabled and not already using SSL
                if self.smtp_use_tls and int(self.smtp_port) != 465:
                    print("EmailWorker: Starting TLS...")
                    try:
                        server.starttls()
                        server.ehlo()
                        print("EmailWorker: TLS started")
                    except (smtplib.SMTPNotSupportedError, smtplib.SMTPException) as e:
                        print(f"EmailWorker: STARTTLS error - {str(e)}")
                        error_msg = "خادم SMTP لا يدعم STARTTLS. يرجى استخدام المنفذ 465 مع SSL أو تعطيل TLS."
                        self.error.emit(error_msg)
                        try:
                            server.quit()
                        except:
                            pass
                        self._cleanup_pdf()
                        self.finished.emit()
                        return

                # Login to SMTP server
                print("EmailWorker: Logging in...")
                server.login(self.smtp_username, self.smtp_password)
                print("EmailWorker: Login successful")

                # Send email
                print("EmailWorker: Sending email...")
                try:
                    # Use a more direct approach to ensure delivery
                    print("EmailWorker: Preparing to send email directly...")

                    # Get all recipients
                    all_recipients = []
                    if msg['To']:
                        all_recipients.extend([addr.strip() for addr in msg['To'].split(',')])
                    if msg.get('Cc'):
                        all_recipients.extend([addr.strip() for addr in msg['Cc'].split(',')])
                    if msg.get('Bcc'):
                        all_recipients.extend([addr.strip() for addr in msg['Bcc'].split(',')])

                    # Convert message to string
                    message_str = msg.as_string()

                    # Add delay to avoid rate limiting
                    import time
                    time.sleep(1)  # Add a 1-second delay before sending

                    # Add additional headers to improve deliverability
                    msg['X-Priority'] = '3'  # Normal priority
                    msg['X-MSMail-Priority'] = 'Normal'
                    msg['Importance'] = 'Normal'
                    msg['X-Mailer'] = 'فوترها Invoice System'

                    # Regenerate message string with new headers
                    message_str = msg.as_string()

                    # Send the message directly with error handling
                    print(f"EmailWorker: Sending to recipients: {all_recipients}")
                    try:
                        refused = server.sendmail(msg['From'], all_recipients, message_str)

                        # Check if there were any rejected recipients
                        if refused:
                            rejected_recipients = list(refused.keys())
                            error_msg = f"فشل إرسال البريد الإلكتروني إلى: {', '.join(rejected_recipients)}"
                            print(f"EmailWorker: Send error - {error_msg}")
                            self.error.emit(error_msg)
                        else:
                            print("EmailWorker: Email sent successfully")
                            # Emit success signal
                            self.success.emit(self.recipient)
                    except smtplib.SMTPRecipientsRefused as e:
                        error_msg = "تم رفض المستلمين: " + str(e)
                        print(f"EmailWorker: Recipients refused - {error_msg}")
                        self.error.emit(error_msg)
                    except smtplib.SMTPResponseException as e:
                        error_code = e.smtp_code
                        error_message = e.smtp_error.decode() if hasattr(e.smtp_error, 'decode') else str(e.smtp_error)

                        if error_code == 550 and "too many errors with receivers" in error_message.lower():
                            error_msg = "تم رفض البريد الإلكتروني من قبل الخادم بسبب وجود الكثير من الأخطاء مع هذا المستلم. قد يكون هذا العنوان غير صالح أو محظور."
                        else:
                            error_msg = f"خطأ في استجابة خادم SMTP: {error_code} - {error_message}"

                        print(f"EmailWorker: SMTP Response error - {error_msg}")
                        self.error.emit(error_msg)

                except smtplib.SMTPRecipientsRefused as e:
                    error_msg = f"تم رفض المستلمين: {str(e)}"
                    print(f"EmailWorker: Recipients refused - {error_msg}")
                    self.error.emit(error_msg)
                except smtplib.SMTPSenderRefused as e:
                    error_msg = f"تم رفض المرسل: {str(e)}"
                    print(f"EmailWorker: Sender refused - {error_msg}")
                    self.error.emit(error_msg)
                except smtplib.SMTPDataError as e:
                    error_msg = f"خطأ في بيانات البريد الإلكتروني: {str(e)}"
                    print(f"EmailWorker: Data error - {error_msg}")
                    self.error.emit(error_msg)
                except Exception as e:
                    error_msg = f"خطأ أثناء إرسال البريد الإلكتروني: {str(e)}"
                    print(f"EmailWorker: Send error - {error_msg}")
                    self.error.emit(error_msg)
                finally:
                    try:
                        server.quit()
                        print("EmailWorker: SMTP connection closed")
                    except:
                        print("EmailWorker: Error closing SMTP connection")

            except smtplib.SMTPAuthenticationError:
                error_msg = "فشل تسجيل الدخول: اسم المستخدم أو كلمة المرور غير صحيحة"
                print(f"EmailWorker: Authentication error - {error_msg}")
                self.error.emit(error_msg)
                try:
                    server.quit()
                except:
                    pass

            except smtplib.SMTPException as smtp_error:
                error_msg = f"خطأ في بروتوكول SMTP: {str(smtp_error)}"
                print(f"EmailWorker: SMTP error - {error_msg}")
                self.error.emit(error_msg)
                try:
                    server.quit()
                except:
                    pass

            except Exception as e:
                error_msg = f"خطأ أثناء إرسال البريد الإلكتروني: {str(e)}"
                print(f"EmailWorker: General error - {error_msg}")
                self.error.emit(error_msg)
                try:
                    server.quit()
                except:
                    pass

        except Exception as e:
            # Emit error signal for any other exceptions
            error_msg = f"خطأ غير متوقع: {str(e)}"
            print(f"EmailWorker: Unexpected error - {error_msg}")
            self.error.emit(error_msg)
        finally:
            # Clean up temporary PDF file
            self._cleanup_pdf()

            # Emit finished signal
            print("EmailWorker: Process completed")
            self.finished.emit()

    def _cleanup_pdf(self):
        """Clean up temporary PDF file if we created it.

        This method is kept for compatibility but no longer used since we don't generate PDFs.
        """
        # We no longer generate PDFs for emails
        pass

    def _generate_pdf(self):
        """Generate a PDF file for the invoice.

        This method is no longer used for email attachments, but kept for future reference.

        Returns:
            str: Path to the generated PDF file
        """
        # Since we're not attaching PDFs anymore, just return None
        print("PDF generation skipped as we're not attaching PDFs to emails anymore")
        return None

    def _create_html_content(self):
        """Create HTML content for the email.

        Returns:
            str: HTML content
        """
        # Format invoice date
        invoice_date = self.invoice.issue_date

        # Format customer information
        customer_name = "عميل"
        customer_email = ""
        customer_phone = ""

        if self.customer:
            customer_name = self.customer.name
            if hasattr(self.customer, 'email') and self.customer.email:
                customer_email = self.customer.email
            if hasattr(self.customer, 'phone') and self.customer.phone:
                customer_phone = self.customer.phone

        # Format company information
        company_name = self.company_info.get('company_name', 'فوترها')
        company_address = self.company_info.get('company_address', '')
        company_phone = self.company_info.get('company_phone', '')

        # Create items table
        items_rows = ""
        total = 0

        for item in self.invoice_items:
            description = item.description
            quantity = item.quantity
            price = item.unit_price
            item_total = quantity * price - item.discount
            total += item_total

            items_rows += f"""
            <tr>
                <td>{description}</td>
                <td>{quantity}</td>
                <td>{price:.2f}</td>
                <td>{item_total:.2f}</td>
            </tr>
            """

        # Create HTML content
        html_content = f"""
        <html dir="rtl">
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; direction: rtl; text-align: right; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background-color: #0d47a1; color: white; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; border: 1px solid #ddd; }}
                .footer {{ text-align: center; margin-top: 20px; font-size: 12px; color: #777; }}
                table {{ width: 100%; border-collapse: collapse; margin: 15px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: right; }}
                th {{ background-color: #f2f2f2; }}
                .total {{ font-weight: bold; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>فاتورة رقم {self.invoice.invoice_number}</h1>
                </div>
                <div class="content">
                    <p>مرحباً {customer_name}،</p>
                    <p>نشكرك على تعاملك معنا. مرفق فاتورة المشتريات الخاصة بك.</p>

                    <h3>تفاصيل الفاتورة:</h3>
                    <p>رقم الفاتورة: {self.invoice.invoice_number}</p>
                    <p>تاريخ الفاتورة: {invoice_date}</p>

                    <h3>المنتجات/الخدمات:</h3>
                    <table>
                        <tr>
                            <th>الوصف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                            <th>الإجمالي</th>
                        </tr>
                        {items_rows}
                        <tr class="total">
                            <td colspan="3">الإجمالي</td>
                            <td>{total:.2f}</td>
                        </tr>
                    </table>

                    <p>يرجى الاطلاع على الفاتورة المرفقة للحصول على مزيد من التفاصيل.</p>
                    <p>إذا كان لديك أي استفسارات، يرجى التواصل معنا.</p>
                </div>
                <div class="footer">
                    <p>{company_name}</p>
                    <p>{company_address}</p>
                    <p>{company_phone}</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_content



class POSView(QWidget):
    """Widget for Point of Sale interface."""

    # Signal to be emitted when the widget is about to be destroyed
    destroyed = Signal()

    def __init__(self, db_manager, currency_manager=None, parent=None):
        """Initialize the POS view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        # Connect the destroyed signal to cleanup method
        self.destroyed.connect(self.cleanup)

        print("POS View: Initializing...")

        self.db_manager = db_manager
        self.currency_manager = currency_manager
        self.pos_manager = POSManager(db_manager)
        self.invoice_manager = InvoiceManager(db_manager, currency_manager)

        # Initialize inventory manager safely
        self.inventory_manager = get_inventory_manager(db_manager, show_error=True, parent=self)

        self.current_user = None
        self.current_session = None
        self.current_invoice = None
        self.current_customer = None
        self.invoice_items = []
        self.products_list = []
        self.categories = []
        self.payment_methods = []

        # Create inventory widget
        self.inventory_widget = QWidget()

        # Connect to translation manager for language changes
        from utils.translation_manager import get_translation_manager
        self.translation_manager = get_translation_manager()
        self.translation_manager.language_changed.connect(self.refresh_ui_translations)

        self.setup_ui()

        # Force initial translation update if language is not Arabic
        if self.translation_manager.current_language != 'ar':
            print(f"POS View: Non-Arabic language detected ({self.translation_manager.current_language}), forcing translation update...")
            self.refresh_ui_translations()

        self.show_login()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Stacked widget for different screens
        self.stacked_widget = QStackedWidget()
        main_layout.addWidget(self.stacked_widget)

        # Create login screen
        self.login_widget = QWidget()
        self.setup_login_ui()
        self.stacked_widget.addWidget(self.login_widget)

        # Create main POS screen
        self.pos_widget = QWidget()
        self.setup_pos_ui()
        self.stacked_widget.addWidget(self.pos_widget)

        # Create payment screen
        self.payment_widget = QWidget()
        self.setup_payment_ui()
        self.stacked_widget.addWidget(self.payment_widget)

        # Add inventory widget to stacked widget
        self.setup_inventory_ui()
        self.stacked_widget.addWidget(self.inventory_widget)

    def setup_login_ui(self):
        """Set up the login screen UI."""
        login_layout = QVBoxLayout(self.login_widget)
        login_layout.setContentsMargins(50, 50, 50, 50)
        login_layout.setSpacing(20)
        login_layout.setAlignment(Qt.AlignCenter)

        # Logo
        logo_label = QLabel()
        logo_pixmap = QPixmap("resources/icons/logo.png")
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        logo_label.setAlignment(Qt.AlignCenter)
        login_layout.addWidget(logo_label)

        # Title
        title_label = QLabel(tr("pos.system_name", "نظام نقاط البيع"))
        title_label.setStyleSheet("font-size: 32pt; font-weight: bold; color: #0d47a1;")
        title_label.setAlignment(Qt.AlignCenter)
        login_layout.addWidget(title_label)

        # Subtitle
        subtitle_label = QLabel(tr("pos.login_subtitle", "يرجى تسجيل الدخول للوصول إلى نظام نقاط البيع"))
        subtitle_label.setStyleSheet("font-size: 14pt; color: #555;")
        subtitle_label.setAlignment(Qt.AlignCenter)
        login_layout.addWidget(subtitle_label)

        # Spacer
        login_layout.addSpacing(30)

        # Login button
        self.login_button = QPushButton(tr("pos.login", "تسجيل الدخول"))
        self.login_button.setMinimumHeight(50)
        self.login_button.setMinimumWidth(200)
        self.login_button.setStyleSheet("""
            QPushButton {
                font-size: 14pt;
                font-weight: bold;
                background-color: #0d47a1;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
        """)
        self.login_button.clicked.connect(self.show_login_dialog)
        login_layout.addWidget(self.login_button, 0, Qt.AlignCenter)

    def setup_pos_ui(self):
        """Set up the main POS screen UI."""
        pos_layout = QVBoxLayout(self.pos_widget)
        pos_layout.setContentsMargins(10, 10, 10, 10)
        pos_layout.setSpacing(10)

        # Header
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(10)

        # Logo
        logo_label = QLabel()
        logo_pixmap = QPixmap("resources/icons/logo.png")
        if not logo_pixmap.isNull():
            logo_label.setPixmap(logo_pixmap.scaled(40, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        header_layout.addWidget(logo_label)

        # Title
        title_label = QLabel(tr("pos.system_name", "نظام نقاط البيع"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #0d47a1;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # Tabs
        tabs_layout = QHBoxLayout()
        tabs_layout.setContentsMargins(0, 0, 0, 0)
        tabs_layout.setSpacing(5)

        # Sales tab
        self.sales_tab_button = QPushButton(tr("pos.sales", "المبيعات"))
        self.sales_tab_button.setCheckable(True)
        self.sales_tab_button.setChecked(True)
        self.sales_tab_button.clicked.connect(lambda: self.show_pos_tab("sales"))
        self.sales_tab_button.setStyleSheet("""
            QPushButton {
                font-size: 12pt;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 5px;
                min-width: 120px;
            }
            QPushButton:checked {
                background-color: #0d47a1;
                color: white;
            }
            QPushButton:hover:!checked {
                background-color: #e3f2fd;
            }
        """)
        tabs_layout.addWidget(self.sales_tab_button)

        # Inventory tab
        self.inventory_tab_button = QPushButton(tr("pos.inventory", "المخزون"))
        self.inventory_tab_button.setCheckable(True)
        self.inventory_tab_button.clicked.connect(lambda: self.show_pos_tab("inventory"))
        self.inventory_tab_button.setStyleSheet("""
            QPushButton {
                font-size: 12pt;
                font-weight: bold;
                padding: 8px 16px;
                border-radius: 5px;
                min-width: 120px;
            }
            QPushButton:checked {
                background-color: #0d47a1;
                color: white;
            }
            QPushButton:hover:!checked {
                background-color: #e3f2fd;
            }
        """)
        tabs_layout.addWidget(self.inventory_tab_button)

        header_layout.addLayout(tabs_layout)

        header_layout.addStretch()

        # Session info
        self.session_label = QLabel()
        self.session_label.setStyleSheet("font-size: 12pt;")
        header_layout.addWidget(self.session_label)

        # User info
        self.user_label = QLabel()
        self.user_label.setStyleSheet("font-size: 12pt; font-weight: bold;")
        header_layout.addWidget(self.user_label)

        # Logout button
        self.logout_button = QPushButton(tr("pos.logout", "تسجيل الخروج"))
        self.logout_button.setIcon(QIcon("resources/icons/logout.png"))
        self.logout_button.clicked.connect(self.logout)
        header_layout.addWidget(self.logout_button)

        pos_layout.addLayout(header_layout)

        # Main content
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(10)

        # Content stacked widget
        self.content_stack = QStackedWidget()

        # Sales content
        self.sales_content = QWidget()
        sales_content_layout = QHBoxLayout(self.sales_content)
        sales_content_layout.setContentsMargins(0, 0, 0, 0)
        sales_content_layout.setSpacing(10)

        # Left panel - Products
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)

        # Search
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("pos.search_products", "بحث عن منتجات..."))
        self.search_edit.setMinimumHeight(40)
        self.search_edit.textChanged.connect(self.filter_products)
        search_layout.addWidget(self.search_edit)

        # Barcode button
        self.barcode_button = QPushButton(tr("pos.scan_barcode", "مسح الباركود"))
        self.barcode_button.setIcon(QIcon("resources/icons/barcode.png"))
        self.barcode_button.clicked.connect(self.scan_barcode)
        search_layout.addWidget(self.barcode_button)

        left_layout.addLayout(search_layout)

        # Categories
        categories_layout = QHBoxLayout()
        categories_layout.setContentsMargins(0, 0, 0, 0)
        categories_layout.setSpacing(5)

        self.category_combo = QComboBox()
        self.category_combo.setMinimumHeight(40)
        self.category_combo.currentIndexChanged.connect(self.filter_products)
        categories_layout.addWidget(self.category_combo)

        left_layout.addLayout(categories_layout)

        # Products grid
        products_scroll = QScrollArea()
        products_scroll.setWidgetResizable(True)
        products_scroll.setFrameShape(QFrame.NoFrame)

        self.products_widget = QWidget()
        self.products_grid = QGridLayout(self.products_widget)
        self.products_grid.setContentsMargins(0, 0, 0, 0)
        self.products_grid.setSpacing(10)

        products_scroll.setWidget(self.products_widget)
        left_layout.addWidget(products_scroll)

        # Right panel - Cart
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)

        # Customer selection
        customer_layout = QHBoxLayout()
        customer_layout.setContentsMargins(0, 0, 0, 0)
        customer_layout.setSpacing(10)

        customer_layout.addWidget(QLabel(tr("pos.customer", "العميل:")))

        self.customer_combo = QComboBox()
        self.customer_combo.setMinimumHeight(40)
        customer_layout.addWidget(self.customer_combo)

        self.add_customer_button = QPushButton(tr("pos.add_customer", "إضافة عميل"))
        self.add_customer_button.setIcon(QIcon("resources/icons/add.png"))
        self.add_customer_button.clicked.connect(self.add_customer)
        customer_layout.addWidget(self.add_customer_button)

        right_layout.addLayout(customer_layout)

        # Cart table with improved contrast
        self.cart_table = QTableWidget()
        self.cart_table.setColumnCount(6)
        self.cart_table.setHorizontalHeaderLabels([
            tr("pos.product", "المنتج"),
            tr("pos.price", "السعر"),
            tr("pos.quantity", "الكمية"),
            tr("pos.discount", "الخصم"),
            tr("pos.total", "الإجمالي"),
            tr("pos.actions", "الإجراءات")
        ])
        self.cart_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.cart_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.cart_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.cart_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.cart_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.cart_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.cart_table.verticalHeader().setVisible(False)
        self.cart_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.cart_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.cart_table.setAlternatingRowColors(True)

        # Improve table styling for better contrast
        self.cart_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #e8f5e9;  /* Light green for alternating rows */
                border: 2px solid #2e7d32;  /* Green border */
                border-radius: 8px;
                padding: 5px;
                font-size: 12pt;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #c8e6c9;  /* Light green border between rows */
            }
            QTableWidget::item:selected {
                background-color: #81c784;  /* Medium green for selected items */
                color: black;  /* Black text on green background for better contrast */
            }
            QHeaderView::section {
                background-color: #2e7d32;  /* Green header */
                color: white;  /* White text for contrast */
                padding: 8px;
                font-weight: bold;
                font-size: 12pt;
                border: 1px solid #1b5e20;  /* Darker green border */
            }
        """)

        right_layout.addWidget(self.cart_table)

        # Totals
        totals_group = QGroupBox(tr("pos.totals", "الإجماليات"))
        totals_layout = QFormLayout(totals_group)
        totals_layout.setContentsMargins(10, 10, 10, 10)
        totals_layout.setSpacing(10)

        # Subtotal
        subtotal_layout = QHBoxLayout()
        self.subtotal_label = QLineEdit(format_currency(0, "EGP"))
        self.subtotal_label.setReadOnly(True)
        self.subtotal_label.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            padding: 8px;
            background-color: #f5f5f5;
            color: #333333;
            border: 2px solid #0d47a1;
            border-radius: 6px;
        """)
        self.subtotal_label.setMinimumWidth(180)
        subtotal_layout.addWidget(self.subtotal_label)

        # Create a label with better contrast
        subtotal_text_label = QLabel(tr("pos.subtotal", "المجموع الفرعي:"))
        subtotal_text_label.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            color: #0d47a1;
        """)
        totals_layout.addRow(subtotal_text_label, subtotal_layout)

        # Tax
        tax_layout = QHBoxLayout()
        self.tax_label = QLineEdit(format_currency(0, "EGP"))
        self.tax_label.setReadOnly(True)
        self.tax_label.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            padding: 8px;
            background-color: #f5f5f5;
            color: #333333;
            border: 2px solid #0d47a1;
            border-radius: 6px;
        """)
        self.tax_label.setMinimumWidth(180)
        tax_layout.addWidget(self.tax_label)

        # Create a label with better contrast
        tax_text_label = QLabel(tr("pos.tax", "الضريبة:"))
        tax_text_label.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            color: #0d47a1;
        """)
        totals_layout.addRow(tax_text_label, tax_layout)

        # Discount
        discount_layout = QHBoxLayout()
        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0)
        self.discount_spin.setMaximum(100)
        self.discount_spin.setSingleStep(1)
        self.discount_spin.setValue(0)
        self.discount_spin.valueChanged.connect(self.update_totals)
        self.discount_spin.setMinimumWidth(100)
        discount_layout.addWidget(self.discount_spin)

        self.discount_type_combo = QComboBox()
        self.discount_type_combo.addItem(tr("pos.percentage", "%"), "percentage")
        self.discount_type_combo.addItem(tr("pos.amount", "مبلغ"), "amount")
        self.discount_type_combo.currentIndexChanged.connect(self.update_totals)
        discount_layout.addWidget(self.discount_type_combo)

        totals_layout.addRow(tr("pos.discount", "الخصم:"), discount_layout)

        # Total
        total_layout = QHBoxLayout()
        self.total_label = QLineEdit(format_currency(0, "EGP"))
        self.total_label.setReadOnly(True)
        self.total_label.setStyleSheet("""
            font-size: 20pt;
            font-weight: bold;
            color: white;
            padding: 10px;
            background-color: #2e7d32;  /* Green background for better visibility */
            border: 3px solid #1b5e20;  /* Darker green border */
            border-radius: 8px;
            margin: 5px;
        """)
        self.total_label.setMinimumWidth(200)
        self.total_label.setMinimumHeight(60)  # Increased height for better visibility
        total_layout.addWidget(self.total_label)

        # Create a label with better contrast
        total_text_label = QLabel(tr("pos.total", "الإجمالي:"))
        total_text_label.setStyleSheet("""
            font-size: 16pt;
            font-weight: bold;
            color: #2e7d32;  /* Green text to match the total field */
        """)
        totals_layout.addRow(total_text_label, total_layout)

        right_layout.addWidget(totals_group)

        # Action buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(10)

        # Clear button
        self.clear_button = QPushButton(tr("pos.clear", "مسح"))
        self.clear_button.setIcon(QIcon("resources/icons/clear.png"))
        self.clear_button.setMinimumHeight(50)
        self.clear_button.clicked.connect(self.clear_cart)
        buttons_layout.addWidget(self.clear_button)

        # Hold button
        self.hold_button = QPushButton(tr("pos.hold", "تعليق"))
        self.hold_button.setIcon(QIcon("resources/icons/hold.png"))
        self.hold_button.setMinimumHeight(50)
        self.hold_button.clicked.connect(self.hold_invoice)
        buttons_layout.addWidget(self.hold_button)

        # Pay button
        self.pay_button = QPushButton(tr("pos.pay", "الدفع"))
        self.pay_button.setIcon(QIcon("resources/icons/pay.png"))
        self.pay_button.setMinimumHeight(50)
        self.pay_button.setStyleSheet("""
            QPushButton {
                font-size: 14pt;
                font-weight: bold;
                background-color: #0d47a1;
                color: white;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
        """)
        self.pay_button.clicked.connect(self.show_payment)
        buttons_layout.addWidget(self.pay_button)

        right_layout.addLayout(buttons_layout)

        # Add panels to sales content layout
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([int(self.width() * 0.6), int(self.width() * 0.4)])
        sales_content_layout.addWidget(splitter)

        # Add sales content to stack
        self.content_stack.addWidget(self.sales_content)

        # Add content stack to main layout
        content_layout.addWidget(self.content_stack)
        pos_layout.addLayout(content_layout)

        # Note: The "complete sale" button at the bottom has been removed as requested

    def setup_inventory_ui(self):
        """Set up the inventory screen UI."""
        inventory_layout = QVBoxLayout(self.inventory_widget)
        inventory_layout.setContentsMargins(0, 0, 0, 0)
        inventory_layout.setSpacing(0)

        # Add toolbar for inventory actions
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(10, 10, 10, 10)
        toolbar_layout.setSpacing(10)

        # Add inventory report button
        inventory_report_button = QPushButton(tr("pos.inventory_report", "تقرير المخزون"))
        inventory_report_button.setIcon(QIcon("resources/icons/report.png"))
        inventory_report_button.clicked.connect(self.show_inventory_report)
        inventory_report_button.setStyleSheet("""
            QPushButton {
                background-color: #0d47a1;
                color: white;
                border: none;
                padding: 8px 16px;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0a2f6b;
            }
        """)
        toolbar_layout.addWidget(inventory_report_button)

        # Add spacer to push buttons to the left
        toolbar_layout.addStretch()

        # Add toolbar to main layout
        inventory_layout.addLayout(toolbar_layout)

        # Create inventory view directly instead of using helper
        try:
            from ui.pos_inventory_view import POSInventoryView
            self.pos_inventory_view = POSInventoryView(self.db_manager, self.pos_manager, self)

            # Connect signals
            self.pos_inventory_view.inventory_updated.connect(self.on_inventory_updated)

            # Connect to language changes - simple direct connection
            translation_manager = get_translation_manager()
            print("Connecting POS inventory view to language changes...")
            translation_manager.language_changed.connect(self.pos_inventory_view.refresh_ui_translations)
            print("POS inventory view connected to language changes successfully")

            # Add to layout
            inventory_layout.addWidget(self.pos_inventory_view)
        except Exception as e:
            print(f"Error initializing inventory view: {str(e)}")
            from PySide6.QtWidgets import QLabel
            error_label = QLabel(tr("inventory.init_error", "خطأ في تهيئة عرض المخزون"))
            inventory_layout.addWidget(error_label)
        else:
            # Show a message if initialization failed
            from PySide6.QtWidgets import QLabel
            error_label = QLabel("لا يمكن تحميل عرض المخزون. يرجى التحقق من إعدادات قاعدة البيانات.")
            error_label.setStyleSheet("color: red; font-size: 16pt; padding: 20px;")
            error_label.setAlignment(Qt.AlignCenter)
            inventory_layout.addWidget(error_label)

    def setup_payment_ui(self):
        """Set up the payment screen UI."""
        payment_layout = QVBoxLayout(self.payment_widget)
        payment_layout.setContentsMargins(20, 20, 20, 20)
        payment_layout.setSpacing(20)

        # Header
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(10)

        # Back button
        self.back_button = QPushButton(tr("pos.back", "رجوع"))
        self.back_button.setIcon(QIcon("resources/icons/back.png"))
        self.back_button.clicked.connect(self.back_to_pos)
        header_layout.addWidget(self.back_button)

        # Title
        title_label = QLabel(tr("pos.payment", "الدفع"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #0d47a1;")
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        payment_layout.addLayout(header_layout)

        # Main content
        content_layout = QHBoxLayout()
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)

        # Left panel - Payment methods
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(10)

        # Payment methods group
        payment_methods_group = QGroupBox(tr("pos.payment_methods", "طرق الدفع"))
        payment_methods_layout = QVBoxLayout(payment_methods_group)
        payment_methods_layout.setContentsMargins(10, 10, 10, 10)
        payment_methods_layout.setSpacing(10)

        # Payment methods
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.setMinimumHeight(40)
        self.payment_method_combo.currentIndexChanged.connect(self.payment_method_changed)
        payment_methods_layout.addWidget(self.payment_method_combo)

        # Reference
        reference_layout = QHBoxLayout()
        reference_layout.setContentsMargins(0, 0, 0, 0)
        reference_layout.setSpacing(10)

        reference_layout.addWidget(QLabel(tr("pos.reference", "المرجع:")))

        self.reference_edit = QLineEdit()
        self.reference_edit.setPlaceholderText(tr("pos.reference_placeholder", "رقم البطاقة، رقم التحويل، إلخ..."))
        self.reference_edit.setMinimumHeight(40)
        reference_layout.addWidget(self.reference_edit)

        payment_methods_layout.addLayout(reference_layout)

        left_layout.addWidget(payment_methods_group)

        # Amount group
        amount_group = QGroupBox(tr("pos.amount", "المبلغ"))
        amount_layout = QVBoxLayout(amount_group)
        amount_layout.setContentsMargins(10, 10, 10, 10)
        amount_layout.setSpacing(10)

        # Total
        total_layout = QHBoxLayout()
        total_layout.setContentsMargins(0, 0, 0, 0)
        total_layout.setSpacing(10)

        total_layout.addWidget(QLabel(tr("pos.total", "الإجمالي:")))

        self.payment_total_label = QLabel(format_currency(0, "EGP"))
        self.payment_total_label.setStyleSheet("font-size: 18pt; font-weight: bold; color: #0d47a1;")
        total_layout.addWidget(self.payment_total_label)

        amount_layout.addLayout(total_layout)

        # Paid amount
        paid_layout = QHBoxLayout()
        paid_layout.setContentsMargins(0, 0, 0, 0)
        paid_layout.setSpacing(10)

        paid_layout.addWidget(QLabel(tr("pos.paid_amount", "المبلغ المدفوع:")))

        self.paid_amount_spin = QDoubleSpinBox()
        self.paid_amount_spin.setMinimum(0)
        self.paid_amount_spin.setMaximum(1000000)
        self.paid_amount_spin.setDecimals(2)
        self.paid_amount_spin.setSingleStep(10)
        self.paid_amount_spin.setMinimumHeight(40)
        self.paid_amount_spin.valueChanged.connect(self.update_change)
        paid_layout.addWidget(self.paid_amount_spin)

        amount_layout.addLayout(paid_layout)

        # Change
        change_layout = QHBoxLayout()
        change_layout.setContentsMargins(0, 0, 0, 0)
        change_layout.setSpacing(10)

        change_layout.addWidget(QLabel(tr("pos.change", "الباقي:")))

        self.change_label = QLabel(format_currency(0, "EGP"))
        self.change_label.setStyleSheet("font-size: 16pt; font-weight: bold; color: green;")
        change_layout.addWidget(self.change_label)

        amount_layout.addLayout(change_layout)

        left_layout.addWidget(amount_group)

        # Right panel - Invoice summary
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(10)

        # Invoice summary group
        summary_group = QGroupBox(tr("pos.invoice_summary", "ملخص الفاتورة"))
        summary_layout = QVBoxLayout(summary_group)
        summary_layout.setContentsMargins(10, 10, 10, 10)
        summary_layout.setSpacing(10)

        # Customer
        customer_layout = QHBoxLayout()
        customer_layout.setContentsMargins(0, 0, 0, 0)
        customer_layout.setSpacing(10)

        customer_layout.addWidget(QLabel(tr("pos.customer", "العميل:")))

        self.summary_customer_label = QLabel()
        self.summary_customer_label.setStyleSheet("font-weight: bold;")
        customer_layout.addWidget(self.summary_customer_label)

        summary_layout.addLayout(customer_layout)

        # Items count
        items_layout = QHBoxLayout()
        items_layout.setContentsMargins(0, 0, 0, 0)
        items_layout.setSpacing(10)

        items_layout.addWidget(QLabel(tr("pos.items_count", "عدد العناصر:")))

        self.items_count_label = QLabel()
        items_layout.addWidget(self.items_count_label)

        summary_layout.addLayout(items_layout)

        # Subtotal
        subtotal_layout = QHBoxLayout()
        subtotal_layout.setContentsMargins(0, 0, 0, 0)
        subtotal_layout.setSpacing(10)

        subtotal_layout.addWidget(QLabel(tr("pos.subtotal", "المجموع الفرعي:")))

        self.summary_subtotal_label = QLabel()
        subtotal_layout.addWidget(self.summary_subtotal_label)

        summary_layout.addLayout(subtotal_layout)

        # Tax
        tax_layout = QHBoxLayout()
        tax_layout.setContentsMargins(0, 0, 0, 0)
        tax_layout.setSpacing(10)

        tax_layout.addWidget(QLabel(tr("pos.tax", "الضريبة:")))

        self.summary_tax_label = QLabel()
        tax_layout.addWidget(self.summary_tax_label)

        summary_layout.addLayout(tax_layout)

        # Discount
        discount_layout = QHBoxLayout()
        discount_layout.setContentsMargins(0, 0, 0, 0)
        discount_layout.setSpacing(10)

        discount_layout.addWidget(QLabel(tr("pos.discount", "الخصم:")))

        self.summary_discount_label = QLabel()
        discount_layout.addWidget(self.summary_discount_label)

        summary_layout.addLayout(discount_layout)

        # Total
        total_layout = QHBoxLayout()
        total_layout.setContentsMargins(0, 0, 0, 0)
        total_layout.setSpacing(10)

        total_layout.addWidget(QLabel(tr("pos.total", "الإجمالي:")))

        self.summary_total_label = QLabel()
        self.summary_total_label.setStyleSheet("font-size: 16pt; font-weight: bold; color: #0d47a1;")
        total_layout.addWidget(self.summary_total_label)

        summary_layout.addLayout(total_layout)

        right_layout.addWidget(summary_group)

        # Options group
        options_group = QGroupBox(tr("pos.options", "الخيارات"))
        options_layout = QVBoxLayout(options_group)
        options_layout.setContentsMargins(10, 10, 10, 10)
        options_layout.setSpacing(10)

        # Print receipt
        self.print_receipt_check = QCheckBox(tr("pos.print_receipt", "طباعة الإيصال"))
        self.print_receipt_check.setChecked(True)
        options_layout.addWidget(self.print_receipt_check)

        # Email receipt
        self.email_receipt_check = QCheckBox(tr("pos.email_receipt", "إرسال الإيصال بالبريد الإلكتروني"))
        options_layout.addWidget(self.email_receipt_check)

        right_layout.addWidget(options_group)

        # Add panels to content layout
        content_layout.addWidget(left_panel)
        content_layout.addWidget(right_panel)

        payment_layout.addLayout(content_layout)

        # Action buttons
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(10)

        # Cancel button
        self.cancel_payment_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_payment_button.setIcon(QIcon("resources/icons/cancel.png"))
        self.cancel_payment_button.setMinimumHeight(50)
        self.cancel_payment_button.clicked.connect(self.back_to_pos)
        buttons_layout.addWidget(self.cancel_payment_button)

        # Complete button with improved contrast and visibility
        self.complete_button = QPushButton(tr("pos.complete_sale", "إتمام البيع"))
        self.complete_button.setIcon(QIcon("resources/icons/complete.png"))
        self.complete_button.setMinimumHeight(60)  # Increased height for better visibility
        self.complete_button.setMinimumWidth(200)  # Set minimum width
        self.complete_button.setStyleSheet("""
            QPushButton {
                font-size: 18pt;  /* Larger font size */
                font-weight: bold;
                background-color: #00c853;  /* Brighter green for better visibility */
                color: white;
                border-radius: 10px;
                border: 3px solid #00b248;  /* Thicker border for better contrast */
                padding: 12px 24px;
                margin: 5px;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);  /* Add shadow for depth */
            }
            QPushButton:hover {
                background-color: #00e676;  /* Even brighter on hover */
                border-color: #00c853;
                color: white;
            }
            QPushButton:pressed {
                background-color: #00b248;
                border-color: #00e676;
                color: white;
            }
            QPushButton:disabled {
                background-color: #b9b9b9;
                border-color: #a0a0a0;
                color: #f0f0f0;
            }
        """)
        # Connect button to a debug wrapper function that will print debug info before calling complete_sale
        self.complete_button.clicked.connect(self.debug_complete_sale)
        buttons_layout.addWidget(self.complete_button)

        payment_layout.addLayout(buttons_layout)

    def show_login(self):
        """Show the login screen."""
        self.stacked_widget.setCurrentWidget(self.login_widget)

    def show_login_dialog(self):
        """Show the login dialog."""
        login_dialog = POSLoginDialog(self.db_manager, self)
        login_dialog.login_successful.connect(self.on_login_successful)
        login_dialog.exec()

    def on_login_successful(self, user):
        """Handle successful login.

        Args:
            user (User): Authenticated user
        """
        self.current_user = user
        self.user_label.setText(user.name)

        # Check if user has an open session
        session = self.pos_manager.get_open_session_by_user(user.id)
        if session:
            self.current_session = session
            self.session_label.setText(tr("pos.session", "الجلسة: ") + f"#{session.id}")
            self.show_pos()
        else:
            # Show session dialog to open a new session
            self.show_session_dialog()

    def show_session_dialog(self):
        """Show the session dialog."""
        session_dialog = POSSessionDialog(self.db_manager, self.current_user, self.current_session, self)
        session_dialog.session_updated.connect(self.on_session_updated)
        session_dialog.exec()

    def on_session_updated(self, session):
        """Handle session update.

        Args:
            session (POSSession): Updated session
        """
        self.current_session = session
        self.session_label.setText(tr("pos.session", "الجلسة: ") + f"#{session.id}")

        if session.is_open():
            self.show_pos()
        else:
            self.logout()

    def show_pos(self):
        """Show the POS screen."""
        self.stacked_widget.setCurrentWidget(self.pos_widget)
        self.load_data()

        # Default to sales tab
        self.show_pos_tab("sales")

    def show_pos_tab(self, tab_name):
        """Show a specific tab in the POS screen.

        Args:
            tab_name (str): Tab name to show ('sales' or 'inventory')
        """
        print(f"POS View: Showing tab: {tab_name}")

        if tab_name == "sales":
            self.content_stack.setCurrentWidget(self.sales_content)
            self.sales_tab_button.setChecked(True)
            self.inventory_tab_button.setChecked(False)
            print("POS View: Sales tab displayed")

            # Refresh products display
            self.load_products()

        elif tab_name == "inventory":
            # Make sure inventory view is loaded
            if not hasattr(self, 'pos_inventory_view') or self.pos_inventory_view is None:
                self.setup_inventory_ui()

            # Make sure inventory widget is in the content stack
            if self.content_stack.indexOf(self.inventory_widget) == -1:
                self.content_stack.addWidget(self.inventory_widget)

            # Refresh inventory data if the view was successfully initialized
            if hasattr(self, 'pos_inventory_view') and self.pos_inventory_view:
                try:
                    self.pos_inventory_view.load_data()
                except Exception as e:
                    print(f"Error loading inventory data: {e}")
                    QMessageBox.warning(
                        self,
                        "خطأ في تحميل بيانات المخزون",
                        f"حدث خطأ أثناء تحميل بيانات المخزون: {str(e)}"
                    )

            self.content_stack.setCurrentWidget(self.inventory_widget)
            self.sales_tab_button.setChecked(False)
            self.inventory_tab_button.setChecked(True)
            print("POS View: Inventory tab displayed")

    def on_inventory_updated(self):
        """Handle inventory updates."""
        # Reload products
        self.load_products()



    def show_inventory_report(self):
        """Show the inventory report dialog."""
        try:
            # Create and show the inventory report dialog
            print("Opening inventory report dialog...")

            # Make sure pos_manager is initialized
            if not hasattr(self, 'pos_manager') or self.pos_manager is None:
                print("POS Manager is not initialized, creating a new instance")
                from database.pos_manager import POSManager
                self.pos_manager = POSManager(self.db_manager)

            # Create the dialog with proper error handling
            try:
                report_dialog = POSInventoryReportDialog(self.db_manager, self.pos_manager, self)
                report_dialog.exec()
            except Exception as e:
                print(f"Error creating inventory report dialog: {e}")
                import traceback
                traceback.print_exc()
                raise

        except Exception as e:
            print(f"Error showing inventory report: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.inventory_report_error", f"حدث خطأ أثناء فتح تقرير المخزون: {str(e)}")
            )

    def logout(self):
        """Logout the current user."""
        # Check if there's an open session
        if self.current_session and self.current_session.is_open():
            # Ask if user wants to close the session
            response = QMessageBox.question(
                self,
                tr("pos.close_session", "إغلاق الجلسة"),
                tr("pos.close_session_question", "هل ترغب في إغلاق جلسة نقاط البيع الحالية؟"),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if response == QMessageBox.Yes:
                # Show session dialog to close the session
                self.show_session_dialog()
                return

        # Reset current user and session
        self.current_user = None
        self.current_session = None
        self.user_label.setText("")
        self.session_label.setText("")

        # Show login screen
        self.show_login()

    def load_data(self):
        """Load data for the POS screen."""
        if not self.current_user or not self.current_session:
            return

        # Load products
        self.load_products()

        # Load categories
        self.load_categories()

        # Load customers
        self.load_customers()

        # Load payment methods
        self.load_payment_methods()

        # Clear cart
        self.clear_cart()

    def load_products(self):
        """Load products from the database."""
        try:
            print("POS View: Loading products from database...")
            # Get products
            query = """
            SELECT *, NULL as category_name
            FROM products
            WHERE type = 'product'
            ORDER BY name
            """

            rows = self.db_manager.execute_query(query)

            print(f"POS View: Found {len(rows)} products in database")

            # Print first few products for debugging
            for i, row in enumerate(rows[:5]):
                print(f"POS View: Product {i+1}: {row.get('name', 'Unknown')}, ID: {row.get('id', 'Unknown')}")

            # Convert to Product objects
            self.products_list = [Product.from_db_row(row) for row in rows]

            print(f"POS View: Converted {len(self.products_list)} products to Product objects")

            # Display products
            print("POS View: Displaying products...")
            self.display_products()
            print("POS View: Products displayed.")
        except Exception as e:
            print(f"POS View: Error loading products: {e}")
            # Fallback to empty product list
            self.products_list = []
            self.display_products()

            QMessageBox.warning(
                self,
                tr("errors.database_error", "خطأ في قاعدة البيانات"),
                tr("pos.products_load_error", f"حدث خطأ أثناء تحميل المنتجات: {str(e)}")
            )

    def load_categories(self):
        """Load product categories from the database."""
        try:
            # Get categories
            query = """
            SELECT * FROM categories
            ORDER BY name
            """
            rows = self.db_manager.execute_query(query)

            # Store categories
            self.categories = rows

            # Clear category combo
            self.category_combo.clear()

            # Add "All" option
            self.category_combo.addItem(tr("pos.all_categories", "جميع الفئات"), None)

            # Add categories
            for category in self.categories:
                self.category_combo.addItem(category['name'], category['id'])
        except Exception as e:
            # Just add "All" option if categories table doesn't exist
            self.category_combo.clear()
            self.category_combo.addItem(tr("pos.all_categories", "جميع الفئات"), None)

    def load_customers(self):
        """Load customers from the database."""
        try:
            # Get customers
            query = """
            SELECT * FROM customers
            ORDER BY name
            """
            rows = self.db_manager.execute_query(query)

            # Clear customer combo
            self.customer_combo.clear()

            # Add "Walk-in Customer" option
            self.customer_combo.addItem(tr("pos.walk_in_customer", "عميل عابر"), None)

            # Add customers
            for customer in rows:
                self.customer_combo.addItem(customer['name'], customer['id'])
        except Exception as e:
            # Just add "Walk-in Customer" option if there's an error
            self.customer_combo.clear()
            self.customer_combo.addItem(tr("pos.walk_in_customer", "عميل عابر"), None)

    def load_payment_methods(self):
        """Load payment methods from the database."""
        try:
            # Get payment methods
            payment_methods = self.pos_manager.get_payment_methods()

            # Store payment methods
            self.payment_methods = payment_methods

            # Clear payment method combo
            self.payment_method_combo.clear()

            # Add payment methods
            for method in payment_methods:
                self.payment_method_combo.addItem(method['name'], method['id'])

            # Set default payment method
            default_method_id = self.pos_manager.get_setting('default_payment_method', '1')
            index = self.payment_method_combo.findData(int(default_method_id))
            if index >= 0:
                self.payment_method_combo.setCurrentIndex(index)
        except Exception as e:
            # Add default payment methods if there's an error
            self.payment_method_combo.clear()

            # Add default payment methods
            self.payment_methods = [
                {'id': 1, 'name': tr("pos.cash", "نقدي"), 'requires_reference': False},
                {'id': 2, 'name': tr("pos.card", "بطاقة ائتمان"), 'requires_reference': True},
                {'id': 3, 'name': tr("pos.bank_transfer", "تحويل بنكي"), 'requires_reference': True}
            ]

            for method in self.payment_methods:
                self.payment_method_combo.addItem(method['name'], method['id'])

            # Set default to cash
            self.payment_method_combo.setCurrentIndex(0)

    def display_products(self, products_to_display=None):
        """Display products in the grid.

        Args:
            products_to_display (list, optional): List of products to display. If None, will filter products.
        """
        print("POS View: Displaying products...")

        # Clear products grid
        for i in reversed(range(self.products_grid.count())):
            widget = self.products_grid.itemAt(i).widget()
            if widget:
                widget.deleteLater()

        # Get products to display
        if products_to_display is None:
            # Get filtered products
            filtered_products = self.get_filtered_products()
        else:
            filtered_products = products_to_display

        # Print debug info
        print(f"POS View: Displaying {len(filtered_products)} products out of {len(self.products_list)}")

        # Add products to grid
        row, col = 0, 0
        max_cols = 4  # Number of columns in the grid

        for product in filtered_products:
            # Create product button
            product_button = QPushButton()
            product_button.setMinimumHeight(80)
            product_button.setMinimumWidth(150)
            product_button.setCursor(Qt.PointingHandCursor)  # Change cursor to hand when hovering
            product_button.setProperty("product_id", product.id)

            # Explicitly create a local copy of product.id to avoid reference issues
            product_id = product.id
            product_button.clicked.connect(lambda checked=False, pid=product_id: self.add_product_to_cart(pid))

            # Add styling to button with improved contrast
            product_button.setStyleSheet("""
                QPushButton {
                    background-color: #e0f2f1;  /* Light teal background */
                    color: #004d40;  /* Dark teal text for contrast */
                    border: 2px solid #00796b;  /* Medium teal border */
                    border-radius: 8px;
                    padding: 10px;
                    text-align: center;
                    font-weight: bold;
                    font-size: 12pt;
                }
                QPushButton:hover {
                    background-color: #b2dfdb;  /* Slightly darker on hover */
                    border-color: #004d40;  /* Darker border on hover */
                }
                QPushButton:pressed {
                    background-color: #004d40;  /* Dark background when pressed */
                    color: white;  /* White text when pressed for contrast */
                    border: 3px solid #00bfa5;  /* Bright border when pressed */
                }
            """)

            # Add price to button
            price_text = format_currency(product.price, "EGP")
            product_button.setText(f"{product.name}\n{price_text}")

            print(f"POS View: Adding product button: {product.name}, ID: {product.id}, Price: {product.price}")

            # Add button to grid
            self.products_grid.addWidget(product_button, row, col)

            # Update row and column
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        print(f"POS View: Added {len(filtered_products)} product buttons to grid")

    def get_filtered_products(self):
        """Filter products based on search text and category without updating the display.

        Returns:
            list: Filtered products
        """
        # Get search text
        search_text = self.search_edit.text().strip().lower()

        # Get selected category
        category_id = self.category_combo.currentData()

        print(f"POS View: Filtering products with search text: '{search_text}', category ID: {category_id}")

        # Filter products
        filtered_products = []
        for product in self.products_list:
            # Check if product matches search text
            if search_text:
                # Check in name, description, and barcode
                name_match = hasattr(product, 'name') and product.name and search_text in product.name.lower()
                desc_match = hasattr(product, 'description') and product.description and search_text in product.description.lower()
                barcode_match = hasattr(product, 'barcode') and product.barcode and search_text in product.barcode.lower()

                if not (name_match or desc_match or barcode_match):
                    continue

            # Check if product matches category
            if category_id and hasattr(product, 'category_id') and product.category_id != category_id:
                continue

            filtered_products.append(product)

        # Print debug info
        print(f"POS View: Filtered products: {len(filtered_products)} out of {len(self.products_list)}")
        for p in filtered_products[:5]:  # Print first 5 for debugging
            print(f"POS View: Filtered product: {p.name}, ID: {p.id}, Price: {p.price}")

        return filtered_products

    def filter_products(self):
        """Filter products based on search text and category and update the display.

        Returns:
            list: Filtered products
        """
        print("POS View: Filtering products and updating display...")

        # Get filtered products
        filtered_products = self.get_filtered_products()

        # Update the display with the filtered products
        if hasattr(self, 'products_grid') and self.products_grid:
            self.display_products(filtered_products)

        return filtered_products

    def scan_barcode(self):
        """Scan barcode and add product to cart."""
        # Show barcode input dialog
        barcode, ok = QInputDialog.getText(
            self,
            tr("pos.scan_barcode", "مسح الباركود"),
            tr("pos.enter_barcode", "أدخل الباركود:")
        )

        if ok and barcode:
            barcode = barcode.strip()
            if not barcode:
                return

            # Find product by barcode
            query = """
            SELECT * FROM products
            WHERE barcode = ? AND type = 'product'
            """
            rows = self.db_manager.execute_query(query, (barcode,))

            if rows:
                # Add product to cart
                product = Product.from_db_row(rows[0])
                self.add_product_to_cart(product.id)

                # Show confirmation
                QMessageBox.information(
                    self,
                    tr("pos.product_found", "تم العثور على المنتج"),
                    tr("pos.product_added", "تمت إضافة المنتج '{0}' إلى السلة").format(product.name)
                )
            else:
                # Try to find product by partial barcode match
                query = """
                SELECT * FROM products
                WHERE barcode LIKE ? AND type = 'product'
                """
                rows = self.db_manager.execute_query(query, (f"%{barcode}%",))

                if rows:
                    # If only one product found, add it
                    if len(rows) == 1:
                        product = Product.from_db_row(rows[0])
                        self.add_product_to_cart(product.id)

                        # Show confirmation
                        QMessageBox.information(
                            self,
                            tr("pos.product_found", "تم العثور على المنتج"),
                            tr("pos.product_added", "تمت إضافة المنتج '{0}' إلى السلة").format(product.name)
                        )
                    else:
                        # Multiple products found, show a message
                        products_list = "\n".join([f"- {Product.from_db_row(row).name}" for row in rows[:5]])
                        if len(rows) > 5:
                            products_list += f"\n- ... ({len(rows) - 5} more)"

                        QMessageBox.information(
                            self,
                            tr("pos.multiple_products", "تم العثور على عدة منتجات"),
                            tr("pos.multiple_products_message", "تم العثور على عدة منتجات تطابق الباركود. يرجى اختيار المنتج من القائمة:\n\n{0}").format(products_list)
                        )

                        # Set the search text to the barcode to filter products
                        self.search_edit.setText(barcode)
                else:
                    # Product not found
                    QMessageBox.warning(
                        self,
                        tr("pos.product_not_found", "المنتج غير موجود"),
                        tr("pos.barcode_not_found", "لم يتم العثور على منتج بهذا الباركود")
                    )

    def add_product_to_cart(self, product_id=None):
        """Add a product to the cart.

        Args:
            product_id: Product ID or None if called from a button
        """
        try:
            # Get product ID from sender if not provided
            if product_id is None:
                button = self.sender()
                if button:
                    product_id = button.property("product_id")

            if not product_id:
                print("No product ID provided")
                return

            # Find product in the list
            product = None
            for p in self.products_list:
                if p.id == product_id:
                    product = p
                    break

            # If not found in the list, try to get it from the database
            if not product:
                query = """
                SELECT * FROM products
                WHERE id = ?
                """
                rows = self.db_manager.execute_query(query, (product_id,))
                if rows:
                    product = Product.from_db_row(rows[0])
                else:
                    print(f"Product with ID {product_id} not found in database")
                    return

            # Check if product is already in cart
            for row in range(self.cart_table.rowCount()):
                item = self.cart_table.item(row, 0)
                if item and item.data(Qt.UserRole) == product_id:
                    # Update quantity
                    quantity_item = self.cart_table.item(row, 2)
                    quantity = int(quantity_item.text()) + 1
                    quantity_item.setText(str(quantity))

                    # Update total
                    price_text = self.cart_table.item(row, 1).text().replace("ج.م", "").strip()
                    # Remove thousand separators
                    price_text = price_text.replace(",", "")
                    try:
                        price = float(price_text)
                    except (ValueError, TypeError):
                        print(f"Warning: Invalid price text: {price_text}, defaulting to 0.0")
                        price = 0.0

                    discount_text = self.cart_table.item(row, 3).text().replace("%", "").strip()
                    try:
                        discount = float(discount_text)
                    except (ValueError, TypeError):
                        print(f"Warning: Invalid discount text: {discount_text}, defaulting to 0.0")
                        discount = 0.0

                    total = price * quantity * (1 - discount / 100)
                    total_item = self.cart_table.item(row, 4)
                    total_item.setText(format_currency(total, "EGP"))

                    # Update invoice totals
                    self.update_totals()
                    return

            # Add new row
            row = self.cart_table.rowCount()
            self.cart_table.insertRow(row)

            # Product name
            name_item = QTableWidgetItem(product.name)
            name_item.setData(Qt.UserRole, product.id)
            self.cart_table.setItem(row, 0, name_item)

            # Price
            price_item = QTableWidgetItem(format_currency(product.price, "EGP"))
            self.cart_table.setItem(row, 1, price_item)

            # Quantity
            quantity_item = QTableWidgetItem("1")
            self.cart_table.setItem(row, 2, quantity_item)

            # Discount
            discount_item = QTableWidgetItem("0%")
            self.cart_table.setItem(row, 3, discount_item)

            # Total
            total_item = QTableWidgetItem(format_currency(product.price, "EGP"))
            self.cart_table.setItem(row, 4, total_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)

            # Edit button with improved styling
            edit_button = QPushButton(tr("pos.edit", "تعديل"))
            edit_button.setProperty("row", row)
            edit_button.clicked.connect(self.edit_cart_item)
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #0d47a1;  /* Blue background */
                    color: white;  /* White text for contrast */
                    border: 2px solid #0d47a1;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-weight: bold;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #1565c0;
                    border-color: #0d47a1;
                }
                QPushButton:pressed {
                    background-color: #0a2f6b;
                    border-color: #1565c0;
                }
            """)
            actions_layout.addWidget(edit_button)

            # Remove button with improved styling
            remove_button = QPushButton(tr("pos.remove", "إزالة"))
            remove_button.setProperty("row", row)
            remove_button.clicked.connect(self.remove_cart_item)
            remove_button.setStyleSheet("""
                QPushButton {
                    background-color: #c62828;  /* Red background */
                    color: white;  /* White text for contrast */
                    border: 2px solid #c62828;
                    border-radius: 5px;
                    padding: 5px 10px;
                    font-weight: bold;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #e53935;
                    border-color: #c62828;
                }
                QPushButton:pressed {
                    background-color: #8e0000;
                    border-color: #e53935;
                }
            """)
            actions_layout.addWidget(remove_button)

            self.cart_table.setCellWidget(row, 5, actions_widget)

            # Update invoice totals
            self.update_totals()
        except Exception as e:
            print(f"Error adding product to cart: {e}")
            QMessageBox.warning(
                self,
                tr("pos.error", "خطأ"),
                tr("pos.add_product_error", f"حدث خطأ أثناء إضافة المنتج: {str(e)}")
            )

    def edit_cart_item(self):
        """Edit a cart item."""
        button = self.sender()
        if not button:
            return

        row = button.property("row")
        if row is None:
            return

        # Get current values
        product_id = self.cart_table.item(row, 0).data(Qt.UserRole)
        product_name = self.cart_table.item(row, 0).text()

        # Get price
        price_text = self.cart_table.item(row, 1).text()
        # Remove currency symbol and thousand separators
        price_text = price_text.replace("ج.م", "").replace(",", "").strip()
        try:
            price = float(price_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid price text in edit_cart_item: {price_text}, defaulting to 0.0")
            price = 0.0

        # Get quantity
        quantity_text = self.cart_table.item(row, 2).text()
        try:
            quantity = int(quantity_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid quantity text: {quantity_text}, defaulting to 1")
            quantity = 1

        # Get discount
        discount_text = self.cart_table.item(row, 3).text()
        discount_text = discount_text.replace("%", "").strip()
        try:
            discount = float(discount_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid discount text: {discount_text}, defaulting to 0.0")
            discount = 0.0

        # Create dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("pos.edit_item", "تعديل العنصر"))
        dialog.setMinimumWidth(300)

        # Layout
        layout = QVBoxLayout(dialog)

        # Form
        form_layout = QFormLayout()

        # Product name
        name_label = QLabel(product_name)
        name_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(tr("pos.product", "المنتج:"), name_label)

        # Price
        price_spin = QDoubleSpinBox()
        price_spin.setMinimum(0)
        price_spin.setMaximum(1000000)
        price_spin.setDecimals(2)
        price_spin.setValue(price)
        form_layout.addRow(tr("pos.price", "السعر:"), price_spin)

        # Quantity
        quantity_spin = QSpinBox()
        quantity_spin.setMinimum(1)
        quantity_spin.setMaximum(1000)
        quantity_spin.setValue(quantity)
        form_layout.addRow(tr("pos.quantity", "الكمية:"), quantity_spin)

        # Discount
        discount_spin = QDoubleSpinBox()
        discount_spin.setMinimum(0)
        discount_spin.setMaximum(100)
        discount_spin.setSingleStep(1)
        discount_spin.setValue(discount)
        discount_spin.setSuffix("%")
        form_layout.addRow(tr("pos.discount", "الخصم:"), discount_spin)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Update cart item
            new_price = price_spin.value()
            new_quantity = quantity_spin.value()
            new_discount = discount_spin.value()

            # Update price
            self.cart_table.item(row, 1).setText(format_currency(new_price, "EGP"))

            # Update quantity
            self.cart_table.item(row, 2).setText(str(new_quantity))

            # Update discount
            self.cart_table.item(row, 3).setText(f"{new_discount}%")

            # Update total
            total = new_price * new_quantity * (1 - new_discount / 100)
            self.cart_table.item(row, 4).setText(format_currency(total, "EGP"))

            # Update invoice totals
            self.update_totals()

    def remove_cart_item(self):
        """Remove a cart item."""
        button = self.sender()
        if not button:
            return

        row = button.property("row")
        if row is None:
            return

        # Remove row
        self.cart_table.removeRow(row)

        # Update row properties for remaining buttons
        for r in range(row, self.cart_table.rowCount()):
            actions_widget = self.cart_table.cellWidget(r, 5)
            if actions_widget:
                for i in range(actions_widget.layout().count()):
                    button = actions_widget.layout().itemAt(i).widget()
                    if button:
                        button.setProperty("row", r)

        # Update invoice totals
        self.update_totals()

    def update_totals(self):
        """Update invoice totals."""
        # Calculate subtotal
        subtotal = 0
        for row in range(self.cart_table.rowCount()):
            price_text = self.cart_table.item(row, 1).text()
            # Remove currency symbol and thousand separators
            price_text = price_text.replace("ج.م", "").replace(",", "").strip()
            try:
                price = float(price_text)
            except (ValueError, TypeError):
                print(f"Warning: Invalid price text in update_totals: {price_text}, defaulting to 0.0")
                price = 0.0

            quantity_text = self.cart_table.item(row, 2).text()
            try:
                quantity = int(quantity_text)
            except (ValueError, TypeError):
                print(f"Warning: Invalid quantity text: {quantity_text}, defaulting to 1")
                quantity = 1

            subtotal += price * quantity

        # Calculate tax (assuming 14% VAT)
        tax_rate = 0.14
        tax = subtotal * tax_rate

        # Calculate discount
        discount_value = 0
        discount_type = self.discount_type_combo.currentData()
        discount = self.discount_spin.value()

        if discount_type == "percentage":
            discount_value = subtotal * (discount / 100)
        else:
            discount_value = discount

        # Calculate total
        total = subtotal + tax - discount_value

        # Update text fields
        self.subtotal_label.setText(format_currency(subtotal, "EGP"))
        self.tax_label.setText(format_currency(tax, "EGP"))
        self.total_label.setText(format_currency(total, "EGP"))

    def clear_cart(self):
        """Clear the cart."""
        # Clear cart table
        self.cart_table.setRowCount(0)

        # Reset discount
        self.discount_spin.setValue(0)
        self.discount_type_combo.setCurrentIndex(0)

        # Update totals
        self.update_totals()

        # Reset current invoice
        self.current_invoice = None

    def add_customer(self):
        """Add a new customer."""
        # Show customer dialog
        from ui.customer_dialog import CustomerDialog
        dialog = CustomerDialog(self.db_manager, None, self)
        if dialog.exec() == QDialog.Accepted:
            # Reload customers
            self.load_customers()

            # Select the new customer
            customer_id = dialog.get_customer_id()
            if customer_id:
                index = self.customer_combo.findData(customer_id)
                if index >= 0:
                    self.customer_combo.setCurrentIndex(index)

    def hold_invoice(self):
        """Hold the current invoice."""
        # Check if cart is empty
        if self.cart_table.rowCount() == 0:
            QMessageBox.warning(
                self,
                tr("pos.empty_cart", "سلة فارغة"),
                tr("pos.empty_cart_message", "لا يمكن تعليق فاتورة فارغة")
            )
            return

        # Ask for a note
        note, ok = QInputDialog.getText(
            self,
            tr("pos.hold_invoice", "تعليق الفاتورة"),
            tr("pos.hold_note", "ملاحظة (اختياري):")
        )

        if not ok:
            return

        # Create invoice
        invoice = self.create_invoice(Invoice.STATUS_DRAFT)
        if invoice:
            QMessageBox.information(
                self,
                tr("pos.invoice_held", "تم تعليق الفاتورة"),
                tr("pos.invoice_held_message", "تم تعليق الفاتورة بنجاح")
            )

            # Clear cart
            self.clear_cart()

    def show_payment(self):
        """Show the payment screen."""
        print("POS View: show_payment called")

        # Check if cart is empty
        if self.cart_table.rowCount() == 0:
            print("POS View: Cart is empty, showing warning")
            QMessageBox.warning(
                self,
                tr("pos.empty_cart", "سلة فارغة"),
                tr("pos.empty_cart_message", "لا يمكن إتمام عملية بيع لسلة فارغة")
            )
            return

        print("POS View: Updating payment screen")
        # Update payment screen
        self.update_payment_screen()

        print("POS View: Showing payment widget")
        # Show payment screen
        self.stacked_widget.setCurrentWidget(self.payment_widget)

    def update_payment_screen(self):
        """Update the payment screen."""
        # Get totals
        subtotal_text = self.subtotal_label.text()
        subtotal_text = subtotal_text.replace("ج.م", "").replace(",", "").strip()
        try:
            subtotal = float(subtotal_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid subtotal text: {subtotal_text}, defaulting to 0.0")
            subtotal = 0.0

        tax_text = self.tax_label.text()
        tax_text = tax_text.replace("ج.م", "").replace(",", "").strip()
        try:
            tax = float(tax_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid tax text: {tax_text}, defaulting to 0.0")
            tax = 0.0

        total_text = self.total_label.text()
        total_text = total_text.replace("ج.م", "").replace(",", "").strip()
        try:
            total = float(total_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid total text: {total_text}, defaulting to 0.0")
            total = 0.0

        # Get customer
        customer_id = self.customer_combo.currentData()
        customer_name = self.customer_combo.currentText()

        # Update payment screen
        self.payment_total_label.setText(format_currency(total, "EGP"))
        self.paid_amount_spin.setValue(total)
        self.change_label.setText(format_currency(0, "EGP"))

        # Update summary
        self.summary_customer_label.setText(customer_name)
        self.items_count_label.setText(str(self.cart_table.rowCount()))
        self.summary_subtotal_label.setText(subtotal_text)
        self.summary_tax_label.setText(tax_text)

        # Get discount
        discount_type = self.discount_type_combo.currentData()
        discount = self.discount_spin.value()

        if discount_type == "percentage":
            self.summary_discount_label.setText(f"{discount}%")
        else:
            self.summary_discount_label.setText(format_currency(discount, "EGP"))

        self.summary_total_label.setText(total_text)

        # Set focus to paid amount
        self.paid_amount_spin.setFocus()

    def back_to_pos(self):
        """Go back to the POS screen."""
        self.stacked_widget.setCurrentWidget(self.pos_widget)

    def payment_method_changed(self):
        """Handle payment method change."""
        # Get selected payment method
        payment_method_id = self.payment_method_combo.currentData()

        # Find payment method
        payment_method = None
        for method in self.payment_methods:
            if method['id'] == payment_method_id:
                payment_method = method
                break

        if not payment_method:
            return

        # Show/hide reference field
        requires_reference = payment_method['requires_reference']
        self.reference_edit.setEnabled(requires_reference)
        self.reference_edit.setVisible(requires_reference)
        self.reference_edit.clear()

    def update_change(self):
        """Update the change amount."""
        # Get total
        total_text = self.payment_total_label.text()
        # Remove currency symbol and thousand separators
        total_text = total_text.replace("ج.م", "").replace(",", "").strip()
        try:
            total = float(total_text)
        except (ValueError, TypeError):
            print(f"Warning: Invalid total text in update_change: {total_text}, defaulting to 0.0")
            total = 0.0

        # Get paid amount
        paid = self.paid_amount_spin.value()

        # Calculate change
        change = paid - total

        # Update change label
        self.change_label.setText(format_currency(change, "EGP"))

    def complete_sale(self):
        """Complete the sale."""
        print("POS View: complete_sale called")

        try:
            # Get payment details
            payment_method_id = self.payment_method_combo.currentData()
            payment_method_name = self.payment_method_combo.currentText()
            reference = self.reference_edit.text().strip()

            print(f"POS View: Payment method: {payment_method_name}, ID: {payment_method_id}")

            # Check if reference is required
            payment_method = None
            for method in self.payment_methods:
                if method['id'] == payment_method_id:
                    payment_method = method
                    break

            if payment_method and payment_method['requires_reference'] and not reference:
                print("POS View: Reference required but not provided")
                QMessageBox.warning(
                    self,
                    tr("pos.missing_reference", "مرجع مفقود"),
                    tr("pos.reference_required", "يرجى إدخال مرجع لطريقة الدفع المحددة")
                )
                self.reference_edit.setFocus()
                return

            # Get total
            total_text = self.payment_total_label.text()
            # Remove currency symbol and thousand separators
            total_text = total_text.replace("ج.م", "").replace(",", "").strip()
            try:
                total = float(total_text)
                print(f"POS View: Total amount: {total}")
            except (ValueError, TypeError):
                print(f"Warning: Invalid total text in complete_sale: {total_text}, defaulting to 0.0")
                total = 0.0

            # Get paid amount
            paid = self.paid_amount_spin.value()
            print(f"POS View: Paid amount: {paid}")

            # Check if paid amount is enough
            if paid < total:
                print(f"POS View: Insufficient payment. Paid: {paid}, Total: {total}")
                QMessageBox.warning(
                    self,
                    tr("pos.insufficient_payment", "دفع غير كافي"),
                    tr("pos.insufficient_payment_message", "المبلغ المدفوع أقل من إجمالي الفاتورة")
                )
                self.paid_amount_spin.setFocus()
                return

            print("POS View: Payment is sufficient, proceeding with sale")

            # Create invoice
            print("POS View: Creating invoice")
            invoice = self.create_invoice(Invoice.STATUS_PAID)
            if not invoice:
                print("POS View: Failed to create invoice")
                QMessageBox.critical(
                    self,
                    tr("pos.invoice_error", "خطأ في الفاتورة"),
                    tr("pos.invoice_creation_failed", "فشل في إنشاء الفاتورة. يرجى المحاولة مرة أخرى.")
                )
                return

            print(f"POS View: Invoice created successfully with ID: {invoice.id}")

            # Create transaction
            print("POS View: Creating transaction")
            transaction = POSTransaction(
                session_id=self.current_session.id,
                invoice_id=invoice.id,
                transaction_type=POSTransaction.TYPE_SALE,
                amount=total,
                payment_method=payment_method_name,
                reference=reference,
                notes=f"Invoice #{invoice.id}"
            )

            transaction_id = self.pos_manager.add_transaction(transaction)
            if transaction_id <= 0:
                print("POS View: Failed to create transaction")
                QMessageBox.critical(
                    self,
                    tr("pos.transaction_error", "خطأ في المعاملة"),
                    tr("pos.transaction_error_message", "حدث خطأ أثناء إنشاء المعاملة")
                )
                return

            print(f"POS View: Transaction created successfully with ID: {transaction_id}")

            # Print receipt if requested
            if self.print_receipt_check.isChecked():
                try:
                    self.print_receipt(invoice.id)
                except Exception as e:
                    print(f"Error printing receipt: {e}")
                    QMessageBox.warning(
                        self,
                        tr("pos.printing_error", "خطأ في الطباعة"),
                        tr("pos.printing_error_message", "حدث خطأ أثناء طباعة الإيصال: {0}").format(str(e))
                    )

            # Email receipt if requested
            if self.email_receipt_check.isChecked():
                try:
                    self.email_receipt(invoice.id)
                except Exception as e:
                    print(f"Error emailing receipt: {e}")
                    QMessageBox.warning(
                        self,
                        tr("pos.emailing_error", "خطأ في الإرسال"),
                        tr("pos.emailing_error_message", "حدث خطأ أثناء إرسال الإيصال بالبريد الإلكتروني: {0}").format(str(e))
                    )

            # Show success message
            change = paid - total
            print(f"POS View: Sale completed. Change: {change}")

            if change > 0:
                print(f"POS View: Showing success message with change: {change}")
                QMessageBox.information(
                    self,
                    tr("pos.sale_completed", "تم إتمام البيع"),
                    tr("pos.sale_completed_with_change", "تم إتمام البيع بنجاح. الباقي: {0}").format(format_currency(change, "EGP"))
                )
            else:
                print("POS View: Showing success message without change")
                QMessageBox.information(
                    self,
                    tr("pos.sale_completed", "تم إتمام البيع"),
                    tr("pos.sale_completed_message", "تم إتمام البيع بنجاح")
                )

            print("POS View: Going back to POS screen")
            # Go back to POS screen
            self.stacked_widget.setCurrentWidget(self.pos_widget)

            print("POS View: Clearing cart")
            # Clear cart
            self.clear_cart()

            print("POS View: Sale process completed successfully")
            return True

        except Exception as e:
            # Catch any unexpected exceptions
            print(f"POS View: Unexpected error in complete_sale: {str(e)}")
            QMessageBox.critical(
                self,
                tr("pos.unexpected_error", "خطأ غير متوقع"),
                tr("pos.unexpected_error_message", "حدث خطأ غير متوقع أثناء إتمام البيع: {0}").format(str(e))
            )
            # Log the full exception for debugging
            import traceback
            print(f"POS View: Exception traceback: {traceback.format_exc()}")
            return False

    def create_invoice(self, status):
        """Create an invoice from the cart.

        Args:
            status (str): Invoice status

        Returns:
            Invoice: Created invoice or None if failed
        """
        print(f"POS View: create_invoice called with status: {status}")

        # Get customer
        customer_id = self.customer_combo.currentData()
        print(f"POS View: Customer ID: {customer_id}")

        # If no customer is selected, use a default customer ID (1 for system admin)
        if customer_id is None or customer_id == 0:
            customer_id = 1
            print(f"POS View: No customer selected, using default customer ID: {customer_id}")

        # Get totals
        subtotal_text = self.subtotal_label.text()
        subtotal_text = subtotal_text.replace("ج.م", "").replace(",", "").strip()
        try:
            subtotal = float(subtotal_text)
            print(f"POS View: Subtotal: {subtotal}")
        except (ValueError, TypeError):
            print(f"Warning: Invalid subtotal text in create_invoice: {subtotal_text}, defaulting to 0.0")
            subtotal = 0.0

        tax_text = self.tax_label.text()
        tax_text = tax_text.replace("ج.م", "").replace(",", "").strip()
        try:
            tax = float(tax_text)
            print(f"POS View: Tax: {tax}")
        except (ValueError, TypeError):
            print(f"Warning: Invalid tax text in create_invoice: {tax_text}, defaulting to 0.0")
            tax = 0.0

        total_text = self.total_label.text()
        total_text = total_text.replace("ج.م", "").replace(",", "").strip()
        try:
            total = float(total_text)
            print(f"POS View: Total: {total}")
        except (ValueError, TypeError):
            print(f"Warning: Invalid total text in create_invoice: {total_text}, defaulting to 0.0")
            total = 0.0

        # Get discount
        discount_type = self.discount_type_combo.currentData()
        discount = self.discount_spin.value()

        discount_value = 0
        if discount_type == "percentage":
            discount_value = subtotal * (discount / 100)
        else:
            discount_value = discount

        # Create invoice
        print("POS View: Creating Invoice object")
        invoice = Invoice(
            customer_id=customer_id,
            subtotal=subtotal,
            tax=tax,
            discount=discount_value,
            total=total,
            status=status,
            amount_paid=total if status == Invoice.STATUS_PAID else 0,
            amount_due=0 if status == Invoice.STATUS_PAID else total,
            notes="Created from POS"
        )

        # Add invoice
        print("POS View: Adding invoice to database")
        invoice_id = self.invoice_manager.add_invoice(invoice)
        if invoice_id <= 0:
            print(f"POS View: Failed to add invoice to database. Return value: {invoice_id}")
            QMessageBox.critical(
                self,
                tr("pos.invoice_error", "خطأ في الفاتورة"),
                tr("pos.invoice_error_message", "حدث خطأ أثناء إنشاء الفاتورة")
            )
            return None

        print(f"POS View: Invoice added to database with ID: {invoice_id}")

        # Get the created invoice
        print(f"POS View: Retrieving invoice with ID: {invoice_id}")
        invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
        if not invoice:
            print("POS View: Failed to retrieve invoice from database")
            QMessageBox.critical(
                self,
                tr("pos.invoice_error", "خطأ في الفاتورة"),
                tr("pos.invoice_error_message", "حدث خطأ أثناء إنشاء الفاتورة")
            )
            return None

        print(f"POS View: Invoice retrieved successfully: {invoice.id}")

        # Add invoice items
        print(f"POS View: Adding {self.cart_table.rowCount()} items to invoice")
        for row in range(self.cart_table.rowCount()):
            product_id = self.cart_table.item(row, 0).data(Qt.UserRole)
            product_name = self.cart_table.item(row, 0).text()
            print(f"POS View: Processing item {row+1}: {product_name}, ID: {product_id}")

            price_text = self.cart_table.item(row, 1).text()
            price_text = price_text.replace("ج.م", "").replace(",", "").strip()
            try:
                price = float(price_text)
                print(f"POS View: Item price: {price}")
            except (ValueError, TypeError):
                print(f"Warning: Invalid price text in create_invoice: {price_text}, defaulting to 0.0")
                price = 0.0

            quantity_text = self.cart_table.item(row, 2).text()
            try:
                quantity = int(quantity_text)
                print(f"POS View: Item quantity: {quantity}")
            except (ValueError, TypeError):
                print(f"Warning: Invalid quantity text in create_invoice: {quantity_text}, defaulting to 1")
                quantity = 1

            discount_text = self.cart_table.item(row, 3).text()
            discount_text = discount_text.replace("%", "").strip()
            try:
                item_discount = float(discount_text)
                print(f"POS View: Item discount: {item_discount}%")
            except (ValueError, TypeError):
                print(f"Warning: Invalid discount text in create_invoice: {discount_text}, defaulting to 0.0")
                item_discount = 0.0

            # Create invoice item
            print(f"POS View: Creating invoice item for product: {product_name}")

            # Calculate discount and tax amounts
            discount_amount = (price * quantity) * (item_discount / 100)
            tax_amount = (price * quantity - discount_amount) * (14 / 100)  # 14% VAT

            item = InvoiceItem(
                invoice_id=invoice_id,
                product_id=product_id,
                description=product_name,
                quantity=quantity,
                unit_price=price,
                discount=discount_amount,
                tax=tax_amount,
                is_product=True,
                track_inventory=True
            )

            # Add invoice item
            print(f"POS View: Adding invoice item to database")
            item_id = self.invoice_manager.add_invoice_item(item)
            if item_id <= 0:
                print(f"POS View: Failed to add invoice item. Return value: {item_id}")
                QMessageBox.warning(
                    self,
                    tr("pos.item_error", "خطأ في العنصر"),
                    tr("pos.item_error_message", "حدث خطأ أثناء إضافة العنصر: {0}").format(product_name)
                )
            else:
                print(f"POS View: Invoice item added with ID: {item_id}")

            # Update inventory if paid
            if status == Invoice.STATUS_PAID:
                print(f"POS View: Updating inventory for product ID: {product_id}, quantity: -{quantity}")
                # Use the safe inventory update function
                result = update_product_stock_safely(
                    self.db_manager,
                    product_id,
                    -quantity,
                    f"POS Sale - Invoice #{invoice_id}",
                    self
                )
                print(f"POS View: Inventory update result: {result}")

        print(f"POS View: All items added to invoice {invoice_id}")
        return invoice

    def print_receipt(self, invoice_id):
        """Print a receipt for an invoice.

        Args:
            invoice_id (int): Invoice ID
        """
        # Get invoice details
        invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
        if not invoice:
            QMessageBox.warning(
                self,
                tr("pos.invoice_error", "خطأ في الفاتورة"),
                tr("pos.invoice_not_found", "لم يتم العثور على الفاتورة")
            )
            return

        # Get invoice items
        items = self.invoice_manager.get_invoice_items(invoice_id)
        if not items:
            QMessageBox.warning(
                self,
                tr("pos.invoice_error", "خطأ في الفاتورة"),
                tr("pos.invoice_empty", "الفاتورة لا تحتوي على عناصر")
            )
            return

        # Get customer
        customer = None
        if invoice.customer_id:
            customer = self.invoice_manager.get_customer_by_id(invoice.customer_id)

        # Create receipt content
        from datetime import datetime
        receipt_content = []

        # Header
        receipt_content.append("=" * 40)
        receipt_content.append(f"{'فوترها':^40}")
        receipt_content.append(f"{'FAWTERHA':^40}")
        receipt_content.append("=" * 40)
        receipt_content.append(f"{'إيصال المبيعات':^40}")
        receipt_content.append(f"{'SALES RECEIPT':^40}")
        receipt_content.append("-" * 40)

        # Invoice details
        receipt_content.append(f"رقم الفاتورة: {invoice.invoice_number}")
        receipt_content.append(f"التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}")

        # Customer details
        if customer:
            receipt_content.append(f"العميل: {customer.name}")
            if customer.phone:
                receipt_content.append(f"الهاتف: {customer.phone}")

        receipt_content.append("-" * 40)

        # Items header
        receipt_content.append(f"{'الوصف':<20}{'الكمية':^6}{'السعر':^7}{'الإجمالي':>7}")
        receipt_content.append("-" * 40)

        # Items
        for item in items:
            description = item.description
            if len(description) > 18:
                description = description[:15] + "..."

            quantity = item.quantity
            price = item.unit_price
            # Calculate total based on discount amount instead of percentage
            discount_percentage = 0
            if price > 0:  # Avoid division by zero
                discount_percentage = (item.discount / (price * quantity)) * 100
            total = quantity * price - item.discount

            receipt_content.append(f"{description:<20}{quantity:^6}{price:^7.2f}{total:>7.2f}")

        receipt_content.append("-" * 40)

        # Totals
        receipt_content.append(f"{'المجموع الفرعي:':<30}{invoice.subtotal:>10.2f}")
        if invoice.discount > 0:
            receipt_content.append(f"{'الخصم:':<30}{invoice.discount:>10.2f}")
        receipt_content.append(f"{'الضريبة:':<30}{invoice.tax:>10.2f}")
        receipt_content.append(f"{'الإجمالي:':<30}{invoice.total:>10.2f}")

        receipt_content.append("=" * 40)
        receipt_content.append(f"{'شكراً لتسوقكم معنا':^40}")
        receipt_content.append(f"{'THANK YOU FOR YOUR BUSINESS':^40}")
        receipt_content.append("=" * 40)

        # Join receipt content
        receipt_text = "\n".join(receipt_content)

        # Print to default printer
        try:
            import tempfile
            import os
            import subprocess

            # Create temporary file
            fd, path = tempfile.mkstemp(suffix='.txt')
            try:
                with os.fdopen(fd, 'w', encoding='utf-8') as temp:
                    temp.write(receipt_text)

                # Print file using the default system printer
                if os.name == 'nt':  # Windows
                    subprocess.call(['notepad', '/p', path])
                else:  # Linux/Mac (this code is unreachable on Windows but kept for cross-platform compatibility)
                    subprocess.call(['lpr', path])

                QMessageBox.information(
                    self,
                    tr("pos.printing_success", "تمت الطباعة"),
                    tr("pos.printing_success_message", "تمت طباعة الإيصال بنجاح")
                )
            finally:
                # Remove temporary file
                try:
                    os.unlink(path)
                except Exception as e:
                    print(f"Error removing temporary file: {e}")
        except Exception as e:
            QMessageBox.warning(
                self,
                tr("pos.printing_error", "خطأ في الطباعة"),
                tr("pos.printing_error_message", "حدث خطأ أثناء طباعة الإيصال: {0}").format(str(e))
            )

    def email_receipt(self, invoice_id):
        """Email a receipt for an invoice.

        Args:
            invoice_id (int): Invoice ID
        """
        # Get invoice details
        invoice = self.invoice_manager.get_invoice_by_id(invoice_id)
        if not invoice:
            QMessageBox.warning(
                self,
                tr("pos.invoice_error", "خطأ في الفاتورة"),
                tr("pos.invoice_not_found", "لم يتم العثور على الفاتورة")
            )
            return

        # Get customer email
        customer_email = ""
        customer_name = "العميل"
        if invoice.customer_id:
            customer = self.invoice_manager.get_customer_by_id(invoice.customer_id)
            if customer:
                if hasattr(customer, 'email') and customer.email:
                    customer_email = customer.email
                if hasattr(customer, 'name') and customer.name:
                    customer_name = customer.name

        # Always ask for email confirmation or change
        from PySide6.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QCheckBox

        email_dialog = QDialog(self)
        email_dialog.setWindowTitle(tr("pos.email_confirmation", "تأكيد البريد الإلكتروني"))
        email_dialog.setMinimumWidth(400)

        layout = QVBoxLayout(email_dialog)

        # Add explanation
        info_label = QLabel(tr("pos.email_info", "سيتم إرسال الإيصال إلى البريد الإلكتروني التالي:"))
        layout.addWidget(info_label)

        # Add email field
        email_edit = QLineEdit(customer_email)
        email_edit.setPlaceholderText(tr("pos.email_placeholder", "أدخل البريد الإلكتروني"))
        layout.addWidget(email_edit)

        # Add save checkbox if customer exists
        save_checkbox = None
        if invoice.customer_id and customer:
            save_checkbox = QCheckBox(tr("pos.save_email", "حفظ هذا البريد الإلكتروني للعميل"))
            save_checkbox.setChecked(False)
            layout.addWidget(save_checkbox)

        # Add buttons
        button_layout = QHBoxLayout()
        cancel_button = QPushButton(tr("pos.cancel", "إلغاء"))
        send_button = QPushButton(tr("pos.send", "إرسال"))
        send_button.setDefault(True)

        button_layout.addWidget(cancel_button)
        button_layout.addWidget(send_button)
        layout.addLayout(button_layout)

        # Connect signals
        cancel_button.clicked.connect(email_dialog.reject)
        send_button.clicked.connect(email_dialog.accept)

        # Show dialog
        if email_dialog.exec() != QDialog.Accepted:
            return

        # Get email from dialog
        customer_email = email_edit.text().strip()

        # Validate email
        if not customer_email:
            QMessageBox.warning(
                self,
                tr("pos.email_required", "البريد الإلكتروني مطلوب"),
                tr("pos.email_required_message", "البريد الإلكتروني مطلوب لإرسال الإيصال")
            )
            return

        # Validate email format with more strict validation
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, customer_email):
            QMessageBox.warning(
                self,
                tr("pos.invalid_email", "بريد إلكتروني غير صالح"),
                tr("pos.invalid_email_message", "يرجى إدخال عنوان بريد إلكتروني صالح")
            )
            return

        # Save email to customer if requested
        if save_checkbox and save_checkbox.isChecked() and invoice.customer_id and customer:
            try:
                # Update customer email
                query = """
                UPDATE customers
                SET email = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
                self.db_manager.execute_update(query, (customer_email, invoice.customer_id))

                print(f"Updated email for customer {customer.name} (ID: {invoice.customer_id}): {customer_email}")
            except Exception as e:
                print(f"Error updating customer email: {e}")
                # Don't show error to user, just log it

        # Get invoice items
        items = self.invoice_manager.get_invoice_items(invoice_id)
        if not items:
            QMessageBox.warning(
                self,
                tr("pos.invoice_error", "خطأ في الفاتورة"),
                tr("pos.invoice_empty", "الفاتورة لا تحتوي على عناصر")
            )
            return

        try:
            # Get email settings from database
            query = "SELECT key, value FROM settings WHERE key IN ('smtp_server', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_use_tls', 'smtp_from_email', 'smtp_from_name')"
            rows = self.db_manager.execute_query(query)

            email_settings = {}
            for row in rows:
                email_settings[row['key']] = row['value']

            # Check if email settings are configured
            if not email_settings.get('smtp_server') or not email_settings.get('smtp_username') or not email_settings.get('smtp_password'):
                QMessageBox.warning(
                    self,
                    tr("pos.email_settings_missing", "إعدادات البريد الإلكتروني غير مكتملة"),
                    tr("pos.configure_email_settings", "يرجى تكوين إعدادات البريد الإلكتروني في صفحة الإعدادات")
                )
                return

            # Get invoice items
            invoice_items = self.invoice_manager.get_invoice_items(invoice.id)

            # Get customer object
            customer = None
            if invoice.customer_id:
                customer = self.invoice_manager.get_customer_by_id(invoice.customer_id)

            # Get company info
            query = "SELECT key, value FROM settings WHERE key IN ('company_name', 'company_address', 'company_phone', 'company_email')"
            rows = self.db_manager.execute_query(query)

            company_info = {}
            for row in rows:
                company_info[row['key']] = row['value']

            # Create progress dialog
            progress = QProgressDialog(
                tr("pos.sending_email", "جاري إرسال البريد الإلكتروني..."),
                tr("pos.cancel", "إلغاء"),
                0, 0, self
            )
            progress.setWindowTitle(tr("pos.email_receipt", "إرسال الإيصال بالبريد الإلكتروني"))
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(500)  # Show after 500ms
            progress.setCancelButton(None)  # Remove cancel button

            # Get SMTP settings
            smtp_server = email_settings.get('smtp_server')
            smtp_port = int(email_settings.get('smtp_port', '587'))
            smtp_username = email_settings.get('smtp_username')
            smtp_password = email_settings.get('smtp_password')
            smtp_use_tls = email_settings.get('smtp_use_tls', 'true').lower() == 'true'
            smtp_from_name = email_settings.get('smtp_from_name', 'فوترها')
            smtp_from_email = email_settings.get('smtp_from_email', '<EMAIL>')

            # Create worker and thread
            self.email_thread = QThread()
            self.email_worker = EmailWorker(
                smtp_server, smtp_port, smtp_username, smtp_password,
                smtp_use_tls, smtp_from_email, smtp_from_name, customer_email,
                invoice, invoice_items, customer, company_info
            )
            self.email_worker.moveToThread(self.email_thread)

            # Connect signals using Qt.QueuedConnection to avoid thread issues
            self.email_thread.started.connect(self.email_worker.send_email)
            self.email_worker.finished.connect(self.email_thread.quit, Qt.QueuedConnection)
            self.email_worker.finished.connect(self.email_worker.deleteLater, Qt.QueuedConnection)
            self.email_thread.finished.connect(self.email_thread.deleteLater, Qt.QueuedConnection)
            self.email_thread.finished.connect(progress.close, Qt.QueuedConnection)

            # Connect success and error signals with Qt.QueuedConnection
            self.email_worker.success.connect(
                lambda recipient: self.on_email_success(recipient, invoice.customer_id, customer, customer_email, save_checkbox),
                Qt.QueuedConnection
            )
            self.email_worker.error.connect(self.on_email_error, Qt.QueuedConnection)

            # Start the thread
            self.email_thread.start()

            # Show progress dialog
            progress.exec()



        except Exception as e:
            QMessageBox.warning(
                self,
                tr("pos.emailing_error", "خطأ في الإرسال"),
                tr("pos.emailing_error_message", "حدث خطأ أثناء إرسال الإيصال بالبريد الإلكتروني: {0}").format(str(e))
            )

    def on_email_success(self, recipient, customer_id, customer, customer_email, save_checkbox):
        """Handle successful email sending.

        Args:
            recipient (str): Email recipient
            customer_id (int): Customer ID
            customer: Customer object
            customer_email (str): Customer email
            save_checkbox: Checkbox to save email to customer
        """
        # Save email to customer if requested
        if save_checkbox and save_checkbox.isChecked() and customer_id and customer:
            try:
                # Update customer email
                query = """
                UPDATE customers
                SET email = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
                self.db_manager.execute_update(query, (customer_email, customer_id))

                print(f"Updated email for customer {customer.name} (ID: {customer_id}): {customer_email}")
            except Exception as e:
                print(f"Error updating customer email: {e}")
                # Don't show error to user, just log it

        QMessageBox.information(
            self,
            tr("pos.email_sent", "تم إرسال البريد الإلكتروني"),
            tr("pos.email_sent_message", f"تم إرسال الفاتورة بنجاح إلى {recipient}")
        )

    def on_email_error(self, error_message):
        """Handle email sending error.

        Args:
            error_message (str): Error message
        """
        print(f"Error sending email: {error_message}")

        # Add troubleshooting tips based on the error message
        troubleshooting_tips = ""

        if "authentication failed" in error_message.lower() or "اسم المستخدم أو كلمة المرور غير صحيحة" in error_message:
            troubleshooting_tips = tr("settings.auth_error_tips", """
            نصائح لحل المشكلة:
            • تأكد من صحة اسم المستخدم وكلمة المرور
            • إذا كنت تستخدم Gmail، قد تحتاج إلى:
              - تمكين "وصول التطبيقات الأقل أمانًا" في إعدادات الحساب
              - أو إنشاء "كلمة مرور للتطبيق" إذا كان التحقق بخطوتين مفعلاً
            """)
        elif "starttls" in error_message.lower():
            troubleshooting_tips = tr("settings.starttls_error_tips", """
            نصائح لحل المشكلة:
            • استخدم المنفذ 465 مع SSL بدلاً من TLS
            • أو قم بتعطيل خيار TLS واستخدم المنفذ 587
            • تأكد من أن خادم SMTP يدعم STARTTLS
            """)
        elif "timeout" in error_message.lower() or "timed out" in error_message.lower():
            troubleshooting_tips = tr("settings.timeout_error_tips", """
            نصائح لحل المشكلة:
            • تأكد من اتصالك بالإنترنت
            • تأكد من صحة عنوان خادم SMTP
            • تحقق من إعدادات جدار الحماية
            """)
        elif "refused" in error_message.lower():
            troubleshooting_tips = tr("settings.connection_refused_tips", """
            نصائح لحل المشكلة:
            • تأكد من صحة رقم المنفذ
            • تأكد من أن خادم SMTP يقبل اتصالات خارجية
            • تحقق من إعدادات جدار الحماية
            """)

        # Create the full error message
        full_error_message = tr("pos.emailing_error_message", "حدث خطأ أثناء إرسال الفاتورة بالبريد الإلكتروني: {0}").format(error_message)

        # Add troubleshooting tips if available
        if troubleshooting_tips:
            full_error_message += f"\n\n{troubleshooting_tips}"

        QMessageBox.warning(
            self,
            tr("pos.emailing_error", "خطأ في الإرسال"),
            full_error_message
        )

    def cleanup(self):
        """Clean up resources before the widget is destroyed."""
        # Stop any running email threads
        if hasattr(self, 'email_thread') and self.email_thread.isRunning():
            self.email_thread.quit()
            self.email_thread.wait(1000)  # Wait for 1 second

    def debug_complete_sale(self):
        """Debug wrapper for complete_sale function.
        This function prints debug information before calling complete_sale.
        """
        print("\n" + "="*50)
        print("DEBUG: Button 'إتمام البيع' clicked")
        print("="*50)

        # Print information about the current state
        print(f"Current user: {self.current_user.name if self.current_user else 'None'}")
        print(f"Current session: {self.current_session.id if self.current_session else 'None'}")
        print(f"Payment method: {self.payment_method_combo.currentText()}")
        print(f"Payment method ID: {self.payment_method_combo.currentData()}")
        print(f"Reference: {self.reference_edit.text()}")
        print(f"Total amount: {self.payment_total_label.text()}")
        print(f"Paid amount: {self.paid_amount_spin.value()}")
        print(f"Cart items count: {self.cart_table.rowCount()}")

        # Call the actual complete_sale function
        try:
            print("Calling complete_sale function...")
            result = self.complete_sale()
            if result:
                print("complete_sale function completed successfully")
            else:
                print("complete_sale function failed")
        except Exception as e:
            print(f"ERROR in complete_sale: {str(e)}")
            import traceback
            print(f"Traceback: {traceback.format_exc()}")

            # Show error message to user
            QMessageBox.critical(
                self,
                tr("pos.error", "خطأ"),
                tr("pos.complete_sale_error", "حدث خطأ أثناء إتمام البيع: {0}").format(str(e))
            )

        print("="*50 + "\n")

    def complete_sale_from_main(self):
        """Complete the sale from the main POS screen.
        This function shows the payment screen first, then allows the user to complete the sale.
        """
        print("POS View: complete_sale_from_main called")

        # Check if cart is empty
        if self.cart_table.rowCount() == 0:
            print("POS View: Cart is empty, showing warning")
            QMessageBox.warning(
                self,
                tr("pos.empty_cart", "سلة فارغة"),
                tr("pos.empty_cart_message", "لا يمكن إتمام عملية بيع لسلة فارغة")
            )
            return

        print("POS View: Showing payment screen")
        # Show payment screen
        self.show_payment()

    def refresh_ui_translations(self):
        """Refresh all UI translations when language changes."""
        print("=== POS VIEW: refresh_ui_translations CALLED ===")
        print(f"Current language: {self.translation_manager.current_language}")

        # Update tab buttons
        if hasattr(self, 'sales_tab_button') and self.sales_tab_button:
            self.sales_tab_button.setText(tr("pos.sales_screen", "Sales"))
            print("Updated Sales tab button")

        if hasattr(self, 'inventory_tab_button') and self.inventory_tab_button:
            self.inventory_tab_button.setText(tr("pos.inventory_screen", "Inventory"))
            print("Updated Inventory tab button")

        # Update other UI elements
        if hasattr(self, 'logout_button') and self.logout_button:
            self.logout_button.setText(tr("pos.logout_button", "Logout"))

        # Update cart table headers
        if hasattr(self, 'cart_table') and self.cart_table:
            self.cart_table.setHorizontalHeaderLabels([
                tr("pos.product", "Product"),
                tr("pos.price", "Price"),
                tr("pos.quantity", "Quantity"),
                tr("pos.discount", "Discount"),
                tr("pos.total", "Total"),
                tr("pos.actions", "Actions")
            ])
            print("Updated cart table headers")

        print("=== POS VIEW: translations refreshed successfully ===")
