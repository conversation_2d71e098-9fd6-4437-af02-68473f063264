#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
فوترها - Fawterha
Invoice Management Application for Small Businesses
Copyright © Hadou Design

This is the main entry point for the application with enhanced UI.
"""

import sys
import os
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import QTranslator, QLocale, QLibraryInfo
from PySide6.QtGui import QPixmap
from ui.main_window import MainWindow
from database.db_manager import DatabaseManager
from database.currency_manager import CurrencyManager


# Override QMessageBox to improve contrast
class EnhancedMessageBox(QMessageBox):
    """Enhanced message box with better contrast."""

    @staticmethod
    def information(parent, title, text):
        """Show an information message box with better contrast."""
        msg_box = QMessageBox(parent)
        msg_box.setObjectName("infoDialog")
        msg_box.setWindowTitle(title)
        msg_box.setIcon(QMessageBox.Information)

        # Format message with better contrast
        formatted_message = f"<p style='color: #212121; font-size: 14pt;'>{text}</p>"
        msg_box.setText(formatted_message)

        # Set button styling
        ok_button = msg_box.addButton(QMessageBox.Ok)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #1e88e5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)

        # Set minimum width for better readability
        msg_box.setMinimumWidth(400)

        return msg_box.exec()

    @staticmethod
    def warning(parent, title, text):
        """Show a warning message box with better contrast."""
        msg_box = QMessageBox(parent)
        msg_box.setObjectName("warningDialog")
        msg_box.setWindowTitle(title)
        msg_box.setIcon(QMessageBox.Warning)

        # Format message with better contrast
        formatted_message = f"<p style='color: #212121; font-size: 14pt; font-weight: bold;'>{text}</p>"
        msg_box.setText(formatted_message)

        # Set button styling
        ok_button = msg_box.addButton(QMessageBox.Ok)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #e65100;
            }
        """)

        # Set minimum width for better readability
        msg_box.setMinimumWidth(400)

        return msg_box.exec()

    @staticmethod
    def critical(parent, title, text):
        """Show a critical error message box with better contrast."""
        msg_box = QMessageBox(parent)
        msg_box.setObjectName("errorDialog")
        msg_box.setWindowTitle(title)
        msg_box.setIcon(QMessageBox.Critical)

        # Format message with better contrast
        formatted_message = f"<p style='color: #b71c1c; font-size: 14pt; font-weight: bold;'>{text}</p>"
        msg_box.setText(formatted_message)

        # Set button styling
        ok_button = msg_box.addButton(QMessageBox.Ok)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)

        # Set minimum width for better readability
        msg_box.setMinimumWidth(400)

        return msg_box.exec()

    @staticmethod
    def about(parent, title, text):
        """Show an about dialog with better contrast."""
        msg_box = QMessageBox(parent)
        msg_box.setObjectName("aboutDialog")
        msg_box.setWindowTitle(title)

        # Set icon
        msg_box.setIconPixmap(QPixmap("resources/icons/logo.png").scaled(64, 64) if os.path.exists("resources/icons/logo.png") else QPixmap())

        # Format message with better contrast
        formatted_message = f"""
        <div style="text-align: center;">
            <h1 style="color: #0d47a1; font-size: 24px; margin-bottom: 10px;">فوترها - Fawterha</h1>
            <p style="color: #212121; font-size: 16px; margin: 5px 0;">الإصدار 1.0</p>
            <p style="color: #212121; font-size: 16px; margin: 5px 0;">تطبيق إدارة الفواتير للمشاريع الصغيرة والشركات</p>
            <p style="color: #212121; font-size: 16px; margin: 5px 0;">حقوق الملكية © Hadou Design</p>
        </div>
        """
        msg_box.setText(formatted_message)

        # Set button styling
        ok_button = msg_box.addButton(QMessageBox.Ok)
        ok_button.setStyleSheet("""
            QPushButton {
                background-color: #1e88e5;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)

        # Set minimum width for better readability
        msg_box.setMinimumWidth(400)

        return msg_box.exec()


def setup_database():
    """Initialize the database if it doesn't exist."""
    # Use local database file
    db_path = "fawterha.db"

    # Initialize database manager and setup database if it doesn't exist
    db_manager = DatabaseManager(db_path)
    db_manager.setup_database()
    return db_manager


def setup_translation():
    """Setup translation for the application."""
    translator = QTranslator()
    # Load Arabic translation files if available
    # This will be implemented later
    return translator


def main():
    """Main application entry point."""
    # Create the application
    app = QApplication(sys.argv)
    app.setApplicationName("فوترها")
    app.setOrganizationName("Hadou Design")

    # Override QMessageBox with our enhanced version
    QMessageBox = EnhancedMessageBox

    # Setup database
    db_manager = setup_database()

    # Setup currency manager
    currency_manager = CurrencyManager(db_manager)

    # Setup translation
    translator = setup_translation()
    app.installTranslator(translator)

    # Load stylesheet
    style_file = os.path.join(os.path.dirname(__file__), "resources", "styles.qss")
    if os.path.exists(style_file):
        with open(style_file, "r") as f:
            app.setStyleSheet(f.read())

    # Create and show the main window
    main_window = MainWindow(db_manager, currency_manager)
    main_window.show()

    # Start the event loop
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
