#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Accounting Period Model for فوترها (Fawterha)
Represents an accounting period in the accounting system
"""

from datetime import datetime


class AccountingPeriod:
    """Accounting period model class."""

    def __init__(self, id=None, name="", start_date=None, end_date=None, is_closed=False,
                 closed_date=None, created_at=None, updated_at=None):
        """Initialize an accounting period.

        Args:
            id (int, optional): Accounting period ID
            name (str): Period name
            start_date (datetime): Start date
            end_date (datetime): End date
            is_closed (bool): Whether the period is closed
            closed_date (datetime, optional): Date when the period was closed
            created_at (datetime, optional): Creation timestamp
            updated_at (datetime, optional): Last update timestamp
        """
        self.id = id
        self.name = name
        self.start_date = start_date
        self.end_date = end_date
        self.is_closed = is_closed
        self.closed_date = closed_date
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create an AccountingPeriod object from a database row.

        Args:
            row (tuple or dict): Database row containing accounting period data

        Returns:
            AccountingPeriod: AccountingPeriod object
        """
        # Handle both tuple and dictionary formats
        if isinstance(row, dict):
            return cls(
                id=row.get('id'),
                name=row.get('name', ''),
                start_date=row.get('start_date'),
                end_date=row.get('end_date'),
                is_closed=bool(row.get('is_closed', False)),
                closed_date=row.get('closed_date'),
                created_at=row.get('created_at'),
                updated_at=row.get('updated_at')
            )
        else:
            # Handle tuple format (indexed access)
            try:
                return cls(
                    id=row[0],
                    name=row[1],
                    start_date=row[2],
                    end_date=row[3],
                    is_closed=bool(row[4]),
                    closed_date=row[5],
                    created_at=row[6],
                    updated_at=row[7]
                )
            except (IndexError, TypeError) as e:
                print(f"Error creating AccountingPeriod from row: {e}")
                print(f"Row data: {row}")
                # Return a default accounting period object
                return cls()

    def to_dict(self):
        """Convert the accounting period to a dictionary.

        Returns:
            dict: Dictionary representation of the accounting period
        """
        return {
            'id': self.id,
            'name': self.name,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'is_closed': self.is_closed,
            'closed_date': self.closed_date,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def __str__(self):
        """Return a string representation of the accounting period.

        Returns:
            str: String representation
        """
        return f"{self.name} ({self.start_date} - {self.end_date})"

    def is_date_in_period(self, date):
        """Check if a date is within this accounting period.

        Args:
            date (datetime): Date to check

        Returns:
            bool: True if the date is within the period, False otherwise
        """
        if not date or not self.start_date or not self.end_date:
            return False

        return self.start_date <= date <= self.end_date
