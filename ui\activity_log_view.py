#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Activity Log View for فوترها (Fawterha)
Provides a view for viewing and filtering activity logs
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QLineEdit, QMessageBox, QDialog, QFormLayout, QDialogButtonBox,
    QCheckBox, QGroupBox, QDateEdit, QSpinBox, QTextEdit
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QIcon, QColor

from database.activity_log_manager import ActivityLogManager, get_activity_log_manager
from models.activity_log import ActivityLog
from utils.translation_manager import tr
import json


class ActivityLogDetailsDialog(QDialog):
    """Dialog for viewing activity log details."""

    def __init__(self, activity_log, parent=None):
        """Initialize the activity log details dialog.

        Args:
            activity_log (ActivityLog): Activity log to view
            parent: Parent widget
        """
        super().__init__(parent)

        self.activity_log = activity_log
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        self.setWindowTitle(tr("activity_log.details", "تفاصيل النشاط"))
        self.setMinimumWidth(500)
        self.setMinimumHeight(400)
        self.setWindowIcon(QIcon("resources/icons/activity.png"))
        self.setLayoutDirection(Qt.RightToLeft)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Form layout
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(10)

        # ID
        self.id_label = QLabel()
        form_layout.addRow(tr("activity_log.id", "رقم:"), self.id_label)

        # User
        self.user_label = QLabel()
        form_layout.addRow(tr("activity_log.user", "المستخدم:"), self.user_label)

        # Activity Type
        self.activity_type_label = QLabel()
        form_layout.addRow(tr("activity_log.activity_type", "نوع النشاط:"), self.activity_type_label)

        # Entity Type
        self.entity_type_label = QLabel()
        form_layout.addRow(tr("activity_log.entity_type", "نوع الكيان:"), self.entity_type_label)

        # Entity ID
        self.entity_id_label = QLabel()
        form_layout.addRow(tr("activity_log.entity_id", "رقم الكيان:"), self.entity_id_label)

        # Description
        self.description_label = QLabel()
        self.description_label.setWordWrap(True)
        form_layout.addRow(tr("activity_log.description", "الوصف:"), self.description_label)

        # IP Address
        self.ip_address_label = QLabel()
        form_layout.addRow(tr("activity_log.ip_address", "عنوان IP:"), self.ip_address_label)

        # Created At
        self.created_at_label = QLabel()
        form_layout.addRow(tr("activity_log.created_at", "تاريخ النشاط:"), self.created_at_label)

        main_layout.addLayout(form_layout)

        # Details
        details_group = QGroupBox(tr("activity_log.details", "التفاصيل"))
        details_layout = QVBoxLayout(details_group)
        
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        details_layout.addWidget(self.details_text)
        
        main_layout.addWidget(details_group)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        main_layout.addWidget(button_box)

    def load_data(self):
        """Load activity log data."""
        if not self.activity_log:
            return
            
        self.id_label.setText(str(self.activity_log.id))
        self.user_label.setText(f"{self.activity_log.username} (ID: {self.activity_log.user_id})" if self.activity_log.username else "-")
        
        # Activity Type
        activity_type_text = self.activity_log.activity_type
        if self.activity_log.activity_type == ActivityLog.TYPE_LOGIN:
            activity_type_text = tr("activity_log.type_login", "تسجيل دخول")
        elif self.activity_log.activity_type == ActivityLog.TYPE_LOGOUT:
            activity_type_text = tr("activity_log.type_logout", "تسجيل خروج")
        elif self.activity_log.activity_type == ActivityLog.TYPE_CREATE:
            activity_type_text = tr("activity_log.type_create", "إنشاء")
        elif self.activity_log.activity_type == ActivityLog.TYPE_UPDATE:
            activity_type_text = tr("activity_log.type_update", "تحديث")
        elif self.activity_log.activity_type == ActivityLog.TYPE_DELETE:
            activity_type_text = tr("activity_log.type_delete", "حذف")
        elif self.activity_log.activity_type == ActivityLog.TYPE_VIEW:
            activity_type_text = tr("activity_log.type_view", "عرض")
        elif self.activity_log.activity_type == ActivityLog.TYPE_EXPORT:
            activity_type_text = tr("activity_log.type_export", "تصدير")
        elif self.activity_log.activity_type == ActivityLog.TYPE_IMPORT:
            activity_type_text = tr("activity_log.type_import", "استيراد")
        elif self.activity_log.activity_type == ActivityLog.TYPE_PRINT:
            activity_type_text = tr("activity_log.type_print", "طباعة")
        
        self.activity_type_label.setText(activity_type_text)
        
        # Entity Type
        entity_type_text = self.activity_log.entity_type
        if self.activity_log.entity_type == ActivityLog.ENTITY_USER:
            entity_type_text = tr("activity_log.entity_user", "مستخدم")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_PRODUCT:
            entity_type_text = tr("activity_log.entity_product", "منتج")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_CUSTOMER:
            entity_type_text = tr("activity_log.entity_customer", "عميل")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_INVOICE:
            entity_type_text = tr("activity_log.entity_invoice", "فاتورة")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_TRANSACTION:
            entity_type_text = tr("activity_log.entity_transaction", "معاملة")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_INVENTORY:
            entity_type_text = tr("activity_log.entity_inventory", "مخزون")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_POS_SESSION:
            entity_type_text = tr("activity_log.entity_pos_session", "جلسة نقاط البيع")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_ACCOUNT:
            entity_type_text = tr("activity_log.entity_account", "حساب")
        elif self.activity_log.entity_type == ActivityLog.ENTITY_SYSTEM:
            entity_type_text = tr("activity_log.entity_system", "النظام")
            
        self.entity_type_label.setText(entity_type_text)
        
        self.entity_id_label.setText(str(self.activity_log.entity_id) if self.activity_log.entity_id else "-")
        self.description_label.setText(self.activity_log.description or "-")
        self.ip_address_label.setText(self.activity_log.ip_address or "-")
        self.created_at_label.setText(str(self.activity_log.created_at))
        
        # Format details as JSON if possible
        if self.activity_log.details:
            try:
                details_dict = json.loads(self.activity_log.details)
                self.details_text.setText(json.dumps(details_dict, indent=4, ensure_ascii=False))
            except:
                self.details_text.setText(self.activity_log.details)
        else:
            self.details_text.setText("-")


class ActivityLogView(QWidget):
    """Widget for viewing activity logs."""

    def __init__(self, db_manager, parent=None):
        """Initialize the activity log view.

        Args:
            db_manager: Database manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.activity_log_manager = get_activity_log_manager(db_manager)
        self.logs = []
        self.current_page = 0
        self.page_size = 50

        self.setup_ui()
        self.load_logs()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Title
        title_label = QLabel(tr("activity_log.title", "سجل الأنشطة"))
        title_label.setStyleSheet("font-size: 24pt; font-weight: bold; color: #0d47a1;")
        title_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(title_label)

        # Description
        description_label = QLabel(tr("activity_log.description", "عرض وتصفية سجل أنشطة المستخدمين في النظام"))
        description_label.setStyleSheet("font-size: 12pt; color: #555;")
        description_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(description_label)

        # Filters
        filters_group = QGroupBox(tr("activity_log.filters", "تصفية"))
        filters_layout = QHBoxLayout(filters_group)
        
        # Activity Type filter
        self.activity_type_combo = QComboBox()
        self.activity_type_combo.addItem(tr("activity_log.all_types", "جميع الأنواع"), None)
        self.activity_type_combo.addItem(tr("activity_log.type_login", "تسجيل دخول"), ActivityLog.TYPE_LOGIN)
        self.activity_type_combo.addItem(tr("activity_log.type_logout", "تسجيل خروج"), ActivityLog.TYPE_LOGOUT)
        self.activity_type_combo.addItem(tr("activity_log.type_create", "إنشاء"), ActivityLog.TYPE_CREATE)
        self.activity_type_combo.addItem(tr("activity_log.type_update", "تحديث"), ActivityLog.TYPE_UPDATE)
        self.activity_type_combo.addItem(tr("activity_log.type_delete", "حذف"), ActivityLog.TYPE_DELETE)
        self.activity_type_combo.addItem(tr("activity_log.type_view", "عرض"), ActivityLog.TYPE_VIEW)
        self.activity_type_combo.addItem(tr("activity_log.type_export", "تصدير"), ActivityLog.TYPE_EXPORT)
        self.activity_type_combo.addItem(tr("activity_log.type_import", "استيراد"), ActivityLog.TYPE_IMPORT)
        self.activity_type_combo.addItem(tr("activity_log.type_print", "طباعة"), ActivityLog.TYPE_PRINT)
        self.activity_type_combo.currentIndexChanged.connect(self.load_logs)
        filters_layout.addWidget(QLabel(tr("activity_log.activity_type", "نوع النشاط:")))
        filters_layout.addWidget(self.activity_type_combo)
        
        # Entity Type filter
        self.entity_type_combo = QComboBox()
        self.entity_type_combo.addItem(tr("activity_log.all_entities", "جميع الكيانات"), None)
        self.entity_type_combo.addItem(tr("activity_log.entity_user", "مستخدم"), ActivityLog.ENTITY_USER)
        self.entity_type_combo.addItem(tr("activity_log.entity_product", "منتج"), ActivityLog.ENTITY_PRODUCT)
        self.entity_type_combo.addItem(tr("activity_log.entity_customer", "عميل"), ActivityLog.ENTITY_CUSTOMER)
        self.entity_type_combo.addItem(tr("activity_log.entity_invoice", "فاتورة"), ActivityLog.ENTITY_INVOICE)
        self.entity_type_combo.addItem(tr("activity_log.entity_transaction", "معاملة"), ActivityLog.ENTITY_TRANSACTION)
        self.entity_type_combo.addItem(tr("activity_log.entity_inventory", "مخزون"), ActivityLog.ENTITY_INVENTORY)
        self.entity_type_combo.addItem(tr("activity_log.entity_pos_session", "جلسة نقاط البيع"), ActivityLog.ENTITY_POS_SESSION)
        self.entity_type_combo.addItem(tr("activity_log.entity_account", "حساب"), ActivityLog.ENTITY_ACCOUNT)
        self.entity_type_combo.addItem(tr("activity_log.entity_system", "النظام"), ActivityLog.ENTITY_SYSTEM)
        self.entity_type_combo.currentIndexChanged.connect(self.load_logs)
        filters_layout.addWidget(QLabel(tr("activity_log.entity_type", "نوع الكيان:")))
        filters_layout.addWidget(self.entity_type_combo)
        
        # Date range
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.dateChanged.connect(self.load_logs)
        filters_layout.addWidget(QLabel(tr("activity_log.start_date", "من تاريخ:")))
        filters_layout.addWidget(self.start_date)
        
        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        self.end_date.dateChanged.connect(self.load_logs)
        filters_layout.addWidget(QLabel(tr("activity_log.end_date", "إلى تاريخ:")))
        filters_layout.addWidget(self.end_date)
        
        # Search
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("activity_log.search", "بحث..."))
        self.search_edit.textChanged.connect(self.filter_logs)
        filters_layout.addWidget(self.search_edit)
        
        # Refresh button
        self.refresh_button = QPushButton(tr("common.refresh", "تحديث"))
        self.refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        self.refresh_button.clicked.connect(self.load_logs)
        filters_layout.addWidget(self.refresh_button)
        
        main_layout.addWidget(filters_group)

        # Logs table
        self.logs_table = QTableWidget()
        self.logs_table.setColumnCount(7)
        self.logs_table.setHorizontalHeaderLabels([
            tr("activity_log.id", "رقم"),
            tr("activity_log.user", "المستخدم"),
            tr("activity_log.activity_type", "نوع النشاط"),
            tr("activity_log.entity_type", "نوع الكيان"),
            tr("activity_log.description", "الوصف"),
            tr("activity_log.created_at", "التاريخ"),
            tr("activity_log.actions", "الإجراءات")
        ])
        self.logs_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.logs_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.logs_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.logs_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.logs_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.logs_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.logs_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)
        self.logs_table.verticalHeader().setVisible(False)
        self.logs_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.logs_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.logs_table.setAlternatingRowColors(True)
        main_layout.addWidget(self.logs_table)
        
        # Pagination
        pagination_layout = QHBoxLayout()
        
        self.prev_button = QPushButton(tr("common.previous", "السابق"))
        self.prev_button.clicked.connect(self.previous_page)
        pagination_layout.addWidget(self.prev_button)
        
        self.page_label = QLabel()
        pagination_layout.addWidget(self.page_label)
        
        self.next_button = QPushButton(tr("common.next", "التالي"))
        self.next_button.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_button)
        
        main_layout.addLayout(pagination_layout)

    def load_logs(self):
        """Load logs from the database."""
        # Get filter values
        activity_type = self.activity_type_combo.currentData()
        entity_type = self.entity_type_combo.currentData()
        start_date = self.start_date.date().toString("yyyy-MM-dd")
        end_date = self.end_date.date().toString("yyyy-MM-dd")
        
        # Get logs
        self.logs = self.activity_log_manager.get_logs(
            limit=self.page_size,
            offset=self.current_page * self.page_size,
            activity_type=activity_type,
            entity_type=entity_type,
            start_date=start_date,
            end_date=end_date
        )
        
        # Display logs
        self.display_logs()
        
        # Update pagination
        self.update_pagination()

    def display_logs(self):
        """Display logs in the table."""
        # Clear table
        self.logs_table.setRowCount(0)
        
        # Add logs to table
        for log in self.logs:
            row = self.logs_table.rowCount()
            self.logs_table.insertRow(row)
            
            # ID
            id_item = QTableWidgetItem(str(log.id))
            id_item.setData(Qt.UserRole, log.id)
            self.logs_table.setItem(row, 0, id_item)
            
            # User
            user_item = QTableWidgetItem(log.username or "-")
            self.logs_table.setItem(row, 1, user_item)
            
            # Activity Type
            activity_type_text = log.activity_type
            if log.activity_type == ActivityLog.TYPE_LOGIN:
                activity_type_text = tr("activity_log.type_login", "تسجيل دخول")
            elif log.activity_type == ActivityLog.TYPE_LOGOUT:
                activity_type_text = tr("activity_log.type_logout", "تسجيل خروج")
            elif log.activity_type == ActivityLog.TYPE_CREATE:
                activity_type_text = tr("activity_log.type_create", "إنشاء")
            elif log.activity_type == ActivityLog.TYPE_UPDATE:
                activity_type_text = tr("activity_log.type_update", "تحديث")
            elif log.activity_type == ActivityLog.TYPE_DELETE:
                activity_type_text = tr("activity_log.type_delete", "حذف")
            elif log.activity_type == ActivityLog.TYPE_VIEW:
                activity_type_text = tr("activity_log.type_view", "عرض")
            elif log.activity_type == ActivityLog.TYPE_EXPORT:
                activity_type_text = tr("activity_log.type_export", "تصدير")
            elif log.activity_type == ActivityLog.TYPE_IMPORT:
                activity_type_text = tr("activity_log.type_import", "استيراد")
            elif log.activity_type == ActivityLog.TYPE_PRINT:
                activity_type_text = tr("activity_log.type_print", "طباعة")
                
            activity_type_item = QTableWidgetItem(activity_type_text)
            self.logs_table.setItem(row, 2, activity_type_item)
            
            # Entity Type
            entity_type_text = log.entity_type
            if log.entity_type == ActivityLog.ENTITY_USER:
                entity_type_text = tr("activity_log.entity_user", "مستخدم")
            elif log.entity_type == ActivityLog.ENTITY_PRODUCT:
                entity_type_text = tr("activity_log.entity_product", "منتج")
            elif log.entity_type == ActivityLog.ENTITY_CUSTOMER:
                entity_type_text = tr("activity_log.entity_customer", "عميل")
            elif log.entity_type == ActivityLog.ENTITY_INVOICE:
                entity_type_text = tr("activity_log.entity_invoice", "فاتورة")
            elif log.entity_type == ActivityLog.ENTITY_TRANSACTION:
                entity_type_text = tr("activity_log.entity_transaction", "معاملة")
            elif log.entity_type == ActivityLog.ENTITY_INVENTORY:
                entity_type_text = tr("activity_log.entity_inventory", "مخزون")
            elif log.entity_type == ActivityLog.ENTITY_POS_SESSION:
                entity_type_text = tr("activity_log.entity_pos_session", "جلسة نقاط البيع")
            elif log.entity_type == ActivityLog.ENTITY_ACCOUNT:
                entity_type_text = tr("activity_log.entity_account", "حساب")
            elif log.entity_type == ActivityLog.ENTITY_SYSTEM:
                entity_type_text = tr("activity_log.entity_system", "النظام")
                
            entity_type_item = QTableWidgetItem(entity_type_text)
            self.logs_table.setItem(row, 3, entity_type_item)
            
            # Description
            description_item = QTableWidgetItem(log.description or "-")
            self.logs_table.setItem(row, 4, description_item)
            
            # Created At
            created_at_item = QTableWidgetItem(str(log.created_at))
            self.logs_table.setItem(row, 5, created_at_item)
            
            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)
            
            # View details button
            view_button = QPushButton(tr("common.view", "عرض"))
            view_button.setProperty("log_id", log.id)
            view_button.clicked.connect(self.view_log_details)
            actions_layout.addWidget(view_button)
            
            self.logs_table.setCellWidget(row, 6, actions_widget)

    def filter_logs(self):
        """Filter logs based on search text."""
        search_text = self.search_edit.text().strip().lower()
        
        # If search text is empty, show all logs
        if not search_text:
            for row in range(self.logs_table.rowCount()):
                self.logs_table.setRowHidden(row, False)
            return
            
        # Hide rows that don't match search text
        for row in range(self.logs_table.rowCount()):
            username = self.logs_table.item(row, 1).text().lower()
            activity_type = self.logs_table.item(row, 2).text().lower()
            entity_type = self.logs_table.item(row, 3).text().lower()
            description = self.logs_table.item(row, 4).text().lower()
            
            if (search_text in username or search_text in activity_type or 
                search_text in entity_type or search_text in description):
                self.logs_table.setRowHidden(row, False)
            else:
                self.logs_table.setRowHidden(row, True)

    def view_log_details(self):
        """View log details."""
        button = self.sender()
        if not button:
            return
            
        log_id = button.property("log_id")
        if not log_id:
            return
            
        # Find log
        log = None
        for l in self.logs:
            if l.id == log_id:
                log = l
                break
                
        if not log:
            return
            
        # Show dialog
        dialog = ActivityLogDetailsDialog(log, self)
        dialog.exec()

    def update_pagination(self):
        """Update pagination controls."""
        # Update page label
        self.page_label.setText(tr("activity_log.page", "الصفحة {0}").format(self.current_page + 1))
        
        # Enable/disable previous button
        self.prev_button.setEnabled(self.current_page > 0)
        
        # Enable/disable next button
        self.next_button.setEnabled(len(self.logs) == self.page_size)

    def previous_page(self):
        """Go to previous page."""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_logs()

    def next_page(self):
        """Go to next page."""
        if len(self.logs) == self.page_size:
            self.current_page += 1
            self.load_logs()
