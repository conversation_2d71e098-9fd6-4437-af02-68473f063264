#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Language Manager for فوترها (Fawterha)
Manages language settings in the database
"""

import logging
from utils.translation_manager import get_translation_manager

# Set up logging
logger = logging.getLogger(__name__)

class LanguageManager:
    """Manages language settings in the database."""

    def __init__(self, db_manager):
        """Initialize the language manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager
        self._ensure_language_settings()

    def _ensure_language_settings(self):
        """Ensure language settings exist in the database."""
        try:
            # Check if language setting exists
            query = "SELECT value FROM settings WHERE key = 'language'"
            result = self.db_manager.execute_query(query)

            if not result:
                # Add language setting with default value 'ar' (Arabic)
                conn = self.db_manager.connect()
                cursor = conn.cursor()
                cursor.execute(
                    "INSERT INTO settings (key, value, created_at, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)",
                    ('language', 'ar')
                )
                conn.commit()
                self.db_manager.close()
                logger.info("Added default language setting (ar)")
        except Exception as e:
            logger.error(f"Error ensuring language settings: {str(e)}")

    def get_current_language(self):
        """Get the current language code.

        Returns:
            str: Language code (e.g., 'ar', 'en')
        """
        try:
            query = "SELECT value FROM settings WHERE key = 'language'"
            result = self.db_manager.execute_query(query)

            if result and len(result) > 0:
                return result[0]['value']

            # Default to Arabic if not found
            return 'ar'
        except Exception as e:
            logger.error(f"Error getting current language: {str(e)}")
            return 'ar'  # Default to Arabic on error

    def set_language(self, language_code, emit_signal=True):
        """Set the current language.

        Args:
            language_code (str): Language code to set
            emit_signal (bool): Whether to emit signal when language changes

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Validate language code
            translation_manager = get_translation_manager()
            available_languages = translation_manager.get_available_languages()
            valid_codes = [lang['code'] for lang in available_languages]

            if language_code not in valid_codes:
                logger.error(f"Invalid language code: {language_code}")
                return False

            # Update language setting
            conn = self.db_manager.connect()
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
                (language_code, 'language')
            )
            conn.commit()
            self.db_manager.close()

            # Update current language in translation manager without emitting signal
            if not emit_signal:
                # Just update the current language without loading the file
                translation_manager.current_language = language_code
            else:
                # Load the language in translation manager (this will emit signal)
                translation_manager.load_language(language_code)

            logger.info(f"Language set to: {language_code}")
            return True
        except Exception as e:
            logger.error(f"Error setting language: {str(e)}")
            return False

    def get_available_languages(self):
        """Get list of available languages.

        Returns:
            list: List of available languages with code and name
        """
        translation_manager = get_translation_manager()
        return translation_manager.get_available_languages()


# Global instance
_instance = None

def get_language_manager(db_manager=None):
    """Get the global LanguageManager instance.

    Args:
        db_manager: Database manager instance (only needed on first call)

    Returns:
        LanguageManager: The global instance
    """
    global _instance
    if _instance is None:
        if db_manager is None:
            raise ValueError("db_manager is required for first initialization")
        _instance = LanguageManager(db_manager)
    return _instance
