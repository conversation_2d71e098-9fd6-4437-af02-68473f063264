#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoices View for فوترها (Fawterha)
Manages invoice data display and editing
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTableWidget, QTableWidgetItem, QLineEdit, QComboBox,
    QHeaderView, QAbstractItemView, QMessageBox, QDialog,
    QDateEdit, QSpinBox, QDoubleSpinBox, QTextEdit, QFormLayout
)
from PySide6.QtCore import Qt, QDate, QSize, Signal
from PySide6.QtGui import QColor, QIcon
import os

from ui.invoice_editor import InvoiceEditorDialog
from ui.modern_invoice_editor import ModernInvoiceEditor
from ui.invoice_improved_preview import InvoiceImprovedPreviewDialog
from ui.professional_invoice_editor import ProfessionalInvoiceEditor
from ui.new_invoice_editor import NewInvoiceEditor
from models.invoice import Invoice
from models.customer import Customer
from models.invoice_item import InvoiceItem
from utils.currency_helper import get_currency_name, get_currency_symbol
from utils.translation_manager import tr


class InvoicesView(QWidget):
    """Widget for managing invoices."""

    # Signal to notify when invoices are updated
    invoice_updated = Signal()

    def __init__(self, db_manager, currency_manager=None, theme_manager=None):
        """Initialize the invoices view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
            theme_manager: Theme manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager
        self.theme_manager = theme_manager

        # Create layout
        layout = QVBoxLayout(self)

        # Create toolbar with improved spacing
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setContentsMargins(0, 0, 0, 15)  # Add bottom margin
        toolbar_layout.setSpacing(15)  # Increase spacing between elements
        layout.addLayout(toolbar_layout)

        # Add new invoice button with theme-aware styling
        self.add_button = QPushButton(tr("invoices.new_invoice", "إنشاء فاتورة جديدة"))
        # Use theme-aware styling
        self.add_button.setProperty("style", "primary")  # Set property for theme-aware styling
        self.add_button.setMinimumHeight(45)
        self.add_button.setMinimumWidth(180)
        self.add_button.setIcon(QIcon("resources/icons/add.png") if os.path.exists("resources/icons/add.png") else QIcon())
        self.add_button.setIconSize(QSize(20, 20))
        self.add_button.clicked.connect(self.create_new_invoice)
        toolbar_layout.addWidget(self.add_button)

        # Add filter by status with theme-aware styling
        status_label = QLabel(tr("common.status", "الحالة:"))
        toolbar_layout.addWidget(status_label)

        self.status_filter = QComboBox()
        self.status_filter.addItem(tr("invoices.all_invoices", "جميع الفواتير"), "all")
        self.status_filter.addItem(tr("invoices.draft", "مسودة"), "draft")
        self.status_filter.addItem(tr("invoices.pending", "منتظرة"), "pending")
        self.status_filter.addItem(tr("invoices.partially_paid", "مدفوع جزئياً"), "partially_paid")
        self.status_filter.addItem(tr("invoices.paid", "مدفوعة"), "paid")
        self.status_filter.addItem(tr("invoices.cancelled", "ملغاة"), "cancelled")
        self.status_filter.setMinimumHeight(45)
        self.status_filter.setMinimumWidth(180)
        self.status_filter.currentIndexChanged.connect(self.load_invoices)
        toolbar_layout.addWidget(self.status_filter)

        # Add search field with theme-aware styling
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("invoices.search_invoice", "بحث عن فاتورة..."))
        self.search_edit.setMinimumHeight(45)
        self.search_edit.setMinimumWidth(250)
        # Add search icon if available
        if os.path.exists("resources/icons/search.png"):
            self.search_edit.addAction(QIcon("resources/icons/search.png"), QLineEdit.LeadingPosition)
        self.search_edit.textChanged.connect(self.filter_invoices)
        toolbar_layout.addWidget(self.search_edit)

        # Create invoices table with improved styling and spacing
        self.invoices_table = QTableWidget()
        self.invoices_table.setColumnCount(11)
        self.invoices_table.setHorizontalHeaderLabels([
            tr("invoices.invoice_number", "رقم الفاتورة"),
            tr("invoices.customer", "العميل"),
            tr("common.date", "التاريخ"),
            tr("invoices.due_date", "تاريخ الاستحقاق"),
            tr("common.total", "المبلغ الإجمالي"),
            tr("invoices.paid_amount", "المدفوع"),
            tr("invoices.remaining_amount", "المتبقي"),
            tr("common.status", "الحالة"),
            tr("common.view", "عرض"),
            tr("common.print", "طباعة"),
            tr("common.delete", "حذف")
        ])
        # Set row height for better readability
        self.invoices_table.verticalHeader().setDefaultSectionSize(60)  # Increase row height
        # Set column resize modes
        for i in range(8):
            self.invoices_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Stretch)
        # Fixed width for action buttons with improved spacing
        self.invoices_table.horizontalHeader().setSectionResizeMode(8, QHeaderView.Fixed)
        self.invoices_table.horizontalHeader().setSectionResizeMode(9, QHeaderView.Fixed)
        self.invoices_table.horizontalHeader().setSectionResizeMode(10, QHeaderView.Fixed)
        self.invoices_table.setColumnWidth(8, 150)  # View button
        self.invoices_table.setColumnWidth(9, 150)  # Print button
        self.invoices_table.setColumnWidth(10, 150)  # Delete button
        self.invoices_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.invoices_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.invoices_table.doubleClicked.connect(self.view_invoice)
        self.invoices_table.setAlternatingRowColors(True)
        self.invoices_table.verticalHeader().setVisible(False)
        self.invoices_table.setShowGrid(True)
        # Use theme-aware styling for the table
        # The table will inherit styles from the global theme
        layout.addWidget(self.invoices_table)

        # Load invoices
        self.load_invoices()

    def load_invoices(self):
        """Load invoices from the database."""
        status_filter = self.status_filter.currentData()

        # First get all currencies to have exchange rates available
        currencies_query = "SELECT * FROM currencies"
        currency_rows = self.db_manager.execute_query(currencies_query)

        # Create a dictionary of currencies by ID for quick lookup
        currencies_by_id = {}
        primary_currency = None

        for crow in currency_rows:
            from models.currency import Currency
            currency = Currency.from_db_row(crow)
            currencies_by_id[currency.id] = currency
            if currency.is_primary:
                primary_currency = currency

        # If no primary currency found, create a default EGP currency
        if not primary_currency:
            from models.currency import Currency
            primary_currency = Currency(
                id=1,
                code="EGP",
                name=tr("currency.egyptian_pound", "الجنيه المصري"),
                symbol="ج.م",
                exchange_rate=1.0,
                is_primary=True,
                is_active=True
            )
            currencies_by_id[1] = primary_currency

        print(f"Primary currency: {primary_currency.code}, Symbol: {primary_currency.symbol}")
        print(f"Available currencies: {', '.join([f'{c.code}({c.id})' for c in currencies_by_id.values()])}")

        # Sort by invoice number in descending order (newest to oldest)
        # This assumes invoice numbers are in format like INV-1001, INV-1002, etc.
        # where higher numbers are newer invoices
        if status_filter == "all":
            query = """
            SELECT i.*, c.name as customer_name
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            ORDER BY i.id DESC
            """
            params = ()
        else:
            query = """
            SELECT i.*, c.name as customer_name
            FROM invoices i
            JOIN customers c ON i.customer_id = c.id
            WHERE i.status = ?
            ORDER BY i.id DESC
            """
            params = (status_filter,)

        rows = self.db_manager.execute_query(query, params)

        self.invoices_table.setRowCount(0)

        for row_dict in rows:
            # Convert sqlite3.Row to dictionary
            row = dict(row_dict)

            # Add currency information to the row
            if 'currency_id' in row and row['currency_id'] and row['currency_id'] in currencies_by_id:
                invoice_currency = currencies_by_id[row['currency_id']]
                row['currency_code'] = invoice_currency.code
                row['currency_symbol'] = invoice_currency.symbol
                row['currency_rate'] = invoice_currency.exchange_rate
                row['currency_is_primary'] = invoice_currency.is_primary

                # Convert values to primary currency (EGP)
                if not invoice_currency.is_primary:
                    # Store original values
                    row['original_total'] = row['total']
                    row['original_amount_paid'] = row['amount_paid'] if 'amount_paid' in row else 0
                    row['original_amount_due'] = row['amount_due'] if 'amount_due' in row else (row['total'] - (row['amount_paid'] if 'amount_paid' in row else 0))

                    # Convert to primary currency
                    row['total'] = invoice_currency.convert_to_primary(row['total'])
                    if 'amount_paid' in row:
                        row['amount_paid'] = invoice_currency.convert_to_primary(row['amount_paid'])
                    if 'amount_due' in row:
                        row['amount_due'] = invoice_currency.convert_to_primary(row['amount_due'])

                    print(f"Converted invoice {row['invoice_number']} from {invoice_currency.code} to {primary_currency.code}")
                    print(f"  Original: {row['original_total']} {invoice_currency.symbol}")
                    print(f"  Converted: {row['total']} {primary_currency.symbol}")
            else:
                # Use primary currency if no currency specified
                row['currency_code'] = primary_currency.code
                row['currency_symbol'] = primary_currency.symbol
                row['currency_rate'] = primary_currency.exchange_rate
                row['currency_is_primary'] = True

            # Add primary currency info to the row
            row['primary_currency_code'] = primary_currency.code
            row['primary_currency_symbol'] = primary_currency.symbol

            self.add_invoice_to_table(row)

    def add_invoice_to_table(self, row):
        """Add an invoice to the table.

        Args:
            row: Database row
        """
        row_position = self.invoices_table.rowCount()
        self.invoices_table.insertRow(row_position)

        # Set invoice data with improved formatting
        # Invoice number
        invoice_number_item = QTableWidgetItem(row['invoice_number'])
        invoice_number_item.setTextAlignment(Qt.AlignCenter)
        font = invoice_number_item.font()
        font.setBold(True)
        invoice_number_item.setFont(font)
        # Store invoice ID in the item's user role
        invoice_number_item.setData(Qt.UserRole, row['id'])
        self.invoices_table.setItem(row_position, 0, invoice_number_item)

        # Customer name
        customer_item = QTableWidgetItem(row['customer_name'])
        customer_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.invoices_table.setItem(row_position, 1, customer_item)

        # Issue date
        issue_date_item = QTableWidgetItem(row['issue_date'])
        issue_date_item.setTextAlignment(Qt.AlignCenter)
        self.invoices_table.setItem(row_position, 2, issue_date_item)

        # Due date
        due_date_item = QTableWidgetItem(row['due_date'] if row['due_date'] else "")
        due_date_item.setTextAlignment(Qt.AlignCenter)
        if row['due_date']:
            # Highlight due date if it's in the past
            from datetime import datetime
            due_date = datetime.strptime(row['due_date'], '%Y-%m-%d').date()
            today = datetime.now().date()
            if due_date < today and row['status'] != 'paid' and row['status'] != 'cancelled':
                due_date_item.setForeground(QColor(183, 28, 28))  # Dark red
                due_date_item.setFont(font)  # Bold
        self.invoices_table.setItem(row_position, 3, due_date_item)

        # Get primary currency symbol from row
        primary_currency_symbol = row['primary_currency_symbol'] if 'primary_currency_symbol' in row else "ج.م"

        # Import helper for formatting
        from utils.currency_helper import format_thousands

        # Get total from row (already converted to primary currency in load_invoices)
        total = row['total']

        # Get original values if available
        original_currency_symbol = row['currency_symbol'] if 'currency_symbol' in row else "ج.م"
        original_total = row['original_total'] if 'original_total' in row else total

        # Format with thousand separators
        formatted_total = format_thousands(f"{total:.2f}")
        total_item = QTableWidgetItem(f"{formatted_total} {primary_currency_symbol}")

        # Add tooltip showing original currency if different
        if 'original_total' in row and 'currency_is_primary' in row and not row['currency_is_primary']:
            formatted_original = format_thousands(f"{original_total:.2f}")
            total_item.setToolTip(tr("invoices.original_value", f"القيمة الأصلية: {formatted_original} {original_currency_symbol}"))

        total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        total_item.setFont(font)  # Bold
        self.invoices_table.setItem(row_position, 4, total_item)

        # Add amount paid (already converted to primary currency in load_invoices)
        amount_paid = row['amount_paid'] if 'amount_paid' in row.keys() else 0.0

        # Get original values if available
        original_amount_paid = row['original_amount_paid'] if 'original_amount_paid' in row else amount_paid

        formatted_amount_paid = format_thousands(f"{amount_paid:.2f}")
        amount_paid_item = QTableWidgetItem(f"{formatted_amount_paid} {primary_currency_symbol}")

        # Add tooltip showing original currency if different
        if 'original_amount_paid' in row and 'currency_is_primary' in row and not row['currency_is_primary'] and original_amount_paid > 0:
            formatted_original = format_thousands(f"{original_amount_paid:.2f}")
            amount_paid_item.setToolTip(tr("invoices.original_value", f"القيمة الأصلية: {formatted_original} {original_currency_symbol}"))

        amount_paid_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        if amount_paid > 0:
            amount_paid_item.setForeground(QColor(27, 94, 32))  # Dark green
            amount_paid_item.setFont(font)  # Bold
        self.invoices_table.setItem(row_position, 5, amount_paid_item)

        # Add amount due (already converted to primary currency in load_invoices)
        amount_due = row['amount_due'] if 'amount_due' in row.keys() else total - amount_paid

        # Get original values if available
        original_amount_due = row['original_amount_due'] if 'original_amount_due' in row else amount_due

        formatted_amount_due = format_thousands(f"{amount_due:.2f}")
        amount_due_item = QTableWidgetItem(f"{formatted_amount_due} {primary_currency_symbol}")

        # Add tooltip showing original currency if different
        if 'original_amount_due' in row and 'currency_is_primary' in row and not row['currency_is_primary'] and original_amount_due > 0:
            formatted_original = format_thousands(f"{original_amount_due:.2f}")
            amount_due_item.setToolTip(tr("invoices.original_value", f"القيمة الأصلية: {formatted_original} {original_currency_symbol}"))

        amount_due_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        if amount_due > 0:
            amount_due_item.setForeground(QColor(183, 28, 28))  # Dark red
            amount_due_item.setFont(font)  # Bold
        self.invoices_table.setItem(row_position, 6, amount_due_item)

        # Set status with color and styling
        status_text = self.get_status_display(row['status'])
        status_item = QTableWidgetItem(status_text)
        status_item.setBackground(self.get_status_color(row['status']))

        # Set text color based on status
        if row['status'] == 'draft':
            status_item.setForeground(QColor(66, 66, 66))  # Dark gray
        elif row['status'] == 'pending':
            status_item.setForeground(QColor(230, 81, 0))  # Dark orange
        elif row['status'] == 'paid':
            status_item.setForeground(QColor(27, 94, 32))  # Dark green
        elif row['status'] == 'cancelled':
            status_item.setForeground(QColor(183, 28, 28))  # Dark red

        # Set alignment and font
        status_item.setTextAlignment(Qt.AlignCenter)
        font = status_item.font()
        font.setBold(True)
        status_item.setFont(font)

        self.invoices_table.setItem(row_position, 7, status_item)

        # Add view button with theme-aware styling
        view_button = QPushButton(tr("common.view", "عرض"))
        view_button.setProperty("style", "primary")  # Set property for theme-aware styling
        view_button.setMinimumHeight(36)
        view_button.setMinimumWidth(110)
        view_button.setIcon(QIcon("resources/icons/view.png") if os.path.exists("resources/icons/view.png") else QIcon())
        view_button.setIconSize(QSize(16, 16))
        view_button.clicked.connect(lambda: self.view_invoice(row['id']))
        self.invoices_table.setCellWidget(row_position, 8, view_button)

        # Add print button with theme-aware styling
        print_button = QPushButton(tr("common.print", "طباعة"))
        print_button.setProperty("style", "success")  # Set property for theme-aware styling
        print_button.setMinimumHeight(36)
        print_button.setMinimumWidth(110)
        print_button.setIcon(QIcon("resources/icons/print.png") if os.path.exists("resources/icons/print.png") else QIcon())
        print_button.setIconSize(QSize(16, 16))
        print_button.clicked.connect(lambda: self.preview_invoice(row['id']))
        self.invoices_table.setCellWidget(row_position, 9, print_button)

        # Add delete button with theme-aware styling
        delete_button = QPushButton(tr("common.delete", "حذف"))
        delete_button.setProperty("style", "danger")  # Set property for theme-aware styling
        delete_button.setMinimumHeight(36)
        delete_button.setMinimumWidth(110)
        delete_button.setIcon(QIcon("resources/icons/delete.png") if os.path.exists("resources/icons/delete.png") else QIcon())
        delete_button.setIconSize(QSize(16, 16))
        delete_button.clicked.connect(lambda: self.delete_invoice(row['id']))
        self.invoices_table.setCellWidget(row_position, 10, delete_button)



    def get_status_display(self, status):
        """Get the display text for a status.

        Args:
            status (str): Status code

        Returns:
            str: Display text
        """
        status_map = {
            'draft': tr("invoices.draft", "مسودة"),
            'pending': tr("invoices.pending", "منتظرة"),
            'partially_paid': tr("invoices.partially_paid", "مدفوع جزئياً"),
            'paid': tr("invoices.paid", "مدفوعة"),
            'cancelled': tr("invoices.cancelled", "ملغاة")
        }
        return status_map.get(status, status)

    def get_status_color(self, status):
        """Get the background color for a status.

        Args:
            status (str): Status code

        Returns:
            QColor: Background color
        """
        status_colors = {
            'draft': QColor(236, 239, 241),      # Blue-gray light
            'pending': QColor(255, 248, 225),    # Amber light
            'partially_paid': QColor(255, 243, 224),  # Orange light
            'paid': QColor(232, 245, 233),       # Green light
            'cancelled': QColor(255, 235, 238)   # Red light
        }
        return status_colors.get(status, QColor(255, 255, 255))

    def create_new_invoice(self):
        """Create a new invoice."""
        # Get next invoice number from settings
        settings_query = "SELECT value FROM settings WHERE key = ?"
        prefix_row = self.db_manager.execute_query(settings_query, ("invoice_prefix",))
        number_row = self.db_manager.execute_query(settings_query, ("next_invoice_number",))

        prefix = prefix_row[0]['value'] if prefix_row else "INV-"
        next_number = number_row[0]['value'] if number_row else "1001"

        invoice_number = f"{prefix}{next_number}"

        # Create new invoice
        invoice = Invoice(invoice_number=invoice_number)

        # Show new invoice editor with theme manager
        dialog = NewInvoiceEditor(
            self,
            self.db_manager,
            invoice,
            currency_manager=self.currency_manager,
            theme_manager=self.theme_manager
        )
        if dialog.exec():
            # Reload invoices
            self.load_invoices()
            # Emit signal to notify that invoices have been updated
            self.invoice_updated.emit()

    def view_invoice(self, invoice_id):
        """View an invoice.

        Args:
            invoice_id: Invoice ID or table index if called from double-click
        """
        # If called from double-click, get invoice ID from table
        if hasattr(invoice_id, 'row'):
            row = invoice_id.row()
            invoice_id = self.invoices_table.item(row, 0).data(Qt.UserRole)

        # Get invoice from database
        query = """
        SELECT i.*, c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.id = ?
        """
        rows = self.db_manager.execute_query(query, (invoice_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("invoices.invoice_not_found", "لم يتم العثور على الفاتورة"))
            return

        # Get invoice items
        items_query = "SELECT * FROM invoice_items WHERE invoice_id = ?"
        items = self.db_manager.execute_query(items_query, (invoice_id,))

        # Create invoice object
        invoice_row = rows[0]
        invoice = Invoice.from_db_row(invoice_row)

        # Get customer
        customer_query = "SELECT * FROM customers WHERE id = ?"
        customer_rows = self.db_manager.execute_query(customer_query, (invoice.customer_id,))
        if customer_rows:
            invoice.customer = Customer.from_db_row(customer_rows[0])

        # Show new invoice editor in view mode with theme manager
        dialog = NewInvoiceEditor(
            self,
            self.db_manager,
            invoice,
            items,
            view_only=False,
            currency_manager=self.currency_manager,
            theme_manager=self.theme_manager
        )
        if dialog.exec():
            # Reload invoices
            self.load_invoices()
            # Emit signal to notify that invoices have been updated
            self.invoice_updated.emit()

    def preview_invoice(self, invoice_id):
        """Preview and print an invoice.

        Args:
            invoice_id: Invoice ID
        """
        # Get invoice from database
        query = """
        SELECT i.*, c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.id = ?
        """
        rows = self.db_manager.execute_query(query, (invoice_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("invoices.invoice_not_found", "لم يتم العثور على الفاتورة"))
            return

        # Get invoice items
        items_query = "SELECT * FROM invoice_items WHERE invoice_id = ?"
        items_rows = self.db_manager.execute_query(items_query, (invoice_id,))

        # Create invoice object
        invoice_row = rows[0]
        invoice = Invoice.from_db_row(invoice_row)

        # Get customer
        customer_query = "SELECT * FROM customers WHERE id = ?"
        customer_rows = self.db_manager.execute_query(customer_query, (invoice.customer_id,))
        if not customer_rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("customers.customer_not_found", "لم يتم العثور على بيانات العميل"))
            return

        customer = Customer.from_db_row(customer_rows[0])

        # Create invoice items
        items = []
        for item_row in items_rows:
            items.append(InvoiceItem.from_db_row(item_row))

        # Get company info
        company_info = {}
        company_info_query = "SELECT key, value FROM settings WHERE key IN ('company_name', 'company_logo', 'company_address', 'company_phone', 'company_email', 'currency')"
        company_info_rows = self.db_manager.execute_query(company_info_query)

        for row in company_info_rows:
            company_info[row['key']] = row['value']

        # Get default template ID
        template_query = "SELECT value FROM settings WHERE key = 'default_template_id'"
        template_rows = self.db_manager.execute_query(template_query)
        template_id = template_rows[0]['value'] if template_rows else None

        # Show invoice preview dialog
        preview_dialog = InvoiceImprovedPreviewDialog(self, invoice, customer, items, company_info, self.db_manager, template_id)
        preview_dialog.exec()

    def delete_invoice(self, invoice_id):
        """Delete an invoice.

        Args:
            invoice_id: Invoice ID
        """
        # Get invoice from database
        query = """
        SELECT i.*, c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.id = ?
        """
        rows = self.db_manager.execute_query(query, (invoice_id,))

        if not rows:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("invoices.invoice_not_found", "لم يتم العثور على الفاتورة"))
            return

        # Check if invoice has items linked to inventory
        items_query = """
        SELECT ii.*, p.name as product_name, p.type, p.track_inventory
        FROM invoice_items ii
        JOIN products p ON ii.product_id = p.id
        WHERE ii.invoice_id = ? AND p.type = 'product' AND p.track_inventory = 1
        """
        inventory_items = self.db_manager.execute_query(items_query, (invoice_id,))

        if inventory_items and len(inventory_items) > 0:
            # Invoice has items linked to inventory
            inventory_warning = QMessageBox.question(
                self,
                tr("messages.warning", "تحذير"),
                tr("invoices.inventory_linked", "تحتوي هذه الفاتورة على منتجات مرتبطة بالمخزون. هل تريد استرجاع المخزون وحذف الفاتورة؟"),
                QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
                QMessageBox.Cancel
            )

            if inventory_warning == QMessageBox.Cancel:
                return

            if inventory_warning == QMessageBox.No:
                QMessageBox.warning(
                    self,
                    tr("messages.error", "خطأ"),
                    tr("invoices.cannot_delete", "يتعذر حذف الفاتورة لأنها تحتوي على منتجات مرتبطة بالمخزون")
                )
                return

        # Confirm deletion
        invoice_row = rows[0]
        confirm = QMessageBox.question(
            self,
            tr("messages.confirm_delete_title", "تأكيد الحذف"),
            tr("invoices.confirm_delete_message", f"هل أنت متأكد من حذف الفاتورة رقم {invoice_row['invoice_number']}؟\n\nهذا الإجراء لا يمكن التراجع عنه."),
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # Connect to database
                conn = self.db_manager.connect()

                # Begin transaction
                conn.execute("BEGIN TRANSACTION")
                cursor = conn.cursor()

                # Restore inventory if needed
                if inventory_items and len(inventory_items) > 0 and inventory_warning == QMessageBox.Yes:
                    from database.inventory_manager import InventoryManager
                    from models.inventory_transaction import InventoryTransaction

                    inventory_manager = InventoryManager(self.db_manager)

                    for item in inventory_items:
                        # Create inventory transaction to restore stock
                        transaction = InventoryTransaction(
                            product_id=item['product_id'],
                            transaction_type=InventoryTransaction.TYPE_ADJUSTMENT,
                            quantity=item['quantity'],  # Positive to add back to inventory
                            reference_type="invoice_deleted",
                            reference_id=invoice_id,
                            notes=f"استرجاع المخزون بعد حذف الفاتورة رقم {invoice_row['invoice_number']}"
                        )

                        # Add transaction to restore stock
                        inventory_manager.add_transaction(transaction)
                        print(f"Restored inventory for product ID: {item['product_id']}, Name: {item['product_name']}, Quantity: {item['quantity']}")

                # Delete invoice items
                cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (invoice_id,))

                # Delete invoice
                cursor.execute("DELETE FROM invoices WHERE id = ?", (invoice_id,))

                # Commit transaction
                conn.commit()

                # Close connection
                self.db_manager.close()

                # Reload invoices
                self.load_invoices()

                # Emit signal to notify that invoices have been updated
                self.invoice_updated.emit()

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("invoices.invoice_deleted", "تم حذف الفاتورة بنجاح"))
            except Exception as e:
                # Rollback transaction if connection is still open
                if hasattr(self.db_manager, 'connection') and self.db_manager.connection:
                    self.db_manager.connection.rollback()
                    self.db_manager.close()
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("invoices.delete_error", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}"))

    def filter_invoices(self, text):
        """Filter invoices by search text.

        Args:
            text (str): Search text
        """
        for row in range(self.invoices_table.rowCount()):
            show_row = False

            for col in range(6):  # Check first 6 columns
                item = self.invoices_table.item(row, col)
                if item and text.lower() in item.text().lower():
                    show_row = True
                    break

            self.invoices_table.setRowHidden(row, not show_row)
