#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Account Model for فوترها (Fawterha)
Represents an account in the accounting system
"""

from datetime import datetime


class Account:
    """Account model class."""

    # Account types
    TYPE_ASSET = 'asset'
    TYPE_LIABILITY = 'liability'
    TYPE_EQUITY = 'equity'
    TYPE_REVENUE = 'revenue'
    TYPE_EXPENSE = 'expense'

    def __init__(self, id=None, name="", code="", type="", category_id=None, description="",
                 is_active=True, balance=0.0, currency_id=None, created_at=None, updated_at=None):
        """Initialize an account.

        Args:
            id (int, optional): Account ID
            name (str): Account name
            code (str): Account code
            type (str): Account type (asset, liability, equity, revenue, expense)
            category_id (int, optional): Category ID
            description (str): Account description
            is_active (bool): Whether the account is active
            balance (float): Account balance
            currency_id (int, optional): Currency ID
            created_at (datetime, optional): Creation timestamp
            updated_at (datetime, optional): Last update timestamp
        """
        self.id = id
        self.name = name
        self.code = code
        self.type = type
        self.category_id = category_id
        self.description = description
        self.is_active = is_active
        self.balance = balance
        self.currency_id = currency_id
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create an Account object from a database row.

        Args:
            row (tuple or dict): Database row containing account data

        Returns:
            Account: Account object
        """
        # Handle both tuple and dictionary formats
        if isinstance(row, dict):
            return cls(
                id=row.get('id'),
                name=row.get('name', ''),
                code=row.get('code', ''),
                type=row.get('type', ''),
                category_id=row.get('category_id'),
                description=row.get('description', ''),
                is_active=bool(row.get('is_active', True)),
                balance=float(row.get('balance', 0.0)),
                currency_id=row.get('currency_id'),
                created_at=row.get('created_at'),
                updated_at=row.get('updated_at')
            )
        else:
            # Handle tuple format (indexed access)
            try:
                return cls(
                    id=row[0],
                    name=row[1],
                    code=row[2],
                    type=row[3],
                    category_id=row[4],
                    description=row[5],
                    is_active=bool(row[6]),
                    balance=float(row[7]),
                    currency_id=row[8],
                    created_at=row[9],
                    updated_at=row[10]
                )
            except (IndexError, TypeError) as e:
                print(f"Error creating Account from row: {e}")
                print(f"Row data: {row}")
                # Return a default account object
                return cls()

    def to_dict(self):
        """Convert the account to a dictionary.

        Returns:
            dict: Dictionary representation of the account
        """
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'type': self.type,
            'category_id': self.category_id,
            'description': self.description,
            'is_active': self.is_active,
            'balance': self.balance,
            'currency_id': self.currency_id,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def __str__(self):
        """Return a string representation of the account.

        Returns:
            str: String representation
        """
        return f"{self.code} - {self.name}"

    def is_debit_account(self):
        """Check if this is a debit-normal account.

        Returns:
            bool: True if this is a debit-normal account, False otherwise
        """
        # Asset and expense accounts are debit-normal
        return self.type in [self.TYPE_ASSET, self.TYPE_EXPENSE]

    def is_credit_account(self):
        """Check if this is a credit-normal account.

        Returns:
            bool: True if this is a credit-normal account, False otherwise
        """
        # Liability, equity, and revenue accounts are credit-normal
        return self.type in [self.TYPE_LIABILITY, self.TYPE_EQUITY, self.TYPE_REVENUE]
