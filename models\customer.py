#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Customer Model for فوترها (Fawterha)
Represents a customer in the system
"""

class Customer:
    """Customer model class."""
    
    def __init__(self, id=None, name="", email="", phone="", address="", 
                 created_at=None, updated_at=None):
        """Initialize a customer object.
        
        Args:
            id (int, optional): Customer ID. Defaults to None.
            name (str, optional): Customer name. Defaults to "".
            email (str, optional): Customer email. Defaults to "".
            phone (str, optional): Customer phone. Defaults to "".
            address (str, optional): Customer address. Defaults to "".
            created_at (str, optional): Creation timestamp. Defaults to None.
            updated_at (str, optional): Update timestamp. Defaults to None.
        """
        self.id = id
        self.name = name
        self.email = email
        self.phone = phone
        self.address = address
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_db_row(cls, row):
        """Create a Customer object from a database row.
        
        Args:
            row: Database row (sqlite3.Row)
            
        Returns:
            Customer: Customer object
        """
        return cls(
            id=row['id'],
            name=row['name'],
            email=row['email'],
            phone=row['phone'],
            address=row['address'],
            created_at=row['created_at'],
            updated_at=row['updated_at']
        )
    
    def to_dict(self):
        """Convert the customer object to a dictionary.
        
        Returns:
            dict: Dictionary representation of the customer
        """
        return {
            'id': self.id,
            'name': self.name,
            'email': self.email,
            'phone': self.phone,
            'address': self.address,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def __str__(self):
        """Return a string representation of the customer.
        
        Returns:
            str: String representation
        """
        return f"{self.name} ({self.email})"
