#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Reports View for فوترها (Fawterha)
Displays reports and statistics
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QComboBox, QDateEdit, QTableWidget, QTableWidgetItem,
    QHeaderView, QAbstractItemView, QMessageBox, QFileDialog
)
from PySide6.QtCore import Qt, QDate, QSize, Signal
from PySide6.QtGui import QColor, QFont, QIcon
from datetime import datetime
import os

from utils.currency_helper import format_currency
from database.currency_manager import CurrencyManager
from utils.translation_manager import tr


class ReportsView(QWidget):
    """Widget for displaying reports and statistics."""

    # Signal to notify when currency data is updated
    currency_updated = Signal()

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the reports view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager

        # Set up currency manager
        if currency_manager:
            self.currency_manager = currency_manager
        else:
            self.currency_manager = CurrencyManager(db_manager)

        # Get primary currency
        self.primary_currency = self.currency_manager.get_primary_currency()

        # Print debug info
        if self.primary_currency:
            print(f"Reports: Primary currency initialized: {self.primary_currency.code} ({self.primary_currency.symbol})")
        else:
            print("Reports: No primary currency found")

        # Create layout with improved spacing
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)  # Add margins
        layout.setSpacing(15)  # Increase spacing between elements

        # No need for custom styling, will use theme-aware styling

        # Create filters section with theme-aware styling
        filters_frame = QWidget()
        filters_frame.setProperty("class", "container")
        filters_layout = QHBoxLayout(filters_frame)
        filters_layout.setContentsMargins(15, 15, 15, 15)
        filters_layout.setSpacing(15)
        layout.addWidget(filters_frame)

        # Report type with theme-aware styling
        report_type_label = QLabel(tr("reports.report_type", "نوع التقرير:"))
        report_type_label.setProperty("class", "label-primary")
        filters_layout.addWidget(report_type_label)

        self.report_type_combo = QComboBox()
        self.report_type_combo.addItem(tr("reports.monthly_statistics", "إحصائيات شهرية"), "monthly_sales")
        self.report_type_combo.addItem(tr("reports.yearly_sales", "المبيعات السنوية"), "yearly_sales")
        self.report_type_combo.addItem(tr("reports.customer_sales", "المبيعات حسب العميل"), "customer_sales")
        self.report_type_combo.currentIndexChanged.connect(self.load_report)
        self.report_type_combo.setMinimumWidth(180)
        filters_layout.addWidget(self.report_type_combo)

        # Add spacer
        filters_layout.addSpacing(20)

        # Date range with theme-aware styling
        start_date_label = QLabel(tr("common.from", "من:"))
        start_date_label.setProperty("class", "label-primary")
        filters_layout.addWidget(start_date_label)

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        self.start_date_edit.dateChanged.connect(self.load_report)
        self.start_date_edit.setMinimumWidth(120)
        filters_layout.addWidget(self.start_date_edit)

        end_date_label = QLabel(tr("common.to", "إلى:"))
        end_date_label.setProperty("class", "label-primary")
        filters_layout.addWidget(end_date_label)

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.dateChanged.connect(self.load_report)
        self.end_date_edit.setMinimumWidth(120)
        filters_layout.addWidget(self.end_date_edit)

        # Add spacer at the end
        filters_layout.addStretch()

        # Export buttons with theme-aware styling
        export_frame = QWidget()
        export_frame.setProperty("class", "container")
        export_layout = QHBoxLayout(export_frame)
        export_layout.setContentsMargins(15, 15, 15, 15)
        layout.addWidget(export_frame)

        export_label = QLabel(tr("reports.export_report", "تصدير التقرير:"))
        export_label.setProperty("class", "label-primary")
        export_layout.addWidget(export_label)

        self.export_pdf_button = QPushButton(tr("reports.export_pdf", "تصدير إلى PDF"))
        self.export_pdf_button.setProperty("style", "danger")  # Set property for theme-aware styling
        self.export_pdf_button.setMinimumWidth(180)
        self.export_pdf_button.setMinimumHeight(40)
        self.export_pdf_button.setIcon(QIcon("resources/icons/pdf.png") if os.path.exists("resources/icons/pdf.png") else QIcon())
        self.export_pdf_button.setIconSize(QSize(24, 24))
        self.export_pdf_button.clicked.connect(self.export_to_pdf)
        export_layout.addWidget(self.export_pdf_button)

        self.export_excel_button = QPushButton(tr("reports.export_excel", "تصدير إلى Excel"))
        self.export_excel_button.setProperty("style", "success")  # Set property for theme-aware styling
        self.export_excel_button.setMinimumWidth(180)
        self.export_excel_button.setMinimumHeight(40)
        self.export_excel_button.setIcon(QIcon("resources/icons/excel.png") if os.path.exists("resources/icons/excel.png") else QIcon())
        self.export_excel_button.setIconSize(QSize(24, 24))
        self.export_excel_button.clicked.connect(self.export_to_excel)
        export_layout.addWidget(self.export_excel_button)

        # Add reload button
        self.reload_button = QPushButton(tr("reports.reload_report", "إعادة تحميل التقرير"))
        self.reload_button.setProperty("style", "primary")  # Set property for theme-aware styling
        self.reload_button.setMinimumWidth(180)
        self.reload_button.setMinimumHeight(40)
        self.reload_button.setIcon(QIcon("resources/icons/refresh.png") if os.path.exists("resources/icons/refresh.png") else QIcon())
        self.reload_button.setIconSize(QSize(24, 24))
        self.reload_button.clicked.connect(self.refresh_reports)
        export_layout.addWidget(self.reload_button)

        # Add spacer at the end
        export_layout.addStretch()

        # Create report table with theme-aware styling
        table_frame = QWidget()
        table_frame.setProperty("class", "container")
        table_layout = QVBoxLayout(table_frame)
        table_layout.setContentsMargins(20, 20, 20, 20)  # Increase padding
        table_layout.setSpacing(15)  # Increase spacing
        layout.addWidget(table_frame)

        table_label = QLabel(tr("reports.report_results", "نتائج التقرير:"))
        table_label.setProperty("class", "label-primary")
        table_layout.addWidget(table_label)

        self.report_table = QTableWidget()
        self.report_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.report_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.report_table.setAlternatingRowColors(True)
        self.report_table.verticalHeader().setVisible(False)
        self.report_table.setShowGrid(True)
        self.report_table.setMinimumHeight(350)  # Increase height
        # Set row height for better readability
        self.report_table.verticalHeader().setDefaultSectionSize(50)  # Increase row height
        table_layout.addWidget(self.report_table)

        # Summary section with theme-aware styling
        summary_frame = QWidget()
        summary_frame.setProperty("class", "container")
        summary_layout = QVBoxLayout(summary_frame)
        summary_layout.setContentsMargins(20, 20, 20, 20)
        summary_layout.setSpacing(15)
        layout.addWidget(summary_frame)

        # Add standardized title with theme-aware styling
        summary_title = QLabel(tr("reports.totals", "الإحصائيات"))
        summary_title.setAlignment(Qt.AlignCenter)
        summary_title.setProperty("class", "heading")
        summary_layout.addWidget(summary_title)

        # Create horizontal layout for totals
        totals_layout = QHBoxLayout()
        totals_layout.setSpacing(20)
        summary_layout.addLayout(totals_layout)

        # We'll use theme-aware styling for boxes, titles, and values

        # Invoice Count Box with theme-aware styling
        invoice_count_box = QWidget()
        invoice_count_box.setProperty("class", "stat-box")
        invoice_count_layout = QVBoxLayout(invoice_count_box)
        invoice_count_layout.setContentsMargins(5, 5, 5, 5)

        invoice_count_title = QLabel(tr("reports.invoice_count", "عدد الفواتير"))
        invoice_count_title.setProperty("class", "stat-title")
        invoice_count_title.setAlignment(Qt.AlignCenter)
        invoice_count_layout.addWidget(invoice_count_title)

        self.invoice_count_label = QLabel("0")
        self.invoice_count_label.setProperty("class", "stat-value")
        self.invoice_count_label.setAlignment(Qt.AlignCenter)
        invoice_count_layout.addWidget(self.invoice_count_label)

        totals_layout.addWidget(invoice_count_box)

        # Total Sales Box with theme-aware styling
        total_sales_box = QWidget()
        total_sales_box.setProperty("class", "stat-box")
        total_sales_layout = QVBoxLayout(total_sales_box)
        total_sales_layout.setContentsMargins(5, 5, 5, 5)

        total_sales_title = QLabel(tr("reports.total_sales", "إجمالي المبيعات"))
        total_sales_title.setProperty("class", "stat-title")
        total_sales_title.setAlignment(Qt.AlignCenter)
        total_sales_layout.addWidget(total_sales_title)

        self.total_sales_label = QLabel("0.00 ج.م")
        self.total_sales_label.setProperty("class", "stat-value")
        self.total_sales_label.setProperty("type", "success")
        self.total_sales_label.setAlignment(Qt.AlignCenter)
        total_sales_layout.addWidget(self.total_sales_label)

        totals_layout.addWidget(total_sales_box)

        # Create second row of totals
        totals_row2_layout = QHBoxLayout()
        totals_row2_layout.setSpacing(20)
        summary_layout.addLayout(totals_row2_layout)

        # Total Paid Box with theme-aware styling
        total_paid_box = QWidget()
        total_paid_box.setProperty("class", "stat-box")
        total_paid_layout = QVBoxLayout(total_paid_box)
        total_paid_layout.setContentsMargins(5, 5, 5, 5)

        total_paid_title = QLabel(tr("reports.total_paid", "إجمالي المدفوع"))
        total_paid_title.setProperty("class", "stat-title")
        total_paid_title.setAlignment(Qt.AlignCenter)
        total_paid_layout.addWidget(total_paid_title)

        self.total_paid_label = QLabel("0.00 ج.م")
        self.total_paid_label.setProperty("class", "stat-value")
        self.total_paid_label.setProperty("type", "success")
        self.total_paid_label.setAlignment(Qt.AlignCenter)
        total_paid_layout.addWidget(self.total_paid_label)

        totals_row2_layout.addWidget(total_paid_box)

        # Fully Paid Invoices Box with theme-aware styling
        fully_paid_invoices_box = QWidget()
        fully_paid_invoices_box.setProperty("class", "stat-box")
        fully_paid_invoices_layout = QVBoxLayout(fully_paid_invoices_box)
        fully_paid_invoices_layout.setContentsMargins(5, 5, 5, 5)

        fully_paid_invoices_title = QLabel(tr("reports.fully_paid_invoices", "الفواتير المدفوعة بالكامل"))
        fully_paid_invoices_title.setProperty("class", "stat-title")
        fully_paid_invoices_title.setAlignment(Qt.AlignCenter)
        fully_paid_invoices_layout.addWidget(fully_paid_invoices_title)

        self.fully_paid_invoices_label = QLabel("0")
        self.fully_paid_invoices_label.setProperty("class", "stat-value")
        self.fully_paid_invoices_label.setProperty("type", "success")
        self.fully_paid_invoices_label.setAlignment(Qt.AlignCenter)
        fully_paid_invoices_layout.addWidget(self.fully_paid_invoices_label)

        totals_row2_layout.addWidget(fully_paid_invoices_box)

        # Total Remaining Box with theme-aware styling
        total_remaining_box = QWidget()
        total_remaining_box.setProperty("class", "stat-box")
        total_remaining_layout = QVBoxLayout(total_remaining_box)
        total_remaining_layout.setContentsMargins(5, 5, 5, 5)

        total_remaining_title = QLabel(tr("reports.total_remaining", "إجمالي المتبقي"))
        total_remaining_title.setProperty("class", "stat-title")
        total_remaining_title.setAlignment(Qt.AlignCenter)
        total_remaining_layout.addWidget(total_remaining_title)

        self.total_remaining_label = QLabel("0.00 ج.م")
        self.total_remaining_label.setProperty("class", "stat-value")
        self.total_remaining_label.setProperty("type", "danger")
        self.total_remaining_label.setAlignment(Qt.AlignCenter)
        total_remaining_layout.addWidget(self.total_remaining_label)

        totals_row2_layout.addWidget(total_remaining_box)

        # Cancelled Invoices Box with theme-aware styling
        cancelled_invoices_box = QWidget()
        cancelled_invoices_box.setProperty("class", "stat-box")
        cancelled_invoices_layout = QVBoxLayout(cancelled_invoices_box)
        cancelled_invoices_layout.setContentsMargins(5, 5, 5, 5)

        cancelled_invoices_title = QLabel(tr("reports.cancelled_invoices", "الفواتير الملغاة"))
        cancelled_invoices_title.setProperty("class", "stat-title")
        cancelled_invoices_title.setAlignment(Qt.AlignCenter)
        cancelled_invoices_layout.addWidget(cancelled_invoices_title)

        self.cancelled_invoices_label = QLabel("0")
        self.cancelled_invoices_label.setProperty("class", "stat-value")
        self.cancelled_invoices_label.setProperty("type", "danger")
        self.cancelled_invoices_label.setAlignment(Qt.AlignCenter)
        cancelled_invoices_layout.addWidget(self.cancelled_invoices_label)

        totals_row2_layout.addWidget(cancelled_invoices_box)

        # Unpaid Invoices Box with theme-aware styling
        unpaid_invoices_box = QWidget()
        unpaid_invoices_box.setProperty("class", "stat-box")
        unpaid_invoices_layout = QVBoxLayout(unpaid_invoices_box)
        unpaid_invoices_layout.setContentsMargins(5, 5, 5, 5)

        unpaid_invoices_title = QLabel(tr("reports.total_invoices_with_balance", "الفواتير المتبقية برصيد"))
        unpaid_invoices_title.setProperty("class", "stat-title")
        unpaid_invoices_title.setAlignment(Qt.AlignCenter)
        unpaid_invoices_layout.addWidget(unpaid_invoices_title)

        self.unpaid_invoices_label = QLabel("0")
        self.unpaid_invoices_label.setProperty("class", "stat-value")
        self.unpaid_invoices_label.setProperty("type", "warning")
        self.unpaid_invoices_label.setAlignment(Qt.AlignCenter)
        unpaid_invoices_layout.addWidget(self.unpaid_invoices_label)

        totals_row2_layout.addWidget(unpaid_invoices_box)

        # Load initial report
        self.load_report()

    def refresh_reports(self):
        """Refresh the current report."""
        # Store old currency code for comparison
        old_currency_code = self.primary_currency.code if self.primary_currency else None

        # Refresh primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()
            if self.primary_currency:
                print(f"Reports: Refreshed primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")

                # Check if currency has changed
                if old_currency_code != self.primary_currency.code:
                    print(f"Reports: Currency changed from {old_currency_code} to {self.primary_currency.code}")
                    # Emit signal to notify other components
                    self.currency_updated.emit()
            else:
                print("Reports: No primary currency found after refresh")

        # Update currency labels
        self.update_currency_labels()

        # Reload report with fresh data
        self.load_report()

    def update_currency_labels(self):
        """Update all currency labels to use the primary currency symbol."""
        if not hasattr(self, 'primary_currency') or not self.primary_currency:
            return

        # Update currency symbols in total labels
        currency_symbol = self.primary_currency.symbol

        # Replace currency symbols with the current currency symbol in all labels
        for label in [self.total_sales_label, self.total_paid_label, self.total_remaining_label]:
            current_text = label.text()
            if "ج.م" in current_text:
                new_text = current_text.replace("ج.م", currency_symbol)
                label.setText(new_text)
            elif "ر.س" in current_text:
                new_text = current_text.replace("ر.س", currency_symbol)
                label.setText(new_text)
            elif "$" in current_text:
                new_text = current_text.replace("$", currency_symbol)
                label.setText(new_text)
            elif "€" in current_text:
                new_text = current_text.replace("€", currency_symbol)
                label.setText(new_text)

    def load_report(self):
        """Load the selected report."""
        # Refresh primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()
            if self.primary_currency:
                print(f"Reports: Refreshed primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
            else:
                print("Reports: No primary currency found after refresh")

        report_type = self.report_type_combo.currentData()
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        if report_type == "monthly_sales":
            self.load_monthly_sales_report(start_date, end_date)
        elif report_type == "yearly_sales":
            self.load_yearly_sales_report(start_date, end_date)
        elif report_type == "customer_sales":
            self.load_customer_sales_report(start_date, end_date)

    def load_monthly_sales_report(self, start_date, end_date):
        """Load monthly sales report.

        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
        """
        # Make sure we have the latest primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()
            if self.primary_currency:
                print(f"Monthly Report: Using primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
            else:
                print("Monthly Report: No primary currency found")
                return
        else:
            print("Monthly Report: No currency manager available")
            return

        # We need to get all invoices and manually group them by month
        # This allows us to properly convert currencies before grouping
        all_invoices_query = """
        SELECT
            id,
            invoice_number,
            total,
            currency_id,
            issue_date,
            status
        FROM invoices
        WHERE issue_date BETWEEN ? AND ?
        ORDER BY issue_date
        """

        all_invoices = self.db_manager.execute_query(all_invoices_query, (start_date, end_date))

        # Group invoices by month
        monthly_data = {}

        for inv in all_invoices:
            if inv['status'] == 'cancelled':
                continue

            # Extract month from issue_date (format: YYYY-MM-DD)
            month = inv['issue_date'][:7]  # Gets YYYY-MM

            # Initialize month data if not exists
            if month not in monthly_data:
                monthly_data[month] = {
                    'invoice_count': 0,
                    'total_amount': 0
                }

            # Convert invoice total to primary currency
            invoice_total = float(inv['total']) if inv['total'] is not None else 0
            currency_id = inv['currency_id']

            if currency_id and currency_id != self.primary_currency.id:
                try:
                    primary_total = self.currency_manager.convert_amount(
                        invoice_total, currency_id, self.primary_currency.id
                    )
                    print(f"Converting invoice {inv['invoice_number']} from currency {currency_id} to {self.primary_currency.id}: {invoice_total} -> {primary_total}")
                except Exception as e:
                    print(f"Error converting amount: {e}")
                    primary_total = invoice_total
            else:
                primary_total = invoice_total

            # Add to monthly totals
            monthly_data[month]['invoice_count'] += 1
            monthly_data[month]['total_amount'] += primary_total

        # Convert to sorted list for display
        sorted_months = sorted(monthly_data.keys())
        rows = []

        for month in sorted_months:
            rows.append({
                'month': month,
                'invoice_count': monthly_data[month]['invoice_count'],
                'total_amount': monthly_data[month]['total_amount']
            })

        # Set up table
        self.report_table.clear()
        self.report_table.setColumnCount(3)
        self.report_table.setHorizontalHeaderLabels([
            tr("reports.month", "الشهر"),
            tr("reports.invoice_count", "عدد الفواتير"),
            tr("reports.total_sales", "إجمالي المبيعات")
        ])
        self.report_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Fill table with improved styling
        self.report_table.setRowCount(len(rows))

        total_sales = 0
        total_invoices = 0

        for i, row in enumerate(rows):
            # Month
            month_item = QTableWidgetItem(self.format_month(row['month']))
            month_item.setTextAlignment(Qt.AlignCenter)
            month_item.setForeground(QColor("#0d47a1"))  # Blue color for month
            self.report_table.setItem(i, 0, month_item)

            # Invoice count
            count_item = QTableWidgetItem(str(row['invoice_count']))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.report_table.setItem(i, 1, count_item)

            # Amount (already converted to primary currency)
            amount = row['total_amount']

            # Format with primary currency symbol
            formatted_amount = format_currency(amount, self.primary_currency.symbol)

            amount_item = QTableWidgetItem(formatted_amount)
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#2e7d32"))  # Green color for amount
            amount_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.report_table.setItem(i, 2, amount_item)

            # Add to totals
            total_sales += amount
            total_invoices += row['invoice_count']

        print(f"Monthly Report: Total sales in {self.primary_currency.code}: {total_sales}")
        print(f"Monthly Report: Total invoices: {total_invoices}")

        # Get all invoices for the period to calculate statistics correctly
        all_invoices_query = """
        SELECT id, invoice_number, total, amount_paid, amount_due, status, currency_id
        FROM invoices
        WHERE issue_date BETWEEN ? AND ?
        """
        all_invoices = self.db_manager.execute_query(all_invoices_query, (start_date, end_date))

        # Initialize counters
        total_paid = 0
        total_remaining = 0
        cancelled_count = 0
        unpaid_count = 0
        fully_paid_count = 0  # Counter for fully paid invoices

        # Calculate statistics from all invoices
        for inv in all_invoices:
            if inv['status'] == 'cancelled':
                cancelled_count += 1
                continue

            # Get currency for this invoice
            currency_id = inv['currency_id']

            # Convert values to primary currency if needed
            if currency_id and currency_id != self.primary_currency.id:
                try:
                    # Convert paid amount to primary currency
                    invoice_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                    primary_paid = self.currency_manager.convert_amount(
                        invoice_paid, currency_id, self.primary_currency.id
                    )

                    # Convert remaining amount to primary currency
                    invoice_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0
                    primary_remaining = self.currency_manager.convert_amount(
                        invoice_remaining, currency_id, self.primary_currency.id
                    )

                    print(f"Converting invoice {inv['invoice_number']} amounts from {currency_id} to {self.primary_currency.id}")
                    print(f"  Paid: {invoice_paid} -> {primary_paid}")
                    print(f"  Remaining: {invoice_remaining} -> {primary_remaining}")
                except Exception as e:
                    print(f"Error converting amounts: {e}")
                    # Use original values if conversion fails
                    invoice_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                    primary_paid = invoice_paid
                    invoice_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0
                    primary_remaining = invoice_remaining
            else:
                # Already in primary currency
                primary_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                primary_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0

            # Add to totals
            total_paid += primary_paid
            total_remaining += primary_remaining

            # Count invoices with remaining balance
            if primary_remaining > 0:
                unpaid_count += 1
            # Count fully paid invoices
            elif primary_remaining == 0 and primary_paid > 0:
                fully_paid_count += 1

        # Print calculated totals for debugging
        print(f"Monthly Report: Total sales in {self.primary_currency.code}: {total_sales}")
        print(f"Monthly Report: Total paid in {self.primary_currency.code}: {total_paid}")
        print(f"Monthly Report: Total remaining in {self.primary_currency.code}: {total_remaining}")

        # Update summary with formatted values using primary currency
        if self.primary_currency:
            formatted_sales = format_currency(total_sales, self.primary_currency.symbol)
            formatted_paid = format_currency(total_paid, self.primary_currency.symbol)
            formatted_remaining = format_currency(total_remaining, self.primary_currency.symbol)
        else:
            formatted_sales = format_currency(total_sales, "ج.م")
            formatted_paid = format_currency(total_paid, "ج.م")
            formatted_remaining = format_currency(total_remaining, "ج.م")

        # Update labels with formatted values
        self.total_sales_label.setText(formatted_sales)
        self.total_paid_label.setText(formatted_paid)
        self.total_remaining_label.setText(formatted_remaining)

        # Update invoice counts
        self.invoice_count_label.setText(str(total_invoices))
        self.fully_paid_invoices_label.setText(str(fully_paid_count))
        self.cancelled_invoices_label.setText(str(cancelled_count))
        self.unpaid_invoices_label.setText(str(unpaid_count))

    def load_yearly_sales_report(self, start_date, end_date):
        """Load yearly sales report.

        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
        """
        # Make sure we have the latest primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()
            if self.primary_currency:
                print(f"Yearly Report: Using primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
            else:
                print("Yearly Report: No primary currency found")
                return
        else:
            print("Yearly Report: No currency manager available")
            return

        # We need to get all invoices and manually group them by year
        # This allows us to properly convert currencies before grouping
        all_invoices_query = """
        SELECT
            id,
            invoice_number,
            total,
            currency_id,
            issue_date,
            status
        FROM invoices
        WHERE issue_date BETWEEN ? AND ?
        ORDER BY issue_date
        """

        all_invoices = self.db_manager.execute_query(all_invoices_query, (start_date, end_date))

        # Group invoices by year
        yearly_data = {}

        for inv in all_invoices:
            if inv['status'] == 'cancelled':
                continue

            # Extract year from issue_date (format: YYYY-MM-DD)
            year = inv['issue_date'][:4]  # Gets YYYY

            # Initialize year data if not exists
            if year not in yearly_data:
                yearly_data[year] = {
                    'invoice_count': 0,
                    'total_amount': 0
                }

            # Convert invoice total to primary currency
            invoice_total = float(inv['total']) if inv['total'] is not None else 0
            currency_id = inv['currency_id']

            if currency_id and currency_id != self.primary_currency.id:
                try:
                    primary_total = self.currency_manager.convert_amount(
                        invoice_total, currency_id, self.primary_currency.id
                    )
                    print(f"Converting invoice {inv['invoice_number']} from currency {currency_id} to {self.primary_currency.id}: {invoice_total} -> {primary_total}")
                except Exception as e:
                    print(f"Error converting amount: {e}")
                    primary_total = invoice_total
            else:
                primary_total = invoice_total

            # Add to yearly totals
            yearly_data[year]['invoice_count'] += 1
            yearly_data[year]['total_amount'] += primary_total

        # Convert to sorted list for display
        sorted_years = sorted(yearly_data.keys())
        rows = []

        for year in sorted_years:
            rows.append({
                'year': year,
                'invoice_count': yearly_data[year]['invoice_count'],
                'total_amount': yearly_data[year]['total_amount']
            })

        # Set up table
        self.report_table.clear()
        self.report_table.setColumnCount(3)
        self.report_table.setHorizontalHeaderLabels([
            tr("reports.year", "السنة"),
            tr("reports.invoice_count", "عدد الفواتير"),
            tr("reports.total_sales", "إجمالي المبيعات")
        ])
        self.report_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Fill table with improved styling
        self.report_table.setRowCount(len(rows))

        total_sales = 0
        total_invoices = 0

        for i, row in enumerate(rows):
            # Year
            year_item = QTableWidgetItem(row['year'])
            year_item.setTextAlignment(Qt.AlignCenter)
            year_item.setForeground(QColor("#0d47a1"))  # Blue color for year
            self.report_table.setItem(i, 0, year_item)

            # Invoice count
            count_item = QTableWidgetItem(str(row['invoice_count']))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.report_table.setItem(i, 1, count_item)

            # Amount (already converted to primary currency)
            amount = row['total_amount']

            # Format with primary currency symbol
            formatted_amount = format_currency(amount, self.primary_currency.symbol)

            amount_item = QTableWidgetItem(formatted_amount)
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#2e7d32"))  # Green color for amount
            amount_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.report_table.setItem(i, 2, amount_item)

            # Add to totals
            total_sales += amount
            total_invoices += row['invoice_count']

        print(f"Yearly Report: Total sales in {self.primary_currency.code}: {total_sales}")
        print(f"Yearly Report: Total invoices: {total_invoices}")

        # Get all invoices for the period to calculate statistics correctly
        all_invoices_query = """
        SELECT id, invoice_number, total, amount_paid, amount_due, status, currency_id
        FROM invoices
        WHERE issue_date BETWEEN ? AND ?
        """
        all_invoices = self.db_manager.execute_query(all_invoices_query, (start_date, end_date))

        # Initialize counters
        total_sales = 0  # Reset total_sales to calculate from all invoices
        total_paid = 0
        total_remaining = 0
        cancelled_count = 0
        unpaid_count = 0
        total_invoices = 0  # Reset total_invoices to calculate from all non-cancelled invoices

        # Calculate statistics from all invoices
        for inv in all_invoices:
            if inv['status'] == 'cancelled':
                cancelled_count += 1
            else:
                total_invoices += 1  # Count non-cancelled invoices
                # Get currency for this invoice
                currency_id = inv['currency_id']

                # Convert values to primary currency if needed
                if currency_id and currency_id != self.primary_currency.id:
                    # Convert total to primary currency
                    invoice_total = float(inv['total']) if inv['total'] is not None else 0
                    primary_total = self.currency_manager.convert_amount(
                        invoice_total, currency_id, self.primary_currency.id
                    )

                    # Convert paid amount to primary currency
                    invoice_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                    primary_paid = self.currency_manager.convert_amount(
                        invoice_paid, currency_id, self.primary_currency.id
                    )

                    # Convert remaining amount to primary currency
                    invoice_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0
                    primary_remaining = self.currency_manager.convert_amount(
                        invoice_remaining, currency_id, self.primary_currency.id
                    )
                else:
                    # Already in primary currency
                    primary_total = float(inv['total']) if inv['total'] is not None else 0
                    primary_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                    primary_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0

                # Add to totals
                total_sales += primary_total
                total_paid += primary_paid
                total_remaining += primary_remaining

                # Count invoices with remaining balance
                if primary_remaining > 0:
                    unpaid_count += 1

        # Update summary with formatted values using primary currency
        formatted_sales = format_currency(total_sales, self.primary_currency.symbol)
        formatted_paid = format_currency(total_paid, self.primary_currency.symbol)
        formatted_remaining = format_currency(total_remaining, self.primary_currency.symbol)

        # Update labels with formatted values
        self.total_sales_label.setText(formatted_sales)
        self.total_paid_label.setText(formatted_paid)
        self.total_remaining_label.setText(formatted_remaining)

        # Update invoice counts
        self.invoice_count_label.setText(str(total_invoices))
        self.cancelled_invoices_label.setText(str(cancelled_count))
        self.unpaid_invoices_label.setText(str(unpaid_count))

    def load_customer_sales_report(self, start_date, end_date):
        """Load customer sales report.

        Args:
            start_date (str): Start date in YYYY-MM-DD format
            end_date (str): End date in YYYY-MM-DD format
        """
        # Make sure we have the latest primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()
            if self.primary_currency:
                print(f"Customer Report: Using primary currency: {self.primary_currency.code} ({self.primary_currency.symbol})")
            else:
                print("Customer Report: No primary currency found")
                return
        else:
            print("Customer Report: No currency manager available")
            return

        # We need to get all invoices and manually group them by customer
        # This allows us to properly convert currencies before grouping
        all_invoices_query = """
        SELECT
            i.id,
            i.invoice_number,
            i.total,
            i.currency_id,
            i.issue_date,
            i.status,
            c.id as customer_id,
            c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.issue_date BETWEEN ? AND ?
        ORDER BY c.name
        """

        all_invoices = self.db_manager.execute_query(all_invoices_query, (start_date, end_date))

        # Group invoices by customer
        customer_data = {}

        for inv in all_invoices:
            if inv['status'] == 'cancelled':
                continue

            # Get customer info
            customer_id = inv['customer_id']
            customer_name = inv['customer_name']

            # Initialize customer data if not exists
            if customer_id not in customer_data:
                customer_data[customer_id] = {
                    'customer_name': customer_name,
                    'invoice_count': 0,
                    'total_amount': 0
                }

            # Convert invoice total to primary currency
            invoice_total = float(inv['total']) if inv['total'] is not None else 0
            currency_id = inv['currency_id']

            if currency_id and currency_id != self.primary_currency.id:
                try:
                    primary_total = self.currency_manager.convert_amount(
                        invoice_total, currency_id, self.primary_currency.id
                    )
                    print(f"Converting invoice {inv['invoice_number']} from currency {currency_id} to {self.primary_currency.id}: {invoice_total} -> {primary_total}")
                except Exception as e:
                    print(f"Error converting amount: {e}")
                    primary_total = invoice_total
            else:
                primary_total = invoice_total

            # Add to customer totals
            customer_data[customer_id]['invoice_count'] += 1
            customer_data[customer_id]['total_amount'] += primary_total

        # Convert to sorted list for display (sort by total amount descending)
        rows = []
        for customer_id, data in customer_data.items():
            rows.append({
                'customer_name': data['customer_name'],
                'invoice_count': data['invoice_count'],
                'total_amount': data['total_amount']
            })

        # Sort by total amount descending
        rows.sort(key=lambda x: x['total_amount'], reverse=True)

        # Set up table
        self.report_table.clear()
        self.report_table.setColumnCount(3)
        self.report_table.setHorizontalHeaderLabels([
            tr("reports.customer", "العميل"),
            tr("reports.invoice_count", "عدد الفواتير"),
            tr("reports.total_sales", "إجمالي المبيعات")
        ])
        self.report_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)

        # Fill table with improved styling
        self.report_table.setRowCount(len(rows))

        total_sales = 0
        total_invoices = 0

        for i, row in enumerate(rows):
            # Customer name
            name_item = QTableWidgetItem(row['customer_name'])
            name_item.setTextAlignment(Qt.AlignCenter)
            name_item.setForeground(QColor("#0d47a1"))  # Blue color for customer name
            self.report_table.setItem(i, 0, name_item)

            # Invoice count
            count_item = QTableWidgetItem(str(row['invoice_count']))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.report_table.setItem(i, 1, count_item)

            # Amount (already converted to primary currency)
            amount = row['total_amount']

            # Format with primary currency symbol
            formatted_amount = format_currency(amount, self.primary_currency.symbol)

            amount_item = QTableWidgetItem(formatted_amount)
            amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            amount_item.setForeground(QColor("#2e7d32"))  # Green color for amount
            amount_item.setFont(QFont("Arial", 10, QFont.Bold))
            self.report_table.setItem(i, 2, amount_item)

            # Add to totals
            total_sales += amount
            total_invoices += row['invoice_count']

        print(f"Customer Report: Total sales in {self.primary_currency.code}: {total_sales}")
        print(f"Customer Report: Total invoices: {total_invoices}")

        # Get all invoices for the period to calculate statistics correctly
        all_invoices_query = """
        SELECT i.id, i.invoice_number, i.total, i.amount_paid, i.amount_due, i.status, i.currency_id
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.issue_date BETWEEN ? AND ?
        """
        all_invoices = self.db_manager.execute_query(all_invoices_query, (start_date, end_date))

        # Initialize counters
        total_sales = 0  # Reset total_sales to calculate from all invoices
        total_paid = 0
        total_remaining = 0
        cancelled_count = 0
        unpaid_count = 0
        total_invoices = 0  # Reset total_invoices to calculate from all non-cancelled invoices

        # Calculate statistics from all invoices
        for inv in all_invoices:
            if inv['status'] == 'cancelled':
                cancelled_count += 1
            else:
                total_invoices += 1  # Count non-cancelled invoices
                # Get currency for this invoice
                currency_id = inv['currency_id']

                # Convert values to primary currency if needed
                if currency_id and currency_id != self.primary_currency.id:
                    # Convert total to primary currency
                    invoice_total = float(inv['total']) if inv['total'] is not None else 0
                    primary_total = self.currency_manager.convert_amount(
                        invoice_total, currency_id, self.primary_currency.id
                    )

                    # Convert paid amount to primary currency
                    invoice_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                    primary_paid = self.currency_manager.convert_amount(
                        invoice_paid, currency_id, self.primary_currency.id
                    )

                    # Convert remaining amount to primary currency
                    invoice_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0
                    primary_remaining = self.currency_manager.convert_amount(
                        invoice_remaining, currency_id, self.primary_currency.id
                    )
                else:
                    # Already in primary currency
                    primary_total = float(inv['total']) if inv['total'] is not None else 0
                    primary_paid = float(inv['amount_paid']) if inv['amount_paid'] is not None else 0
                    primary_remaining = float(inv['amount_due']) if inv['amount_due'] is not None else 0

                # Add to totals
                total_sales += primary_total
                total_paid += primary_paid
                total_remaining += primary_remaining

                # Count invoices with remaining balance
                if primary_remaining > 0:
                    unpaid_count += 1

        # Update summary with formatted values using primary currency
        formatted_sales = format_currency(total_sales, self.primary_currency.symbol)
        formatted_paid = format_currency(total_paid, self.primary_currency.symbol)
        formatted_remaining = format_currency(total_remaining, self.primary_currency.symbol)

        # Update labels with formatted values
        self.total_sales_label.setText(formatted_sales)
        self.total_paid_label.setText(formatted_paid)
        self.total_remaining_label.setText(formatted_remaining)

        # Update invoice counts
        self.invoice_count_label.setText(str(total_invoices))
        self.cancelled_invoices_label.setText(str(cancelled_count))
        self.unpaid_invoices_label.setText(str(unpaid_count))

    def format_month(self, month_str):
        """Format month string to Arabic month name.

        Args:
            month_str (str): Month string in YYYY-MM format

        Returns:
            str: Formatted month string
        """
        year, month = month_str.split('-')

        month_names = {
            '01': 'يناير',
            '02': 'فبراير',
            '03': 'مارس',
            '04': 'أبريل',
            '05': 'مايو',
            '06': 'يونيو',
            '07': 'يوليو',
            '08': 'أغسطس',
            '09': 'سبتمبر',
            '10': 'أكتوبر',
            '11': 'نوفمبر',
            '12': 'ديسمبر'
        }

        return f"{month_names.get(month, month)} {year}"

    def export_to_pdf(self):
        """Export the current report to PDF using a simple HTML approach."""
        # Get report data
        report_type = self.report_type_combo.currentText()
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Get table data
        rows = []
        for row in range(self.report_table.rowCount()):
            row_data = []
            for col in range(self.report_table.columnCount()):
                item = self.report_table.item(row, col)
                row_data.append(item.text() if item else "")
            rows.append(row_data)

        # Get column headers
        headers = []
        for col in range(self.report_table.columnCount()):
            headers.append(self.report_table.horizontalHeaderItem(col).text())

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ التقرير", "", "ملفات PDF (*.pdf)"
        )

        if file_path:
            try:
                # Create a temporary HTML file
                import tempfile
                temp_html = tempfile.NamedTemporaryFile(suffix='.html', delete=False)
                temp_html_path = temp_html.name
                temp_html.close()

                # Generate HTML content
                html_content = f"""
                <!DOCTYPE html>
                <html dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير {report_type}</title>
                    <style>
                        body {{
                            font-family: Arial, sans-serif;
                            margin: 20px;
                            direction: rtl;
                            text-align: right;
                        }}
                        h1 {{
                            color: #0D47A1;
                            text-align: center;
                        }}
                        .info {{
                            text-align: center;
                            margin-bottom: 20px;
                        }}
                        table {{
                            width: 100%;
                            border-collapse: collapse;
                            margin: 20px 0;
                        }}
                        th {{
                            background-color: #1976D2;
                            color: white;
                            padding: 10px;
                            text-align: center;
                            font-weight: bold;
                        }}
                        td {{
                            padding: 8px;
                            text-align: center;
                            border: 1px solid #ddd;
                        }}
                        tr:nth-child(even) {{
                            background-color: #f2f2f2;
                        }}
                        .amount {{
                            color: #2E7D32;
                            font-weight: bold;
                        }}
                        .summary {{
                            background-color: #E3F2FD;
                            padding: 15px;
                            border-radius: 5px;
                            margin-top: 20px;
                        }}
                        .summary-title {{
                            color: #0D47A1;
                            font-weight: bold;
                            font-size: 18px;
                            margin-bottom: 10px;
                        }}
                        .summary-item {{
                            margin: 5px 0;
                        }}
                        .summary-value {{
                            color: #2E7D32;
                            font-weight: bold;
                        }}
                    </style>
                </head>
                <body>
                    <h1>تقرير {report_type}</h1>
                    <div class="info">
                        <p>الفترة: من {start_date} إلى {end_date}</p>
                        <p>تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}</p>
                    </div>

                    <table>
                        <thead>
                            <tr>
                """

                # Add table headers
                for header in headers:
                    html_content += f"<th>{header}</th>"

                html_content += """
                            </tr>
                        </thead>
                        <tbody>
                """

                # Add table rows
                for row in rows:
                    html_content += "<tr>"
                    for i, cell in enumerate(row):
                        if i == 0:  # First column (Month/Year/Customer)
                            html_content += f"<td><strong>{cell}</strong></td>"
                        elif i == 1:  # Second column (Invoice count)
                            html_content += f"<td>{cell}</td>"
                        elif i == 2:  # Third column (Amount)
                            html_content += f"<td class='amount'>{cell}</td>"
                        else:
                            html_content += f"<td>{cell}</td>"
                    html_content += "</tr>"

                html_content += """
                        </tbody>
                    </table>

                    <div class="summary">
                        <div class="summary-title">ملخص التقرير</div>
                        <div class="summary-item">إجمالي المبيعات: <span class="summary-value">""" + self.total_sales_label.text() + """</span></div>
                        <div class="summary-item">إجمالي المدفوع: <span class="summary-value">""" + self.total_paid_label.text() + """</span></div>
                        <div class="summary-item">إجمالي المتبقي: <span class="summary-value">""" + self.total_remaining_label.text() + """</span></div>
                        <div class="summary-item">عدد الفواتير: <span class="summary-value">""" + self.invoice_count_label.text() + """</span></div>
                        <div class="summary-item">الفواتير المدفوعة بالكامل: <span class="summary-value">""" + self.fully_paid_invoices_label.text() + """</span></div>
                        <div class="summary-item">الفواتير الملغاة: <span class="summary-value">""" + self.cancelled_invoices_label.text() + """</span></div>
                        <div class="summary-item">""" + tr("reports.total_invoices_with_balance", "الفواتير المتبقية برصيد") + """: <span class="summary-value">""" + self.unpaid_invoices_label.text() + """</span></div>
                    </div>
                </body>
                </html>
                """

                # Write HTML content to temporary file
                with open(temp_html_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)

                # Try to use QWebEngineView if available
                try:
                    from PySide6.QtWebEngineWidgets import QWebEngineView
                    from PySide6.QtCore import QUrl, QEventLoop
                    from PySide6.QtGui import QPageLayout, QPageSize

                    # Create a QWebEngineView
                    web_view = QWebEngineView()

                    # Create an event loop to wait for the page to load
                    loop = QEventLoop()
                    web_view.loadFinished.connect(loop.quit)

                    # Load the HTML file
                    web_view.load(QUrl.fromLocalFile(temp_html_path))
                    loop.exec_()

                    # Set up the page layout
                    layout = QPageLayout()
                    layout.setPageSize(QPageSize(QPageSize.A4))
                    layout.setOrientation(QPageLayout.Portrait)

                    # Print to PDF
                    web_view.page().printToPdf(file_path, layout)

                    # Create another event loop to wait for PDF generation
                    pdf_loop = QEventLoop()
                    web_view.page().pdfPrintingFinished.connect(pdf_loop.quit)
                    pdf_loop.exec_()

                except ImportError:
                    # Fallback to a simpler method using weasyprint if available
                    try:
                        import weasyprint
                        weasyprint.HTML(filename=temp_html_path).write_pdf(file_path)
                    except ImportError:
                        # If weasyprint is not available, try using pdfkit
                        try:
                            import pdfkit
                            pdfkit.from_file(temp_html_path, file_path)
                        except ImportError:
                            # If all else fails, just copy the HTML file
                            import shutil
                            html_output_path = file_path.replace('.pdf', '.html')
                            shutil.copy(temp_html_path, html_output_path)
                            QMessageBox.information(self, tr("reports.export_completed", "تم التصدير"),
                                tr("reports.exported_as_html", f"تم تصدير التقرير إلى ملف HTML بدلاً من PDF: {html_output_path}"))
                            return

                # Clean up the temporary HTML file
                import os
                try:
                    os.remove(temp_html_path)
                except:
                    pass

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("reports.export_success", "تم تصدير التقرير بنجاح"))

            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("reports.export_error", f"حدث خطأ أثناء تصدير التقرير: {str(e)}"))
                import traceback
                traceback.print_exc()

    def export_to_excel(self):
        """Export the current report to Excel using a simple approach."""
        # Get report data
        report_type = self.report_type_combo.currentText()
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Get table data
        rows = []
        for row in range(self.report_table.rowCount()):
            row_data = []
            for col in range(self.report_table.columnCount()):
                item = self.report_table.item(row, col)
                row_data.append(item.text() if item else "")
            rows.append(row_data)

        # Get column headers
        headers = []
        for col in range(self.report_table.columnCount()):
            headers.append(self.report_table.horizontalHeaderItem(col).text())

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, tr("reports.save_report", "حفظ التقرير"), "", tr("reports.excel_files", "ملفات Excel (*.xlsx)")
        )

        if file_path:
            try:
                # Create a new workbook
                import openpyxl
                from openpyxl.styles import Font, Alignment, PatternFill, Border, Side

                wb = openpyxl.Workbook()
                ws = wb.active
                ws.title = "تقرير"

                # Set RTL direction
                ws.sheet_view.rightToLeft = True

                # Define styles
                title_font = Font(name='Arial', size=16, bold=True, color="0D47A1")  # Dark blue
                header_font = Font(name='Arial', size=12, bold=True, color="FFFFFF")  # White
                normal_font = Font(name='Arial', size=11)
                bold_font = Font(name='Arial', size=11, bold=True)
                amount_font = Font(name='Arial', size=11, bold=True, color="2E7D32")  # Green

                header_fill = PatternFill(start_color="1976D2", end_color="1976D2", fill_type="solid")  # Blue
                alt_row_fill = PatternFill(start_color="F5F5F5", end_color="F5F5F5", fill_type="solid")  # Light gray
                summary_fill = PatternFill(start_color="E3F2FD", end_color="E3F2FD", fill_type="solid")  # Light blue

                centered = Alignment(horizontal="center", vertical="center", wrap_text=True)
                right_aligned = Alignment(horizontal="right", vertical="center")

                thin_border = Border(
                    left=Side(style='thin', color="BDBDBD"),
                    right=Side(style='thin', color="BDBDBD"),
                    top=Side(style='thin', color="BDBDBD"),
                    bottom=Side(style='thin', color="BDBDBD")
                )

                # Add report header with improved styling
                ws['A1'] = f"تقرير {report_type}"
                ws['A1'].font = title_font
                ws['A1'].alignment = centered
                ws.merge_cells('A1:C1')

                ws['A2'] = f"الفترة: من {start_date} إلى {end_date}"
                ws['A2'].font = normal_font
                ws['A2'].alignment = centered
                ws.merge_cells('A2:C2')

                ws['A3'] = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d')}"
                ws['A3'].font = normal_font
                ws['A3'].alignment = centered
                ws.merge_cells('A3:C3')

                # Add headers with improved styling
                header_row = 5
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=header_row, column=col, value=header)
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = centered
                    cell.border = thin_border

                # Add data rows with improved styling
                for row_idx, row_data in enumerate(rows, header_row + 1):
                    # Apply alternating row colors
                    row_fill = alt_row_fill if row_idx % 2 == 0 else None

                    for col_idx, cell_value in enumerate(row_data, 1):
                        cell = ws.cell(row=row_idx, column=col_idx, value=cell_value)

                        # Apply different styling based on column
                        if col_idx == 1:  # First column (Month/Year/Customer)
                            cell.font = bold_font
                            cell.alignment = centered
                        elif col_idx == 2:  # Second column (Invoice count)
                            cell.font = normal_font
                            cell.alignment = centered
                        elif col_idx == 3:  # Third column (Amount)
                            cell.font = amount_font
                            cell.alignment = right_aligned

                            # Try to extract numeric value from amount string
                            try:
                                # Extract numeric part from strings like "1234.56 ج.م"
                                if isinstance(cell_value, str) and ("ج.م" in cell_value or "ر.س" in cell_value):
                                    numeric_part = cell_value.replace("ج.م", "").replace("ر.س", "").strip()
                                    cell.value = float(numeric_part)
                                    cell.number_format = '#,##0.00 "ج.م"'
                            except:
                                # If conversion fails, keep as string
                                pass

                        if row_fill:
                            cell.fill = row_fill
                        cell.border = thin_border

                # Add summary with improved styling
                summary_row = header_row + len(rows) + 2

                # Summary header
                summary_cell = ws.cell(row=summary_row - 1, column=1, value="ملخص التقرير")
                summary_cell.font = title_font
                summary_cell.alignment = centered
                ws.merge_cells(f'A{summary_row - 1}:C{summary_row - 1}')

                # Total sales
                total_label = ws.cell(row=summary_row, column=1, value="إجمالي المبيعات:")
                total_label.font = bold_font
                total_label.alignment = right_aligned

                total_value = ws.cell(row=summary_row, column=2, value=self.total_sales_label.text())
                total_value.font = amount_font
                total_value.alignment = right_aligned

                # Try to extract numeric value from total sales string
                try:
                    total_sales_text = self.total_sales_label.text()
                    if "ج.م" in total_sales_text or "ر.س" in total_sales_text:
                        numeric_part = total_sales_text.replace("ج.م", "").replace("ر.س", "").strip()
                        total_value.value = float(numeric_part)
                        total_value.number_format = '#,##0.00 "ج.م"'
                except:
                    pass

                # Total paid
                paid_label = ws.cell(row=summary_row + 1, column=1, value="إجمالي المدفوع:")
                paid_label.font = bold_font
                paid_label.alignment = right_aligned

                paid_value = ws.cell(row=summary_row + 1, column=2, value=self.total_paid_label.text())
                paid_value.font = amount_font
                paid_value.alignment = right_aligned

                # Try to extract numeric value from total paid string
                try:
                    total_paid_text = self.total_paid_label.text()
                    if "ج.م" in total_paid_text or "ر.س" in total_paid_text:
                        numeric_part = total_paid_text.replace("ج.م", "").replace("ر.س", "").strip()
                        paid_value.value = float(numeric_part)
                        paid_value.number_format = '#,##0.00 "ج.م"'
                except:
                    pass

                # Total remaining
                remaining_label = ws.cell(row=summary_row + 2, column=1, value="إجمالي المتبقي:")
                remaining_label.font = bold_font
                remaining_label.alignment = right_aligned

                remaining_value = ws.cell(row=summary_row + 2, column=2, value=self.total_remaining_label.text())
                remaining_value.font = amount_font
                remaining_value.alignment = right_aligned

                # Try to extract numeric value from total remaining string
                try:
                    total_remaining_text = self.total_remaining_label.text()
                    if "ج.م" in total_remaining_text or "ر.س" in total_remaining_text:
                        numeric_part = total_remaining_text.replace("ج.م", "").replace("ر.س", "").strip()
                        remaining_value.value = float(numeric_part)
                        remaining_value.number_format = '#,##0.00 "ج.م"'
                except:
                    pass

                # Invoice count
                count_label = ws.cell(row=summary_row + 3, column=1, value="عدد الفواتير:")
                count_label.font = bold_font
                count_label.alignment = right_aligned

                count_value = ws.cell(row=summary_row + 3, column=2, value=self.invoice_count_label.text())
                count_value.font = bold_font
                count_value.alignment = right_aligned

                # Fully paid invoices
                fully_paid_label = ws.cell(row=summary_row + 4, column=1, value="الفواتير المدفوعة بالكامل:")
                fully_paid_label.font = bold_font
                fully_paid_label.alignment = right_aligned

                fully_paid_value = ws.cell(row=summary_row + 4, column=2, value=self.fully_paid_invoices_label.text())
                fully_paid_value.font = bold_font
                fully_paid_value.alignment = right_aligned
                fully_paid_value.font = Font(name='Arial', size=11, bold=True, color="4CAF50")  # Green color

                # Cancelled invoices
                cancelled_label = ws.cell(row=summary_row + 5, column=1, value="الفواتير الملغاة:")
                cancelled_label.font = bold_font
                cancelled_label.alignment = right_aligned

                cancelled_value = ws.cell(row=summary_row + 5, column=2, value=self.cancelled_invoices_label.text())
                cancelled_value.font = bold_font
                cancelled_value.alignment = right_aligned
                cancelled_value.font = Font(name='Arial', size=11, bold=True, color="F44336")  # Red color

                # Unpaid invoices
                unpaid_label = ws.cell(row=summary_row + 6, column=1, value=tr("reports.total_invoices_with_balance", "الفواتير المتبقية برصيد") + ":")
                unpaid_label.font = bold_font
                unpaid_label.alignment = right_aligned

                unpaid_value = ws.cell(row=summary_row + 6, column=2, value=self.unpaid_invoices_label.text())
                unpaid_value.font = bold_font
                unpaid_value.alignment = right_aligned
                unpaid_value.font = Font(name='Arial', size=11, bold=True, color="FF9800")  # Orange color

                # Apply fill to summary section
                for row in range(summary_row - 1, summary_row + 7):
                    for col in range(1, 4):
                        cell = ws.cell(row=row, column=col)
                        cell.fill = summary_fill
                        cell.border = thin_border

                # Auto-adjust column widths - with fix for merged cells
                try:
                    # Set default column widths first
                    for i in range(1, len(headers) + 1):
                        column_letter = openpyxl.utils.get_column_letter(i)
                        ws.column_dimensions[column_letter].width = 20  # Default width

                    # Then try to auto-adjust if possible
                    for col in ws.columns:
                        try:
                            max_length = 0
                            column_letter = openpyxl.utils.get_column_letter(col[0].column)

                            for cell in col:
                                if cell.value:
                                    max_length = max(max_length, len(str(cell.value)))

                            if max_length > 0:
                                adjusted_width = (max_length + 2) * 1.2
                                ws.column_dimensions[column_letter].width = adjusted_width
                        except:
                            # Skip problematic columns (like merged cells)
                            continue
                except Exception as e:
                    print(f"Warning: Could not adjust column widths: {str(e)}")

                # Save the workbook
                wb.save(file_path)

                QMessageBox.information(self, tr("messages.success", "نجاح"), tr("reports.export_success", "تم تصدير التقرير بنجاح"))

            except Exception as e:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("reports.export_error", f"حدث خطأ أثناء تصدير التقرير: {str(e)}"))
                import traceback
                traceback.print_exc()
