#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Currency Manager for فوترها (Fawterha)
Handles database operations for currencies
"""

from models.currency import Currency


class CurrencyManager:
    """Manager for currency database operations."""

    def __init__(self, db_manager):
        """Initialize the currency manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    def get_all_currencies(self):
        """Get all currencies.

        Returns:
            list: List of Currency objects
        """
        query = """
        SELECT id, code, name, symbol, exchange_rate, is_primary, is_active, created_at, updated_at
        FROM currencies
        ORDER BY is_primary DESC, name
        """
        rows = self.db_manager.execute_query(query)
        return [Currency.from_db_row(row) for row in rows]

    def get_active_currencies(self):
        """Get all active currencies.

        Returns:
            list: List of active Currency objects
        """
        query = """
        SELECT id, code, name, symbol, exchange_rate, is_primary, is_active, created_at, updated_at
        FROM currencies
        WHERE is_active = 1
        ORDER BY is_primary DESC, name
        """
        rows = self.db_manager.execute_query(query)
        return [Currency.from_db_row(row) for row in rows]

    def get_currency_by_id(self, currency_id):
        """Get a currency by ID.

        Args:
            currency_id (int): Currency ID

        Returns:
            Currency: Currency object or None if not found
        """
        query = """
        SELECT id, code, name, symbol, exchange_rate, is_primary, is_active, created_at, updated_at
        FROM currencies
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (currency_id,))
        return Currency.from_db_row(rows[0]) if rows else None

    def get_currency_by_code(self, code):
        """Get a currency by code.

        Args:
            code (str): Currency code

        Returns:
            Currency: Currency object or None if not found
        """
        query = """
        SELECT id, code, name, symbol, exchange_rate, is_primary, is_active, created_at, updated_at
        FROM currencies
        WHERE code = ?
        """
        rows = self.db_manager.execute_query(query, (code,))
        return Currency.from_db_row(rows[0]) if rows else None

    def get_primary_currency(self):
        """Get the primary currency.

        Returns:
            Currency: Primary currency object or None if not found
        """
        query = """
        SELECT id, code, name, symbol, exchange_rate, is_primary, is_active, created_at, updated_at
        FROM currencies
        WHERE is_primary = 1
        LIMIT 1
        """
        rows = self.db_manager.execute_query(query)
        return Currency.from_db_row(rows[0]) if rows else None

    def add_currency(self, currency):
        """Add a new currency.

        Args:
            currency (Currency): Currency object to add

        Returns:
            int: ID of the new currency
        """
        # If this is set as primary, unset all other currencies as primary
        if currency.is_primary:
            self.db_manager.execute_query("""
            UPDATE currencies SET is_primary = 0
            """)

        query = """
        INSERT INTO currencies (code, name, symbol, exchange_rate, is_primary, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        params = (
            currency.code,
            currency.name,
            currency.symbol,
            currency.exchange_rate,
            1 if currency.is_primary else 0,
            1 if currency.is_active else 0
        )
        return self.db_manager.execute_insert(query, params)

    def update_currency(self, currency):
        """Update an existing currency.

        Args:
            currency (Currency): Currency object to update

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is being set as primary and was not primary before
        is_new_primary = False
        old_primary = None

        if currency.is_primary:
            # Get the current currency from database to check if it was already primary
            current_currency = self.get_currency_by_id(currency.id)
            if current_currency and not current_currency.is_primary:
                is_new_primary = True
                # Get the current primary currency
                old_primary = self.get_primary_currency()

        # Start a transaction
        conn = self.db_manager.connect()
        try:
            cursor = conn.cursor()

            # If this is set as primary, unset all other currencies as primary
            if currency.is_primary:
                cursor.execute("UPDATE currencies SET is_primary = 0")

            # Update the currency
            query = """
            UPDATE currencies
            SET code = ?, name = ?, symbol = ?, exchange_rate = ?, is_primary = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            params = (
                currency.code,
                currency.name,
                currency.symbol,
                currency.exchange_rate,
                1 if currency.is_primary else 0,
                1 if currency.is_active else 0,
                currency.id
            )
            cursor.execute(query, params)

            # If this is a new primary currency, recalculate all exchange rates
            if is_new_primary and old_primary:
                self._recalculate_exchange_rates(conn, old_primary, currency)

            # Commit the transaction
            conn.commit()
            return True
        except Exception as e:
            # Rollback in case of error
            conn.rollback()
            print(f"Error updating currency: {e}")
            return False
        finally:
            # Close the connection
            self.db_manager.close()

    def _recalculate_exchange_rates(self, conn, old_primary, new_primary):
        """Recalculate exchange rates when primary currency changes.

        Args:
            conn: Database connection
            old_primary (Currency): Old primary currency
            new_primary (Currency): New primary currency
        """
        try:
            cursor = conn.cursor()

            # Get all currencies except the new primary
            query = """
            SELECT id, code, name, symbol, exchange_rate, is_primary, is_active
            FROM currencies
            WHERE id != ?
            """
            rows = cursor.execute(query, (new_primary.id,)).fetchall()

            # Convert rows to Currency objects
            currencies = []
            for row in rows:
                currency = Currency(
                    id=row[0],
                    code=row[1],
                    name=row[2],
                    symbol=row[3],
                    exchange_rate=row[4],
                    is_primary=row[5],
                    is_active=row[6]
                )
                currencies.append(currency)

            # Calculate new exchange rates
            for currency in currencies:
                # Convert to absolute value in old primary currency
                absolute_value = currency.exchange_rate * old_primary.exchange_rate

                # Calculate new exchange rate relative to new primary
                new_rate = absolute_value / new_primary.exchange_rate

                # Update the currency exchange rate
                update_query = """
                UPDATE currencies
                SET exchange_rate = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
                cursor.execute(update_query, (new_rate, currency.id))

                print(f"Recalculated exchange rate for {currency.code}: {currency.exchange_rate} -> {new_rate}")

            # Set the new primary currency exchange rate to 1.0
            update_query = """
            UPDATE currencies
            SET exchange_rate = 1.0, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            cursor.execute(update_query, (new_primary.id,))

            print(f"Set exchange rate for new primary currency {new_primary.code} to 1.0")

            # Update product prices to reflect the new primary currency
            self._update_product_prices(conn, old_primary, new_primary)

            return True
        except Exception as e:
            print(f"Error recalculating exchange rates: {e}")
            return False

    def _update_product_prices(self, conn, old_primary, new_primary):
        """Update product prices when primary currency changes.

        Args:
            conn: Database connection
            old_primary (Currency): Old primary currency
            new_primary (Currency): New primary currency
        """
        try:
            cursor = conn.cursor()

            # Get all products
            query = """
            SELECT id, name, price
            FROM products
            """
            rows = cursor.execute(query).fetchall()

            # Update each product's price
            for row in rows:
                product_id = row[0]
                product_name = row[1]
                old_price = row[2]

                # Convert price to absolute value in old primary currency
                # For example, if old_price is 10 USD and old_primary is USD with rate 50.6,
                # then absolute_price = 10 * 50.6 = 506 EGP
                absolute_price = old_price * old_primary.exchange_rate

                # Convert absolute price to new primary currency
                # For example, if absolute_price is 506 EGP and new_primary is EUR with rate 56.9,
                # then new_price = 506 / 56.9 = 8.89 EUR
                new_price = absolute_price / new_primary.exchange_rate

                # Update the product price
                update_query = """
                UPDATE products
                SET price = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
                cursor.execute(update_query, (new_price, product_id))

                print(f"Updated price for product '{product_name}': {old_price} -> {new_price}")

            return True
        except Exception as e:
            print(f"Error updating product prices: {e}")
            return False

    def delete_currency(self, currency_id):
        """Delete a currency.

        Args:
            currency_id (int): ID of the currency to delete

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if this is the primary currency
        currency = self.get_currency_by_id(currency_id)
        if currency and currency.is_primary:
            return False  # Cannot delete primary currency

        query = """
        DELETE FROM currencies
        WHERE id = ?
        """
        return self.db_manager.execute_query(query, (currency_id,)) is not None

    def convert_amount(self, amount, from_currency_id, to_currency_id):
        """Convert an amount between currencies.

        Args:
            amount (float): Amount to convert
            from_currency_id (int): Source currency ID
            to_currency_id (int): Target currency ID

        Returns:
            float: Converted amount
        """
        if from_currency_id == to_currency_id:
            return amount

        from_currency = self.get_currency_by_id(from_currency_id)
        to_currency = self.get_currency_by_id(to_currency_id)

        if not from_currency or not to_currency:
            return amount

        print(f"Converting {amount} from {from_currency.code} (rate: {from_currency.exchange_rate}) to {to_currency.code} (rate: {to_currency.exchange_rate})")

        # Convert to primary currency first
        # For example, if from_currency is USD with exchange_rate 50.0 to EGP (primary),
        # then 100 USD = 100 * 50.0 = 5000 EGP
        primary_amount = from_currency.convert_to_primary(amount)

        # Then convert from primary to target currency
        # For example, if to_currency is EUR with exchange_rate 55.0 to EGP (primary),
        # then 5000 EGP = 5000 / 55.0 = 90.91 EUR
        result = to_currency.convert_from_primary(primary_amount)

        print(f"Final result: {amount} {from_currency.code} = {result} {to_currency.code}")
        return result
