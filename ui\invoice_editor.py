#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Editor for فوترها (Fawterha)
Dialog for creating and editing invoices
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout, QLabel,
    QLineEdit, QDateEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QPushButton, QDoubleSpinBox, QSpinBox, QTextEdit, QMessageBox,
    QHeaderView, QAbstractItemView, QDialogButtonBox, QGroupBox,
    QWidget, QSizePolicy
)
from PySide6.QtCore import Qt, QDate, QSize
from PySide6.QtGui import QFont
from PySide6.QtWidgets import QStyle

from models.invoice import Invoice
from models.invoice_item import InvoiceItem
from models.customer import Customer
from models.product import Product
from models.currency import Currency
from ui.products_view import ProductsView
from utils.currency_helper import (
    format_currency, convert_currency, get_styled_currency_label,
    get_totals_section_title, get_totals_container_style
)


class InvoiceItemDialog(QDialog):
    """Dialog for adding or editing an invoice item."""

    def __init__(self, parent=None, item=None):
        """Initialize the invoice item dialog.

        Args:
            parent: Parent widget
            item (InvoiceItem, optional): Item to edit. Defaults to None.
        """
        super().__init__(parent)

        self.item = item
        self.setWindowTitle("إضافة منتج/خدمة" if not item else "تعديل منتج/خدمة")
        self.setMinimumWidth(400)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        layout = QVBoxLayout(self)

        # Create form layout
        form_layout = QFormLayout()
        layout.addLayout(form_layout)

        # Add form fields
        self.description_edit = QLineEdit()
        self.description_edit.setPlaceholderText("وصف المنتج أو الخدمة")
        form_layout.addRow("الوصف:", self.description_edit)

        self.quantity_spin = QDoubleSpinBox()
        self.quantity_spin.setMinimum(0.01)
        self.quantity_spin.setMaximum(9999.99)
        self.quantity_spin.setValue(1.0)
        self.quantity_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("الكمية:", self.quantity_spin)

        self.unit_price_spin = QDoubleSpinBox()
        self.unit_price_spin.setMinimum(0.0)
        self.unit_price_spin.setMaximum(9999999.99)
        self.unit_price_spin.setDecimals(2)
        self.unit_price_spin.setSuffix(" ر.س")
        self.unit_price_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("سعر الوحدة:", self.unit_price_spin)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(9999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.setSuffix(" ر.س")
        self.discount_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("الخصم:", self.discount_spin)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMinimum(0.0)
        self.tax_spin.setMaximum(9999999.99)
        self.tax_spin.setDecimals(2)
        self.tax_spin.setSuffix(" ر.س")
        self.tax_spin.valueChanged.connect(self.calculate_total)
        form_layout.addRow("الضريبة:", self.tax_spin)

        self.total_label = QLabel("0.00 ر.س")
        self.total_label.setAlignment(Qt.AlignRight)
        form_layout.addRow("الإجمالي:", self.total_label)

        # Add buttons
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)

        self.save_button = QPushButton("حفظ")
        self.save_button.clicked.connect(self.accept)
        button_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        # Fill form if editing an existing item
        if item:
            self.description_edit.setText(item.description)
            self.quantity_spin.setValue(item.quantity)
            self.unit_price_spin.setValue(item.unit_price)
            self.discount_spin.setValue(item.discount)
            self.tax_spin.setValue(item.tax)

        # Calculate initial total
        self.calculate_total()

    def calculate_total(self):
        """Calculate the total for this item."""
        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()

        total = quantity * unit_price - discount + tax

        # Format with thousand separators
        from utils.currency_helper import format_thousands
        formatted_total = format_thousands(f"{total:.2f}")
        self.total_label.setText(f"{formatted_total} ر.س")

    def get_item_data(self):
        """Get the item data from the form.

        Returns:
            dict: Item data
        """
        quantity = self.quantity_spin.value()
        unit_price = self.unit_price_spin.value()
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()
        total = quantity * unit_price - discount + tax

        return {
            'description': self.description_edit.text(),
            'quantity': quantity,
            'unit_price': unit_price,
            'discount': discount,
            'tax': tax,
            'total': total
        }


class InvoiceEditorDialog(QDialog):
    """Dialog for creating and editing invoices."""

    def __init__(self, parent=None, db_manager=None, invoice=None, items=None, view_only=False, currency_manager=None):
        """Initialize the invoice editor dialog.

        Args:
            parent: Parent widget
            db_manager: Database manager instance
            invoice (Invoice, optional): Invoice to edit. Defaults to None.
            items (list, optional): Invoice items. Defaults to None.
            view_only (bool, optional): Whether to open in view-only mode. Defaults to False.
            currency_manager: Currency manager instance
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.invoice = invoice
        self.view_only = view_only
        self.items_list = []
        self.currency_manager = currency_manager
        self.current_currency = None
        self.primary_currency = None

        # Set window properties to full screen size with improved styling
        self.setWindowTitle(f"فاتورة رقم {invoice.invoice_number}" if invoice else "فاتورة جديدة")
        self.setMinimumSize(1200, 900)

        # Force window to be maximized (full screen)
        self.showMaximized()

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Set window background color with improved styling
        self.setStyleSheet("""
            QDialog {
                background-color: #f5f5f5;
            }
        """)

        # Create main layout as horizontal layout with two main sections
        layout = QHBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # Create left (red) and right (green) containers - SWAPPED as per request
        left_container = QWidget()  # Red container for invoice details
        right_container = QWidget() # Green container for totals

        # Set styling for containers
        container_style = """
            background-color: white;
            border-radius: 8px;
            border: 1px solid #cccccc;
        """
        left_container.setStyleSheet(container_style)
        right_container.setStyleSheet(container_style)

        # Create layouts for left and right containers
        left_layout = QVBoxLayout(left_container)
        right_layout = QVBoxLayout(right_container)

        # Set margins and spacing
        left_layout.setContentsMargins(10, 10, 10, 10)
        right_layout.setContentsMargins(10, 10, 10, 10)
        left_layout.setSpacing(10)
        right_layout.setSpacing(10)

        # Add containers to main layout with appropriate sizing
        layout.addWidget(left_container, 70)  # 70% of width (red) - for invoice details
        layout.addWidget(right_container, 30)  # 30% of width (green) - for totals

        # Create a title label for the invoice (left side - red)
        left_title_label = QLabel("معلومات الفاتورة")
        left_title_label.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            color: #0066cc;
            padding: 5px;
            margin-bottom: 5px;
        """)
        left_title_label.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(left_title_label)

        # Create a title label for the totals (right side - green)
        right_title_label = QLabel("الإجماليات")
        right_title_label.setStyleSheet("""
            font-size: 13pt;
            font-weight: bold;
            color: #0066cc;
            padding: 3px;
            margin-bottom: 3px;
        """)
        right_title_label.setAlignment(Qt.AlignCenter)
        right_layout.addWidget(right_title_label)

        # Create header container (left side - red)
        header_container = QWidget()
        header_container.setStyleSheet("""
            background-color: white;
            border-radius: 5px;
            border: 1px solid #cccccc;
        """)
        left_layout.addWidget(header_container)

        # Create a form layout for header fields
        header_layout = QFormLayout(header_container)
        header_layout.setContentsMargins(10, 10, 10, 10)
        header_layout.setHorizontalSpacing(10)
        header_layout.setVerticalSpacing(10)
        header_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # Define common styles for header fields to match original
        header_label_style = """
            font-size: 12pt;
            font-weight: bold;
            color: #333333;
            padding: 2px;
            margin-right: 5px;
        """

        header_field_style = """
            font-size: 13pt;
            padding: 8px;
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: #ffffff;
            min-width: 300px;
            min-height: 35px;
            color: #333333;
        """

        # Invoice number
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        self.invoice_number_edit.setStyleSheet(header_field_style)
        if invoice:
            self.invoice_number_edit.setText(invoice.invoice_number)

        invoice_number_label = QLabel("رقم الفاتورة:")
        invoice_number_label.setStyleSheet(header_label_style)
        invoice_number_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        header_layout.addRow(invoice_number_label, self.invoice_number_edit)

        # Customer selection
        self.customer_combo = QComboBox()
        self.customer_combo.setStyleSheet(header_field_style)
        self.load_customers()
        if invoice and invoice.customer_id:
            # Set the current customer
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == invoice.customer_id:
                    self.customer_combo.setCurrentIndex(i)
                    break

        customer_label = QLabel("العميل:")
        customer_label.setStyleSheet(header_label_style)
        customer_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        header_layout.addRow(customer_label, self.customer_combo)

        # Issue date
        self.issue_date_edit = QDateEdit()
        self.issue_date_edit.setCalendarPopup(True)
        self.issue_date_edit.setDate(QDate.currentDate())
        self.issue_date_edit.setStyleSheet(header_field_style)
        if invoice and invoice.issue_date:
            self.issue_date_edit.setDate(QDate.fromString(invoice.issue_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        issue_date_label = QLabel("تاريخ الإصدار:")
        issue_date_label.setStyleSheet(header_label_style)
        issue_date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        header_layout.addRow(issue_date_label, self.issue_date_edit)

        # Due date
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        self.due_date_edit.setStyleSheet(header_field_style)
        if invoice and invoice.due_date:
            self.due_date_edit.setDate(QDate.fromString(invoice.due_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        due_date_label = QLabel("تاريخ الاستحقاق:")
        due_date_label.setStyleSheet(header_label_style)
        due_date_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        header_layout.addRow(due_date_label, self.due_date_edit)

        # Status
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet(header_field_style)
        self.status_combo.addItem("مسودة", "draft")
        self.status_combo.addItem("منتظرة", "pending")
        self.status_combo.addItem("مدفوع جزئياً", "partially_paid")
        self.status_combo.addItem("مدفوعة", "paid")
        self.status_combo.addItem("ملغاة", "cancelled")
        if invoice and invoice.status:
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == invoice.status:
                    self.status_combo.setCurrentIndex(i)
                    break

        status_label = QLabel("الحالة:")
        status_label.setStyleSheet(header_label_style)
        status_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        header_layout.addRow(status_label, self.status_combo)

        # Create a section title for products and services (left side - red)
        items_title = QLabel("المنتجات والخدمات")
        items_title.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            color: #0066cc;
            padding: 5px;
            margin-top: 10px;
            margin-bottom: 5px;
        """)
        items_title.setAlignment(Qt.AlignCenter)
        left_layout.addWidget(items_title)

        # Create items container with white background (left side - red)
        items_container = QWidget()
        items_container.setStyleSheet("""
            background-color: white;
            border-radius: 5px;
            border: 1px solid #cccccc;
        """)
        left_layout.addWidget(items_container)

        # Set items container to expand and take available space
        items_container.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # Create layout for items
        items_layout = QVBoxLayout(items_container)
        items_layout.setContentsMargins(10, 10, 10, 10)
        items_layout.setSpacing(10)

        # Items toolbar with original styling
        items_toolbar = QHBoxLayout()
        items_toolbar.setSpacing(5)
        items_layout.addLayout(items_toolbar)

        # Button style for the original look with larger size
        select_button_style = """
            QPushButton {
                background-color: #008080;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13pt;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #006666;
            }
            QPushButton:pressed {
                background-color: #004d4d;
            }
        """

        add_button_style = """
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 13pt;
                min-height: 40px;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """

        # Select product button (on the right)
        self.select_product_button = QPushButton("اختيار من المنتجات والخدمات")
        self.select_product_button.setStyleSheet(select_button_style)
        self.select_product_button.clicked.connect(self.select_product)
        items_toolbar.addWidget(self.select_product_button)

        # Add item button (on the left)
        self.add_item_button = QPushButton("إضافة منتج/خدمة يدوياً")
        self.add_item_button.setStyleSheet(add_button_style)
        self.add_item_button.clicked.connect(self.add_item)
        items_toolbar.addWidget(self.add_item_button)

        # Items table with original styling
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            "الوصف", "الكمية", "سعر الوحدة", "الخصم", "الضريبة", "الإجمالي", ""
        ])

        # Set table to expand and take available space
        self.items_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.items_table.setMinimumHeight(300)  # Increased minimum height

        # Configure column sizes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 6):
            self.items_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)
        self.items_table.setColumnWidth(6, 100)

        # Configure table behavior
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setShowGrid(True)
        self.items_table.setGridStyle(Qt.SolidLine)

        # Set table style to match original
        self.items_table.setStyleSheet("""
            QTableWidget {
                background-color: #FFFFFF;
                alternate-background-color: #F5F5F5;
                border: 1px solid #CCCCCC;
                gridline-color: #DDDDDD;
                selection-background-color: #E6F2FF;
                selection-color: #000000;
                font-size: 11pt;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #EEEEEE;
            }
            QTableWidget::item:selected {
                background-color: #E6F2FF;
                color: #000000;
            }
            QHeaderView::section {
                background-color: #0D47A1;
                color: white;
                padding: 8px;
                border: none;
                font-weight: bold;
                font-size: 11pt;
            }
        """)
        self.items_table.setAlternatingRowColors(True)
        items_layout.addWidget(self.items_table, 1)

        # Create notes container (left side - red)
        notes_container = QWidget()
        notes_container.setStyleSheet("""
            background-color: white;
            border-radius: 8px;
            border: 1px solid #cccccc;
        """)
        left_layout.addWidget(notes_container)

        # Create a title for notes section
        notes_title = QLabel("ملاحظات")
        notes_title.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            color: #0066cc;
            padding: 5px;
            margin-bottom: 5px;
            border-bottom: 1px solid #0066cc;
        """)
        notes_title.setAlignment(Qt.AlignCenter)

        # Create a form layout for notes
        notes_layout = QVBoxLayout(notes_container)
        notes_layout.setContentsMargins(10, 10, 10, 10)
        notes_layout.setSpacing(5)
        notes_layout.addWidget(notes_title)

        # Create totals container (right side - green)
        totals_container = QWidget()
        totals_container.setStyleSheet("""
            background-color: white;
            border-radius: 6px;
            border: 1px solid #cccccc;
        """)
        right_layout.addWidget(totals_container, 1)  # Give it stretch factor to take available space

        # Create a form layout for totals with appropriate space
        totals_layout = QVBoxLayout(totals_container)
        totals_layout.setContentsMargins(10, 10, 10, 10)  # Reduced margins
        totals_layout.setSpacing(8)  # Reduced spacing

        # Create a grid layout for totals fields with appropriate spacing
        # Using 2 columns for better organization (label, value)
        totals_grid = QGridLayout()
        totals_layout.addLayout(totals_grid)
        totals_grid.setVerticalSpacing(10)  # Reduced vertical spacing
        totals_grid.setHorizontalSpacing(8)  # Reduced horizontal spacing
        totals_grid.setContentsMargins(5, 5, 5, 5)  # Reduced margins around the grid

        # Style for the notes text edit
        notes_edit_style = """
            font-size: 12pt;
            padding: 5px;
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: #ffffff;
        """

        # Create notes text edit - smaller height
        self.notes_edit = QTextEdit()
        self.notes_edit.setMinimumHeight(100)  # Reduced height
        self.notes_edit.setMaximumHeight(120)  # Added maximum height
        self.notes_edit.setStyleSheet(notes_edit_style)
        if invoice and invoice.notes:
            self.notes_edit.setText(invoice.notes)
        notes_layout.addWidget(self.notes_edit)

        # Removed duplicate currency selection code

        # Define common style for labels with natural size and improved readability
        label_style = """
            font-weight: bold;
            font-size: 11pt;
            padding: 4px;
            text-align: right;
            margin-right: 5px;
            color: #333333;
            background-color: #f5f5f5;
            border-radius: 3px;
            min-width: 80px;
        """

        # Define common style for value fields with natural size and improved readability
        value_style = """
            font-size: 11pt;
            font-weight: bold;
            padding: 4px 6px;
            border: 1px solid #BDBDBD;
            border-radius: 3px;
            min-width: 100px;
            background-color: #FFFFFF;
        """

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Create all fields with consistent styling

        # 1. Currency field (top left) - improved styling with smaller size
        currency_label = QLabel("العملة:")
        currency_label.setStyleSheet(label_style + """
            color: #0d47a1;
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
        """)
        currency_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.currency_combo = QComboBox()
        self.currency_combo.setStyleSheet("""
            font-size: 11pt;
            padding: 3px;
            border: 1px solid #1976d2;
            border-radius: 3px;
            min-width: 100px;
            background-color: #ffffff;
            color: #0d47a1;
            font-weight: bold;
        """)

        if self.currency_manager:
            self.load_currencies()
            self.currency_combo.currentIndexChanged.connect(self.on_currency_changed)

            # Always set to primary currency for existing invoices
            for i in range(self.currency_combo.count()):
                if self.currency_combo.itemData(i) == self.primary_currency.id:
                    self.currency_combo.setCurrentIndex(i)
                    break

        # 2. Subtotal field (top right) - enhanced visibility
        subtotal_label = QLabel("المجموع الفرعي:")
        subtotal_label.setStyleSheet(label_style + """
            color: #424242;
            background-color: #F5F5F5;
            border: 1px solid #E0E0E0;
            min-width: 120px;
        """)
        subtotal_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.subtotal_label = QLabel(f"0.00 {currency_symbol}")
        self.subtotal_label.setAlignment(Qt.AlignCenter)
        self.subtotal_label.setStyleSheet(value_style + """
            background-color: #F5F5F5;
            color: #424242;
            border: 2px solid #9E9E9E;
            min-width: 180px;
            max-width: 220px;
            font-size: 15pt;
        """)

        # 3. Discount field (middle left) - enhanced visibility
        discount_label = QLabel("الخصم:")
        discount_label.setStyleSheet(label_style + """
            color: #5D4037;
            background-color: #EFEBE9;
            border: 1px solid #D7CCC8;
            min-width: 120px;
        """)
        discount_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(9999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.setSuffix(f" {currency_symbol}")
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        self.discount_spin.setStyleSheet("""
            font-size: 11pt;
            border: 1px solid #8D6E63;
            border-radius: 3px;
            padding: 3px;
            min-width: 100px;
            background-color: #EFEBE9;
            color: #5D4037;
            font-weight: bold;
        """)
        if invoice:
            self.discount_spin.setValue(invoice.discount)

        # 4. Tax field (middle right) - enhanced visibility
        tax_label = QLabel("الضريبة:")
        tax_label.setStyleSheet(label_style + """
            color: #455A64;
            background-color: #ECEFF1;
            border: 1px solid #CFD8DC;
            min-width: 120px;
        """)
        tax_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMinimum(0.0)
        self.tax_spin.setMaximum(9999999.99)
        self.tax_spin.setDecimals(2)
        self.tax_spin.setSuffix(f" {currency_symbol}")
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        self.tax_spin.setStyleSheet("""
            font-size: 11pt;
            border: 1px solid #607D8B;
            border-radius: 3px;
            padding: 3px;
            min-width: 100px;
            background-color: #ECEFF1;
            color: #455A64;
            font-weight: bold;
        """)
        if invoice:
            self.tax_spin.setValue(invoice.tax)

        # 5. Total field (bottom left) - enhanced visibility
        total_label = QLabel("الإجمالي:")
        total_label.setStyleSheet(label_style + """
            color: #2E7D32;
            font-size: 11pt;
            background-color: #E8F5E9;
            border: 1px solid #A5D6A7;
            min-width: 80px;
        """)
        total_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.total_label = QLabel(f"0.00 {currency_symbol}")
        self.total_label.setAlignment(Qt.AlignCenter)
        self.total_label.setStyleSheet("""
            background-color: #E8F5E9;
            border: 1px solid #66BB6A;
            border-radius: 3px;
            color: #2E7D32;
            font-size: 11pt;
            font-weight: bold;
            padding: 3px 6px;
            min-width: 100px;
        """)

        # 6. Paid Amount field (bottom right) - enhanced visibility
        paid_label = QLabel("المدفوع:")
        paid_label.setStyleSheet(label_style + """
            color: #00695C;
            background-color: #E0F2F1;
            border: 1px solid #B2DFDB;
            font-size: 11pt;
            min-width: 80px;
        """)
        paid_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.amount_paid_spin = QDoubleSpinBox()
        self.amount_paid_spin.setMinimum(0.0)
        self.amount_paid_spin.setMaximum(9999999.99)
        self.amount_paid_spin.setDecimals(2)
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")
        self.amount_paid_spin.valueChanged.connect(self.update_payment_info)
        self.amount_paid_spin.setStyleSheet("""
            font-size: 11pt;
            border: 1px solid #00897B;
            border-radius: 3px;
            padding: 3px 6px;
            min-width: 100px;
            background-color: #E0F2F1;
            color: #00695C;
            font-weight: bold;
        """)
        if invoice:
            self.amount_paid_spin.setValue(invoice.amount_paid)

        # 7. Due Amount field (extra row) - enhanced visibility
        due_label = QLabel("المتبقي:")
        due_label.setStyleSheet(label_style + """
            color: #c62828;
            background-color: #FFEBEE;
            border: 1px solid #FFCDD2;
            font-size: 11pt;
            min-width: 80px;
        """)
        due_label.setAlignment(Qt.AlignRight | Qt.AlignVCenter)

        self.amount_due_spin = QDoubleSpinBox()
        self.amount_due_spin.setMinimum(0.0)
        self.amount_due_spin.setMaximum(9999999.99)
        self.amount_due_spin.setDecimals(2)
        self.amount_due_spin.setSuffix(f" {currency_symbol}")
        self.amount_due_spin.valueChanged.connect(self.update_payment_from_due)
        self.amount_due_spin.setStyleSheet("""
            color: #C62828;
            font-weight: bold;
            font-size: 11pt;
            background-color: #FFEBEE;
            border: 1px solid #EF5350;
            border-radius: 3px;
            padding: 3px 6px;
            min-width: 100px;
        """)
        if invoice:
            self.amount_due_spin.setValue(invoice.amount_due if hasattr(invoice, 'amount_due') else 0.0)

        # Add all widgets to the grid with improved layout and spacing
        # First row
        totals_grid.addWidget(currency_label, 0, 0)
        totals_grid.addWidget(self.currency_combo, 0, 1)

        totals_grid.addWidget(subtotal_label, 0, 2)
        totals_grid.addWidget(self.subtotal_label, 0, 3)

        # Second row - add more spacing
        spacer_widget1 = QWidget()
        spacer_widget1.setMinimumHeight(15)
        totals_grid.addWidget(spacer_widget1, 1, 0, 1, 4)

        # Third row
        totals_grid.addWidget(discount_label, 2, 0)
        totals_grid.addWidget(self.discount_spin, 2, 1)

        totals_grid.addWidget(tax_label, 2, 2)
        totals_grid.addWidget(self.tax_spin, 2, 3)

        # Fourth row - add more spacing
        spacer_widget2 = QWidget()
        spacer_widget2.setMinimumHeight(15)
        totals_grid.addWidget(spacer_widget2, 3, 0, 1, 4)

        # Fifth row
        totals_grid.addWidget(total_label, 4, 0)
        totals_grid.addWidget(self.total_label, 4, 1)

        totals_grid.addWidget(paid_label, 4, 2)
        totals_grid.addWidget(self.amount_paid_spin, 4, 3)

        # Sixth row - add more spacing
        spacer_widget3 = QWidget()
        spacer_widget3.setMinimumHeight(15)
        totals_grid.addWidget(spacer_widget3, 5, 0, 1, 4)

        # Seventh row
        totals_grid.addWidget(due_label, 6, 0)
        totals_grid.addWidget(self.amount_due_spin, 6, 1)

        # Add empty widget to balance the layout
        empty_label = QLabel("")
        totals_grid.addWidget(empty_label, 6, 2)
        empty_widget = QWidget()
        totals_grid.addWidget(empty_widget, 6, 3)

        # Add buttons with improved styling and visibility (right side - green)
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)  # Reduced spacing between buttons
        button_layout.setContentsMargins(10, 10, 10, 10)  # Reduced margins around buttons

        # Add a separator line above buttons for better visibility
        separator = QWidget()
        separator.setFixedHeight(2)
        separator.setStyleSheet("background-color: #bdbdbd;")
        right_layout.addWidget(separator)

        # Create save button with smaller styling and improved visibility
        self.save_button = QPushButton("حفظ")
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
                min-height: 35px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #1976d2;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)
        self.save_button.clicked.connect(self.save_invoice)

        # Create cancel button with smaller styling and improved visibility
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 12pt;
                min-width: 100px;
                min-height: 35px;
                margin: 5px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #b71c1c;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)

        # Add buttons to layout with improved visibility (right side - green)
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.save_button)

        # Add button layout to right container
        right_layout.addLayout(button_layout)

        # Ensure buttons are visible by adding extra space and setting fixed height
        spacer = QWidget()
        spacer.setMinimumHeight(20)
        right_layout.addWidget(spacer)

        # Create button box for signal connections (hidden)
        button_box = QDialogButtonBox()
        button_box.addButton(self.save_button, QDialogButtonBox.AcceptRole)
        button_box.addButton(self.cancel_button, QDialogButtonBox.RejectRole)
        button_box.setVisible(False)
        right_layout.addWidget(button_box)

        # Load items if provided
        if items:
            for item_row in items:
                item = InvoiceItem.from_db_row(item_row)
                self.items_list.append(item)
                self.add_item_to_table(item)

        # Calculate initial totals
        self.calculate_totals()

        # Set view-only mode if specified
        if view_only:
            self.set_view_only_mode()

    def set_view_only_mode(self):
        """Set the dialog to view-only mode."""
        self.customer_combo.setEnabled(False)
        self.issue_date_edit.setEnabled(False)
        self.due_date_edit.setEnabled(False)
        self.status_combo.setEnabled(False)
        self.add_item_button.setEnabled(False)
        self.select_product_button.setEnabled(False)
        self.notes_edit.setReadOnly(True)
        self.discount_spin.setReadOnly(True)
        self.tax_spin.setReadOnly(True)

        # Hide edit/delete buttons in the items table
        for row in range(self.items_table.rowCount()):
            self.items_table.removeCellWidget(row, 6)
            self.items_table.setItem(row, 6, QTableWidgetItem(""))

        # Change save button to close
        self.save_button.setText("إغلاق")
        self.save_button.clicked.disconnect()
        self.save_button.clicked.connect(self.accept)

        # Hide cancel button
        self.cancel_button.hide()

    def load_customers(self):
        """Load customers from the database."""
        query = "SELECT id, name FROM customers ORDER BY name"
        rows = self.db_manager.execute_query(query)

        self.customer_combo.clear()
        for row in rows:
            self.customer_combo.addItem(row['name'], row['id'])

    def load_currencies(self):
        """Load currencies from the database."""
        if not self.currency_manager:
            return

        currencies = self.currency_manager.get_active_currencies()

        self.currency_combo.clear()

        # Find primary currency
        for currency in currencies:
            if currency.is_primary:
                self.primary_currency = currency
                break

        # Add currencies to combo box
        for currency in currencies:
            self.currency_combo.addItem(f"{currency.name} ({currency.code})", currency.id)

            # Set current currency if this is the primary currency
            if currency.is_primary:
                self.current_currency = currency
                self.currency_combo.setCurrentIndex(self.currency_combo.count() - 1)

    def on_currency_changed(self, index):
        """Handle currency change.

        Args:
            index (int): New index in the currency combo
        """
        if index < 0 or not self.currency_manager:
            return

        # Get the selected currency ID
        currency_id = self.currency_combo.itemData(index)

        # Get the currency object
        new_currency = self.currency_manager.get_currency_by_id(currency_id)

        if not new_currency or new_currency.id == self.current_currency.id:
            return

        # Store old currency for conversion
        old_currency = self.current_currency
        self.current_currency = new_currency

        # Update currency symbols in the UI
        currency_suffix = f" {new_currency.symbol}"

        # Update unit price suffix in item dialog
        for row in range(self.items_table.rowCount()):
            # Update unit price display
            unit_price_item = self.items_table.item(row, 2)
            if unit_price_item:
                # Extract numeric value
                try:
                    old_value = float(unit_price_item.text().split()[0])
                    # Convert to new currency
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    unit_price_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

            # Update discount display
            discount_item = self.items_table.item(row, 3)
            if discount_item:
                try:
                    old_value = float(discount_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    discount_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

            # Update tax display
            tax_item = self.items_table.item(row, 4)
            if tax_item:
                try:
                    old_value = float(tax_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    tax_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

            # Update total display
            total_item = self.items_table.item(row, 5)
            if total_item:
                try:
                    old_value = float(total_item.text().split()[0])
                    new_value = convert_currency(old_value, old_currency.exchange_rate, new_currency.exchange_rate)
                    total_item.setText(f"{new_value:.2f} {new_currency.symbol}")
                except:
                    pass

        # Update items in the list
        for item in self.items_list:
            item.unit_price = convert_currency(item.unit_price, old_currency.exchange_rate, new_currency.exchange_rate)
            item.discount = convert_currency(item.discount, old_currency.exchange_rate, new_currency.exchange_rate)
            item.tax = convert_currency(item.tax, old_currency.exchange_rate, new_currency.exchange_rate)
            item.total = convert_currency(item.total, old_currency.exchange_rate, new_currency.exchange_rate)

        # Update discount and tax spinboxes
        self.discount_spin.setSuffix(currency_suffix)
        old_discount = self.discount_spin.value()
        new_discount = convert_currency(old_discount, old_currency.exchange_rate, new_currency.exchange_rate)
        self.discount_spin.setValue(new_discount)

        self.tax_spin.setSuffix(currency_suffix)
        old_tax = self.tax_spin.value()
        new_tax = convert_currency(old_tax, old_currency.exchange_rate, new_currency.exchange_rate)
        self.tax_spin.setValue(new_tax)

        # Update amount paid and amount due spinboxes
        if hasattr(self, 'amount_paid_spin'):
            self.amount_paid_spin.setSuffix(currency_suffix)
            old_amount_paid = self.amount_paid_spin.value()
            new_amount_paid = convert_currency(old_amount_paid, old_currency.exchange_rate, new_currency.exchange_rate)
            self.amount_paid_spin.setValue(new_amount_paid)

        if hasattr(self, 'amount_due_spin'):
            self.amount_due_spin.setSuffix(currency_suffix)

        # Recalculate totals
        self.calculate_totals()

    def add_item(self):
        """Add a new item to the invoice manually."""
        dialog = InvoiceItemDialog(self)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف المنتج أو الخدمة")
                return

            # Create item object
            item = InvoiceItem(
                description=item_data['description'],
                quantity=item_data['quantity'],
                unit_price=item_data['unit_price'],
                discount=item_data['discount'],
                tax=item_data['tax'],
                total=item_data['total']
            )

            # Add to list and table
            self.items_list.append(item)
            self.add_item_to_table(item)

            # Recalculate totals
            self.calculate_totals()

    def select_product(self):
        """Select a product or service from the products list."""
        # Create products view in select mode
        products_dialog = QDialog(self)
        products_dialog.setWindowTitle("اختيار منتج أو خدمة")
        products_dialog.setMinimumSize(800, 600)

        # Set RTL layout direction
        products_dialog.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        layout = QVBoxLayout(products_dialog)

        # Create products view
        products_view = ProductsView(self.db_manager, select_mode=True)
        # Pass currency manager if available
        if hasattr(self, 'currency_manager') and self.currency_manager:
            products_view.currency_manager = self.currency_manager
        layout.addWidget(products_view)

        # Connect product selection signal
        products_view.product_selected.connect(lambda product: self.add_product_to_invoice(product, products_dialog))

        # Show dialog
        products_dialog.exec()

    def add_product_to_invoice(self, product, dialog=None):
        """Add a product to the invoice.

        Args:
            product: Product object
            dialog: Dialog to close after adding the product
        """
        # Check inventory if this is a product (not a service) and inventory tracking is enabled
        if product.type == 'product' and product.track_inventory:
            # Check if we have enough stock using the safe helper function
            from utils.inventory_helper import check_product_availability_safely

            is_available = check_product_availability_safely(self.db_manager, product.id, 1, self)
            if not is_available:
                QMessageBox.warning(
                    self,
                    "المخزون غير كافي",
                    f"المخزون غير كافي للمنتج '{product.name}'. الكمية المتاحة: {product.stock_quantity}."
                )
                # Don't close the dialog
                return

        # Get tax settings
        tax_query = "SELECT value FROM settings WHERE key = 'tax_rate'"
        tax_rows = self.db_manager.execute_query(tax_query)
        default_tax_rate = float(tax_rows[0]['value']) if tax_rows else 15.0

        # Calculate tax amount
        tax_amount = (product.price * product.tax_rate / 100) if product.tax_rate > 0 else (product.price * default_tax_rate / 100)

        # Create item object
        item = InvoiceItem(
            description=product.name,
            quantity=1.0,
            unit_price=product.price,
            discount=0.0,
            tax=tax_amount,
            total=product.price + tax_amount
        )

        # Store product ID for inventory tracking
        item.product_id = product.id
        item.is_product = product.type == 'product'
        item.track_inventory = product.track_inventory if product.type == 'product' else False

        # Add to list and table
        self.items_list.append(item)
        self.add_item_to_table(item)

        # Recalculate totals
        self.calculate_totals()

        # Close dialog if provided
        if dialog:
            dialog.accept()

    def add_item_to_table(self, item):
        """Add an item to the table.

        Args:
            item (InvoiceItem): Item to add
        """
        row_position = self.items_table.rowCount()
        self.items_table.insertRow(row_position)

        # Format with thousand separators
        from utils.currency_helper import format_thousands

        # Set item data
        self.items_table.setItem(row_position, 0, QTableWidgetItem(item.description))
        self.items_table.setItem(row_position, 1, QTableWidgetItem(f"{item.quantity}"))
        self.items_table.setItem(row_position, 2, QTableWidgetItem(format_thousands(f"{item.unit_price:.2f}")))
        self.items_table.setItem(row_position, 3, QTableWidgetItem(format_thousands(f"{item.discount:.2f}")))
        self.items_table.setItem(row_position, 4, QTableWidgetItem(format_thousands(f"{item.tax:.2f}")))

        total_item = QTableWidgetItem(format_thousands(f"{item.total:.2f}"))
        total_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
        self.items_table.setItem(row_position, 5, total_item)

        # Add edit/delete buttons if not in view-only mode
        if not self.view_only:
            buttons_widget = QWidget()
            buttons_layout = QHBoxLayout(buttons_widget)
            buttons_layout.setContentsMargins(2, 2, 2, 2)
            buttons_layout.setSpacing(4)

            edit_button = QPushButton("تعديل")
            edit_button.setMaximumWidth(55)
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #00897b;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 4px;
                    font-weight: bold;
                    font-size: 10pt;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #00796b;
                }
            """)
            edit_button.clicked.connect(lambda: self.edit_item(row_position))
            buttons_layout.addWidget(edit_button)

            delete_button = QPushButton("حذف")
            delete_button.setMaximumWidth(55)
            delete_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    padding: 4px;
                    font-weight: bold;
                    font-size: 10pt;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
            """)
            delete_button.clicked.connect(lambda: self.delete_item(row_position))
            buttons_layout.addWidget(delete_button)

            self.items_table.setCellWidget(row_position, 6, buttons_widget)

    def edit_item(self, row):
        """Edit an item.

        Args:
            row (int): Row index
        """
        item = self.items_list[row]

        dialog = InvoiceItemDialog(self, item)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف المنتج أو الخدمة")
                return

            # Update item
            item.description = item_data['description']
            item.quantity = item_data['quantity']
            item.unit_price = item_data['unit_price']
            item.discount = item_data['discount']
            item.tax = item_data['tax']
            item.total = item_data['total']

            # Format with thousand separators
            from utils.currency_helper import format_thousands

            # Update table
            self.items_table.item(row, 0).setText(item.description)
            self.items_table.item(row, 1).setText(f"{item.quantity}")
            self.items_table.item(row, 2).setText(format_thousands(f"{item.unit_price:.2f}"))
            self.items_table.item(row, 3).setText(format_thousands(f"{item.discount:.2f}"))
            self.items_table.item(row, 4).setText(format_thousands(f"{item.tax:.2f}"))
            self.items_table.item(row, 5).setText(format_thousands(f"{item.total:.2f}"))

            # Recalculate totals
            self.calculate_totals()

    def delete_item(self, row):
        """Delete an item.

        Args:
            row (int): Row index
        """
        confirm = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا العنصر؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            # Remove from list and table
            self.items_list.pop(row)
            self.items_table.removeRow(row)

            # Recalculate totals
            self.calculate_totals()

    def calculate_totals(self):
        """Calculate invoice totals."""
        subtotal = sum(item.total for item in self.items_list)
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()
        total = subtotal - discount + tax

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Format with thousand separators
        from utils.currency_helper import format_thousands
        formatted_subtotal = format_thousands(f"{subtotal:.2f}")
        formatted_total = format_thousands(f"{total:.2f}")

        # Update all labels with formatted values
        self.subtotal_label.setText(f"{formatted_subtotal} {currency_symbol}")
        self.total_label.setText(f"{formatted_total} {currency_symbol}")

        # Update discount and tax spinboxes suffix with formatted values
        self.discount_spin.setSuffix(f" {currency_symbol}")
        self.tax_spin.setSuffix(f" {currency_symbol}")

        # Update amount paid and due spinboxes suffix
        if hasattr(self, 'amount_paid_spin'):
            self.amount_paid_spin.setSuffix(f" {currency_symbol}")

        if hasattr(self, 'amount_due_spin'):
            self.amount_due_spin.setSuffix(f" {currency_symbol}")

        # Apply consistent styling to labels with improved contrast and spacing
        self.subtotal_label.setStyleSheet("""
            font-size: 14pt;
            font-weight: bold;
            padding: 12px 15px;
            background-color: #f5f5f5;
            border: 2px solid #9E9E9E;
            border-radius: 6px;
            margin: 6px 0;
            color: #212121;
            min-width: 180px;
            max-width: 220px;
            text-align: center;
        """)

        self.total_label.setStyleSheet("""
            font-size: 18pt;
            font-weight: bold;
            padding: 12px 15px;
            background-color: #E8F5E9;
            border: 3px solid #66BB6A;
            border-radius: 8px;
            margin: 6px 0;
            color: #2E7D32;
            min-width: 200px;
            max-width: 250px;
            text-align: center;
        """)

        # Update payment info after total is calculated
        if hasattr(self, 'amount_paid_spin'):
            self.update_payment_info()

    def update_payment_info(self):
        """Update payment information and status."""
        if not hasattr(self, 'amount_paid_spin') or not hasattr(self, 'amount_due_spin'):
            return

        # Get total and amount paid
        total_text = self.total_label.text().split()[0].replace(',', '')
        try:
            total = float(total_text)
        except ValueError:
            total = 0.0

        amount_paid = self.amount_paid_spin.value()

        # Calculate amount due
        amount_due = max(0, total - amount_paid)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Update amount due spin without triggering valueChanged signal
        self.amount_due_spin.blockSignals(True)
        self.amount_due_spin.setValue(amount_due)
        self.amount_due_spin.blockSignals(False)

        # Update suffix with current currency
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")
        self.amount_due_spin.setSuffix(f" {currency_symbol}")

        # Apply consistent styling to spinboxes with improved contrast and spacing
        self.amount_paid_spin.setStyleSheet("""
            font-size: 18pt;
            font-weight: bold;
            padding: 12px 15px;
            background-color: #E0F2F1;
            border: 3px solid #00897B;
            border-radius: 6px;
            margin: 6px 0;
            color: #00695C;
            min-width: 200px;
            max-width: 250px;
        """)

        self.amount_due_spin.setStyleSheet("""
            font-size: 18pt;
            font-weight: bold;
            padding: 12px 15px;
            background-color: #FFEBEE;
            border: 3px solid #EF5350;
            border-radius: 6px;
            margin: 6px 0;
            color: #C62828;
            min-width: 200px;
            max-width: 250px;
        """)

    def update_payment_from_due(self):
        """Update amount paid when amount due is changed."""
        if not hasattr(self, 'amount_paid_spin') or not hasattr(self, 'amount_due_spin'):
            return

        # Get total
        total_text = self.total_label.text().split()[0].replace(',', '')
        try:
            total = float(total_text)
        except ValueError:
            total = 0.0

        # Get amount due
        amount_due = self.amount_due_spin.value()

        # Calculate amount paid
        amount_paid = max(0, total - amount_due)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Update amount paid spin without triggering valueChanged signal
        self.amount_paid_spin.blockSignals(True)
        self.amount_paid_spin.setValue(amount_paid)
        self.amount_paid_spin.blockSignals(False)

        # Update suffix with current currency
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")

        # Apply consistent styling to spinboxes with improved contrast and spacing
        self.amount_paid_spin.setStyleSheet("""
            font-size: 18pt;
            font-weight: bold;
            padding: 12px 15px;
            background-color: #E0F2F1;
            border: 3px solid #00897B;
            border-radius: 6px;
            margin: 6px 0;
            color: #00695C;
            min-width: 200px;
            max-width: 250px;
        """)

        # Update status based on payment
        if amount_paid <= 0:
            # If nothing paid, set to draft or pending
            if self.status_combo.currentData() == "paid" or self.status_combo.currentData() == "partially_paid":
                # Find pending status index
                for i in range(self.status_combo.count()):
                    if self.status_combo.itemData(i) == "pending":
                        self.status_combo.setCurrentIndex(i)
                        break
        elif amount_paid >= total:
            # If fully paid, set to paid
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == "paid":
                    self.status_combo.setCurrentIndex(i)
                    break
            # Ensure we don't overpay
            if amount_paid > total:
                self.amount_paid_spin.setValue(total)
        elif amount_paid > 0 and amount_paid < total:
            # If partially paid, set to partially paid
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == "partially_paid":
                    self.status_combo.setCurrentIndex(i)
                    break

    def save_invoice(self):
        """Save the invoice."""
        # Validate data
        if self.customer_combo.currentIndex() == -1:
            QMessageBox.warning(self, "خطأ", "يجب اختيار عميل")
            return

        if not self.items_list:
            QMessageBox.warning(self, "خطأ", "يجب إضافة منتج أو خدمة واحدة على الأقل")
            return

        # Get data
        customer_id = self.customer_combo.currentData()
        issue_date = self.issue_date_edit.date().toString("yyyy-MM-dd")
        due_date = self.due_date_edit.date().toString("yyyy-MM-dd")
        status = self.status_combo.currentData()
        notes = self.notes_edit.toPlainText()

        # Get currency ID
        currency_id = None
        if self.currency_manager and hasattr(self, 'currency_combo') and self.currency_combo.count() > 0:
            currency_id = self.currency_combo.currentData()

        subtotal = sum(item.total for item in self.items_list)
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()
        total = subtotal - discount + tax

        # Get payment information
        amount_paid = self.amount_paid_spin.value() if hasattr(self, 'amount_paid_spin') else 0.0
        amount_due = max(0, total - amount_paid)

        try:
            # Begin transaction
            conn = self.db_manager.connect()
            cursor = conn.cursor()

            if self.invoice.id:
                # Update existing invoice
                update_query = """
                UPDATE invoices
                SET customer_id = ?, issue_date = ?, due_date = ?, currency_id = ?, subtotal = ?,
                    discount = ?, tax = ?, total = ?, amount_paid = ?, amount_due = ?,
                    notes = ?, status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """
                cursor.execute(update_query, (
                    customer_id, issue_date, due_date, currency_id, subtotal,
                    discount, tax, total, amount_paid, amount_due,
                    notes, status, self.invoice.id
                ))

                # Delete existing items
                cursor.execute("DELETE FROM invoice_items WHERE invoice_id = ?", (self.invoice.id,))

                invoice_id = self.invoice.id
            else:
                # Insert new invoice
                insert_query = """
                INSERT INTO invoices (
                    invoice_number, customer_id, issue_date, due_date, currency_id,
                    subtotal, discount, tax, total, amount_paid, amount_due, notes, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                cursor.execute(insert_query, (
                    self.invoice.invoice_number, customer_id, issue_date, due_date, currency_id,
                    subtotal, discount, tax, total, amount_paid, amount_due, notes, status
                ))

                invoice_id = cursor.lastrowid

            # Insert items and update inventory
            from utils.inventory_helper import get_inventory_manager
            from models.inventory_transaction import InventoryTransaction

            # Get inventory manager with proper error handling
            inventory_manager = get_inventory_manager(self.db_manager, parent=self)

            for item in self.items_list:
                # Check if item has product_id attribute
                if hasattr(item, 'product_id') and item.product_id:
                    item_query = """
                    INSERT INTO invoice_items (
                        invoice_id, description, quantity, unit_price,
                        discount, tax, total, product_id, is_product, track_inventory
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """
                    cursor.execute(item_query, (
                        invoice_id, item.description, item.quantity, item.unit_price,
                        item.discount, item.tax, item.total, item.product_id,
                        1 if item.is_product else 0, 1 if item.track_inventory else 0
                    ))
                else:
                    item_query = """
                    INSERT INTO invoice_items (
                        invoice_id, description, quantity, unit_price,
                        discount, tax, total
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    cursor.execute(item_query, (
                        invoice_id, item.description, item.quantity, item.unit_price,
                        item.discount, item.tax, item.total
                    ))

                # Update inventory for products with inventory tracking enabled
                if hasattr(item, 'is_product') and item.is_product and hasattr(item, 'track_inventory') and item.track_inventory:
                    # Debug print to check inventory update
                    print(f"Updating inventory for product ID: {item.product_id}")
                    print(f"Transaction type: {InventoryTransaction.TYPE_SALE}")
                    print(f"Quantity: {item.quantity}")

                    # Only proceed if we have a valid inventory manager
                    if inventory_manager:
                        try:
                            # Create inventory transaction
                            transaction = InventoryTransaction(
                                product_id=item.product_id,
                                transaction_type=InventoryTransaction.TYPE_SALE,
                                quantity=item.quantity,
                                reference_type="invoice",
                                reference_id=invoice_id,
                                notes=f"بيع من خلال الفاتورة رقم {self.invoice.invoice_number}"
                            )

                            # Add transaction (this will also update the product stock)
                            transaction_id = inventory_manager.add_transaction(transaction)

                            # Debug print after inventory update
                            new_stock = inventory_manager.get_product_stock(item.product_id)
                            print(f"New stock quantity after update: {new_stock}, Transaction ID: {transaction_id}")
                        except Exception as e:
                            # Log the error but continue with invoice creation
                            print(f"Error updating inventory: {str(e)}")
                            QMessageBox.warning(
                                self,
                                "تحذير",
                                f"تم حفظ الفاتورة ولكن حدث خطأ أثناء تحديث المخزون: {str(e)}"
                            )
                    else:
                        # Warn the user that inventory wasn't updated
                        print("Warning: Inventory manager not available, skipping inventory update")
                        QMessageBox.warning(
                            self,
                            "تحذير",
                            "تم حفظ الفاتورة ولكن لم يتم تحديث المخزون بسبب عدم توفر مدير المخزون"
                        )

            # Commit transaction
            conn.commit()

            QMessageBox.information(self, "نجاح", "تم حفظ الفاتورة بنجاح")
            self.accept()
        except Exception as e:
            conn.rollback()
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة: {str(e)}")
        finally:
            self.db_manager.close()
