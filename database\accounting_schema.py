#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Accounting Schema for فوترها (Fawterha)
Defines the database tables for the accounting system
"""

def create_accounting_tables(connection):
    """Create accounting database tables if they don't exist.

    Args:
        connection: SQLite database connection
    """
    cursor = connection.cursor()

    # Create account_categories table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS account_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        type TEXT NOT NULL,
        description TEXT,
        parent_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (parent_id) REFERENCES account_categories (id) ON DELETE SET NULL
    )
    ''')

    # Create accounts table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        code TEXT NOT NULL UNIQUE,
        type TEXT NOT NULL,
        category_id INTEGER,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        balance REAL DEFAULT 0.0,
        currency_id INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES account_categories (id) ON DELETE SET NULL,
        FOREIGN KEY (currency_id) REFERENCES currencies (id) ON DELETE SET NULL
    )
    ''')

    # Create transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transaction_number TEXT NOT NULL UNIQUE,
        transaction_date DATE NOT NULL,
        description TEXT,
        reference_type TEXT,
        reference_id INTEGER,
        amount REAL NOT NULL,
        currency_id INTEGER,
        status TEXT DEFAULT 'posted',
        created_by TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (currency_id) REFERENCES currencies (id) ON DELETE SET NULL
    )
    ''')

    # Create transaction_details table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transaction_details (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        transaction_id INTEGER NOT NULL,
        account_id INTEGER NOT NULL,
        debit REAL DEFAULT 0.0,
        credit REAL DEFAULT 0.0,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE CASCADE,
        FOREIGN KEY (account_id) REFERENCES accounts (id) ON DELETE RESTRICT
    )
    ''')

    # Create accounting_periods table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS accounting_periods (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        start_date DATE NOT NULL,
        end_date DATE NOT NULL,
        is_closed INTEGER DEFAULT 0,
        closed_date DATE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Insert default account categories if they don't exist
    cursor.execute('''
    SELECT COUNT(*) FROM account_categories
    ''')
    category_count = cursor.fetchone()[0]

    if category_count == 0:
        # Add default account categories
        default_categories = [
            # Assets
            ('الأصول', '1', 'asset', 'الأصول والممتلكات', None),
            ('النقدية', '11', 'asset', 'الحسابات النقدية', 1),
            ('المخزون', '12', 'asset', 'المخزون والبضائع', 1),
            ('الذمم المدينة', '13', 'asset', 'المبالغ المستحقة للشركة', 1),
            ('الأصول الثابتة', '14', 'asset', 'الأصول طويلة الأجل', 1),
            
            # Liabilities
            ('الخصوم', '2', 'liability', 'الالتزامات والديون', None),
            ('الذمم الدائنة', '21', 'liability', 'المبالغ المستحقة على الشركة', 6),
            ('القروض', '22', 'liability', 'القروض والتمويل', 6),
            
            # Equity
            ('حقوق الملكية', '3', 'equity', 'حقوق أصحاب الشركة', None),
            ('رأس المال', '31', 'equity', 'رأس مال الشركة', 9),
            ('الأرباح المحتجزة', '32', 'equity', 'الأرباح المحتفظ بها', 9),
            
            # Revenue
            ('الإيرادات', '4', 'revenue', 'إيرادات الشركة', None),
            ('إيرادات المبيعات', '41', 'revenue', 'إيرادات من بيع المنتجات والخدمات', 12),
            ('إيرادات أخرى', '42', 'revenue', 'إيرادات من مصادر أخرى', 12),
            
            # Expenses
            ('المصروفات', '5', 'expense', 'مصروفات الشركة', None),
            ('تكلفة المبيعات', '51', 'expense', 'تكلفة البضائع المباعة', 15),
            ('المصروفات التشغيلية', '52', 'expense', 'المصروفات اليومية للتشغيل', 15),
            ('مصروفات الرواتب', '53', 'expense', 'رواتب الموظفين', 15),
            ('مصروفات أخرى', '54', 'expense', 'مصروفات متنوعة', 15)
        ]

        for name, code, type, description, parent_id in default_categories:
            cursor.execute('''
            INSERT INTO account_categories (name, code, type, description, parent_id)
            VALUES (?, ?, ?, ?, ?)
            ''', (name, code, type, description, parent_id))

    # Insert default accounts if they don't exist
    cursor.execute('''
    SELECT COUNT(*) FROM accounts
    ''')
    account_count = cursor.fetchone()[0]

    if account_count == 0:
        # Get primary currency ID
        cursor.execute('''
        SELECT id FROM currencies WHERE is_primary = 1 LIMIT 1
        ''')
        currency_id = cursor.fetchone()[0]

        # Add default accounts
        default_accounts = [
            # Cash accounts
            ('الصندوق', '1101', 'asset', 2, 'النقدية في الصندوق', currency_id),
            ('البنك', '1102', 'asset', 2, 'الحساب البنكي الرئيسي', currency_id),
            
            # Inventory accounts
            ('مخزون البضائع', '1201', 'asset', 3, 'مخزون البضائع للبيع', currency_id),
            
            # Accounts Receivable
            ('ذمم العملاء', '1301', 'asset', 4, 'المبالغ المستحقة من العملاء', currency_id),
            
            # Fixed Assets
            ('أثاث ومعدات', '1401', 'asset', 5, 'الأثاث والمعدات المكتبية', currency_id),
            
            # Accounts Payable
            ('ذمم الموردين', '2101', 'liability', 7, 'المبالغ المستحقة للموردين', currency_id),
            
            # Loans
            ('قروض قصيرة الأجل', '2201', 'liability', 8, 'القروض قصيرة الأجل', currency_id),
            
            # Capital
            ('رأس المال', '3101', 'equity', 10, 'رأس مال الشركة', currency_id),
            
            # Retained Earnings
            ('الأرباح المحتجزة', '3201', 'equity', 11, 'الأرباح المحتفظ بها', currency_id),
            
            # Sales Revenue
            ('إيرادات المبيعات', '4101', 'revenue', 13, 'إيرادات من بيع المنتجات', currency_id),
            ('إيرادات الخدمات', '4102', 'revenue', 13, 'إيرادات من تقديم الخدمات', currency_id),
            
            # Cost of Sales
            ('تكلفة البضاعة المباعة', '5101', 'expense', 16, 'تكلفة البضائع المباعة', currency_id),
            
            # Operating Expenses
            ('مصروفات الإيجار', '5201', 'expense', 17, 'إيجار المكتب أو المحل', currency_id),
            ('مصروفات الكهرباء والماء', '5202', 'expense', 17, 'فواتير الكهرباء والماء', currency_id),
            
            # Salary Expenses
            ('الرواتب والأجور', '5301', 'expense', 18, 'رواتب وأجور الموظفين', currency_id)
        ]

        for name, code, type, category_id, description, currency_id in default_accounts:
            cursor.execute('''
            INSERT INTO accounts (name, code, type, category_id, description, currency_id)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (name, code, type, category_id, description, currency_id))

    # Commit the changes
    connection.commit()
