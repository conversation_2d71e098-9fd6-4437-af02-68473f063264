#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Enhanced Inventory Manager for فوترها (Fawterha)
Handles database operations for the enhanced inventory system
"""

from datetime import datetime, timedelta
import uuid
import os
import qrcode
from barcode import Code128
from barcode.writer import ImageWriter

from models.inventory_transaction import InventoryTransaction
from models.product import Product
from models.warehouse import Warehouse
from models.warehouse_inventory import WarehouseInventory
from models.inventory_alert import InventoryAlert

class EnhancedInventoryManager:
    """Manager for enhanced inventory database operations."""

    def __init__(self, db_manager):
        """Initialize the enhanced inventory manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    # Warehouse Management Methods

    def get_all_warehouses(self, active_only=True):
        """Get all warehouses.

        Args:
            active_only (bool, optional): Whether to get only active warehouses. Defaults to True.

        Returns:
            list: List of Warehouse objects
        """
        query = """
        SELECT * FROM warehouses
        """
        
        if active_only:
            query += " WHERE is_active = 1"
            
        query += " ORDER BY name"
        
        rows = self.db_manager.execute_query(query)
        return [Warehouse.from_db_row(row) for row in rows]

    def get_warehouse_by_id(self, warehouse_id):
        """Get a warehouse by ID.

        Args:
            warehouse_id (int): Warehouse ID

        Returns:
            Warehouse: Warehouse object or None if not found
        """
        query = """
        SELECT * FROM warehouses
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (warehouse_id,))
        return Warehouse.from_db_row(rows[0]) if rows else None

    def add_warehouse(self, warehouse):
        """Add a new warehouse.

        Args:
            warehouse (Warehouse): Warehouse object to add

        Returns:
            int: ID of the new warehouse
        """
        query = """
        INSERT INTO warehouses (name, location, description, is_active)
        VALUES (?, ?, ?, ?)
        """
        params = (
            warehouse.name,
            warehouse.location,
            warehouse.description,
            1 if warehouse.is_active else 0
        )
        return self.db_manager.execute_insert(query, params)

    def update_warehouse(self, warehouse):
        """Update an existing warehouse.

        Args:
            warehouse (Warehouse): Warehouse object to update

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE warehouses
        SET name = ?, location = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            warehouse.name,
            warehouse.location,
            warehouse.description,
            1 if warehouse.is_active else 0,
            warehouse.id
        )
        return self.db_manager.execute_update(query, params) > 0

    def delete_warehouse(self, warehouse_id):
        """Delete a warehouse.

        Args:
            warehouse_id (int): Warehouse ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if there are any products in this warehouse
        check_query = """
        SELECT COUNT(*) FROM warehouse_inventory
        WHERE warehouse_id = ?
        """
        rows = self.db_manager.execute_query(check_query, (warehouse_id,))
        if rows and rows[0][0] > 0:
            return False  # Warehouse has products, can't delete
            
        # Delete the warehouse
        query = """
        DELETE FROM warehouses
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (warehouse_id,)) > 0

    # Warehouse Inventory Methods

    def get_warehouse_inventory(self, warehouse_id=None, product_id=None):
        """Get inventory for a warehouse or product.

        Args:
            warehouse_id (int, optional): Warehouse ID. Defaults to None.
            product_id (int, optional): Product ID. Defaults to None.

        Returns:
            list: List of WarehouseInventory objects
        """
        query = """
        SELECT wi.*, p.name as product_name, w.name as warehouse_name
        FROM warehouse_inventory wi
        JOIN products p ON wi.product_id = p.id
        JOIN warehouses w ON wi.warehouse_id = w.id
        """
        
        params = []
        where_clauses = []
        
        if warehouse_id:
            where_clauses.append("wi.warehouse_id = ?")
            params.append(warehouse_id)
            
        if product_id:
            where_clauses.append("wi.product_id = ?")
            params.append(product_id)
            
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)
            
        query += " ORDER BY p.name"
        
        rows = self.db_manager.execute_query(query, tuple(params))
        return [WarehouseInventory.from_db_row(row) for row in rows]

    def get_product_warehouse_inventory(self, product_id, warehouse_id):
        """Get inventory for a specific product in a specific warehouse.

        Args:
            product_id (int): Product ID
            warehouse_id (int): Warehouse ID

        Returns:
            WarehouseInventory: WarehouseInventory object or None if not found
        """
        query = """
        SELECT * FROM warehouse_inventory
        WHERE product_id = ? AND warehouse_id = ?
        """
        rows = self.db_manager.execute_query(query, (product_id, warehouse_id))
        return WarehouseInventory.from_db_row(rows[0]) if rows else None

    def update_warehouse_inventory(self, product_id, warehouse_id, quantity, min_level=None):
        """Update inventory for a product in a warehouse.

        Args:
            product_id (int): Product ID
            warehouse_id (int): Warehouse ID
            quantity (int): New quantity
            min_level (int, optional): New minimum level. Defaults to None.

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if inventory record exists
        inventory = self.get_product_warehouse_inventory(product_id, warehouse_id)
        
        if inventory:
            # Update existing record
            query = """
            UPDATE warehouse_inventory
            SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
            """
            
            params = [quantity]
            
            if min_level is not None:
                query += ", min_stock_level = ?"
                params.append(min_level)
                
            query += " WHERE product_id = ? AND warehouse_id = ?"
            params.extend([product_id, warehouse_id])
            
            return self.db_manager.execute_update(query, tuple(params)) > 0
        else:
            # Insert new record
            query = """
            INSERT INTO warehouse_inventory (product_id, warehouse_id, stock_quantity, min_stock_level)
            VALUES (?, ?, ?, ?)
            """
            
            if min_level is None:
                # Get min_level from product
                product_query = """
                SELECT min_stock_level FROM products
                WHERE id = ?
                """
                rows = self.db_manager.execute_query(product_query, (product_id,))
                min_level = rows[0]['min_stock_level'] if rows else 0
                
            return self.db_manager.execute_insert(query, (product_id, warehouse_id, quantity, min_level)) > 0

    def transfer_inventory(self, product_id, from_warehouse_id, to_warehouse_id, quantity, notes=""):
        """Transfer inventory from one warehouse to another.

        Args:
            product_id (int): Product ID
            from_warehouse_id (int): Source warehouse ID
            to_warehouse_id (int): Destination warehouse ID
            quantity (int): Quantity to transfer
            notes (str, optional): Transaction notes. Defaults to "".

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if source has enough inventory
        source_inventory = self.get_product_warehouse_inventory(product_id, from_warehouse_id)
        if not source_inventory or source_inventory.stock_quantity < quantity:
            return False
            
        # Get destination inventory
        dest_inventory = self.get_product_warehouse_inventory(product_id, to_warehouse_id)
        
        # Start a transaction
        conn = self.db_manager.connect()
        cursor = conn.cursor()
        
        try:
            # Begin transaction
            conn.execute("BEGIN TRANSACTION")
            
            # Update source inventory
            self.update_warehouse_inventory(
                product_id, 
                from_warehouse_id, 
                source_inventory.stock_quantity - quantity
            )
            
            # Update destination inventory
            if dest_inventory:
                self.update_warehouse_inventory(
                    product_id, 
                    to_warehouse_id, 
                    dest_inventory.stock_quantity + quantity
                )
            else:
                self.update_warehouse_inventory(
                    product_id, 
                    to_warehouse_id, 
                    quantity
                )
                
            # Create inventory transaction for source (negative)
            source_transaction = InventoryTransaction(
                product_id=product_id,
                transaction_type=InventoryTransaction.TYPE_ADJUSTMENT,
                quantity=-quantity,
                notes=f"Transfer to warehouse {to_warehouse_id}: {notes}",
                warehouse_id=from_warehouse_id
            )
            
            # Create inventory transaction for destination (positive)
            dest_transaction = InventoryTransaction(
                product_id=product_id,
                transaction_type=InventoryTransaction.TYPE_ADJUSTMENT,
                quantity=quantity,
                notes=f"Transfer from warehouse {from_warehouse_id}: {notes}",
                warehouse_id=to_warehouse_id
            )
            
            # Add transactions
            self.add_transaction(source_transaction)
            self.add_transaction(dest_transaction)
            
            # Commit transaction
            conn.execute("COMMIT")
            return True
        except Exception as e:
            # Rollback transaction on error
            conn.execute("ROLLBACK")
            print(f"Error in transfer_inventory: {str(e)}")
            return False
        finally:
            self.db_manager.close()

    # Inventory Alerts Methods

    def get_alerts(self, is_read=None, alert_type=None, limit=50):
        """Get inventory alerts.

        Args:
            is_read (bool, optional): Filter by read status. Defaults to None.
            alert_type (str, optional): Filter by alert type. Defaults to None.
            limit (int, optional): Maximum number of alerts to return. Defaults to 50.

        Returns:
            list: List of InventoryAlert objects
        """
        query = """
        SELECT a.*, p.name as product_name, w.name as warehouse_name
        FROM inventory_alerts a
        JOIN products p ON a.product_id = p.id
        LEFT JOIN warehouses w ON a.warehouse_id = w.id
        """
        
        params = []
        where_clauses = []
        
        if is_read is not None:
            where_clauses.append("a.is_read = ?")
            params.append(1 if is_read else 0)
            
        if alert_type:
            where_clauses.append("a.alert_type = ?")
            params.append(alert_type)
            
        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)
            
        query += " ORDER BY a.created_at DESC LIMIT ?"
        params.append(limit)
        
        rows = self.db_manager.execute_query(query, tuple(params))
        return [InventoryAlert.from_db_row(row) for row in rows]

    def add_alert(self, alert):
        """Add a new inventory alert.

        Args:
            alert (InventoryAlert): Alert to add

        Returns:
            int: ID of the new alert
        """
        query = """
        INSERT INTO inventory_alerts (product_id, warehouse_id, alert_type, message, is_read)
        VALUES (?, ?, ?, ?, ?)
        """
        params = (
            alert.product_id,
            alert.warehouse_id,
            alert.alert_type,
            alert.message,
            1 if alert.is_read else 0
        )
        return self.db_manager.execute_insert(query, params)

    def mark_alert_as_read(self, alert_id):
        """Mark an alert as read.

        Args:
            alert_id (int): Alert ID

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE inventory_alerts
        SET is_read = 1
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (alert_id,)) > 0

    def delete_alert(self, alert_id):
        """Delete an alert.

        Args:
            alert_id (int): Alert ID

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        DELETE FROM inventory_alerts
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (alert_id,)) > 0

    def check_low_stock_alerts(self):
        """Check for low stock and create alerts if needed.

        Returns:
            int: Number of alerts created
        """
        # Get all warehouse inventory with low stock
        query = """
        SELECT wi.*, p.name as product_name, w.name as warehouse_name
        FROM warehouse_inventory wi
        JOIN products p ON wi.product_id = p.id
        JOIN warehouses w ON wi.warehouse_id = w.id
        WHERE wi.stock_quantity <= wi.min_stock_level
        AND p.type = 'product' AND p.track_inventory = 1
        """
        
        rows = self.db_manager.execute_query(query)
        alerts_created = 0
        
        for row in rows:
            # Check if alert already exists
            check_query = """
            SELECT COUNT(*) FROM inventory_alerts
            WHERE product_id = ? AND warehouse_id = ? AND alert_type = ? AND is_read = 0
            """
            check_rows = self.db_manager.execute_query(
                check_query, 
                (row['product_id'], row['warehouse_id'], InventoryAlert.TYPE_LOW_STOCK)
            )
            
            if check_rows and check_rows[0][0] == 0:
                # Create alert
                alert = InventoryAlert(
                    product_id=row['product_id'],
                    warehouse_id=row['warehouse_id'],
                    alert_type=InventoryAlert.TYPE_LOW_STOCK,
                    message=f"Low stock for {row['product_name']} in {row['warehouse_name']}: {row['stock_quantity']} (min: {row['min_stock_level']})"
                )
                self.add_alert(alert)
                alerts_created += 1
                
        return alerts_created

    def check_expiry_alerts(self, days_threshold=30):
        """Check for products nearing expiry and create alerts if needed.

        Args:
            days_threshold (int, optional): Days threshold for expiry alerts. Defaults to 30.

        Returns:
            int: Number of alerts created
        """
        # Calculate threshold date
        threshold_date = (datetime.now() + timedelta(days=days_threshold)).strftime("%Y-%m-%d")
        
        # Get all products with expiry date within threshold
        query = """
        SELECT p.*, w.id as warehouse_id, w.name as warehouse_name
        FROM products p
        JOIN warehouse_inventory wi ON p.id = wi.product_id
        JOIN warehouses w ON wi.warehouse_id = w.id
        WHERE p.expiry_date IS NOT NULL
        AND p.expiry_date <= ?
        AND p.expiry_date >= date('now')
        AND p.type = 'product' AND p.track_inventory = 1
        """
        
        rows = self.db_manager.execute_query(query, (threshold_date,))
        alerts_created = 0
        
        for row in rows:
            # Check if alert already exists
            check_query = """
            SELECT COUNT(*) FROM inventory_alerts
            WHERE product_id = ? AND warehouse_id = ? AND alert_type = ? AND is_read = 0
            """
            check_rows = self.db_manager.execute_query(
                check_query, 
                (row['id'], row['warehouse_id'], InventoryAlert.TYPE_EXPIRY)
            )
            
            if check_rows and check_rows[0][0] == 0:
                # Calculate days until expiry
                expiry_date = datetime.strptime(row['expiry_date'], "%Y-%m-%d")
                days_until_expiry = (expiry_date - datetime.now()).days
                
                # Create alert
                alert = InventoryAlert(
                    product_id=row['id'],
                    warehouse_id=row['warehouse_id'],
                    alert_type=InventoryAlert.TYPE_EXPIRY,
                    message=f"{row['name']} in {row['warehouse_name']} expires in {days_until_expiry} days (on {row['expiry_date']})"
                )
                self.add_alert(alert)
                alerts_created += 1
                
        return alerts_created

    # Barcode and QR Code Methods

    def generate_barcode(self, product_id, save_to_db=True):
        """Generate a barcode for a product.

        Args:
            product_id (int): Product ID
            save_to_db (bool, optional): Whether to save the barcode to the database. Defaults to True.

        Returns:
            str: Path to the generated barcode image
        """
        # Get product
        query = """
        SELECT * FROM products
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (product_id,))
        if not rows:
            return None
            
        product = Product.from_db_row(rows[0])
        
        # Generate barcode if not exists
        if not product.barcode:
            # Generate unique barcode
            barcode_value = f"FWTR{product_id:06d}"
            
            # Create barcode directory if not exists
            barcode_dir = os.path.join(os.getcwd(), "resources", "barcodes")
            os.makedirs(barcode_dir, exist_ok=True)
            
            # Generate barcode
            barcode_path = os.path.join(barcode_dir, f"barcode_{product_id}.png")
            barcode = Code128(barcode_value, writer=ImageWriter())
            barcode.save(barcode_path)
            
            # Save to database if requested
            if save_to_db:
                update_query = """
                UPDATE products
                SET barcode = ?
                WHERE id = ?
                """
                self.db_manager.execute_update(update_query, (barcode_value, product_id))
                
            return barcode_path
        else:
            # Barcode already exists, regenerate image
            barcode_dir = os.path.join(os.getcwd(), "resources", "barcodes")
            os.makedirs(barcode_dir, exist_ok=True)
            
            barcode_path = os.path.join(barcode_dir, f"barcode_{product_id}.png")
            barcode = Code128(product.barcode, writer=ImageWriter())
            barcode.save(barcode_path)
            
            return barcode_path

    def generate_qr_code(self, product_id, save_to_db=True):
        """Generate a QR code for a product.

        Args:
            product_id (int): Product ID
            save_to_db (bool, optional): Whether to save the QR code to the database. Defaults to True.

        Returns:
            str: Path to the generated QR code image
        """
        # Get product
        query = """
        SELECT * FROM products
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (product_id,))
        if not rows:
            return None
            
        product = Product.from_db_row(rows[0])
        
        # Generate QR code if not exists
        if not product.qr_code:
            # Generate unique QR code value
            qr_value = f"FWTR-QR-{product_id}-{uuid.uuid4().hex[:8]}"
            
            # Create QR code directory if not exists
            qr_dir = os.path.join(os.getcwd(), "resources", "qrcodes")
            os.makedirs(qr_dir, exist_ok=True)
            
            # Generate QR code
            qr_path = os.path.join(qr_dir, f"qrcode_{product_id}.png")
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(qr_value)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            img.save(qr_path)
            
            # Save to database if requested
            if save_to_db:
                update_query = """
                UPDATE products
                SET qr_code = ?
                WHERE id = ?
                """
                self.db_manager.execute_update(update_query, (qr_value, product_id))
                
            return qr_path
        else:
            # QR code already exists, regenerate image
            qr_dir = os.path.join(os.getcwd(), "resources", "qrcodes")
            os.makedirs(qr_dir, exist_ok=True)
            
            qr_path = os.path.join(qr_dir, f"qrcode_{product_id}.png")
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(product.qr_code)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            img.save(qr_path)
            
            return qr_path
