#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory View for فوترها (Fawterha)
Provides UI for managing POS-specific inventory
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QLineEdit, QMessageBox, QSplitter, QFrame, QGroupBox,
    QCheckBox, QSpinBox, QDoubleSpinBox, QFormLayout,
    QDialog, QDialogButtonBox, QTabWidget, QTextEdit,
    QInputDialog
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QIcon

from models.pos_inventory import POSInventory
from models.pos_inventory_transaction import POSInventoryTransaction
from models.product import Product
from utils.translation_manager import tr
from utils.formatting import format_currency, format_date

class POSInventoryView(QWidget):
    """POS Inventory view widget."""

    inventory_updated = Signal()
    language_changed = Signal(str)

    def __init__(self, db_manager, pos_manager, parent=None):
        """Initialize the POS inventory view.

        Args:
            db_manager: Database manager
            pos_manager: POS manager
            parent: Parent widget
        """
        super().__init__(parent)
        self.db_manager = db_manager
        self.pos_manager = pos_manager
        self.inventory_list = []
        self.selected_inventory_id = None

        # Connect to translation manager's language_changed signal
        from utils.translation_manager import get_translation_manager
        self.translation_manager = get_translation_manager()
        print(f"POS Inventory View: Connecting to translation manager...")
        self.translation_manager.language_changed.connect(self.refresh_ui_translations)
        print(f"POS Inventory View: Connected to language_changed signal successfully")

        self.init_ui()
        self.load_data()

        # Force initial translation update based on current language
        print(f"POS Inventory View: Initial language is: {self.translation_manager.current_language}")
        if self.translation_manager.current_language != 'ar':
            print("POS Inventory View: Non-Arabic language detected, forcing translation update...")
            self.refresh_ui_translations()

        # Force another update after a short delay to ensure all widgets are ready
        from PySide6.QtCore import QTimer
        QTimer.singleShot(1000, self.refresh_ui_translations)

    def init_ui(self):
        """Initialize the UI."""
        # Set layout direction based on current language
        if hasattr(self, 'translation_manager') and self.translation_manager.current_language != 'ar':
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
        else:
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Main layout with optimized spacing
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(25)
        main_layout.setContentsMargins(25, 25, 25, 25)

        # Header with enhanced spacing
        header_layout = QVBoxLayout()
        header_layout.setSpacing(20)
        header_layout.setContentsMargins(0, 0, 0, 15)

        # Title row with better proportions
        title_row = QHBoxLayout()
        title_row.setSpacing(15)
        title_label = QLabel(tr("inventory.pos_inventory_management", "إدارة مخزون نقاط البيع"))
        title_label.setMinimumHeight(50)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #0d47a1;
                padding: 15px 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 2px solid #e3f2fd;
                margin-bottom: 5px;
            }
        """)
        title_row.addWidget(title_label)
        title_row.addStretch()
        header_layout.addLayout(title_row)

        # Controls row with enhanced spacing
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(15)
        controls_layout.setContentsMargins(0, 10, 0, 10)

        # Search with improved dimensions
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText(tr("inventory.search_products_placeholder", "بحث عن منتجات..."))
        self.search_edit.setMinimumWidth(250)
        self.search_edit.setMinimumHeight(40)
        self.search_edit.setStyleSheet("""
            QLineEdit {
                padding: 10px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                font-size: 14px;
                color: #333333;
            }
            QLineEdit:focus {
                border-color: #2196f3;
                outline: none;
                background-color: #fafafa;
            }
            QLineEdit:hover {
                border-color: #bdbdbd;
            }
        """)
        self.search_edit.textChanged.connect(self.filter_inventory)
        controls_layout.addWidget(self.search_edit)

        # Category filter with improved dimensions
        self.category_combo = QComboBox()
        self.category_combo.setMinimumWidth(180)
        self.category_combo.setMinimumHeight(40)
        self.category_combo.setStyleSheet("""
            QComboBox {
                padding: 10px 15px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                font-size: 14px;
                color: #333333;
            }
            QComboBox:focus {
                border-color: #2196f3;
                outline: none;
                background-color: #fafafa;
            }
            QComboBox:hover {
                border-color: #bdbdbd;
            }
            QComboBox::drop-down {
                border: none;
                width: 25px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: url(resources/icons/arrow-down.png);
                width: 12px;
                height: 12px;
                margin-right: 5px;
            }
        """)
        self.category_combo.currentIndexChanged.connect(self.filter_inventory)
        controls_layout.addWidget(self.category_combo)

        # Show zero stock checkbox with improved dimensions
        self.show_zero_stock = QCheckBox(tr("inventory.show_zero_stock", "إظهار المنتجات بدون مخزون"))
        self.show_zero_stock.setChecked(True)
        self.show_zero_stock.setMinimumHeight(40)
        self.show_zero_stock.setStyleSheet("""
            QCheckBox {
                font-size: 14px;
                color: #333333;
                spacing: 10px;
                padding: 8px;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #e0e0e0;
                border-radius: 4px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #2196f3;
                border-radius: 4px;
                background-color: #2196f3;
                image: url(resources/icons/check.png);
            }
            QCheckBox::indicator:hover {
                border-color: #bdbdbd;
            }
        """)
        self.show_zero_stock.stateChanged.connect(self.filter_inventory)
        controls_layout.addWidget(self.show_zero_stock)

        # Spacer
        controls_layout.addStretch()

        # Action buttons with improved dimensions
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # Add product button
        self.add_product_button = QPushButton(tr("inventory.add_product", "إضافة منتج"))
        self.add_product_button.setIcon(QIcon("resources/icons/add.png"))
        self.add_product_button.setMinimumHeight(40)
        self.add_product_button.setMinimumWidth(140)
        self.add_product_button.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #388e3c;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #2e7d32;
                transform: translateY(0px);
            }
        """)
        self.add_product_button.clicked.connect(self.add_new_product)
        buttons_layout.addWidget(self.add_product_button)

        # Sync button
        self.sync_button = QPushButton(tr("inventory.sync_from_main", "مزامنة من المخزون الرئيسي"))
        self.sync_button.setIcon(QIcon("resources/icons/sync.png"))
        self.sync_button.setMinimumHeight(40)
        self.sync_button.setMinimumWidth(200)
        self.sync_button.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976d2;
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background-color: #1565c0;
                transform: translateY(0px);
            }
        """)
        self.sync_button.clicked.connect(self.sync_from_main)
        buttons_layout.addWidget(self.sync_button)

        controls_layout.addLayout(buttons_layout)
        header_layout.addLayout(controls_layout)

        main_layout.addLayout(header_layout)

        # Splitter for inventory and details with improved styling
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)
        splitter.setHandleWidth(8)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #e0e0e0;
                border-radius: 4px;
                margin: 2px;
            }
            QSplitter::handle:hover {
                background-color: #bdbdbd;
            }
        """)

        # Inventory table with enhanced styling
        inventory_frame = QFrame()
        inventory_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_frame)
        inventory_layout.setSpacing(15)
        inventory_layout.setContentsMargins(20, 20, 20, 20)

        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(6)
        self.inventory_table.setMinimumHeight(450)
        self.inventory_table.setHorizontalHeaderLabels([
            tr("inventory.product_column", "المنتج"),
            tr("inventory.description_column", "الوصف"),
            tr("inventory.barcode", "الباركود"),
            tr("inventory.stock_column", "المخزون"),
            tr("inventory.min_stock_column", "الحد الأدنى"),
            tr("inventory.actions_column", "الإجراءات")
        ])

        # Set enhanced header style
        self.inventory_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #2196f3;
                color: white;
                padding: 12px 8px;
                border: none;
                border-bottom: 2px solid #1976d2;
                font-weight: bold;
                font-size: 14px;
                text-align: left;
            }
            QHeaderView::section:hover {
                background-color: #1976d2;
            }
        """)

        # Set enhanced table style
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #e0e0e0;
                background-color: white;
                alternate-background-color: #f8f9fa;
                selection-background-color: #e3f2fd;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #f0f0f0;
                color: #333333;
            }
            QTableWidget::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QTableWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)

        # Set optimized column widths for better layout
        self.inventory_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # Product name
        self.inventory_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Interactive)  # Description
        self.inventory_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Interactive)  # Barcode
        self.inventory_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)  # Stock
        self.inventory_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Min stock
        self.inventory_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)  # Actions

        # Set enhanced column widths for English content
        self.inventory_table.setColumnWidth(1, 200)  # Description - wider for English text
        self.inventory_table.setColumnWidth(2, 150)  # Barcode - wider for better visibility
        self.inventory_table.setColumnWidth(5, 220)  # Actions - wider for button layout
        self.inventory_table.verticalHeader().setVisible(False)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setSelectionMode(QTableWidget.SingleSelection)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.selectionModel().selectionChanged.connect(self.on_inventory_selected)

        # Set row height
        self.inventory_table.verticalHeader().setDefaultSectionSize(40)

        inventory_layout.addWidget(self.inventory_table)

        # Details frame with enhanced styling
        details_frame = QFrame()
        details_frame.setMinimumWidth(500)
        details_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin: 5px;
            }
        """)
        details_layout = QVBoxLayout(details_frame)
        details_layout.setSpacing(20)
        details_layout.setContentsMargins(25, 25, 25, 25)

        # Details tabs
        self.details_tabs = QTabWidget()
        self.details_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                background-color: white;
                margin-top: 5px;
            }
            QTabBar::tab {
                background-color: #f5f5f5;
                color: #666666;
                padding: 12px 24px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-width: 100px;
            }
            QTabBar::tab:selected {
                background-color: #2196f3;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)

        # Details tab
        details_tab = QWidget()
        details_tab_layout = QVBoxLayout(details_tab)
        details_tab_layout.setSpacing(20)
        details_tab_layout.setContentsMargins(20, 20, 20, 20)

        # Product details
        product_group = QGroupBox(tr("inventory.product_details_section", "تفاصيل المنتج"))
        product_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #0d47a1;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 10px;
                background-color: #fafafa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background-color: #fafafa;
            }
        """)
        product_layout = QFormLayout(product_group)

        # Set proper spacing and margins for the form layout
        product_layout.setSpacing(15)
        product_layout.setContentsMargins(20, 25, 20, 20)
        product_layout.setLabelAlignment(Qt.AlignLeft)
        product_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        product_layout.setRowWrapPolicy(QFormLayout.DontWrapRows)

        # Create styled labels for product information
        label_style = """
            QLabel {
                padding: 8px 12px;
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                font-size: 13px;
                color: #333333;
                min-height: 20px;
            }
        """

        self.product_name_label = QLabel()
        self.product_name_label.setMinimumHeight(35)
        self.product_name_label.setStyleSheet(label_style)
        product_layout.addRow(tr("inventory.product_name_field", "اسم المنتج:"), self.product_name_label)

        self.product_category_label = QLabel()
        self.product_category_label.setMinimumHeight(35)
        self.product_category_label.setStyleSheet(label_style)
        product_layout.addRow(tr("inventory.description_field", "الوصف:"), self.product_category_label)

        self.product_barcode_label = QLabel()
        self.product_barcode_label.setMinimumHeight(35)
        self.product_barcode_label.setStyleSheet(label_style)
        product_layout.addRow(tr("inventory.barcode", "الباركود:"), self.product_barcode_label)

        details_tab_layout.addWidget(product_group)

        # Inventory details
        inventory_group = QGroupBox(tr("inventory.stock_details", "تفاصيل المخزون"))
        inventory_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #0d47a1;
                border: 2px solid #e3f2fd;
                border-radius: 8px;
                margin-top: 12px;
                padding-top: 10px;
                background-color: #fafafa;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 8px 0 8px;
                background-color: #fafafa;
            }
        """)
        inventory_layout = QVBoxLayout(inventory_group)

        # Set proper spacing and margins for the inventory group
        inventory_layout.setSpacing(20)
        inventory_layout.setContentsMargins(20, 25, 20, 20)

        # Form layout for inventory details
        form_layout = QFormLayout()
        form_layout.setSpacing(18)
        form_layout.setLabelAlignment(Qt.AlignLeft)
        form_layout.setFieldGrowthPolicy(QFormLayout.ExpandingFieldsGrow)
        form_layout.setRowWrapPolicy(QFormLayout.DontWrapRows)

        # Define consistent input styling
        input_style = """
            QSpinBox, QLineEdit, QTextEdit {
                padding: 8px 12px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                background-color: white;
                font-size: 13px;
                color: #333333;
                selection-background-color: #bbdefb;
            }
            QSpinBox:focus, QLineEdit:focus, QTextEdit:focus {
                border-color: #2196f3;
                outline: none;
                background-color: #fafafa;
            }
            QSpinBox:hover, QLineEdit:hover, QTextEdit:hover {
                border-color: #bdbdbd;
            }
        """

        # Stock quantity with +/- buttons
        stock_layout = QHBoxLayout()
        stock_layout.setSpacing(5)

        self.stock_quantity_spin = QSpinBox()
        self.stock_quantity_spin.setRange(0, 100000)
        self.stock_quantity_spin.setButtonSymbols(QSpinBox.NoButtons)
        self.stock_quantity_spin.setMinimumWidth(120)
        self.stock_quantity_spin.setMinimumHeight(40)
        self.stock_quantity_spin.setStyleSheet(input_style)
        stock_layout.addWidget(self.stock_quantity_spin)

        # Decrease button
        decrease_button = QPushButton("-")
        decrease_button.setFixedSize(40, 40)
        decrease_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 18px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #c62828;
                transform: scale(0.95);
            }
        """)
        decrease_button.clicked.connect(lambda: self.stock_quantity_spin.setValue(self.stock_quantity_spin.value() - 1))
        stock_layout.addWidget(decrease_button)

        # Increase button
        increase_button = QPushButton("+")
        increase_button.setFixedSize(40, 40)
        increase_button.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 18px;
                margin: 0px;
            }
            QPushButton:hover {
                background-color: #388e3c;
                transform: scale(1.05);
            }
            QPushButton:pressed {
                background-color: #2e7d32;
                transform: scale(0.95);
            }
        """)
        increase_button.clicked.connect(lambda: self.stock_quantity_spin.setValue(self.stock_quantity_spin.value() + 1))
        stock_layout.addWidget(increase_button)

        form_layout.addRow(tr("inventory.current_quantity_field", "الكمية الحالية:"), stock_layout)

        # Min stock level
        self.min_stock_level_spin = QSpinBox()
        self.min_stock_level_spin.setRange(0, 100000)
        self.min_stock_level_spin.setMinimumHeight(40)
        self.min_stock_level_spin.setStyleSheet(input_style)
        form_layout.addRow(tr("inventory.min_stock_level_field", "الحد الأدنى للمخزون:"), self.min_stock_level_spin)

        # Location
        self.location_edit = QLineEdit()
        self.location_edit.setMinimumHeight(40)
        self.location_edit.setStyleSheet(input_style)
        form_layout.addRow(tr("inventory.location_field", "الموقع:"), self.location_edit)

        # Notes
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(100)
        self.notes_edit.setMinimumHeight(100)
        self.notes_edit.setStyleSheet(input_style)
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), self.notes_edit)

        inventory_layout.addLayout(form_layout)

        details_tab_layout.addWidget(inventory_group)

        # Add spacing before buttons
        details_tab_layout.addSpacing(25)

        # Button container with better styling
        button_container = QWidget()
        button_container.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }
        """)
        button_layout = QVBoxLayout(button_container)
        button_layout.setSpacing(15)

        # Save button
        self.save_button = QPushButton(tr("inventory.save_changes", "حفظ التغييرات"))
        self.save_button.setMinimumHeight(45)
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
                text-transform: uppercase;
            }
            QPushButton:hover {
                background-color: #1976d2;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
            }
            QPushButton:pressed {
                background-color: #1565c0;
                transform: translateY(0px);
            }
        """)
        self.save_button.clicked.connect(self.save_inventory)
        button_layout.addWidget(self.save_button)

        # Add/remove stock buttons
        stock_buttons_layout = QHBoxLayout()
        stock_buttons_layout.setSpacing(15)

        self.add_stock_button = QPushButton(tr("inventory.add_stock_button", "إضافة مخزون"))
        self.add_stock_button.setMinimumHeight(45)
        self.add_stock_button.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #388e3c;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
            }
            QPushButton:pressed {
                background-color: #2e7d32;
                transform: translateY(0px);
            }
        """)
        self.add_stock_button.clicked.connect(self.add_stock)
        stock_buttons_layout.addWidget(self.add_stock_button)

        self.remove_stock_button = QPushButton(tr("inventory.remove_stock_button", "سحب مخزون"))
        self.remove_stock_button.setMinimumHeight(45)
        self.remove_stock_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #d32f2f;
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
            }
            QPushButton:pressed {
                background-color: #c62828;
                transform: translateY(0px);
            }
        """)
        self.remove_stock_button.clicked.connect(self.remove_stock)
        stock_buttons_layout.addWidget(self.remove_stock_button)

        button_layout.addLayout(stock_buttons_layout)
        details_tab_layout.addWidget(button_container)

        # Spacer
        details_tab_layout.addStretch()

        self.details_tabs.addTab(details_tab, tr("inventory.details", "التفاصيل"))

        # Transactions tab
        transactions_tab = QWidget()
        transactions_layout = QVBoxLayout(transactions_tab)

        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(5)
        self.transactions_table.setHorizontalHeaderLabels([
            tr("inventory.date_column", "التاريخ"),
            tr("inventory.type_column", "النوع"),
            tr("inventory.quantity_column", "الكمية"),
            tr("inventory.reference_column", "المرجع"),
            tr("inventory.notes_column", "ملاحظات")
        ])
        self.transactions_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.Stretch)
        self.transactions_table.verticalHeader().setVisible(False)
        self.transactions_table.setEditTriggers(QTableWidget.NoEditTriggers)

        transactions_layout.addWidget(self.transactions_table)

        self.details_tabs.addTab(transactions_tab, tr("inventory.movements", "الحركات"))

        details_layout.addWidget(self.details_tabs)

        # Add frames to splitter with optimized proportions
        splitter.addWidget(inventory_frame)
        splitter.addWidget(details_frame)

        # Set optimal proportions: 60% for inventory table, 40% for details
        splitter.setSizes([600, 400])
        splitter.setStretchFactor(0, 3)  # Inventory table gets more space
        splitter.setStretchFactor(1, 2)  # Details panel gets less space

        main_layout.addWidget(splitter)

        # Add enhanced status bar at the bottom
        self.status_label = QLabel(tr("pos.inventory_load_error_message", "Cannot update inventory view, please check database settings"))
        self.status_label.setMinimumHeight(40)
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #fff3e0;
                color: #e65100;
                padding: 12px 20px;
                border: 2px solid #ffb74d;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                text-align: left;
                direction: ltr;
            }
        """)
        main_layout.addWidget(self.status_label)

        # Initialize details with default values instead of disabling
        self.init_details_with_defaults()

    def init_details_with_defaults(self):
        """Initialize details with default values."""
        self.product_name_label.setText(tr("inventory.no_product_selected", "لم يتم اختيار منتج"))
        self.product_category_label.setText("-")
        self.product_barcode_label.setText("-")
        self.stock_quantity_spin.setValue(0)
        self.min_stock_level_spin.setValue(0)
        self.location_edit.setText("")
        self.notes_edit.setText(tr("pos.select_product_message", "Please click on a product to view details and manage inventory"))

        # Clear transactions table
        self.transactions_table.setRowCount(0)
        row = self.transactions_table.rowCount()
        self.transactions_table.insertRow(row)
        no_data_item = QTableWidgetItem(tr("inventory.no_transactions", "لا توجد حركات"))
        no_data_item.setTextAlignment(Qt.AlignCenter)
        self.transactions_table.setItem(row, 0, no_data_item)
        self.transactions_table.setSpan(row, 0, 1, 5)

    def load_data(self):
        """Load data from the database."""
        # Load categories
        self.load_categories()

        # Load inventory
        self.load_inventory()

        # Force translation update after data is loaded
        if self.translation_manager.current_language != 'ar':
            self.refresh_ui_translations()
            # Force immediate UI update
            self.update()
            self.repaint()

    def load_categories(self):
        """Load product categories."""
        try:
            # Get categories
            query = """
            SELECT * FROM categories
            ORDER BY name
            """
            rows = self.db_manager.execute_query(query)

            # Clear category combo
            self.category_combo.clear()

            # Add "All" option
            self.category_combo.addItem(tr("inventory.all_categories", "جميع الفئات"), None)

            # Add categories
            for category in rows:
                self.category_combo.addItem(category['name'], category['id'])
        except Exception as e:
            # Just add "All" option if categories table doesn't exist
            self.category_combo.clear()
            self.category_combo.addItem(tr("inventory.all_categories", "جميع الفئات"), None)

    def load_inventory(self):
        """Load inventory items."""
        try:
            print("Loading POS inventory...")

            # Get inventory
            self.inventory_list = self.pos_manager.get_all_pos_inventory(
                include_zero_stock=self.show_zero_stock.isChecked(),
                category_id=self.category_combo.currentData()
            )

            print(f"Loaded {len(self.inventory_list)} inventory items")

            # If no inventory items found, try to sync from main inventory
            if not self.inventory_list:
                print("No POS inventory found, attempting to sync from main inventory...")
                try:
                    synced_count = self.pos_manager.sync_pos_inventory_from_main()
                    print(f"Synced {synced_count} products from main inventory")

                    # Reload inventory after sync
                    self.inventory_list = self.pos_manager.get_all_pos_inventory(
                        include_zero_stock=self.show_zero_stock.isChecked(),
                        category_id=self.category_combo.currentData()
                    )
                    print(f"After sync: {len(self.inventory_list)} inventory items")

                    # Update status message
                    if synced_count > 0:
                        self.status_label.setText(tr("inventory.sync_success", f"تم مزامنة {synced_count} منتج من المخزون الرئيسي"))
                        self.status_label.setStyleSheet("""
                            QLabel {
                                background-color: #e8f5e8;
                                color: #2e7d32;
                                padding: 12px 20px;
                                border: 2px solid #4caf50;
                                border-radius: 6px;
                                font-size: 14px;
                                font-weight: 500;
                            }
                        """)
                    else:
                        self.status_label.setText(tr("inventory.no_products_to_sync", "لا توجد منتجات للمزامنة من المخزون الرئيسي"))
                except Exception as sync_error:
                    print(f"Error syncing from main inventory: {sync_error}")
                    self.status_label.setText(tr("inventory.sync_error", f"خطأ في المزامنة: {str(sync_error)}"))

            # Display inventory
            self.display_inventory()

            # Update status if we have inventory
            if self.inventory_list:
                self.status_label.setText(tr("inventory.loaded_successfully", f"تم تحميل {len(self.inventory_list)} عنصر بنجاح"))
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #e8f5e8;
                        color: #2e7d32;
                        padding: 12px 20px;
                        border: 2px solid #4caf50;
                        border-radius: 6px;
                        font-size: 14px;
                        font-weight: 500;
                    }
                """)

        except Exception as e:
            print(f"Error loading inventory: {e}")
            self.inventory_list = []
            self.display_inventory()

            # Update status with error message
            self.status_label.setText(tr("inventory.load_error", f"خطأ في تحميل المخزون: {str(e)}"))
            self.status_label.setStyleSheet("""
                QLabel {
                    background-color: #ffebee;
                    color: #c62828;
                    padding: 12px 20px;
                    border: 2px solid #f44336;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                }
            """)

    def display_inventory(self, filtered_inventory=None):
        """Display inventory items in the table.

        Args:
            filtered_inventory (list, optional): Pre-filtered inventory list. If None, will call filter_inventory().
        """
        # Clear table
        self.inventory_table.setRowCount(0)

        # Filter inventory if not provided
        if filtered_inventory is None:
            filtered_inventory = self.filter_inventory()

        # Add inventory items to table
        for inventory in filtered_inventory:
            row = self.inventory_table.rowCount()
            self.inventory_table.insertRow(row)

            # Product name
            name_item = QTableWidgetItem(inventory.product_name or "")
            name_item.setData(Qt.UserRole, inventory.id)
            self.inventory_table.setItem(row, 0, name_item)

            # Category
            category_item = QTableWidgetItem(inventory.category_name or "")
            self.inventory_table.setItem(row, 1, category_item)

            # Barcode
            barcode_item = QTableWidgetItem(inventory.product_barcode or "")
            self.inventory_table.setItem(row, 2, barcode_item)

            # Stock quantity
            stock_item = QTableWidgetItem(str(inventory.stock_quantity))
            # Highlight low stock
            if inventory.min_stock_level > 0 and inventory.stock_quantity <= inventory.min_stock_level:
                stock_item.setBackground(Qt.red)
                stock_item.setForeground(Qt.white)
            self.inventory_table.setItem(row, 3, stock_item)

            # Min stock level
            min_stock_item = QTableWidgetItem(str(inventory.min_stock_level))
            self.inventory_table.setItem(row, 4, min_stock_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)
            actions_layout.setAlignment(Qt.AlignCenter)

            # Store inventory ID for later use
            inventory_id = inventory.id

            # Edit button
            edit_button = QPushButton(tr("inventory.edit_button", "تعديل"))
            edit_button.setProperty("inventory_id", inventory_id)
            edit_button.setCursor(Qt.PointingHandCursor)
            edit_button.setIcon(QIcon("resources/icons/edit.png"))
            edit_button.setStyleSheet("""
                QPushButton {
                    background-color: #2196f3;
                    color: white;
                    border-radius: 4px;
                    padding: 5px 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #1976d2;
                }
                QPushButton:pressed {
                    background-color: #0d47a1;
                }
            """)
            # Use lambda to capture the current inventory ID
            edit_button.clicked.connect(lambda checked=False, inv_id=inventory_id: self.edit_inventory(inv_id))
            actions_layout.addWidget(edit_button)

            # Add stock button
            add_stock_button = QPushButton("+")
            add_stock_button.setProperty("inventory_id", inventory_id)
            add_stock_button.setCursor(Qt.PointingHandCursor)
            add_stock_button.setToolTip(tr("inventory.add_stock", "إضافة مخزون"))
            add_stock_button.setMaximumWidth(30)
            add_stock_button.setStyleSheet("""
                QPushButton {
                    background-color: #4caf50;
                    color: white;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #388e3c;
                }
                QPushButton:pressed {
                    background-color: #2e7d32;
                }
            """)
            # Use lambda to capture the current inventory ID
            add_stock_button.clicked.connect(lambda checked=False, inv_id=inventory_id: self.quick_add_stock(inv_id))
            actions_layout.addWidget(add_stock_button)

            # Remove stock button
            remove_stock_button = QPushButton("-")
            remove_stock_button.setProperty("inventory_id", inventory_id)
            remove_stock_button.setCursor(Qt.PointingHandCursor)
            remove_stock_button.setToolTip(tr("inventory.remove_stock", "سحب مخزون"))
            remove_stock_button.setMaximumWidth(30)
            remove_stock_button.setStyleSheet("""
                QPushButton {
                    background-color: #f44336;
                    color: white;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #d32f2f;
                }
                QPushButton:pressed {
                    background-color: #b71c1c;
                }
            """)
            # Use lambda to capture the current inventory ID
            remove_stock_button.clicked.connect(lambda checked=False, inv_id=inventory_id: self.quick_remove_stock(inv_id))
            actions_layout.addWidget(remove_stock_button)

            self.inventory_table.setCellWidget(row, 5, actions_widget)

    def filter_inventory(self):
        """Filter inventory based on search text and category.

        Returns:
            list: Filtered inventory
        """
        # Get search text
        search_text = self.search_edit.text().strip().lower()

        # Get selected category
        category_id = self.category_combo.currentData()

        # Filter inventory
        filtered_inventory = []
        for inventory in self.inventory_list:
            # Check if inventory matches search text
            if search_text and search_text not in (inventory.product_name or "").lower() and search_text not in (inventory.product_barcode or "").lower():
                continue

            # Check if inventory matches category
            if category_id and inventory.category_id != category_id:
                continue

            # Check if we should show zero stock
            if not self.show_zero_stock.isChecked() and inventory.stock_quantity <= 0:
                continue

            filtered_inventory.append(inventory)

        return filtered_inventory

    def refresh_ui_translations(self):
        """Refresh all UI translations when language changes."""
        print("=== POS INVENTORY VIEW: refresh_ui_translations CALLED ===")
        print(f"Current language: {self.translation_manager.current_language}")

        # Update layout direction based on language
        if self.translation_manager.current_language != 'ar':
            self.setLayoutDirection(Qt.LayoutDirection.LeftToRight)
            print("Set layout direction to LTR")
        else:
            self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            print("Set layout direction to RTL")

        # Update all UI elements with new translations
        self.update_all_translations()

        # Apply language-specific styling
        self.apply_language_styling()

        # Force immediate UI refresh
        self.update()
        self.repaint()

        print("=== POS INVENTORY VIEW: translations refreshed successfully ===")

    def apply_language_styling(self):
        """Apply language-specific styling to UI elements."""
        if self.translation_manager.current_language != 'ar':
            # English/LTR styling
            print("Applying LTR styling...")

            # Update status label styling for LTR
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #ffebee;
                        color: #c62828;
                        padding: 8px;
                        border: 1px solid #ef5350;
                        border-radius: 4px;
                        font-size: 12px;
                        text-align: left;
                        direction: ltr;
                    }
                """)

            # Update group box styling for LTR
            for group_box in self.findChildren(QGroupBox):
                group_box.setStyleSheet("""
                    QGroupBox {
                        font-weight: bold;
                        border: 2px solid #cccccc;
                        border-radius: 5px;
                        margin-top: 1ex;
                        direction: ltr;
                    }
                    QGroupBox::title {
                        subcontrol-origin: margin;
                        left: 10px;
                        padding: 0 5px 0 5px;
                    }
                """)
        else:
            # Arabic/RTL styling
            print("Applying RTL styling...")

            # Update status label styling for RTL
            if hasattr(self, 'status_label') and self.status_label:
                self.status_label.setStyleSheet("""
                    QLabel {
                        background-color: #ffebee;
                        color: #c62828;
                        padding: 8px;
                        border: 1px solid #ef5350;
                        border-radius: 4px;
                        font-size: 12px;
                        text-align: right;
                        direction: rtl;
                    }
                """)

    def update_all_translations(self):
        """Update all UI elements with current language translations."""
        print(f"Current language in translation manager: {self.translation_manager.current_language}")

        # Test a simple translation
        test_translation = tr("pos.pos_inventory_management", "DEFAULT")
        print(f"Test translation result: '{test_translation}'")

        # Find and update all labels, buttons, and other text elements
        for widget in self.findChildren(QLabel):
            self.update_widget_translation(widget)

        for widget in self.findChildren(QPushButton):
            self.update_widget_translation(widget)

        for widget in self.findChildren(QCheckBox):
            self.update_widget_translation(widget)

        # Update table headers
        if hasattr(self, 'inventory_table') and self.inventory_table:
            self.update_table_headers()

        # Update combo box items
        if hasattr(self, 'category_combo') and self.category_combo:
            self.update_category_combo()

        # Update placeholders
        if hasattr(self, 'search_edit') and self.search_edit:
            self.search_edit.setPlaceholderText(self.translation_manager.get_text("pos.search_products_placeholder", "Search products..."))
            print("Updated search placeholder")

        # Update tab widgets
        self.update_tab_widgets()

    def update_widget_translation(self, widget):
        """Update a single widget's translation based on its current text."""
        current_text = widget.text()
        print(f"Checking widget text: '{current_text}'")

        # Map of Arabic texts to translation keys (using exact keys from en.json)
        translation_map = {
            "إدارة مخزون نقاط البيع": "pos.pos_inventory_management",
            "إظهار المنتجات بدون مخزون": "pos.show_zero_stock",
            "إضافة منتج": "pos.add_product",
            "مزامنة من المخزون الرئيسي": "pos.sync_from_main",
            "حفظ التغييرات": "pos.save_changes",
            "إضافة مخزون": "pos.add_stock_button",
            "سحب مخزون": "pos.remove_stock_button",
            "تعديل": "pos.edit_button",
            "جميع الفئات": "pos.all_categories",
            "اسم المنتج:": "pos.product_name_field",
            "الوصف:": "pos.description_field",
            "الكمية الحالية:": "pos.current_quantity_field",
            "الحد الأدنى للمخزون:": "pos.min_stock_level_field",
            "الموقع:": "pos.location_field",
            "ملاحظات:": "pos.notes_field",
            "لم يتم اختيار منتج": "pos.no_product_selected",
            # Additional translations for new elements
            "المبيعات": "pos.sales_screen",
            "المخزون": "pos.inventory_screen",
            "المنتجات": "pos.products_tab",
            "الحركات": "pos.movements",
            "تفاصيل المنتج": "pos.product_details",
            "لا يمكن تحديل عرض المخزون، يرجى التحقق من إعدادات قاعدة البيانات": "pos.inventory_load_error_message",
            "يرجى النقر على منتج لعرض التفاصيل وإدارة المخزون": "inventory.select_product_message",
            "Search for products...": "pos.search_products_placeholder"
        }

        # Check if current text matches any Arabic text
        for arabic_text, translation_key in translation_map.items():
            if current_text == arabic_text:
                # Get English translation using tr function
                english_text = tr(translation_key, "")  # Use empty default to see if key exists
                if not english_text:  # If key doesn't exist, try without default
                    english_text = tr(translation_key)
                print(f"Found match! Translation key: '{translation_key}', Result: '{english_text}'")
                widget.setText(english_text)
                return

        print(f"No translation found for: '{current_text}'")

    def update_table_headers(self):
        """Update table headers with current language."""
        if hasattr(self, 'inventory_table') and self.inventory_table:
            self.inventory_table.setHorizontalHeaderLabels([
                self.translation_manager.get_text("pos.product_column", "Product"),
                self.translation_manager.get_text("pos.description_column", "Description"),
                self.translation_manager.get_text("pos.barcode", "Barcode"),
                self.translation_manager.get_text("pos.stock_column", "Stock"),
                self.translation_manager.get_text("pos.min_stock_column", "Min Stock"),
                self.translation_manager.get_text("pos.actions_column", "Actions")
            ])
            print("Updated inventory table headers")

        if hasattr(self, 'transactions_table') and self.transactions_table:
            self.transactions_table.setHorizontalHeaderLabels([
                self.translation_manager.get_text("pos.date_column", "Date"),
                self.translation_manager.get_text("pos.type_column", "Type"),
                self.translation_manager.get_text("pos.quantity_column", "Quantity"),
                self.translation_manager.get_text("pos.reference_column", "Reference"),
                self.translation_manager.get_text("pos.notes_column", "Notes")
            ])
            print("Updated transactions table headers")

    def update_category_combo(self):
        """Update category combo box items."""
        if hasattr(self, 'category_combo') and self.category_combo:
            current_data = self.category_combo.currentData()
            self.category_combo.setItemText(0, self.translation_manager.get_text("pos.all_categories", "All Categories"))
            print("Updated category combo box")
            # Restore selection
            index = self.category_combo.findData(current_data)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)

    def update_tab_widgets(self):
        """Update tab widgets and other UI elements."""
        # Find and update QTabWidget tabs
        all_tab_widgets = self.findChildren(QTabWidget)
        print(f"Total QTabWidget found: {len(all_tab_widgets)}")

        for idx, tab_widget in enumerate(all_tab_widgets):
            print(f"TabWidget {idx+1} has {tab_widget.count()} tabs")
            for i in range(tab_widget.count()):
                current_text = tab_widget.tabText(i)
                print(f"  Tab {i}: '{current_text}'")
                if current_text == "المنتجات":
                    tab_widget.setTabText(i, self.translation_manager.get_text("pos.products_tab", "Products"))
                    print("✅ Updated Products tab")
                elif current_text == "الحركات":
                    tab_widget.setTabText(i, self.translation_manager.get_text("pos.movements", "Movements"))
                    print("✅ Updated Movements tab")
                elif current_text == "التفاصيل":
                    tab_widget.setTabText(i, self.translation_manager.get_text("pos.details", "Details"))
                    print("✅ Updated Details tab")
                elif current_text == "Products":
                    print("Products tab already in English")
                elif current_text == "Movements":
                    print("Movements tab already in English")
                elif current_text == "Details":
                    print("Details tab already in English")

        # Update any labels that might contain error messages
        for label in self.findChildren(QLabel):
            if "لا يمكن تحديل عرض المخزون" in label.text():
                label.setText(self.translation_manager.get_text("pos.inventory_load_error_message",
                    "Cannot update inventory view, please check database settings"))
                print("Updated error message")

        # Update group box titles
        for group_box in self.findChildren(QGroupBox):
            if group_box.title() == "تفاصيل المنتج":
                group_box.setTitle(self.translation_manager.get_text("pos.product_details", "Product Details"))
                print("Updated Product Details group box title")

        # Update search placeholders and text
        for line_edit in self.findChildren(QLineEdit):
            if line_edit.placeholderText() == "Search for products...":
                line_edit.setPlaceholderText(self.translation_manager.get_text("pos.search_products_placeholder", "Search for products..."))
                print("Updated search placeholder")

        # Update any status bar or bottom messages
        for label in self.findChildren(QLabel):
            label_text = label.text()
            if "لا يمكن تحديل عرض المخزون" in label_text or "يرجى التحقق من إعدادات قاعدة البيانات" in label_text:
                label.setText(self.translation_manager.get_text("pos.inventory_load_error_message",
                    "Cannot update inventory view, please check database settings"))
                print("Updated bottom error message")

        # Update status label specifically
        if hasattr(self, 'status_label') and self.status_label:
            if "لا يمكن تحديل عرض المخزون" in self.status_label.text():
                self.status_label.setText(self.translation_manager.get_text("pos.inventory_load_error_message",
                    "Cannot update inventory view, please check database settings"))
                print("Updated status label")

        # Force update of all combo boxes
        for combo_box in self.findChildren(QComboBox):
            for i in range(combo_box.count()):
                item_text = combo_box.itemText(i)
                if item_text == "جميع الفئات":
                    combo_box.setItemText(i, self.translation_manager.get_text("pos.all_categories", "All Categories"))
                    print("Updated combo box item: All Categories")

        # Search for any other UI elements that might contain "المنتجات"
        print("=== Searching for 'المنتجات' in all widgets ===")
        for widget in self.findChildren(QWidget):
            if hasattr(widget, 'text') and callable(widget.text):
                try:
                    text = widget.text()
                    if "المنتجات" in text:
                        print(f"Found 'المنتجات' in {type(widget).__name__}: '{text}'")
                        if hasattr(widget, 'setText'):
                            widget.setText(text.replace("المنتجات", "Products"))
                            print(f"✅ Updated {type(widget).__name__} text")
                except:
                    pass

        # Force update specific elements that were highlighted
        self.force_update_highlighted_elements()

    def force_update_highlighted_elements(self):
        """Force update the specific elements that were highlighted in red."""
        print("=== FORCE UPDATING HIGHLIGHTED ELEMENTS ===")

        # 1. Update dropdown "جميع الفئات" → "All Categories"
        if hasattr(self, 'category_combo'):
            print(f"Category combo found with {self.category_combo.count()} items")
            for i in range(self.category_combo.count()):
                item_text = self.category_combo.itemText(i)
                print(f"  Item {i}: '{item_text}'")
                if item_text == "جميع الفئات":
                    self.category_combo.setItemText(i, "All Categories")
                    print("🔴 FORCED UPDATE: Dropdown 'جميع الفئات' → 'All Categories'")
        else:
            print("Category combo not found")

        # 2. Update section headers
        group_boxes = self.findChildren(QGroupBox)
        print(f"Found {len(group_boxes)} group boxes")
        for group_box in group_boxes:
            title = group_box.title()
            print(f"  Group box title: '{title}'")
            if title == "تفاصيل المنتج":
                group_box.setTitle("Product Details")
                print("🔴 FORCED UPDATE: Section header 'تفاصيل المنتج' → 'Product Details'")
            elif title == "تفاصيل المخزون":
                group_box.setTitle("Inventory Details")
                print("🔴 FORCED UPDATE: Section header 'تفاصيل المخزون' → 'Inventory Details'")

        # 3. Update bottom status message and other labels
        labels = self.findChildren(QLabel)
        print(f"Found {len(labels)} labels")
        for label in labels:
            text = label.text()
            if "لا يمكن تحديل عرض المخزون" in text:
                print(f"  Found status label: '{text[:50]}...'")
                label.setText("Cannot update inventory view, please check database settings")
                print("🔴 FORCED UPDATE: Bottom status message")
            elif "يرجى النقر على منتج لعرض التفاصيل وإدارة المخزون" in text:
                print(f"  Found instruction label: '{text[:50]}...'")
                label.setText("Please click on a product to view details and manage inventory")
                print("🔴 FORCED UPDATE: Instruction message")
            elif text == "لا يمكن تحديل عرض المخزون، يرجى التحقق من إعدادات قاعدة البيانات":
                print(f"  Found exact status label: '{text}'")
                label.setText("Cannot update inventory view, please check database settings")
                print("🔴 FORCED UPDATE: Exact bottom status message")
            elif text == "يرجى النقر على منتج لعرض التفاصيل وإدارة المخزون":
                print(f"  Found exact instruction label: '{text}'")
                label.setText("Please click on a product to view details and manage inventory")
                print("🔴 FORCED UPDATE: Exact instruction message")

        # 4. Search for "المنتجات" tab in all possible locations
        tab_widgets = self.findChildren(QTabWidget)
        print(f"Found {len(tab_widgets)} tab widgets")
        for idx, tab_widget in enumerate(tab_widgets):
            print(f"  Tab widget {idx+1} has {tab_widget.count()} tabs")
            for i in range(tab_widget.count()):
                tab_text = tab_widget.tabText(i)
                print(f"    Tab {i}: '{tab_text}'")
                if tab_text == "المنتجات":
                    tab_widget.setTabText(i, "Products")
                    print("🔴 FORCED UPDATE: Tab 'المنتجات' → 'Products'")

        # 5. Update status label specifically
        if hasattr(self, 'status_label') and self.status_label:
            print(f"  Status label current text: '{self.status_label.text()}'")
            if "لا يمكن تحديل عرض المخزون" in self.status_label.text() or self.status_label.text() == "لا يمكن تحديل عرض المخزون، يرجى التحقق من إعدادات قاعدة البيانات":
                self.status_label.setText("Cannot update inventory view, please check database settings")
                print("🔴 FORCED UPDATE: Status label updated")
            elif self.status_label.text() == "Cannot update inventory view, please check database settings":
                print("Status label already in English")

        # Force immediate UI refresh
        self.update()
        self.repaint()
        print("=== FORCED UPDATE COMPLETE ===")

    def clear_layout(self):
        """Clear the current layout."""
        layout = self.layout()
        if layout:
            # Clear all items from the layout
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
                elif child.layout():
                    self.clear_child_layout(child.layout())

            # Delete the layout itself
            layout.deleteLater()
            # Remove the layout from the widget
            self.setLayout(None)

    def clear_child_layout(self, layout):
        """Recursively clear a child layout."""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
            elif child.layout():
                self.clear_child_layout(child.layout())
        layout.deleteLater()

    def on_inventory_selected(self):
        """Handle inventory selection."""
        selected_items = self.inventory_table.selectedItems()
        if not selected_items:
            self.enable_details(False)
            self.selected_inventory_id = None
            return

        # Get inventory ID
        inventory_id = selected_items[0].data(Qt.UserRole)
        self.selected_inventory_id = inventory_id

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
        if not inventory_item:
            self.enable_details(False)
            return

        # Update details
        self.product_name_label.setText(inventory_item.product_name or "")
        self.product_category_label.setText(inventory_item.category_name or "")
        self.product_barcode_label.setText(inventory_item.product_barcode or "")

        self.stock_quantity_spin.setValue(inventory_item.stock_quantity)
        self.min_stock_level_spin.setValue(inventory_item.min_stock_level)
        self.location_edit.setText(inventory_item.location or "")
        self.notes_edit.setText(inventory_item.notes or "")

        # Load transactions
        self.load_transactions(inventory_id)

        # Enable details
        self.enable_details(True)

    def load_transactions(self, inventory_id):
        """Load transactions for an inventory item.

        Args:
            inventory_id (int): Inventory ID
        """
        # Clear table
        self.transactions_table.setRowCount(0)

        # Get transactions
        transactions = self.pos_manager.get_pos_inventory_transactions(inventory_id=inventory_id)

        # Add transactions to table
        for transaction in transactions:
            row = self.transactions_table.rowCount()
            self.transactions_table.insertRow(row)

            # Date
            date_item = QTableWidgetItem(format_date(transaction.created_at))
            self.transactions_table.setItem(row, 0, date_item)

            # Type
            type_text = ""
            if transaction.transaction_type == POSInventoryTransaction.TYPE_PURCHASE:
                type_text = tr("inventory.purchase_type", "شراء")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_SALE:
                type_text = tr("inventory.sale_type", "بيع")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_ADJUSTMENT:
                type_text = tr("inventory.adjustment_type", "تعديل")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_TRANSFER_IN:
                type_text = tr("inventory.transfer_in_type", "تحويل وارد")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_TRANSFER_OUT:
                type_text = tr("inventory.transfer_out_type", "تحويل صادر")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_RETURN:
                type_text = tr("inventory.return_type", "مرتجع")
            elif transaction.transaction_type == POSInventoryTransaction.TYPE_INITIAL:
                type_text = tr("inventory.initial_type", "رصيد افتتاحي")

            type_item = QTableWidgetItem(type_text)
            self.transactions_table.setItem(row, 1, type_item)

            # Quantity
            quantity_item = QTableWidgetItem(str(transaction.quantity))
            self.transactions_table.setItem(row, 2, quantity_item)

            # Reference
            reference_text = ""
            if transaction.reference_type == 'invoice':
                reference_text = tr("inventory.invoice_ref", "فاتورة #{0}").format(transaction.reference_id)
            elif transaction.reference_type == 'manual':
                reference_text = tr("inventory.manual_ref", "يدوي")

            reference_item = QTableWidgetItem(reference_text)
            self.transactions_table.setItem(row, 3, reference_item)

            # Notes
            notes_item = QTableWidgetItem(transaction.notes or "")
            self.transactions_table.setItem(row, 4, notes_item)

    def init_details_with_defaults(self):
        """Initialize details panel with default values."""
        # Set default values for product details
        self.product_name_label.setText(tr("inventory.no_product_selected", "لم يتم اختيار منتج"))
        self.product_category_label.setText("-")
        self.product_barcode_label.setText("-")

        # Set default values for inventory details
        self.stock_quantity_spin.setValue(0)
        self.min_stock_level_spin.setValue(0)
        self.location_edit.setText("")
        self.notes_edit.setText(tr("pos.select_product_message", "Please click on a product to view details and manage inventory"))

        # Enable the details panel but disable editing
        self.details_tabs.setEnabled(True)
        self.save_button.setEnabled(False)
        self.add_stock_button.setEnabled(False)
        self.remove_stock_button.setEnabled(False)

        # Clear transactions table
        self.transactions_table.setRowCount(0)

        # Add a hint row to transactions table
        self.transactions_table.insertRow(0)
        hint_item = QTableWidgetItem(tr("inventory.no_transactions", "لا توجد حركات"))
        hint_item.setTextAlignment(Qt.AlignCenter)
        self.transactions_table.setSpan(0, 0, 1, 5)
        self.transactions_table.setItem(0, 0, hint_item)

    def enable_details(self, enabled):
        """Enable or disable details widgets.

        Args:
            enabled (bool): Whether to enable the widgets
        """
        # If not enabled, show default values
        if not enabled:
            self.init_details_with_defaults()
            return

        # Otherwise enable editing controls
        self.details_tabs.setEnabled(True)
        self.save_button.setEnabled(True)
        self.add_stock_button.setEnabled(True)
        self.remove_stock_button.setEnabled(True)

    def update_translations(self):
        """Update all UI elements with current translations."""
        # Update title
        title_label = self.findChild(QLabel, "title_label")
        if title_label:
            title_label.setText(tr("inventory.pos_inventory_management", "إدارة مخزون نقاط البيع"))

        # Update search placeholder
        self.search_edit.setPlaceholderText(tr("inventory.search_products_placeholder", "بحث عن منتجات..."))

        # Update checkbox
        self.show_zero_stock.setText(tr("inventory.show_zero_stock", "إظهار المنتجات بدون مخزون"))

        # Update buttons
        self.add_product_button.setText(tr("inventory.add_product", "إضافة منتج"))
        self.sync_button.setText(tr("inventory.sync_from_main", "مزامنة من المخزون الرئيسي"))
        self.save_button.setText(tr("inventory.save_changes", "حفظ التغييرات"))
        self.add_stock_button.setText(tr("inventory.add_stock_button", "إضافة مخزون"))
        self.remove_stock_button.setText(tr("inventory.remove_stock_button", "سحب مخزون"))

        # Update table headers
        self.inventory_table.setHorizontalHeaderLabels([
            tr("inventory.product_column", "المنتج"),
            tr("inventory.description_column", "الوصف"),
            tr("inventory.barcode", "الباركود"),
            tr("inventory.stock_column", "المخزون"),
            tr("inventory.min_stock_column", "الحد الأدنى"),
            tr("inventory.actions_column", "الإجراءات")
        ])

        # Update transactions table headers
        self.transactions_table.setHorizontalHeaderLabels([
            tr("inventory.date_column", "التاريخ"),
            tr("inventory.type_column", "النوع"),
            tr("inventory.quantity_column", "الكمية"),
            tr("inventory.reference_column", "المرجع"),
            tr("inventory.notes_column", "ملاحظات")
        ])

        # Update group boxes
        for group_box in self.findChildren(QGroupBox):
            if group_box.title() == "تفاصيل المنتج":
                group_box.setTitle(tr("inventory.product_details_section", "تفاصيل المنتج"))
            elif group_box.title() == "تفاصيل المخزون":
                group_box.setTitle(tr("inventory.stock_details", "تفاصيل المخزون"))

        # Update tab titles
        self.details_tabs.setTabText(0, tr("inventory.details", "التفاصيل"))
        self.details_tabs.setTabText(1, tr("inventory.movements", "الحركات"))

        # Update form labels
        self.init_details_with_defaults()

        # Refresh the inventory display
        self.display_inventory()

    def save_inventory(self):
        """Save inventory changes."""
        if not self.selected_inventory_id:
            return

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(self.selected_inventory_id)
        if not inventory_item:
            return

        # Update inventory item
        inventory_item.stock_quantity = self.stock_quantity_spin.value()
        inventory_item.min_stock_level = self.min_stock_level_spin.value()
        inventory_item.location = self.location_edit.text()
        inventory_item.notes = self.notes_edit.toPlainText()

        # Save changes
        if self.pos_manager.update_pos_inventory_item(inventory_item):
            QMessageBox.information(
                self,
                tr("messages.success", "نجاح"),
                tr("inventory.adjustment_success", "تم التعديل بنجاح")
            )

            # Reload inventory
            self.load_data()

            # Emit signal
            self.inventory_updated.emit()
        else:
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.update_error", "حدث خطأ أثناء تحديث المخزون")
            )

    def add_stock(self):
        """Add stock to inventory."""
        if not self.selected_inventory_id:
            return

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(self.selected_inventory_id)
        if not inventory_item:
            return

        # Show dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("inventory.add_stock", "إضافة مخزون"))
        dialog.setMinimumWidth(300)

        # Layout
        layout = QVBoxLayout(dialog)

        # Form
        form_layout = QFormLayout()

        # Product name
        name_label = QLabel(inventory_item.product_name)
        name_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(tr("inventory.product_name_field", "المنتج:"), name_label)

        # Current stock
        current_stock_label = QLabel(str(inventory_item.stock_quantity))
        form_layout.addRow(tr("inventory.current_stock", "المخزون الحالي:"), current_stock_label)

        # Quantity to add
        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, 100000)
        quantity_spin.setValue(1)
        form_layout.addRow(tr("inventory.quantity_to_add", "الكمية المضافة:"), quantity_spin)

        # Notes
        notes_edit = QLineEdit()
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Add transaction
            try:
                # Ensure we have a valid notes value
                notes_text = notes_edit.text()
                if not notes_text or notes_text.strip() == "":
                    notes_text = tr("inventory.manual_addition", "إضافة يدوية")

                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_item.id,
                    transaction_type=POSInventoryTransaction.TYPE_PURCHASE,
                    quantity=quantity_spin.value(),
                    reference_type='manual',
                    notes=notes_text
                )
            except Exception as e:
                print(f"Error creating transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                QMessageBox.information(
                    self,
                    tr("messages.success", "نجاح"),
                    tr("inventory.stock_added", "تمت إضافة {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity_spin.value(), inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Reload transactions
                self.load_transactions(self.selected_inventory_id)

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_add_error", "حدث خطأ أثناء إضافة المخزون")
                )

    def remove_stock(self):
        """Remove stock from inventory."""
        if not self.selected_inventory_id:
            return

        # Get inventory item
        inventory_item = self.pos_manager.get_pos_inventory_item(self.selected_inventory_id)
        if not inventory_item:
            return

        # Check if there's enough stock
        if inventory_item.stock_quantity <= 0:
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.no_stock", "لا يوجد مخزون كافي")
            )
            return

        # Show dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("inventory.remove_stock", "سحب مخزون"))
        dialog.setMinimumWidth(300)

        # Layout
        layout = QVBoxLayout(dialog)

        # Form
        form_layout = QFormLayout()

        # Product name
        name_label = QLabel(inventory_item.product_name)
        name_label.setStyleSheet("font-weight: bold;")
        form_layout.addRow(tr("inventory.product_name_field", "المنتج:"), name_label)

        # Current stock
        current_stock_label = QLabel(str(inventory_item.stock_quantity))
        form_layout.addRow(tr("inventory.current_stock", "المخزون الحالي:"), current_stock_label)

        # Quantity to remove
        quantity_spin = QSpinBox()
        quantity_spin.setRange(1, inventory_item.stock_quantity)
        quantity_spin.setValue(1)
        form_layout.addRow(tr("inventory.quantity_to_remove", "الكمية المسحوبة:"), quantity_spin)

        # Notes
        notes_edit = QLineEdit()
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Add transaction
            try:
                # Ensure we have a valid notes value
                notes_text = notes_edit.text()
                if not notes_text or notes_text.strip() == "":
                    notes_text = tr("inventory.manual_removal", "سحب يدوي")

                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_item.id,
                    transaction_type=POSInventoryTransaction.TYPE_ADJUSTMENT,
                    quantity=quantity_spin.value(),
                    reference_type='decrease',
                    notes=notes_text
                )
            except Exception as e:
                print(f"Error creating transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                QMessageBox.information(
                    self,
                    tr("messages.success", "نجاح"),
                    tr("inventory.stock_removed", "تم سحب {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity_spin.value(), inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Reload transactions
                self.load_transactions(self.selected_inventory_id)

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون")
                )

    def add_new_product(self):
        """Add a new product to POS inventory."""
        # Get products from main inventory that are not in POS inventory
        query = """
        SELECT p.*
        FROM products p
        LEFT JOIN pos_inventory pi ON p.id = pi.product_id
        WHERE pi.id IS NULL AND p.type = 'product'
        ORDER BY p.name
        """

        rows = self.db_manager.execute_query(query)
        if not rows:
            QMessageBox.information(
                self,
                tr("messages.info", "معلومات"),
                tr("inventory.no_products_to_add", "لا توجد منتجات جديدة للإضافة. جميع المنتجات موجودة بالفعل في مخزون نقاط البيع.")
            )
            return

        # Create dialog
        dialog = QDialog(self)
        dialog.setWindowTitle(tr("inventory.add_product", "إضافة منتج"))
        dialog.setMinimumWidth(400)

        # Layout
        layout = QVBoxLayout(dialog)

        # Product selection
        form_layout = QFormLayout()

        # Product combo
        product_combo = QComboBox()
        for row in rows:
            product_combo.addItem(row['name'], row['id'])
        form_layout.addRow(tr("inventory.product_name_field", "المنتج:"), product_combo)

        # Initial stock
        stock_spin = QSpinBox()
        stock_spin.setRange(0, 10000)
        stock_spin.setValue(0)
        form_layout.addRow(tr("inventory.initial_stock", "المخزون الأولي:"), stock_spin)

        # Min stock level
        min_stock_spin = QSpinBox()
        min_stock_spin.setRange(0, 10000)
        min_stock_spin.setValue(0)
        form_layout.addRow(tr("inventory.min_stock_level", "الحد الأدنى للمخزون:"), min_stock_spin)

        # Location
        location_edit = QLineEdit()
        form_layout.addRow(tr("inventory.location_field", "الموقع:"), location_edit)

        # Notes
        notes_edit = QTextEdit()
        notes_edit.setMaximumHeight(100)
        form_layout.addRow(tr("inventory.notes", "ملاحظات:"), notes_edit)

        layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(dialog.accept)
        button_box.rejected.connect(dialog.reject)
        layout.addWidget(button_box)

        # Show dialog
        if dialog.exec() == QDialog.Accepted:
            # Get selected product
            product_id = product_combo.currentData()

            # Create inventory item
            inventory_item = POSInventory(
                product_id=product_id,
                stock_quantity=stock_spin.value(),
                min_stock_level=min_stock_spin.value(),
                location=location_edit.text(),
                notes=notes_edit.toPlainText()
            )

            # Add to POS inventory
            inventory_id = self.pos_manager.add_pos_inventory_item(inventory_item)

            if inventory_id > 0:
                QMessageBox.information(
                    self,
                    tr("messages.success", "نجاح"),
                    tr("inventory.product_added", "تمت إضافة المنتج بنجاح")
                )

                # Reload inventory
                self.load_data()

                # Select the new item
                for row in range(self.inventory_table.rowCount()):
                    item = self.inventory_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == inventory_id:
                        self.inventory_table.selectRow(row)
                        break
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.product_add_error", "حدث خطأ أثناء إضافة المنتج")
                )

    def edit_inventory(self, inventory_id=None):
        """Edit inventory item.

        Args:
            inventory_id (int, optional): Inventory ID to edit. If None, will try to get from sender.
        """
        try:
            # Get inventory ID from sender if not provided
            if inventory_id is None:
                button = self.sender()
                if button:
                    inventory_id = button.property("inventory_id")

            if not inventory_id:
                print("No inventory ID provided")
                return

            # Get inventory item
            inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
            if not inventory_item:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.inventory_not_found", "لم يتم العثور على المنتج في المخزون")
                )
                return

            # Select the item in the table to show details
            for row in range(self.inventory_table.rowCount()):
                item = self.inventory_table.item(row, 0)
                if item and item.data(Qt.UserRole) == inventory_id:
                    self.inventory_table.selectRow(row)
                    break

            # Switch to details tab
            self.details_tabs.setCurrentIndex(0)
        except Exception as e:
            print(f"Error editing inventory: {e}")
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.edit_error", "حدث خطأ أثناء تعديل المنتج: {0}").format(str(e))
            )

    def quick_add_stock(self, inventory_id=None):
        """Quickly add stock to inventory item.

        Args:
            inventory_id (int, optional): Inventory ID to add stock to. If None, will try to get from sender.
        """
        try:
            # Get inventory ID from sender if not provided
            if inventory_id is None:
                button = self.sender()
                if button:
                    inventory_id = button.property("inventory_id")

            if not inventory_id:
                print("No inventory ID provided")
                return

            # Get inventory item
            inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
            if not inventory_item:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.inventory_not_found", "لم يتم العثور على المنتج في المخزون")
                )
                return

            # Ask for quantity
            quantity, ok = QInputDialog.getInt(
                self,
                tr("inventory.add_stock_button", "إضافة مخزون"),
                tr("inventory.enter_quantity", "أدخل الكمية:"),
                1, 1, 1000, 1
            )

            if not ok:
                return

            # Add transaction
            try:
                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_id,
                    transaction_type=POSInventoryTransaction.TYPE_PURCHASE,
                    quantity=quantity,
                    reference_type='manual',
                    notes=tr("inventory.quick_add", "إضافة سريعة")
                )
            except Exception as e:
                print(f"Error creating quick add transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                # Show confirmation
                QMessageBox.information(
                    self,
                    tr("inventory.success", "نجاح"),
                    tr("inventory.stock_added", "تمت إضافة {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity, inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Select the item
                for row in range(self.inventory_table.rowCount()):
                    item = self.inventory_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == inventory_id:
                        self.inventory_table.selectRow(row)
                        break

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_add_error", "حدث خطأ أثناء إضافة المخزون")
                )
        except Exception as e:
            print(f"Error adding stock: {e}")
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.stock_add_error", "حدث خطأ أثناء إضافة المخزون: {0}").format(str(e))
            )

    def quick_remove_stock(self, inventory_id=None):
        """Quickly remove stock from inventory item.

        Args:
            inventory_id (int, optional): Inventory ID to remove stock from. If None, will try to get from sender.
        """
        try:
            # Get inventory ID from sender if not provided
            if inventory_id is None:
                button = self.sender()
                if button:
                    inventory_id = button.property("inventory_id")

            if not inventory_id:
                print("No inventory ID provided")
                return

            # Get inventory item
            inventory_item = self.pos_manager.get_pos_inventory_item(inventory_id)
            if not inventory_item:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.inventory_not_found", "لم يتم العثور على المنتج في المخزون")
                )
                return

            # Check if there's enough stock
            if inventory_item.stock_quantity <= 0:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.no_stock", "لا يوجد مخزون كافي")
                )
                return

            # Ask for quantity
            quantity, ok = QInputDialog.getInt(
                self,
                tr("inventory.remove_stock_button", "سحب مخزون"),
                tr("inventory.enter_quantity", "أدخل الكمية:"),
                1, 1, inventory_item.stock_quantity, 1
            )

            if not ok:
                return

            # Add transaction
            try:
                transaction = POSInventoryTransaction(
                    pos_inventory_id=inventory_id,
                    transaction_type=POSInventoryTransaction.TYPE_ADJUSTMENT,
                    quantity=quantity,
                    reference_type='decrease',
                    notes=tr("inventory.quick_remove", "سحب سريع")
                )
            except Exception as e:
                print(f"Error creating quick remove transaction: {str(e)}")
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.transaction_create_error", "حدث خطأ أثناء إنشاء المعاملة: {0}").format(str(e))
                )
                return

            if self.pos_manager.add_pos_inventory_transaction(transaction) > 0:
                # Show confirmation
                QMessageBox.information(
                    self,
                    tr("inventory.success", "نجاح"),
                    tr("inventory.stock_removed", "تم سحب {0} وحدة من المنتج '{1}' بنجاح").format(
                        quantity, inventory_item.product_name)
                )

                # Reload inventory
                self.load_data()

                # Select the item
                for row in range(self.inventory_table.rowCount()):
                    item = self.inventory_table.item(row, 0)
                    if item and item.data(Qt.UserRole) == inventory_id:
                        self.inventory_table.selectRow(row)
                        break

                # Emit signal
                self.inventory_updated.emit()
            else:
                QMessageBox.warning(
                    self,
                    tr("errors.error", "خطأ"),
                    tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون")
                )
        except Exception as e:
            print(f"Error removing stock: {e}")
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون: {0}").format(str(e))
            )

            # Select the item
            for row in range(self.inventory_table.rowCount()):
                item = self.inventory_table.item(row, 0)
                if item and item.data(Qt.UserRole) == inventory_id:
                    self.inventory_table.selectRow(row)
                    break
        else:
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("inventory.stock_remove_error", "حدث خطأ أثناء سحب المخزون")
            )

    def sync_from_main(self):
        """Sync inventory from main inventory."""
        # Confirm
        result = QMessageBox.question(
            self,
            tr("inventory.confirm", "تأكيد"),
            tr("inventory.sync_confirm", "هل تريد مزامنة المنتجات من المخزون الرئيسي؟"),
            QMessageBox.Yes | QMessageBox.No
        )

        if result != QMessageBox.Yes:
            return

        # Sync
        count = self.pos_manager.sync_pos_inventory_from_main()

        if count > 0:
            QMessageBox.information(
                self,
                tr("inventory.success", "نجاح"),
                tr("inventory.sync_success", "تمت مزامنة {0} منتج من المخزون الرئيسي").format(count)
            )

            # Reload inventory
            self.load_data()

            # Emit signal
            self.inventory_updated.emit()
        else:
            QMessageBox.information(
                self,
                tr("inventory.info", "معلومات"),
                tr("inventory.no_new_products", "لا توجد منتجات جديدة للمزامنة")
            )

    def showEvent(self, event):
        """Handle show event."""
        super().showEvent(event)
        # Force update highlighted elements when view is shown
        if hasattr(self, 'translation_manager') and self.translation_manager.current_language != 'ar':
            self.force_update_highlighted_elements()
