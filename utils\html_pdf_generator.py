#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HTML to PDF Generator for فوترها (Fawterha)
Generates PDF files for invoices and reports using HTML templates
"""

import os
import tempfile
from datetime import datetime
from xhtml2pdf import pisa
# Import only necessary modules

# HTML template for customer statement
CUSTOMER_STATEMENT_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>كشف حساب العميل</title>
    <style>
        @page {
            margin: 1cm;
        }
        @font-face {
            font-family: 'Arabic';
            src: url('C:/Windows/Fonts/arabtype.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }
        @font-face {
            font-family: 'Arabic-Bold';
            src: url('C:/Windows/Fonts/arabtype.ttf') format('truetype');
            font-weight: bold;
            font-style: normal;
        }
        @font-face {
            font-family: 'Tahoma';
            src: url('C:/Windows/Fonts/tahoma.ttf') format('truetype');
            font-weight: normal;
            font-style: normal;
        }
        @font-face {
            font-family: 'Tahoma-Bold';
            src: url('C:/Windows/Fonts/tahomabd.ttf') format('truetype');
            font-weight: bold;
            font-style: normal;
        }
        body {
            font-family: 'Tahoma', 'Arabic', 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            font-size: 12pt;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-bottom: 2px solid #000066;
        }
        .title {
            font-size: 20pt;
            font-weight: bold;
            color: #000066;
            margin-bottom: 10px;
            font-family: 'Tahoma-Bold', 'Arabic-Bold', 'Arial', sans-serif;
        }
        .subtitle {
            font-size: 16pt;
            color: #000066;
            margin-bottom: 5px;
            font-weight: bold;
            font-family: 'Tahoma-Bold', 'Arabic-Bold', 'Arial', sans-serif;
        }
        .info {
            margin-bottom: 8px;
            font-size: 14pt;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 2px solid #000066;
            table-layout: fixed;
        }
        th {
            background-color: #000066;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            font-size: 12pt;
            border: 1px solid #fff;
            font-family: 'Tahoma-Bold', 'Arabic-Bold', 'Arial', sans-serif;
        }
        td {
            padding: 8px;
            text-align: center;
            border: 1px solid #aaa;
            font-size: 11pt;
            word-wrap: break-word;
            font-family: 'Tahoma', 'Arabic', 'Arial', sans-serif;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr.even {
            background-color: #f2f2f2;
        }
        tr.odd {
            background-color: #ffffff;
        }
        td.amount {
            font-weight: bold;
            color: #000066;
            direction: ltr;
            text-align: left;
            font-family: 'Tahoma-Bold', 'Arabic-Bold', 'Arial', sans-serif;
        }
        /* Column widths */
        th:nth-child(1), td:nth-child(1) { width: 12%; } /* Date */
        th:nth-child(2), td:nth-child(2) { width: 12%; } /* Reference */
        th:nth-child(3), td:nth-child(3) { width: 15%; } /* Type */
        th:nth-child(4), td:nth-child(4) { width: 25%; } /* Description */
        th:nth-child(5), td:nth-child(5) { width: 12%; } /* Debit */
        th:nth-child(6), td:nth-child(6) { width: 12%; } /* Credit */
        th:nth-child(7), td:nth-child(7) { width: 12%; } /* Balance */
        .summary {
            margin-top: 25px;
            font-weight: bold;
            font-size: 14pt;
            color: #000066;
            border-top: 1px solid #000066;
            padding-top: 15px;
            font-family: 'Tahoma-Bold', 'Arabic-Bold', 'Arial', sans-serif;
        }
        .summary div {
            margin-bottom: 10px;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">كشف حساب العميل</div>
    </div>

    <div class="info">
        <div class="subtitle">العميل: {customer_name}</div>
        <div class="info">الفترة: من {start_date} إلى {end_date}</div>
        <div class="info">تاريخ التقرير: {report_date}</div>
        {customer_email}
        {customer_phone}
        {customer_address}
    </div>

    <table>
        <thead>
            <tr>
                {header_cells}
            </tr>
        </thead>
        <tbody>
            {row_cells}
        </tbody>
    </table>

    <div class="summary">
        <div>إجمالي الفواتير: {total_invoices}</div>
        <div>إجمالي المدفوعات: {total_paid}</div>
        <div>الرصيد الحالي: {balance}</div>
    </div>

    <div class="footer">
        تم إنشاء هذا التقرير بواسطة تطبيق فوترها - {timestamp}
    </div>

    <!-- No script needed for PDF generation -->
</body>
</html>
"""

def convert_html_to_pdf(html_content, output_file):
    """Convert HTML content to PDF file.

    Args:
        html_content (str): HTML content to convert
        output_file (str): Path to save the PDF file

    Returns:
        bool: True if successful, False otherwise
    """
    # Create a temporary HTML file
    with tempfile.NamedTemporaryFile(suffix='.html', delete=False, mode='w', encoding='utf-8') as f:
        f.write(html_content)
        temp_html = f.name

    # Convert HTML to PDF
    try:
        with open(temp_html, 'r', encoding='utf-8') as f:
            html_content = f.read()

        # Create PDF with improved options for Arabic text
        with open(output_file, 'wb') as f:
            # Configure PDF options
            pdf_options = {
                "encoding": "UTF-8",
                "page-size": "A4",
                "margin-top": "1.0cm",
                "margin-right": "1.0cm",
                "margin-bottom": "1.0cm",
                "margin-left": "1.0cm",
            }

            # Create PDF with Arabic text support
            pdf = pisa.CreatePDF(
                src=html_content,
                dest=f,
                encoding='utf-8',
                link_callback=None,
                show_error_as_pdf=True,  # Show errors in the PDF
                raise_exception=False,   # Don't raise exceptions
                default_css="""
                    @page { size: A4; margin: 1cm; }
                    @font-face { font-family: Tahoma; src: url('C:/Windows/Fonts/tahoma.ttf'); }
                    body { font-family: Tahoma, Arial, sans-serif; direction: rtl; }
                """
            )

        # Check if PDF creation was successful
        if pdf.err:
            print(f"Error creating PDF: {pdf.err}")
            return False

        # Clean up temporary file
        os.unlink(temp_html)
        return True
    except Exception as e:
        print(f"Error converting HTML to PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        # Clean up temporary file
        if os.path.exists(temp_html):
            os.unlink(temp_html)
        return False

def generate_customer_statement_pdf(file_path, customer, start_date, end_date, headers, rows, total_invoices, total_paid, balance):
    """Generate a PDF file for a customer statement using HTML template.

    Args:
        file_path (str): Path to save the PDF file
        customer (Customer): Customer object
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Statement data rows
        total_invoices (str): Total invoices amount
        total_paid (str): Total paid amount
        balance (str): Current balance
    """
    try:
        # Format customer information
        customer_email = f'<div class="info">البريد الإلكتروني: {customer.email}</div>' if customer.email else ''
        customer_phone = f'<div class="info">الهاتف: {customer.phone}</div>' if customer.phone else ''
        customer_address = f'<div class="info">العنوان: {customer.address}</div>' if customer.address else ''

        # Format headers
        header_cells = ''.join([f'<th>{header}</th>' for header in headers])

        # Format rows with improved styling
        row_cells = ''
        for i, row in enumerate(rows):
            # Add alternating row classes
            row_class = 'even' if i % 2 == 0 else 'odd'
            row_cells += f'<tr class="{row_class}">'

            for j, cell in enumerate(row):
                # Add special styling for amount columns (columns 4, 5, 6)
                cell_class = ''
                if j in [4, 5, 6]:  # Amount columns
                    cell_class = 'amount'

                row_cells += f'<td class="{cell_class}">{cell}</td>'
            row_cells += '</tr>'

        # Fill template
        html_content = CUSTOMER_STATEMENT_TEMPLATE.format(
            customer_name=customer.name,
            start_date=start_date,
            end_date=end_date,
            report_date=datetime.now().strftime('%Y-%m-%d'),
            customer_email=customer_email,
            customer_phone=customer_phone,
            customer_address=customer_address,
            header_cells=header_cells,
            row_cells=row_cells,
            total_invoices=total_invoices,
            total_paid=total_paid,
            balance=balance,
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M')
        )

        # Save HTML file for debugging (optional)
        debug_html_path = file_path.replace('.pdf', '.html')
        with open(debug_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        # Convert HTML to PDF
        success = convert_html_to_pdf(html_content, file_path)

        if not success:
            print(f"Failed to convert HTML to PDF: {file_path}")
            # Provide the HTML file as fallback
            return debug_html_path

        return file_path
    except Exception as e:
        print(f"Error generating customer statement PDF: {str(e)}")
        raise
