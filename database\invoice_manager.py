#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Manager for فوترها (Fawterha)
Handles database operations for invoices
"""

from models.invoice import Invoice
from models.invoice_item import InvoiceItem
from models.customer import Customer


class InvoiceManager:
    """Manager for invoice database operations."""

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the invoice manager.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        self.db_manager = db_manager
        self.currency_manager = currency_manager

    def get_all_invoices(self):
        """Get all invoices.

        Returns:
            list: List of Invoice objects
        """
        query = """
        SELECT i.*, c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        ORDER BY i.id DESC
        """
        rows = self.db_manager.execute_query(query)
        invoices = []

        for row in rows:
            invoice = Invoice.from_db_row(row)
            invoice.customer = Customer(id=row['customer_id'], name=row['customer_name'])

            # Add currency information if available
            if self.currency_manager and invoice.currency_id:
                invoice.currency = self.currency_manager.get_currency_by_id(invoice.currency_id)

            invoices.append(invoice)

        return invoices

    def get_invoices_by_status(self, status):
        """Get invoices by status.

        Args:
            status (str): Invoice status

        Returns:
            list: List of Invoice objects
        """
        query = """
        SELECT i.*, c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.status = ?
        ORDER BY i.id DESC
        """
        rows = self.db_manager.execute_query(query, (status,))
        invoices = []

        for row in rows:
            invoice = Invoice.from_db_row(row)
            invoice.customer = Customer(id=row['customer_id'], name=row['customer_name'])

            # Add currency information if available
            if self.currency_manager and invoice.currency_id:
                invoice.currency = self.currency_manager.get_currency_by_id(invoice.currency_id)

            invoices.append(invoice)

        return invoices

    def get_invoice_by_id(self, invoice_id):
        """Get an invoice by ID.

        Args:
            invoice_id (int): Invoice ID

        Returns:
            Invoice: Invoice object or None if not found
        """
        query = """
        SELECT i.*, c.name as customer_name
        FROM invoices i
        JOIN customers c ON i.customer_id = c.id
        WHERE i.id = ?
        """
        rows = self.db_manager.execute_query(query, (invoice_id,))

        if not rows:
            return None

        invoice = Invoice.from_db_row(rows[0])
        invoice.customer = Customer(id=rows[0]['customer_id'], name=rows[0]['customer_name'])

        # Add currency information if available
        if self.currency_manager and invoice.currency_id:
            invoice.currency = self.currency_manager.get_currency_by_id(invoice.currency_id)

        # Get invoice items
        items_query = """
        SELECT * FROM invoice_items
        WHERE invoice_id = ?
        """
        item_rows = self.db_manager.execute_query(items_query, (invoice_id,))
        invoice.items = [InvoiceItem.from_db_row(row) for row in item_rows]

        return invoice

    def get_customer_by_id(self, customer_id):
        """Get a customer by ID.

        Args:
            customer_id (int): Customer ID

        Returns:
            Customer: Customer object or None if not found
        """
        query = """
        SELECT * FROM customers
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (customer_id,))
        return Customer.from_db_row(rows[0]) if rows else None

    def get_invoice_items(self, invoice_id):
        """Get all items for an invoice.

        Args:
            invoice_id (int): Invoice ID

        Returns:
            list: List of InvoiceItem objects
        """
        query = """
        SELECT * FROM invoice_items
        WHERE invoice_id = ?
        """
        rows = self.db_manager.execute_query(query, (invoice_id,))
        return [InvoiceItem.from_db_row(row) for row in rows]

    def add_invoice(self, invoice, items=None):
        """Add a new invoice.

        Args:
            invoice (Invoice): Invoice object to add
            items (list, optional): List of InvoiceItem objects. Defaults to None.

        Returns:
            int: ID of the new invoice
        """
        try:
            # Generate invoice number if not provided
            if not hasattr(invoice, 'invoice_number') or not invoice.invoice_number:
                # Get next invoice number from settings
                settings_query = "SELECT value FROM settings WHERE key = ?"
                prefix_row = self.db_manager.execute_query(settings_query, ("invoice_prefix",))
                number_row = self.db_manager.execute_query(settings_query, ("next_invoice_number",))

                prefix = prefix_row[0]['value'] if prefix_row else "INV-"
                next_number = number_row[0]['value'] if number_row else "1001"

                invoice.invoice_number = f"{prefix}{next_number}"

                # Increment next invoice number
                next_number_int = int(next_number) + 1
                update_query = "UPDATE settings SET value = ? WHERE key = ?"
                self.db_manager.execute_update(update_query, (str(next_number_int), "next_invoice_number"))

            # Set issue date if not provided
            if not hasattr(invoice, 'issue_date') or not invoice.issue_date:
                from datetime import datetime
                invoice.issue_date = datetime.now()

            # Set due date if not provided
            if not hasattr(invoice, 'due_date') or not invoice.due_date:
                from datetime import datetime, timedelta
                invoice.due_date = datetime.now() + timedelta(days=30)

            # Set amount_due if not provided
            if not hasattr(invoice, 'amount_due') or invoice.amount_due is None:
                invoice.amount_due = invoice.total - (invoice.amount_paid or 0)

            # Insert invoice using db_manager's execute_insert method
            query = """
            INSERT INTO invoices (
                invoice_number, customer_id, issue_date, due_date, currency_id,
                subtotal, discount, tax, total, amount_paid, amount_due, notes, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                invoice.invoice_number,
                invoice.customer_id,
                invoice.issue_date.strftime('%Y-%m-%d'),
                invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else None,
                getattr(invoice, 'currency_id', None),
                invoice.subtotal,
                invoice.discount,
                invoice.tax,
                invoice.total,
                getattr(invoice, 'amount_paid', 0),
                invoice.amount_due,
                getattr(invoice, 'notes', ''),
                invoice.status
            )

            invoice_id = self.db_manager.execute_insert(query, params)

            # Insert items if provided
            if items and invoice_id > 0:
                for item in items:
                    item_query = """
                    INSERT INTO invoice_items (
                        invoice_id, description, quantity, unit_price,
                        discount, tax, total
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    item_params = (
                        invoice_id, item.description, item.quantity, item.unit_price,
                        item.discount, item.tax, item.total
                    )
                    self.db_manager.execute_insert(item_query, item_params)

            return invoice_id
        except Exception as e:
            print(f"Error adding invoice: {e}")
            raise e

    def update_invoice(self, invoice, items):
        """Update an existing invoice.

        Args:
            invoice (Invoice): Invoice object to update
            items (list): List of InvoiceItem objects

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Update invoice using db_manager's execute_update method
            query = """
            UPDATE invoices
            SET invoice_number = ?, customer_id = ?, issue_date = ?, due_date = ?, currency_id = ?,
                subtotal = ?, discount = ?, tax = ?, total = ?, amount_paid = ?, amount_due = ?,
                notes = ?, status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            params = (
                invoice.invoice_number,
                invoice.customer_id,
                invoice.issue_date.strftime('%Y-%m-%d'),
                invoice.due_date.strftime('%Y-%m-%d') if invoice.due_date else None,
                invoice.currency_id,
                invoice.subtotal,
                invoice.discount,
                invoice.tax,
                invoice.total,
                invoice.amount_paid,
                invoice.amount_due,
                invoice.notes,
                invoice.status,
                invoice.id
            )

            result = self.db_manager.execute_update(query, params) > 0

            if result:
                # Delete existing items
                delete_query = "DELETE FROM invoice_items WHERE invoice_id = ?"
                self.db_manager.execute_update(delete_query, (invoice.id,))

                # Insert new items
                for item in items:
                    item_query = """
                    INSERT INTO invoice_items (
                        invoice_id, description, quantity, unit_price,
                        discount, tax, total
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    item_params = (
                        invoice.id, item.description, item.quantity, item.unit_price,
                        item.discount, item.tax, item.total
                    )
                    self.db_manager.execute_insert(item_query, item_params)

            return result
        except Exception as e:
            print(f"Error updating invoice: {e}")
            raise e

    def delete_invoice(self, invoice_id):
        """Delete an invoice.

        Args:
            invoice_id (int): ID of the invoice to delete

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Delete invoice items first (should cascade, but just to be safe)
            delete_items_query = "DELETE FROM invoice_items WHERE invoice_id = ?"
            self.db_manager.execute_update(delete_items_query, (invoice_id,))

            # Delete invoice
            delete_invoice_query = "DELETE FROM invoices WHERE id = ?"
            result = self.db_manager.execute_update(delete_invoice_query, (invoice_id,)) > 0

            return result
        except Exception as e:
            print(f"Error deleting invoice: {e}")
            raise e

    def update_invoice_status(self, invoice_id, status):
        """Update an invoice's status.

        Args:
            invoice_id (int): Invoice ID
            status (str): New status

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE invoices
        SET status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (status, invoice_id)) > 0

    def add_invoice_item(self, item):
        """Add a new invoice item.

        Args:
            item (InvoiceItem): Invoice item to add

        Returns:
            int: ID of the new invoice item
        """
        query = """
        INSERT INTO invoice_items (
            invoice_id, product_id, description, quantity, unit_price,
            discount_percent, tax_percent, discount, tax, total
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """

        # Calculate values if not already set
        if not hasattr(item, 'discount') or item.discount is None:
            item.discount = item.unit_price * item.quantity * (item.discount_percent / 100)

        if not hasattr(item, 'tax') or item.tax is None:
            item.tax = (item.unit_price * item.quantity - item.discount) * (item.tax_percent / 100)

        if not hasattr(item, 'total') or item.total is None:
            item.total = (item.unit_price * item.quantity) - item.discount + item.tax

        params = (
            item.invoice_id,
            getattr(item, 'product_id', None),
            item.description,
            item.quantity,
            item.unit_price,
            getattr(item, 'discount_percent', 0),
            getattr(item, 'tax_percent', 0),
            item.discount,
            item.tax,
            item.total
        )

        return self.db_manager.execute_insert(query, params)
