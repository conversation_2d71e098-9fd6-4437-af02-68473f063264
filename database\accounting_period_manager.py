#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Accounting Period Manager for فوترها (Fawterha)
Handles database operations for accounting periods
"""

from datetime import datetime
from models.accounting_period import AccountingPeriod


class AccountingPeriodManager:
    """Manager for accounting period database operations."""

    def __init__(self, db_manager):
        """Initialize the accounting period manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    def get_all_periods(self):
        """Get all accounting periods.

        Returns:
            list: List of AccountingPeriod objects
        """
        query = """
        SELECT id, name, start_date, end_date, is_closed, closed_date, created_at, updated_at
        FROM accounting_periods
        ORDER BY start_date DESC
        """
        rows = self.db_manager.execute_query(query)
        return [AccountingPeriod.from_db_row(row) for row in rows]

    def get_period_by_id(self, period_id):
        """Get an accounting period by ID.

        Args:
            period_id (int): Period ID

        Returns:
            AccountingPeriod: AccountingPeriod object or None if not found
        """
        query = """
        SELECT id, name, start_date, end_date, is_closed, closed_date, created_at, updated_at
        FROM accounting_periods
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (period_id,))
        return AccountingPeriod.from_db_row(rows[0]) if rows else None

    def get_period_by_date(self, date):
        """Get an accounting period by date.

        Args:
            date (datetime): Date to find period for

        Returns:
            AccountingPeriod: AccountingPeriod object or None if not found
        """
        query = """
        SELECT id, name, start_date, end_date, is_closed, closed_date, created_at, updated_at
        FROM accounting_periods
        WHERE start_date <= ? AND end_date >= ?
        """
        rows = self.db_manager.execute_query(query, (date, date))
        return AccountingPeriod.from_db_row(rows[0]) if rows else None

    def get_current_period(self):
        """Get the current accounting period.

        Returns:
            AccountingPeriod: AccountingPeriod object or None if not found
        """
        today = datetime.now().date()
        return self.get_period_by_date(today)

    def add_period(self, period):
        """Add a new accounting period.

        Args:
            period (AccountingPeriod): AccountingPeriod object to add

        Returns:
            int: ID of the new period
        """
        # Check for overlapping periods
        if self._is_period_overlapping(period):
            raise ValueError("The accounting period overlaps with an existing period")

        query = """
        INSERT INTO accounting_periods (name, start_date, end_date, is_closed)
        VALUES (?, ?, ?, ?)
        """
        params = (
            period.name,
            period.start_date,
            period.end_date,
            1 if period.is_closed else 0
        )
        return self.db_manager.execute_insert(query, params)

    def update_period(self, period):
        """Update an existing accounting period.

        Args:
            period (AccountingPeriod): AccountingPeriod object to update

        Returns:
            bool: True if successful, False otherwise
        """
        # Check for overlapping periods
        if self._is_period_overlapping(period, exclude_id=period.id):
            raise ValueError("The accounting period overlaps with an existing period")

        query = """
        UPDATE accounting_periods
        SET name = ?, start_date = ?, end_date = ?, is_closed = ?, closed_date = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            period.name,
            period.start_date,
            period.end_date,
            1 if period.is_closed else 0,
            period.closed_date,
            period.id
        )
        return self.db_manager.execute_update(query, params) > 0

    def close_period(self, period_id):
        """Close an accounting period.

        Args:
            period_id (int): Period ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Get the period
        period = self.get_period_by_id(period_id)
        if not period:
            return False

        # Check if the period is already closed
        if period.is_closed:
            return True

        # Close the period
        query = """
        UPDATE accounting_periods
        SET is_closed = 1, closed_date = CURRENT_DATE, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (period_id,)) > 0

    def reopen_period(self, period_id):
        """Reopen a closed accounting period.

        Args:
            period_id (int): Period ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Get the period
        period = self.get_period_by_id(period_id)
        if not period:
            return False

        # Check if the period is already open
        if not period.is_closed:
            return True

        # Reopen the period
        query = """
        UPDATE accounting_periods
        SET is_closed = 0, closed_date = NULL, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (period_id,)) > 0

    def delete_period(self, period_id):
        """Delete an accounting period.

        Args:
            period_id (int): Period ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Get the period
        period = self.get_period_by_id(period_id)
        if not period:
            return False

        # Check if there are any transactions in this period
        check_query = """
        SELECT COUNT(*) FROM transactions
        WHERE transaction_date BETWEEN ? AND ?
        """
        result = self.db_manager.execute_query(check_query, (period.start_date, period.end_date))
        if result and result[0][0] > 0:
            return False  # Period has transactions

        # Delete the period
        query = """
        DELETE FROM accounting_periods WHERE id = ?
        """
        return self.db_manager.execute_update(query, (period_id,)) > 0

    def _is_period_overlapping(self, period, exclude_id=None):
        """Check if a period overlaps with existing periods.

        Args:
            period (AccountingPeriod): Period to check
            exclude_id (int, optional): Period ID to exclude from the check

        Returns:
            bool: True if the period overlaps, False otherwise
        """
        query = """
        SELECT COUNT(*) FROM accounting_periods
        WHERE (
            (start_date <= ? AND end_date >= ?) OR
            (start_date <= ? AND end_date >= ?) OR
            (start_date >= ? AND end_date <= ?)
        )
        """
        params = [
            period.end_date, period.end_date,  # Period ends during an existing period
            period.start_date, period.start_date,  # Period starts during an existing period
            period.start_date, period.end_date  # Period contains an existing period
        ]

        if exclude_id:
            query += " AND id != ?"
            params.append(exclude_id)

        result = self.db_manager.execute_query(query, tuple(params))
        return result and result[0][0] > 0
