#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
HTML Generator for فوترها (Fawterha)
Generates HTML content for invoices
"""

from utils.currency_helper import get_currency_symbol
import os
import datetime

def get_status_display(status):
    """Get the display text for a status.

    Args:
        status (str): Status code

    Returns:
        str: Display text
    """
    status_map = {
        'draft': "مسودة",
        'pending': "منتظرة",
        'paid': "مدفوعة",
        'cancelled': "ملغاة"
    }
    return status_map.get(status, status)

def get_status_color(status):
    """Get the color for a status.

    Args:
        status (str): Status code

    Returns:
        str: Color code
    """
    status_colors = {
        'draft': "#546e7a",      # Blue-gray
        'pending': "#ff8f00",    # Amber
        'paid': "#2e7d32",       # Green
        'cancelled': "#c62828"   # Red
    }
    return status_colors.get(status, "#212121")

def generate_invoice_html(invoice, customer, items, company_info, template=None):
    """Generate HTML content for an invoice.

    Args:
        invoice: Invoice object
        customer: Customer object
        items: List of invoice items
        company_info: Company information dictionary
        template: Template object (optional)

    Returns:
        str: HTML content
    """
    # Get colors from template
    header_color = template.header_color if template else "#0d47a1"
    text_color = template.text_color if template else "#212121"
    accent_color = template.accent_color if template else "#1e88e5"
    font_family = template.font_family if template else "Arial"
    font_size = template.font_size if template else 12

    # Get currency
    currency = company_info.get('currency', 'SAR')
    currency_symbol = get_currency_symbol(currency)

    # Generate CSS
    css = f"""
        @page {{
            size: A4;
            margin: 1cm;
        }}
        @media print {{
            body {{
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }}
        }}
        body {{
            font-family: {font_family}, 'Segoe UI', 'Arial', sans-serif;
            font-size: {font_size}pt;
            color: {text_color};
            background-color: white;
            margin: 0;
            padding: 20px;
            direction: rtl;
        }}
        .invoice-header {{
            background-color: {header_color};
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .company-info {{
            flex: 1;
        }}
        .company-name {{
            font-size: 24pt;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .invoice-title {{
            font-size: 28pt;
            font-weight: bold;
            color: {accent_color};
            text-align: center;
            margin: 20px 0;
        }}
        .details-container {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }}
        .invoice-details, .customer-details {{
            border: 2px solid #bdbdbd;
            border-radius: 8px;
            background-color: #f5f5f5;
            padding: 20px;
            flex: 1;
            margin: 0 10px;
        }}
        .section-title {{
            font-size: 16pt;
            font-weight: bold;
            color: {accent_color};
            margin-bottom: 10px;
        }}
        .items-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        .items-table th {{
            background-color: {accent_color};
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
        }}
        .items-table td {{
            padding: 8px;
            border-bottom: 1px solid #e0e0e0;
            text-align: center;
        }}
        .items-table td:first-child {{
            text-align: right;
        }}
        .items-table tr:nth-child(even) {{
            background-color: #f5f5f5;
        }}
        .totals {{
            text-align: left;
            margin-top: 20px;
            margin-right: 20px;
            border: 2px solid #0000ff;
            border-radius: 8px;
            padding: 10px;
            width: 300px;
            float: left;
        }}
        .total-row {{
            margin: 5px 0;
        }}
        .grand-total {{
            font-size: 14pt;
            font-weight: bold;
            color: {accent_color};
        }}
        .notes {{
            border: 2px solid #bdbdbd;
            border-radius: 8px;
            background-color: #f5f5f5;
            padding: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
        }}
        .footer {{
            text-align: center;
            color: #757575;
            font-size: 10pt;
            margin-top: 40px;
            padding-top: 10px;
            border-top: 1px solid #e0e0e0;
        }}
        /* POS-style invoice */
        .pos-invoice {{
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: {font_family}, 'Segoe UI', 'Arial', sans-serif;
        }}
        .pos-header {{
            text-align: left;
            margin-bottom: 20px;
        }}
        .pos-title {{
            font-size: 18pt;
            font-weight: bold;
            color: #000066;
            text-align: right;
        }}
        .pos-date {{
            margin-top: 5px;
            font-size: 10pt;
        }}
        .pos-customer {{
            margin-top: 20px;
            margin-bottom: 20px;
        }}
        .pos-table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        .pos-table th {{
            background-color: #000066;
            color: white;
            padding: 8px;
            text-align: center;
            font-weight: bold;
            font-size: 10pt;
        }}
        .pos-table td {{
            padding: 6px;
            border-bottom: 1px solid #e0e0e0;
            text-align: center;
            font-size: 10pt;
        }}
        .pos-total {{
            text-align: left;
            color: #006600;
            font-weight: bold;
            font-size: 14pt;
        }}
        .pos-footer {{
            margin-top: 30px;
            text-align: center;
            font-size: 9pt;
        }}
    """

    # Check if this is a POS invoice
    is_pos_invoice = hasattr(invoice, 'source') and invoice.source == 'pos'

    # Start HTML content
    html = f"""<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة {invoice.invoice_number}</title>
    <style>
{css}
    </style>
</head>
<body>
"""

    # If this is a POS invoice, use the POS-style template
    if is_pos_invoice:
        # Create POS-style invoice based on the invoice image
        html += f"""
<div class="pos-invoice" style="font-family: Arial, sans-serif; direction: rtl; max-width: 800px; margin: 0 auto;">
    <div style="text-align: right; margin-bottom: 20px;">
        <div style="font-size: 18pt; font-weight: bold; color: #000066;">Hadou Design</div>
    </div>

    <div style="text-align: right; margin-bottom: 20px;">
        <div style="font-size: 14pt; font-weight: bold; color: #000066;">INV-{invoice.invoice_number} :رقم الفاتورة</div>
        <div style="font-size: 10pt; margin-top: 5px;">تاريخ الإصدار: {invoice.issue_date.strftime('%d-%m-%Y')}</div>
        <div style="font-size: 10pt; margin-top: 5px;">تاريخ الاستحقاق: {invoice.due_date.strftime('%d-%m-%Y') if invoice.due_date else ''}</div>
    </div>

    <div style="text-align: right; margin-bottom: 20px;">
        <div style="font-size: 14pt; font-weight: bold; color: #000066;">:معلومات العميل</div>
        <div style="font-size: 10pt; margin-top: 5px;">الاسم: {customer.name if customer else 'عميل افتراضي'}</div>
        <div style="font-size: 10pt; margin-top: 5px;">البريد الإلكتروني: {customer.email if hasattr(customer, 'email') and customer.email else '<EMAIL>'}</div>
        <div style="font-size: 10pt; margin-top: 5px;">الهاتف: {customer.phone if hasattr(customer, 'phone') and customer.phone else '0000000000'}</div>
        <div style="font-size: 10pt; margin-top: 5px;">العنوان: {customer.address if hasattr(customer, 'address') and customer.address else 'العنوان غير متوفر'}</div>
    </div>

    <div style="margin-bottom: 20px;">
        <div style="font-size: 14pt; font-weight: bold; color: #000066; text-align: right; margin-bottom: 10px;">:المنتجات والخدمات</div>
        <table style="width: 100%; border-collapse: collapse;">
            <tr>
                <th style="background-color: #000066; color: white; padding: 8px; text-align: center; border: 1px solid #000066;">وصف</th>
                <th style="background-color: #000066; color: white; padding: 8px; text-align: center; border: 1px solid #000066;">كمية</th>
                <th style="background-color: #000066; color: white; padding: 8px; text-align: center; border: 1px solid #000066;">سعر الوحدة</th>
                <th style="background-color: #000066; color: white; padding: 8px; text-align: center; border: 1px solid #000066;">الخصم</th>
                <th style="background-color: #000066; color: white; padding: 8px; text-align: center; border: 1px solid #000066;">الضريبة</th>
                <th style="background-color: #000066; color: white; padding: 8px; text-align: center; border: 1px solid #000066;">الإجمالي</th>
            </tr>
"""

        # Add items
        for item in items:
            html += f"""
            <tr>
                <td style="border: 1px solid #cccccc; padding: 5px; text-align: center; background-color: #f2f2f2;">{item.description}</td>
                <td style="border: 1px solid #cccccc; padding: 5px; text-align: center; background-color: #f2f2f2;">{item.quantity}</td>
                <td style="border: 1px solid #cccccc; padding: 5px; text-align: center; background-color: #f2f2f2;">{item.unit_price:.2f}</td>
                <td style="border: 1px solid #cccccc; padding: 5px; text-align: center; background-color: #f2f2f2;">{item.discount:.2f}</td>
                <td style="border: 1px solid #cccccc; padding: 5px; text-align: center; background-color: #f2f2f2;">{item.tax:.2f}</td>
                <td style="border: 1px solid #cccccc; padding: 5px; text-align: center; background-color: #f2f2f2; color: #006600; font-weight: bold;">{item.total:.2f}</td>
            </tr>
"""

        # Add totals
        html += f"""
        </table>

        <div style="text-align: right; margin-top: 15px;">
            <div style="font-size: 10pt; margin: 3px 0;">المجموع الفرعي: {currency_symbol} {invoice.subtotal:.2f}</div>
            <div style="font-size: 10pt; margin: 3px 0;">الخصم: {currency_symbol} {invoice.discount:.2f}</div>
            <div style="font-size: 10pt; margin: 3px 0;">الضريبة: {currency_symbol} {invoice.tax:.2f}</div>
            <div style="font-size: 12pt; margin: 3px 0; color: #000066; font-weight: bold;">الإجمالي: {currency_symbol} {invoice.total:.2f}</div>
        </div>
    </div>

    <div style="margin-bottom: 20px;">
        <div style="font-size: 14pt; font-weight: bold; color: #000066; text-align: right; margin-bottom: 10px;">:ملاحظات</div>
        <div style="font-size: 10pt; text-align: right;">{invoice.notes if invoice.notes else 'Created from POS'}</div>
    </div>
</div>
"""

        # End HTML content for POS invoice
        html += """
</body>
</html>
"""

        return html

    # Add header if template allows
    if not template or template.show_header:
        html += f"""
    <div class="invoice-header">
"""

        # Add logo if template allows and logo exists
        if (not template or template.show_logo) and company_info.get('company_logo') and os.path.exists(company_info.get('company_logo')):
            html += f"""
        <div class="logo">
            <img src="{company_info.get('company_logo')}" alt="شعار الشركة" style="max-width: 150px; max-height: 150px;">
        </div>
"""

        # Add company info
        html += f"""
        <div class="company-info">
            <div class="company-name">{company_info.get('company_name', '')}</div>
"""

        if company_info.get('company_address'):
            html += f"""
            <div>{company_info.get('company_address')}</div>
"""

        if company_info.get('company_phone'):
            html += f"""
            <div>هاتف: {company_info.get('company_phone')}</div>
"""

        if company_info.get('company_email'):
            html += f"""
            <div>بريد إلكتروني: {company_info.get('company_email')}</div>
"""

        html += """
        </div>
    </div>
"""

    # Add invoice title
    html += """
    <div class="invoice-title">فاتورة</div>

    <div class="details-container">
"""

    # Add invoice details
    html += f"""
        <div class="invoice-details">
            <div><strong>رقم الفاتورة:</strong> {invoice.invoice_number}</div>
            <div><strong>تاريخ الإصدار:</strong> {invoice.issue_date.strftime("%Y-%m-%d")}</div>
"""

    if invoice.due_date:
        html += f"""
            <div><strong>تاريخ الاستحقاق:</strong> {invoice.due_date.strftime("%Y-%m-%d")}</div>
"""

    html += f"""
            <div><strong>الحالة:</strong> <span style="color: {get_status_color(invoice.status)}; font-weight: bold;">{get_status_display(invoice.status)}</span></div>
        </div>
"""

    # Add customer details
    html += f"""
        <div class="customer-details">
            <div class="section-title">معلومات العميل</div>
            <div><strong>الاسم:</strong> {customer.name}</div>
"""

    if customer.email:
        html += f"""
            <div><strong>البريد الإلكتروني:</strong> {customer.email}</div>
"""

    if customer.phone:
        html += f"""
            <div><strong>الهاتف:</strong> {customer.phone}</div>
"""

    if customer.address:
        html += f"""
            <div><strong>العنوان:</strong> {customer.address}</div>
"""

    html += """
        </div>
    </div>
"""

    # Add items table
    html += """
    <div>
        <div class="section-title">المنتجات والخدمات</div>
        <table class="items-table">
            <thead>
                <tr>
                    <th>الوصف</th>
                    <th>الكمية</th>
                    <th>سعر الوحدة</th>
                    <th>الخصم</th>
                    <th>الضريبة</th>
                    <th>الإجمالي</th>
                </tr>
            </thead>
            <tbody>
"""

    # Add items
    for item in items:
        html += f"""
                <tr>
                    <td>{item.description}</td>
                    <td>{item.quantity}</td>
                    <td>{item.unit_price:.2f} {currency_symbol}</td>
                    <td>{item.discount:.2f} {currency_symbol}</td>
                    <td>{item.tax:.2f} {currency_symbol}</td>
                    <td><strong>{item.total:.2f} {currency_symbol}</strong></td>
                </tr>
"""

    html += """
            </tbody>
        </table>
"""

    # Add totals
    html += f"""
        <div class="totals">
            <div class="total-row"><strong>المجموع الفرعي:</strong> {invoice.subtotal:.2f} {currency_symbol}</div>
            <div class="total-row"><strong>الخصم:</strong> {invoice.discount:.2f} {currency_symbol}</div>
            <div class="total-row"><strong>الضريبة:</strong> {invoice.tax:.2f} {currency_symbol}</div>
            <div class="total-row grand-total"><strong>الإجمالي:</strong> {invoice.total:.2f} {currency_symbol}</div>
        </div>
    </div>
"""

    # Add notes if available
    if invoice.notes:
        html += f"""
    <div class="notes">
        <div class="section-title">ملاحظات</div>
        <div>{invoice.notes}</div>
    </div>
"""

    # Add footer if template allows
    if not template or template.show_footer:
        footer_text = template.footer_text if template and template.footer_text else "تم إنشاء هذه الفاتورة بواسطة تطبيق فوترها"
        html += f"""
    <div class="footer">
        {footer_text} - {datetime.now().strftime('%Y-%m-%d %H:%M')}
    </div>
"""

    # End HTML content
    html += """
</body>
</html>
"""

    return html
