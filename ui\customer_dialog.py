#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Customer Dialog for فوترها (Fawterha)
Provides a dialog for adding and editing customers
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QLineEdit, QMessageBox, QFormLayout, QDialogButtonBox
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from models.customer import Customer
from utils.translation_manager import tr


class CustomerDialog(QDialog):
    """Dialog for adding and editing customers."""

    def __init__(self, db_manager, customer=None, parent=None):
        """Initialize the customer dialog.

        Args:
            db_manager: Database manager instance
            customer (Customer, optional): Customer to edit. Defaults to None.
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.customer = customer
        self.customer_id = None
        self.is_editing = customer is not None

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        if self.is_editing:
            self.setWindowTitle(tr("customers.edit_customer", "تعديل العميل"))
        else:
            self.setWindowTitle(tr("customers.new_customer", "عميل جديد"))

        self.setMinimumWidth(400)
        self.setMinimumHeight(300)
        self.setWindowIcon(QIcon("resources/icons/customer.png"))
        self.setLayoutDirection(Qt.RightToLeft)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # Form layout
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 0, 0, 0)
        form_layout.setSpacing(10)

        # Name
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText(tr("customers.name", "الاسم"))
        self.name_edit.setMinimumHeight(40)
        form_layout.addRow(tr("customers.name", "الاسم:"), self.name_edit)

        # Email
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText(tr("customers.email", "البريد الإلكتروني"))
        self.email_edit.setMinimumHeight(40)
        form_layout.addRow(tr("customers.email", "البريد الإلكتروني:"), self.email_edit)

        # Phone
        self.phone_edit = QLineEdit()
        self.phone_edit.setPlaceholderText(tr("customers.phone", "الهاتف"))
        self.phone_edit.setMinimumHeight(40)
        form_layout.addRow(tr("customers.phone", "الهاتف:"), self.phone_edit)

        # Address
        self.address_edit = QLineEdit()
        self.address_edit.setPlaceholderText(tr("customers.address", "العنوان"))
        self.address_edit.setMinimumHeight(40)
        form_layout.addRow(tr("customers.address", "العنوان:"), self.address_edit)

        main_layout.addLayout(form_layout)

        # Buttons
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.save_customer)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

    def load_data(self):
        """Load customer data if editing."""
        if self.is_editing and self.customer and hasattr(self.customer, 'name'):
            self.name_edit.setText(self.customer.name)
            self.email_edit.setText(self.customer.email)
            self.phone_edit.setText(self.customer.phone)
            self.address_edit.setText(self.customer.address)

    def save_customer(self):
        """Save the customer."""
        # Get data
        name = self.name_edit.text().strip()
        email = self.email_edit.text().strip()
        phone = self.phone_edit.text().strip()
        address = self.address_edit.text().strip()

        # Validate data
        if not name:
            QMessageBox.warning(
                self,
                tr("errors.required_field", "حقل مطلوب"),
                tr("customers.name_required", "يرجى إدخال اسم العميل")
            )
            self.name_edit.setFocus()
            return

        # Create or update customer
        if self.is_editing:
            # Update customer
            self.customer.name = name
            self.customer.email = email
            self.customer.phone = phone
            self.customer.address = address

            # Save to database
            query = """
            UPDATE customers
            SET name = ?, email = ?, phone = ?, address = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            params = (name, email, phone, address, self.customer.id)

            try:
                self.db_manager.execute_update(query, params)
                self.customer_id = self.customer.id
                QMessageBox.information(
                    self,
                    tr("customers.customer_updated", "تم تحديث العميل"),
                    tr("customers.customer_updated_message", "تم تحديث بيانات العميل بنجاح")
                )
                self.accept()
            except Exception as e:
                QMessageBox.critical(
                    self,
                    tr("errors.database_error", "خطأ في قاعدة البيانات"),
                    tr("customers.update_error", f"حدث خطأ أثناء تحديث العميل: {str(e)}")
                )
        else:
            # Create new customer
            customer = Customer(
                name=name,
                email=email,
                phone=phone,
                address=address
            )

            # Save to database
            query = """
            INSERT INTO customers (name, email, phone, address)
            VALUES (?, ?, ?, ?)
            """
            params = (name, email, phone, address)

            try:
                self.customer_id = self.db_manager.execute_insert(query, params)
                QMessageBox.information(
                    self,
                    tr("customers.customer_created", "تم إضافة العميل"),
                    tr("customers.customer_created_message", "تم إضافة العميل بنجاح")
                )
                self.accept()
            except Exception as e:
                QMessageBox.critical(
                    self,
                    tr("errors.database_error", "خطأ في قاعدة البيانات"),
                    tr("customers.add_error", f"حدث خطأ أثناء إضافة العميل: {str(e)}")
                )

    def get_customer_id(self):
        """Get the ID of the created or updated customer.

        Returns:
            int: Customer ID or None if canceled
        """
        return self.customer_id
