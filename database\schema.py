#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Schema for فوترها (Fawterha)
Defines the database tables and relationships
"""

from database.accounting_schema import create_accounting_tables
from database.inventory_schema import create_inventory_tables
from database.pos_schema import create_pos_tables

def create_tables(connection):
    """Create database tables if they don't exist.

    Args:
        connection: SQLite database connection
    """
    cursor = connection.cursor()

    # Create categories table first (needed for products)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Insert default categories if they don't exist
    cursor.execute('''
    SELECT COUNT(*) FROM categories
    ''')
    categories_count = cursor.fetchone()[0]

    if categories_count == 0:
        cursor.execute('''
        INSERT INTO categories (name, description)
        VALUES
        ('إلكترونيات', 'الأجهزة الإلكترونية والكمبيوتر'),
        ('أدوات مكتبية', 'أدوات ومستلزمات المكتب'),
        ('خدمات', 'خدمات متنوعة')
        ''')

    # Create customers table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Insert default customer if none exists
    cursor.execute('''
    SELECT COUNT(*) FROM customers
    ''')
    customer_count = cursor.fetchone()[0]

    if customer_count == 0:
        cursor.execute('''
        INSERT INTO customers (name, email, phone, address)
        VALUES ('عميل افتراضي', '<EMAIL>', '0000000000', 'العنوان الافتراضي')
        ''')
        print("Added default customer to database")

    # Create invoices table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS invoices (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_number TEXT NOT NULL UNIQUE,
        customer_id INTEGER NOT NULL,
        issue_date DATE NOT NULL,
        due_date DATE,
        currency_id INTEGER,
        subtotal REAL NOT NULL,
        discount REAL DEFAULT 0,
        tax REAL DEFAULT 0,
        total REAL NOT NULL,
        amount_paid REAL DEFAULT 0,
        amount_due REAL DEFAULT 0,
        notes TEXT,
        status TEXT DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
        FOREIGN KEY (currency_id) REFERENCES currencies (id) ON DELETE SET NULL
    )
    ''')

    # Create invoice_items table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS invoice_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        invoice_id INTEGER NOT NULL,
        description TEXT NOT NULL,
        quantity REAL NOT NULL,
        unit_price REAL NOT NULL,
        discount_percent REAL DEFAULT 0,
        tax_percent REAL DEFAULT 0,
        discount REAL DEFAULT 0,
        tax REAL DEFAULT 0,
        total REAL NOT NULL,
        product_id INTEGER,
        is_product INTEGER DEFAULT 0,
        track_inventory INTEGER DEFAULT 0,
        FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE SET NULL
    )
    ''')

    # Create products table with inventory fields
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        tax_rate REAL DEFAULT 0,
        type TEXT DEFAULT 'product',
        stock_quantity INTEGER DEFAULT 0,
        min_stock_level INTEGER DEFAULT 0,
        track_inventory INTEGER DEFAULT 0,
        category_id INTEGER,
        barcode TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE SET NULL
    )
    ''')

    # Create inventory_transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS inventory_transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        product_id INTEGER NOT NULL,
        transaction_type TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        reference_type TEXT,
        reference_id INTEGER,
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
    )
    ''')

    # Create invoice templates table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS invoice_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        is_default INTEGER DEFAULT 0,
        header_color TEXT DEFAULT '#0d47a1',
        text_color TEXT DEFAULT '#212121',
        accent_color TEXT DEFAULT '#1e88e5',
        font_family TEXT DEFAULT 'Arial',
        font_size INTEGER DEFAULT 12,
        show_logo INTEGER DEFAULT 1,
        show_header INTEGER DEFAULT 1,
        show_footer INTEGER DEFAULT 1,
        footer_text TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create currencies table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS currencies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        symbol TEXT NOT NULL,
        exchange_rate REAL NOT NULL DEFAULT 1.0,
        is_primary INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Create settings table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT NOT NULL UNIQUE,
        value TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Insert default currencies if they don't exist
    cursor.execute('''
    SELECT COUNT(*) FROM currencies
    ''')
    currency_count = cursor.fetchone()[0]

    if currency_count == 0:
        # Add default currencies
        default_currencies = [
            ('EGP', 'الجنيه المصري', 'ج.م', 1.0, 1, 1),  # Primary currency
            ('USD', 'الدولار الأمريكي', '$', 50.6, 0, 1),  # 1 USD = 50.6 EGP (متوسط سعر البنك المركزي)
            ('EUR', 'اليورو', '€', 56.9, 0, 1),  # 1 EUR = 56.9 EGP (متوسط سعر البنك المركزي)
            ('SAR', 'الريال السعودي', 'ر.س', 13.65, 0, 1),  # 1 SAR = 13.65 EGP (متوسط سعر البنك المركزي)
            ('AED', 'الدرهم الإماراتي', 'د.إ', 13.55, 0, 1)  # 1 AED = 13.55 EGP (متوسط سعر السوق)
        ]

        for code, name, symbol, rate, is_primary, is_active in default_currencies:
            cursor.execute('''
            INSERT OR IGNORE INTO currencies (code, name, symbol, exchange_rate, is_primary, is_active)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', (code, name, symbol, rate, is_primary, is_active))

    # Insert default settings if they don't exist
    default_settings = [
        ('company_name', 'Hadou Design'),
        ('company_logo', ''),
        ('company_address', ''),
        ('company_phone', ''),
        ('company_email', ''),
        ('primary_currency', 'EGP'),  # Changed from currency to primary_currency
        ('language', 'ar'),
        ('tax_rate', '15'),
        ('invoice_prefix', 'INV-'),
        ('next_invoice_number', '1001'),
        ('default_template_id', '1'),
        ('theme', 'default'),  # Default theme
        ('smtp_server', 'smtp.gmail.com'),
        ('smtp_port', '587'),
        ('smtp_username', ''),
        ('smtp_password', ''),
        ('smtp_use_tls', 'true'),
        ('smtp_from_email', '<EMAIL>'),
        ('smtp_from_name', 'فوترها')
    ]

    for key, value in default_settings:
        cursor.execute('''
        INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)
        ''', (key, value))

    # Insert default invoice template if it doesn't exist
    cursor.execute('''
    SELECT COUNT(*) FROM invoice_templates
    ''')
    template_count = cursor.fetchone()[0]

    if template_count == 0:
        cursor.execute('''
        INSERT INTO invoice_templates (
            name, description, is_default,
            header_color, text_color, accent_color,
            font_family, font_size, show_logo,
            show_header, show_footer, footer_text
        ) VALUES (
            'القالب الافتراضي', 'القالب الافتراضي للفواتير', 1,
            '#0d47a1', '#212121', '#1e88e5',
            'Arial', 12, 1,
            1, 1, 'تم إنشاء هذه الفاتورة بواسطة تطبيق فوترها'
        )
        ''')

    # Create accounting tables
    create_accounting_tables(connection)

    # Create inventory tables
    create_inventory_tables(connection)

    # Create POS tables
    create_pos_tables(connection)

    # Commit the changes
    connection.commit()
