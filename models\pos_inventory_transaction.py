#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory Transaction Model for فوترها (Fawterha)
Represents inventory transactions specific to the Point of Sale system
"""

class POSInventoryTransaction:
    """POS Inventory Transaction model class."""

    # Transaction types
    TYPE_PURCHASE = 'purchase'
    TYPE_SALE = 'sale'
    TYPE_ADJUSTMENT = 'adjustment'
    TYPE_TRANSFER_IN = 'transfer_in'
    TYPE_TRANSFER_OUT = 'transfer_out'
    TYPE_RETURN = 'return'
    TYPE_INITIAL = 'initial'

    def __init__(self, id=None, pos_inventory_id=None, transaction_type=None,
                 quantity=0, reference_type=None, reference_id=None,
                 notes=None, created_at=None, product_name=None):
        """Initialize a POS inventory transaction object.

        Args:
            id (int, optional): Transaction ID. Defaults to None.
            pos_inventory_id (int, optional): POS Inventory ID. Defaults to None.
            transaction_type (str, optional): Transaction type. Defaults to None.
            quantity (int, optional): Quantity. Defaults to 0.
            reference_type (str, optional): Reference type (e.g., 'invoice', 'manual'). Defaults to None.
            reference_id (int, optional): Reference ID. Defaults to None.
            notes (str, optional): Notes. Defaults to None.
            created_at (str, optional): Creation timestamp. Defaults to None.
            product_name (str, optional): Product name (for display). Defaults to None.
        """
        self.id = id

        # Validate pos_inventory_id
        if pos_inventory_id is None:
            raise ValueError("pos_inventory_id cannot be None")
        self.pos_inventory_id = pos_inventory_id

        # Validate transaction_type
        if transaction_type is None:
            raise ValueError("transaction_type cannot be None")
        self.transaction_type = transaction_type

        # Ensure quantity is an integer and positive
        try:
            quantity_int = int(quantity)
            if quantity_int <= 0 and transaction_type != self.TYPE_ADJUSTMENT:
                raise ValueError(f"quantity must be greater than 0, got {quantity_int}")
            self.quantity = quantity_int
        except (ValueError, TypeError) as e:
            if "must be greater than 0" in str(e):
                raise
            print(f"Warning: Invalid quantity value: {quantity}, defaulting to 0")
            self.quantity = 0

        self.reference_type = reference_type
        self.reference_id = reference_id

        # Ensure notes is not None
        self.notes = notes if notes is not None else ""

        self.created_at = created_at
        self.product_name = product_name

    @classmethod
    def from_db_row(cls, row):
        """Create a POSInventoryTransaction object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            POSInventoryTransaction: POSInventoryTransaction object
        """
        # Convert row to dict for easier access
        row_dict = dict(row)

        # Create the transaction object with all fields from the row
        transaction = cls(
            id=row_dict.get('id'),
            pos_inventory_id=row_dict.get('pos_inventory_id'),
            transaction_type=row_dict.get('transaction_type'),
            quantity=row_dict.get('quantity', 0),
            reference_type=row_dict.get('reference_type'),
            reference_id=row_dict.get('reference_id'),
            notes=row_dict.get('notes'),
            created_at=row_dict.get('created_at'),
            product_name=row_dict.get('product_name')
        )

        return transaction

    def to_dict(self):
        """Convert the transaction object to a dictionary.

        Returns:
            dict: Dictionary representation of the transaction
        """
        return {
            'id': self.id,
            'pos_inventory_id': self.pos_inventory_id,
            'transaction_type': self.transaction_type,
            'quantity': self.quantity,
            'reference_type': self.reference_type,
            'reference_id': self.reference_id,
            'notes': self.notes,
            'created_at': self.created_at,
            'product_name': self.product_name
        }

    def __str__(self):
        """Return a string representation of the transaction.

        Returns:
            str: String representation
        """
        return f"{self.transaction_type} - {self.quantity} - {self.product_name}"
