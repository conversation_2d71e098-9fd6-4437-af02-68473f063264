#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Styles for فوترها (Fawterha)
Centralized styles for consistent UI appearance
"""

# Main application style
MAIN_STYLE = """
    QWidget {
        background-color: #f5f5f5;
        color: #212121;
        font-family: '<PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
    }
    QLabel {
        color: #212121;
        font-size: 13pt;
        margin: 4px;
    }
    QLineEdit, QTextEdit, QComboBox, QSpinBox, QDoubleSpinBox, QDateEdit {
        background-color: white;
        border: 1px solid #bdbdbd;
        border-radius: 6px;
        padding: 10px;
        min-height: 40px;
        font-size: 12pt;
        selection-background-color: #bbdefb;
        selection-color: #212121;
    }
    QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus, QDoubleSpinBox:focus, QDateEdit:focus {
        border: 2px solid #1976d2;
    }
    QComboBox::drop-down {
        border: 0px;
        width: 25px;
    }
    QComboBox::down-arrow {
        image: url(resources/icons/dropdown.png);
        width: 12px;
        height: 12px;
    }
    QSpinBox::up-button, QDoubleSpinBox::up-button, QDateEdit::up-button {
        subcontrol-origin: border;
        subcontrol-position: top right;
        width: 20px;
        border-left: 1px solid #bdbdbd;
        border-bottom: 1px solid #bdbdbd;
        border-top-right-radius: 4px;
        background-color: #f5f5f5;
    }
    QSpinBox::down-button, QDoubleSpinBox::down-button, QDateEdit::down-button {
        subcontrol-origin: border;
        subcontrol-position: bottom right;
        width: 20px;
        border-left: 1px solid #bdbdbd;
        border-top: 1px solid #bdbdbd;
        border-bottom-right-radius: 4px;
        background-color: #f5f5f5;
    }
    QPushButton {
        background-color: #1976d2;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: bold;
        font-size: 13pt;
        min-width: 130px;
        min-height: 45px;
    }
    QPushButton:hover {
        background-color: #1565c0;
    }
    QPushButton:pressed {
        background-color: #0d47a1;
    }
    QPushButton:disabled {
        background-color: #bdbdbd;
        color: #757575;
    }
    QTabWidget::pane {
        border: 1px solid #bdbdbd;
        border-radius: 4px;
        background-color: white;
        top: -1px;
    }
    QTabBar::tab {
        background-color: #e0e0e0;
        color: #424242;
        border: 1px solid #bdbdbd;
        border-bottom: none;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        padding: 10px 20px;
        font-size: 13pt;
        min-width: 120px;
        margin-right: 2px;
    }
    QTabBar::tab:selected {
        background-color: #1976d2;
        color: white;
        font-weight: bold;
    }
    QTabBar::tab:hover:!selected {
        background-color: #bbdefb;
    }
    QTableWidget {
        border: 1px solid #bdbdbd;
        border-radius: 6px;
        background-color: white;
        gridline-color: #e0e0e0;
        font-size: 12pt;
    }
    QTableWidget::item {
        padding: 12px;
        border-bottom: 1px solid #e0e0e0;
        min-height: 30px;
    }
    QTableWidget::item:selected {
        background-color: #e3f2fd;
        color: #212121;
    }
    QHeaderView::section {
        background-color: #0d47a1;
        color: white;
        padding: 14px;
        border: none;
        font-weight: bold;
        font-size: 14pt;
    }
    QTableWidget::item:alternate {
        background-color: #f5f5f5;
    }
    QScrollBar:vertical {
        border: none;
        background: #f5f5f5;
        width: 12px;
        margin: 0px;
    }
    QScrollBar::handle:vertical {
        background: #bdbdbd;
        min-height: 20px;
        border-radius: 6px;
    }
    QScrollBar::handle:vertical:hover {
        background: #9e9e9e;
    }
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }
    QScrollBar:horizontal {
        border: none;
        background: #f5f5f5;
        height: 12px;
        margin: 0px;
    }
    QScrollBar::handle:horizontal {
        background: #bdbdbd;
        min-width: 20px;
        border-radius: 6px;
    }
    QScrollBar::handle:horizontal:hover {
        background: #9e9e9e;
    }
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        width: 0px;
    }
    QMessageBox {
        background-color: white;
    }
    QMessageBox QLabel {
        color: #212121;
    }
    QMessageBox QPushButton {
        min-width: 100px;
    }
"""

# Button styles
PRIMARY_BUTTON_STYLE = """
    QPushButton {
        background-color: #1976d2;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: bold;
        font-size: 13pt;
        min-width: 130px;
        min-height: 45px;
        font-family: 'Almarai', 'Arial', sans-serif;
    }
    QPushButton:hover {
        background-color: #1565c0;
    }
    QPushButton:pressed {
        background-color: #0d47a1;
    }
    QPushButton:disabled {
        background-color: #bdbdbd;
        color: #757575;
    }
"""

SUCCESS_BUTTON_STYLE = """
    QPushButton {
        background-color: #2e7d32;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: bold;
        font-size: 13pt;
        min-width: 130px;
        min-height: 45px;
        font-family: 'Almarai', 'Arial', sans-serif;
    }
    QPushButton:hover {
        background-color: #1b5e20;
    }
    QPushButton:pressed {
        background-color: #1a4314;
    }
    QPushButton:disabled {
        background-color: #bdbdbd;
        color: #757575;
    }
"""

DANGER_BUTTON_STYLE = """
    QPushButton {
        background-color: #e53935;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: bold;
        font-size: 13pt;
        min-width: 130px;
        min-height: 45px;
        font-family: 'Almarai', 'Arial', sans-serif;
    }
    QPushButton:hover {
        background-color: #d32f2f;
    }
    QPushButton:pressed {
        background-color: #c62828;
    }
    QPushButton:disabled {
        background-color: #bdbdbd;
        color: #757575;
    }
"""

WARNING_BUTTON_STYLE = """
    QPushButton {
        background-color: #ff9800;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: bold;
        font-size: 13pt;
        min-width: 130px;
        min-height: 45px;
        font-family: 'Almarai', 'Arial', sans-serif;
    }
    QPushButton:hover {
        background-color: #f57c00;
    }
    QPushButton:pressed {
        background-color: #ef6c00;
    }
    QPushButton:disabled {
        background-color: #bdbdbd;
        color: #757575;
    }
"""

# Frame styles
CARD_STYLE = """
    QWidget {
        background-color: white;
        border: 1px solid #bdbdbd;
        border-radius: 10px;
        padding: 15px;
    }
"""

# Table styles
TABLE_STYLE = """
    QTableWidget {
        border: 1px solid #bdbdbd;
        border-radius: 6px;
        background-color: white;
        gridline-color: #e0e0e0;
        font-size: 13pt;
        font-family: 'Almarai', 'Arial', sans-serif;
    }
    QTableWidget::item {
        padding: 10px;
        border-bottom: 1px solid #e0e0e0;
        min-height: 36px;
        margin: 2px;
    }
    QTableWidget::item:selected {
        background-color: #e3f2fd;
        color: #212121;
    }
    QHeaderView::section {
        background-color: #0d47a1;
        color: white;
        padding: 16px;
        border: none;
        font-weight: bold;
        font-size: 14pt;
        font-family: 'Almarai', 'Arial', sans-serif;
    }
    QTableWidget::item:alternate {
        background-color: #f5f5f5;
    }
"""
