#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Warehouse Model for فوترها (Fawterha)
Represents a warehouse in the system
"""

from datetime import datetime

class Warehouse:
    """Warehouse model class."""

    def __init__(self, id=None, name="", location="", description="",
                 is_active=True, created_at=None, updated_at=None):
        """Initialize a warehouse object.

        Args:
            id (int, optional): Warehouse ID. Defaults to None.
            name (str, optional): Warehouse name. Defaults to "".
            location (str, optional): Warehouse location. Defaults to "".
            description (str, optional): Warehouse description. Defaults to "".
            is_active (bool, optional): Whether the warehouse is active. Defaults to True.
            created_at (datetime, optional): Creation timestamp. Defaults to None.
            updated_at (datetime, optional): Update timestamp. Defaults to None.
        """
        self.id = id
        self.name = name
        self.location = location
        self.description = description
        self.is_active = is_active
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()

    @classmethod
    def from_db_row(cls, row):
        """Create a Warehouse object from a database row.

        Args:
            row: Database row (sqlite3.Row)

        Returns:
            Warehouse: Warehouse object
        """
        # Convert row to dict for easier access
        if isinstance(row, dict):
            row_dict = row
        else:
            row_dict = dict(row)

        # Create the warehouse object with all fields from the row
        warehouse = cls(
            id=row_dict.get('id'),
            name=row_dict.get('name', ''),
            location=row_dict.get('location', ''),
            description=row_dict.get('description', ''),
            is_active=bool(row_dict.get('is_active', True)),
            created_at=row_dict.get('created_at'),
            updated_at=row_dict.get('updated_at')
        )

        return warehouse

    def to_dict(self):
        """Convert the warehouse object to a dictionary.

        Returns:
            dict: Dictionary representation of the warehouse
        """
        return {
            'id': self.id,
            'name': self.name,
            'location': self.location,
            'description': self.description,
            'is_active': self.is_active,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }

    def __str__(self):
        """Return a string representation of the warehouse.

        Returns:
            str: String representation
        """
        return f"{self.name} ({self.location})"
