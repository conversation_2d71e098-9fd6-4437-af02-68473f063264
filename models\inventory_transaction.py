#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Transaction Model for فوترها (Fawterha)
Represents an inventory transaction in the system
"""

from datetime import datetime

class InventoryTransaction:
    """Inventory transaction model class."""
    
    # Transaction types
    TYPE_PURCHASE = "purchase"
    TYPE_SALE = "sale"
    TYPE_ADJUSTMENT = "adjustment"
    TYPE_RETURN = "return"
    
    def __init__(self, id=None, product_id=None, transaction_type=TYPE_PURCHASE, 
                 quantity=0, reference_type=None, reference_id=None, 
                 notes="", created_at=None):
        """Initialize an inventory transaction object.
        
        Args:
            id (int, optional): Transaction ID. Defaults to None.
            product_id (int, optional): Product ID. Defaults to None.
            transaction_type (str, optional): Transaction type. Defaults to TYPE_PURCHASE.
            quantity (int, optional): Transaction quantity. Defaults to 0.
            reference_type (str, optional): Reference type (e.g., invoice, purchase). Defaults to None.
            reference_id (int, optional): Reference ID. Defaults to None.
            notes (str, optional): Transaction notes. Defaults to "".
            created_at (datetime, optional): Creation timestamp. Defaults to None.
        """
        self.id = id
        self.product_id = product_id
        self.transaction_type = transaction_type
        self.quantity = quantity
        self.reference_type = reference_type
        self.reference_id = reference_id
        self.notes = notes
        self.created_at = created_at or datetime.now()
    
    @classmethod
    def from_db_row(cls, row):
        """Create an InventoryTransaction object from a database row.
        
        Args:
            row: Database row (sqlite3.Row)
            
        Returns:
            InventoryTransaction: InventoryTransaction object
        """
        return cls(
            id=row['id'],
            product_id=row['product_id'],
            transaction_type=row['transaction_type'],
            quantity=row['quantity'],
            reference_type=row['reference_type'],
            reference_id=row['reference_id'],
            notes=row['notes'],
            created_at=row['created_at']
        )
    
    def to_dict(self):
        """Convert the inventory transaction object to a dictionary.
        
        Returns:
            dict: Dictionary representation of the inventory transaction
        """
        return {
            'id': self.id,
            'product_id': self.product_id,
            'transaction_type': self.transaction_type,
            'quantity': self.quantity,
            'reference_type': self.reference_type,
            'reference_id': self.reference_id,
            'notes': self.notes,
            'created_at': self.created_at
        }
    
    def __str__(self):
        """Return a string representation of the inventory transaction.
        
        Returns:
            str: String representation
        """
        return f"{self.transaction_type} - {self.quantity} - {self.created_at}"
