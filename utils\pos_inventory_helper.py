#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory Helper for فوترها (Fawterha)
Provides utility functions for POS inventory management
"""

from PySide6.QtWidgets import QMessageBox
from utils.inventory_helper import get_inventory_manager
import traceback

def update_pos_inventory(db_manager, product_id, quantity_change, reference="", parent=None):
    """Update POS inventory safely with error handling.
    
    Args:
        db_manager: Database manager instance
        product_id (int): Product ID
        quantity_change (int): Quantity to add (positive) or subtract (negative)
        reference (str): Reference information for the transaction
        parent: Parent widget for error dialog
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get inventory manager
        inventory_manager = get_inventory_manager(db_manager, show_error=False)
        
        if not inventory_manager:
            print("Warning: Could not initialize inventory manager for POS inventory update")
            if parent:
                QMessageBox.warning(
                    parent,
                    "خطأ في نظام المخزون",
                    "لا يمكن تحديث المخزون: لم يتم تهيئة مدير المخزون بشكل صحيح"
                )
            return False
            
        # Update stock
        result = inventory_manager.update_stock_quantity(product_id, quantity_change)
        
        # Log the result
        print(f"POS inventory update for product {product_id}: {quantity_change} units, result: {result}")
        
        return result
        
    except Exception as e:
        error_message = f"خطأ في تحديث مخزون نقاط البيع: {str(e)}"
        error_details = traceback.format_exc()
        
        print(f"Error updating POS inventory: {error_message}")
        print(error_details)
        
        if parent:
            QMessageBox.warning(
                parent,
                "خطأ في تحديث المخزون",
                error_message
            )
            
        return False

def check_pos_inventory(db_manager, product_id, quantity, parent=None):
    """Check POS inventory availability safely with error handling.
    
    Args:
        db_manager: Database manager instance
        product_id (int): Product ID
        quantity (int): Quantity to check
        parent: Parent widget for error dialog
        
    Returns:
        bool: True if available, False otherwise
    """
    try:
        # Get inventory manager
        inventory_manager = get_inventory_manager(db_manager, show_error=False)
        
        if not inventory_manager:
            # If inventory manager fails, assume product is available to avoid blocking sales
            print("Warning: Inventory manager initialization failed, assuming product is available")
            return True
            
        # Get product details
        query = """
        SELECT * FROM products
        WHERE id = ?
        """
        rows = db_manager.execute_query(query, (product_id,))
        
        if not rows:
            print(f"Warning: Product {product_id} not found in database")
            return False
            
        product_row = rows[0]
        
        # If product is not tracked, always return True
        if not product_row.get('track_inventory', False):
            return True
            
        # Check if stock is sufficient
        stock = product_row.get('stock_quantity', 0)
        return stock >= quantity
        
    except Exception as e:
        error_message = f"خطأ في التحقق من توفر المنتج: {str(e)}"
        print(error_message)
        
        if parent:
            QMessageBox.warning(
                parent,
                "خطأ في التحقق من المخزون",
                error_message
            )
            
        # In case of error, assume product is available to avoid blocking sales
        return True
