#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Invoice Web Preview for فوترها (Fawterha)
Displays and prints invoices using HTML and WebEngine
"""

from PySide6.QtCore import Qt, QUrl, QTemporaryFile, QIODevice, QSize
from PySide6.QtGui import QIcon, QPixmap, QPageLayout, QPageSize
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QComboBox, QMessageBox, QFileDialog, QSizePolicy
)
from PySide6.QtWebEngineWidgets import QWebEngineView
from PySide6.QtWebEngineCore import QWebEngineSettings, QWebEnginePage
from PySide6.QtPrintSupport import QPrinter, QPrintDialog, QPrintPreviewDialog

import os
import sys
import tempfile
from datetime import datetime

from models.invoice_template import InvoiceTemplate
from utils.html_generator import generate_invoice_html


class InvoiceWebPreviewDialog(QDialog):
    """Dialog for previewing and printing invoices using HTML and WebEngine."""

    def __init__(self, parent=None, invoice=None, customer=None, items=None, company_info=None, db_manager=None, template_id=None):
        """Initialize the invoice preview dialog.

        Args:
            parent: Parent widget
            invoice: Invoice object
            customer: Customer object
            items: List of invoice items
            company_info: Company information dictionary
            db_manager: Database manager instance
            template_id: Template ID to use (optional)
        """
        super().__init__(parent)

        self.invoice = invoice
        self.customer = customer
        self.items = items
        self.company_info = company_info
        self.db_manager = db_manager
        self.html_file = None  # Will hold the temporary HTML file

        # Load template
        self.template = None
        if template_id:
            # Load specific template
            template_query = "SELECT * FROM invoice_templates WHERE id = ?"
            template_rows = self.db_manager.execute_query(template_query, (template_id,))
            if template_rows:
                self.template = InvoiceTemplate.from_db_row(template_rows[0])

        if not self.template:
            # Load default template
            default_template_query = "SELECT value FROM settings WHERE key = 'default_template_id'"
            default_template_rows = self.db_manager.execute_query(default_template_query)
            if default_template_rows and default_template_rows[0]['value']:
                template_id = default_template_rows[0]['value']
                template_query = "SELECT * FROM invoice_templates WHERE id = ?"
                template_rows = self.db_manager.execute_query(template_query, (template_id,))
                if template_rows:
                    self.template = InvoiceTemplate.from_db_row(template_rows[0])

        if not self.template:
            # Load first template as fallback
            template_query = "SELECT * FROM invoice_templates LIMIT 1"
            template_rows = self.db_manager.execute_query(template_query)
            if template_rows:
                self.template = InvoiceTemplate.from_db_row(template_rows[0])
            else:
                # Create default template if none exists
                self.template = InvoiceTemplate(
                    name="القالب الافتراضي",
                    description="القالب الافتراضي للفواتير",
                    is_default=True
                )

        # Set window properties
        self.setWindowTitle(f"معاينة الفاتورة رقم {invoice.invoice_number}")
        self.setMinimumSize(900, 700)

        # Set RTL layout direction
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        main_layout = QVBoxLayout(self)

        # Create top toolbar layout
        top_layout = QHBoxLayout()
        main_layout.addLayout(top_layout)

        # Add template selection
        template_layout = QHBoxLayout()
        top_layout.addLayout(template_layout)

        template_label = QLabel("قالب الفاتورة:")
        template_label.setStyleSheet("font-weight: bold; font-size: 12pt;")
        template_layout.addWidget(template_label)

        self.template_combo = QComboBox()
        self.template_combo.setMinimumWidth(200)
        self.template_combo.setStyleSheet("""
            QComboBox {
                padding: 5px;
                border: 1px solid #bdbdbd;
                border-radius: 4px;
                background-color: white;
                min-height: 30px;
                font-size: 11pt;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top right;
                width: 25px;
                border-left: 1px solid #bdbdbd;
            }
        """)

        # Load templates
        template_query = "SELECT id, name, is_default FROM invoice_templates ORDER BY is_default DESC, name"
        template_rows = self.db_manager.execute_query(template_query)

        selected_index = 0
        for i, row in enumerate(template_rows):
            template_name = f"{row['name']} {'(افتراضي)' if row['is_default'] else ''}"
            self.template_combo.addItem(template_name, row['id'])

            # Select current template
            if self.template and self.template.id == row['id']:
                selected_index = i

        self.template_combo.setCurrentIndex(selected_index)
        self.template_combo.currentIndexChanged.connect(self.change_template)
        template_layout.addWidget(self.template_combo)

        top_layout.addStretch()

        # Create buttons layout
        buttons_layout = QHBoxLayout()
        main_layout.addLayout(buttons_layout)

        # Add print button
        self.print_button = QPushButton("طباعة")
        if os.path.exists("resources/icons/print.png"):
            self.print_button.setIcon(QIcon(QPixmap("resources/icons/print.png")))
        self.print_button.setStyleSheet("""
            QPushButton {
                background-color: #1976d2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #1565c0;
            }
            QPushButton:pressed {
                background-color: #0d47a1;
            }
        """)
        self.print_button.clicked.connect(self.print_invoice)
        buttons_layout.addWidget(self.print_button)

        # Add print preview button
        self.preview_button = QPushButton("معاينة الطباعة")
        if os.path.exists("resources/icons/preview.png"):
            self.preview_button.setIcon(QIcon(QPixmap("resources/icons/preview.png")))
        self.preview_button.setStyleSheet("""
            QPushButton {
                background-color: #00897b;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #00796b;
            }
            QPushButton:pressed {
                background-color: #004d40;
            }
        """)
        self.preview_button.clicked.connect(self.preview_print)
        buttons_layout.addWidget(self.preview_button)

        # Add export to PDF button
        self.pdf_button = QPushButton("تصدير إلى PDF")
        if os.path.exists("resources/icons/pdf.png"):
            self.pdf_button.setIcon(QIcon(QPixmap("resources/icons/pdf.png")))
        self.pdf_button.setStyleSheet("""
            QPushButton {
                background-color: #e53935;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
        """)
        self.pdf_button.clicked.connect(self.export_to_pdf)
        buttons_layout.addWidget(self.pdf_button)

        # Add email button
        self.email_button = QPushButton("إرسال بالبريد الإلكتروني")
        if os.path.exists("resources/icons/email.png"):
            self.email_button.setIcon(QIcon(QPixmap("resources/icons/email.png")))
        self.email_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #388E3C;
            }
            QPushButton:pressed {
                background-color: #2E7D32;
            }
        """)
        self.email_button.clicked.connect(self.email_invoice)
        buttons_layout.addWidget(self.email_button)

        # Add close button
        self.close_button = QPushButton("إغلاق")
        self.close_button.setStyleSheet("""
            QPushButton {
                background-color: #757575;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 11pt;
            }
            QPushButton:hover {
                background-color: #616161;
            }
            QPushButton:pressed {
                background-color: #424242;
            }
        """)
        self.close_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.close_button)

        # Create web view for invoice preview
        self.web_view = QWebEngineView()
        self.web_view.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.web_view.setMinimumSize(QSize(800, 500))

        # Enable background colors when printing
        self.web_view.page().settings().setAttribute(QWebEngineSettings.PrintBackgrounds, True)

        # Set zoom factor for better readability
        self.web_view.setZoomFactor(1.0)

        main_layout.addWidget(self.web_view)

        # Generate and load HTML content
        self.load_invoice_html()

    def load_invoice_html(self):
        """Generate HTML content and load it into the web view."""
        try:
            # Generate HTML content
            html_content = generate_invoice_html(
                self.invoice,
                self.customer,
                self.items,
                self.company_info,
                self.template
            )

            # Create a temporary file to store the HTML
            if self.html_file:
                # Close and remove previous file
                self.html_file.close()

            # Create new temporary file
            self.html_file = tempfile.NamedTemporaryFile(suffix='.html', delete=False)
            self.html_file.write(html_content.encode('utf-8'))
            self.html_file.flush()

            # Load the HTML file into the web view
            self.web_view.load(QUrl.fromLocalFile(self.html_file.name))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء معاينة الفاتورة: {str(e)}")
            import traceback
            traceback.print_exc()

    def change_template(self, index):
        """Change the invoice template.

        Args:
            index (int): Index of the selected template
        """
        try:
            template_id = self.template_combo.itemData(index)

            # Load template
            template_query = "SELECT * FROM invoice_templates WHERE id = ?"
            template_rows = self.db_manager.execute_query(template_query, (template_id,))

            if template_rows:
                self.template = InvoiceTemplate.from_db_row(template_rows[0])

                # Reload invoice HTML
                self.load_invoice_html()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تغيير قالب الفاتورة: {str(e)}")
            import traceback
            traceback.print_exc()

    def print_invoice(self):
        """Print the invoice."""
        try:
            # Create printer with high resolution
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPageSize(QPageSize.A4))
            printer.setPageOrientation(QPageLayout.Portrait)

            # Show print dialog
            dialog = QPrintDialog(printer, self)
            dialog.setWindowTitle("طباعة الفاتورة")

            if dialog.exec() == QPrintDialog.Accepted:
                # Print the web page
                self.web_view.page().print(printer, self.print_finished)
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الطباعة", f"حدث خطأ أثناء تهيئة الطباعة: {str(e)}")
            import traceback
            traceback.print_exc()

    def print_finished(self, success):
        """Callback for print finished.

        Args:
            success (bool): Whether printing was successful
        """
        if success:
            QMessageBox.information(self, "تمت الطباعة", "تمت طباعة الفاتورة بنجاح.")
        else:
            QMessageBox.warning(self, "خطأ في الطباعة", "حدث خطأ أثناء طباعة الفاتورة.")

    def preview_print(self):
        """Show print preview dialog."""
        try:
            # Create printer with high resolution
            printer = QPrinter(QPrinter.HighResolution)
            printer.setPageSize(QPageSize(QPageSize.A4))
            printer.setPageOrientation(QPageLayout.Portrait)

            # Create preview dialog
            preview = QPrintPreviewDialog(printer, self)
            preview.setWindowTitle("معاينة طباعة الفاتورة")
            preview.setMinimumSize(1000, 700)

            # Connect paint request to our print function
            preview.paintRequested.connect(self.print_preview)

            # Show the dialog
            preview.exec()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في المعاينة", f"حدث خطأ أثناء تهيئة معاينة الطباعة: {str(e)}")
            import traceback
            traceback.print_exc()

    def print_preview(self, printer):
        """Print preview callback.

        Args:
            printer: QPrinter object
        """
        self.web_view.page().print(printer, lambda success: None)

    def export_to_pdf(self):
        """Export the invoice to PDF."""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ الفاتورة كملف PDF",
            f"فاتورة_{self.invoice.invoice_number}.pdf",
            "ملفات PDF (*.pdf)"
        )

        if file_path:
            try:
                # Create PDF printer
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.PdfFormat)
                printer.setOutputFileName(file_path)
                printer.setPageSize(QPageSize(QPageSize.A4))
                printer.setPageOrientation(QPageLayout.Portrait)

                # Print to PDF
                self.web_view.page().print(printer, self.pdf_export_finished)

                # Store the file path for later use
                self.pdf_file_path = file_path

            except Exception as e:
                QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء تصدير الفاتورة: {str(e)}")
                import traceback
                traceback.print_exc()

    def pdf_export_finished(self, success):
        """Callback for PDF export finished.

        Args:
            success (bool): Whether PDF export was successful
        """
        if success:
            QMessageBox.information(self, "تم التصدير", "تم تصدير الفاتورة إلى ملف PDF بنجاح.")

            # Ask if user wants to open the PDF
            open_pdf = QMessageBox.question(
                self,
                "فتح الملف",
                "هل تريد فتح ملف PDF؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            if open_pdf == QMessageBox.Yes:
                # Open the PDF file with the default application
                import subprocess
                import platform

                if platform.system() == 'Windows':
                    os.startfile(self.pdf_file_path)
                elif platform.system() == 'Darwin':  # macOS
                    subprocess.call(('open', self.pdf_file_path))
                else:  # Linux
                    subprocess.call(('xdg-open', self.pdf_file_path))
        else:
            QMessageBox.warning(self, "خطأ في التصدير", "حدث خطأ أثناء تصدير الفاتورة إلى ملف PDF.")

    def email_invoice(self):
        """Email the invoice."""
        # First export to PDF
        try:
            # Create a temporary PDF file
            temp_pdf_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
            temp_pdf_path = temp_pdf_file.name
            temp_pdf_file.close()

            # Create PDF printer
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(temp_pdf_path)
            printer.setPageSize(QPageSize(QPageSize.A4))
            printer.setPageOrientation(QPageLayout.Portrait)

            # Print to PDF
            self.web_view.page().print(printer, lambda success: self.continue_email_invoice(success, temp_pdf_path))

        except Exception as e:
            QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء تصدير الفاتورة: {str(e)}")
            import traceback
            traceback.print_exc()

    def continue_email_invoice(self, success, temp_pdf_path):
        """Continue emailing the invoice after PDF is generated.

        Args:
            success (bool): Whether PDF export was successful
            temp_pdf_path (str): Path to the temporary PDF file
        """
        if not success:
            QMessageBox.warning(self, "خطأ في التصدير", "حدث خطأ أثناء تصدير الفاتورة إلى ملف PDF.")
            try:
                os.unlink(temp_pdf_path)
            except:
                pass
            return

        # Get database manager from parent dialog
        db_manager = self.db_manager

        # Get SMTP settings
        smtp_settings = {}
        settings_query = """
        SELECT key, value FROM settings
        WHERE key IN ('smtp_server', 'smtp_port', 'smtp_username', 'smtp_password',
                     'smtp_use_tls', 'smtp_from_email', 'smtp_from_name')
        """
        settings_rows = db_manager.execute_query(settings_query)

        for row in settings_rows:
            smtp_settings[row['key']] = row['value']

        # Check if SMTP settings are configured
        required_settings = ['smtp_server', 'smtp_port', 'smtp_username', 'smtp_password', 'smtp_from_email']
        missing_settings = [setting for setting in required_settings if setting not in smtp_settings or not smtp_settings[setting]]

        if missing_settings:
            QMessageBox.warning(
                self,
                "إعدادات البريد الإلكتروني غير مكتملة",
                "يرجى إكمال إعدادات البريد الإلكتروني في صفحة الإعدادات قبل إرسال الفواتير."
            )
            try:
                os.unlink(temp_pdf_path)
            except:
                pass
            return

        # Get customer email if available
        recipient_email = ""
        if self.customer and hasattr(self.customer, 'email') and self.customer.email:
            recipient_email = self.customer.email

        # Ask for recipient email
        from PySide6.QtWidgets import QInputDialog
        email, ok = QInputDialog.getText(
            self,
            "إرسال الفاتورة بالبريد الإلكتروني",
            "أدخل عنوان البريد الإلكتروني:",
            text=recipient_email
        )

        if not ok or not email:
            try:
                os.unlink(temp_pdf_path)
            except:
                pass
            return

        # Validate email format
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            QMessageBox.warning(
                self,
                "بريد إلكتروني غير صالح",
                "يرجى إدخال عنوان بريد إلكتروني صالح."
            )
            try:
                os.unlink(temp_pdf_path)
            except:
                pass
            return

        # Create progress dialog
        from PySide6.QtWidgets import QProgressDialog
        progress = QProgressDialog(
            "جاري إرسال البريد الإلكتروني...",
            "إلغاء",
            0, 0, self
        )
        progress.setWindowTitle("إرسال الفاتورة بالبريد الإلكتروني")
        progress.setWindowModality(Qt.WindowModal)
        progress.setMinimumDuration(0)
        progress.show()

        # Create email worker
        from ui.pos_view import EmailWorker
        from PySide6.QtCore import QThread

        # Convert smtp_use_tls to boolean
        smtp_use_tls = smtp_settings.get('smtp_use_tls', 'false').lower() in ('true', '1', 'yes')

        # Create worker and thread
        self.email_thread = QThread()
        self.email_worker = EmailWorker(
            smtp_settings.get('smtp_server', ''),
            smtp_settings.get('smtp_port', ''),
            smtp_settings.get('smtp_username', ''),
            smtp_settings.get('smtp_password', ''),
            smtp_use_tls,
            smtp_settings.get('smtp_from_email', ''),
            smtp_settings.get('smtp_from_name', ''),
            email,
            self.invoice,
            self.items,
            self.customer,
            self.company_info,
            temp_pdf_path
        )
        self.email_worker.moveToThread(self.email_thread)

        # Connect signals using Qt.QueuedConnection to avoid thread issues
        self.email_thread.started.connect(self.email_worker.send_email)
        self.email_worker.finished.connect(self.email_thread.quit, Qt.QueuedConnection)
        self.email_worker.finished.connect(self.email_worker.deleteLater, Qt.QueuedConnection)
        self.email_thread.finished.connect(self.email_thread.deleteLater, Qt.QueuedConnection)
        self.email_thread.finished.connect(progress.close, Qt.QueuedConnection)

        # Connect success and error signals with Qt.QueuedConnection
        self.email_worker.success.connect(self.on_email_success, Qt.QueuedConnection)
        self.email_worker.error.connect(self.on_email_error, Qt.QueuedConnection)

        # Start the thread
        self.email_thread.start()

        # Show progress dialog
        progress.exec()

    def on_email_success(self, recipient):
        """Handle successful email sending.

        Args:
            recipient (str): Recipient email address
        """
        QMessageBox.information(
            self,
            "تم إرسال البريد الإلكتروني",
            f"تم إرسال الفاتورة بنجاح إلى {recipient}"
        )

    def on_email_error(self, error_message):
        """Handle email sending error.

        Args:
            error_message (str): Error message
        """
        # Add troubleshooting tips based on the error message
        troubleshooting_tips = ""

        if "authentication failed" in error_message.lower() or "اسم المستخدم أو كلمة المرور غير صحيحة" in error_message:
            troubleshooting_tips = """
            نصائح لحل المشكلة:
            • تأكد من صحة اسم المستخدم وكلمة المرور
            • إذا كنت تستخدم Gmail، قد تحتاج إلى:
              - تمكين "وصول التطبيقات الأقل أمانًا" في إعدادات الحساب
              - أو إنشاء "كلمة مرور للتطبيق" إذا كان التحقق بخطوتين مفعلاً
            """
        elif "starttls" in error_message.lower():
            troubleshooting_tips = """
            نصائح لحل المشكلة:
            • استخدم المنفذ 465 مع SSL بدلاً من TLS
            • أو قم بتعطيل خيار TLS واستخدم المنفذ 587
            • تأكد من أن خادم SMTP يدعم STARTTLS
            """
        elif "timeout" in error_message.lower() or "timed out" in error_message.lower():
            troubleshooting_tips = """
            نصائح لحل المشكلة:
            • تأكد من اتصالك بالإنترنت
            • تأكد من صحة عنوان خادم SMTP
            • تحقق من إعدادات جدار الحماية
            """
        elif "refused" in error_message.lower():
            troubleshooting_tips = """
            نصائح لحل المشكلة:
            • تأكد من صحة رقم المنفذ
            • تأكد من أن خادم SMTP يقبل اتصالات خارجية
            • تحقق من إعدادات جدار الحماية
            """

        # Create the full error message
        full_error_message = f"حدث خطأ أثناء إرسال الفاتورة بالبريد الإلكتروني: {error_message}"

        # Add troubleshooting tips if available
        if troubleshooting_tips:
            full_error_message += f"\n\n{troubleshooting_tips}"

        QMessageBox.warning(
            self,
            "خطأ في الإرسال",
            full_error_message
        )

    def closeEvent(self, event):
        """Handle close event to clean up temporary files.

        Args:
            event: Close event
        """
        # Clean up temporary file
        if self.html_file:
            try:
                self.html_file.close()
                os.unlink(self.html_file.name)
            except:
                pass

        super().closeEvent(event)
