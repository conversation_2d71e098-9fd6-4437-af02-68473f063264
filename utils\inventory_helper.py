#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Helper for فوترها (Fawterha)
Provides utility functions for inventory management
"""

from PySide6.QtWidgets import QMessageBox
import traceback

def get_inventory_manager(db_manager, show_error=True, parent=None):
    """Get an instance of the InventoryManager with proper error handling.
    
    Args:
        db_manager: Database manager instance
        show_error (bool): Whether to show error message if initialization fails
        parent: Parent widget for error dialog
        
    Returns:
        InventoryManager or None: Inventory manager instance or None if initialization fails
    """
    try:
        # Import here to avoid circular imports
        from database.inventory_manager import InventoryManager
        
        # Check if db_manager is valid
        if db_manager is None:
            raise ValueError("Database manager is None")
            
        # Create inventory manager
        inventory_manager = InventoryManager(db_manager)
        
        # Verify it works by calling a simple method
        try:
            # Try to get a product stock (any ID will do, even if it doesn't exist)
            inventory_manager.get_product_stock(1)
        except Exception as e:
            # If this fails, there's a problem with the inventory manager
            print(f"Error verifying inventory manager: {str(e)}")
            raise Exception(f"Failed to verify inventory manager: {str(e)}")
            
        return inventory_manager
        
    except Exception as e:
        error_message = f"لا يوجد مدير للمخزون أو لم يتم تهيئته بشكل صحيح: {str(e)}"
        error_details = traceback.format_exc()
        
        print(f"Error initializing inventory manager: {error_message}")
        print(error_details)
        
        if show_error and parent:
            QMessageBox.warning(
                parent,
                "خطأ في نظام المخزون",
                error_message
            )
            
        return None

def update_product_stock_safely(db_manager, product_id, quantity_change, reference="", parent=None):
    """Update product stock safely with error handling.
    
    Args:
        db_manager: Database manager instance
        product_id (int): Product ID
        quantity_change (int): Quantity to add (positive) or subtract (negative)
        reference (str): Reference information for the transaction
        parent: Parent widget for error dialog
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get inventory manager
        inventory_manager = get_inventory_manager(db_manager, show_error=False)
        
        if not inventory_manager:
            raise Exception("Could not initialize inventory manager")
            
        # Update stock
        transaction_id = inventory_manager.update_stock(product_id, quantity_change, reference)
        
        return transaction_id > 0
        
    except Exception as e:
        error_message = f"خطأ في تحديث المخزون: {str(e)}"
        print(error_message)
        
        if parent:
            QMessageBox.warning(
                parent,
                "خطأ في تحديث المخزون",
                error_message
            )
            
        return False

def check_product_availability_safely(db_manager, product_id, quantity, parent=None):
    """Check product availability safely with error handling.
    
    Args:
        db_manager: Database manager instance
        product_id (int): Product ID
        quantity (int): Quantity to check
        parent: Parent widget for error dialog
        
    Returns:
        bool: True if available, False otherwise
    """
    try:
        # Get inventory manager
        inventory_manager = get_inventory_manager(db_manager, show_error=False)
        
        if not inventory_manager:
            # If inventory manager fails, assume product is available to avoid blocking sales
            print("Warning: Inventory manager initialization failed, assuming product is available")
            return True
            
        # Check availability
        return inventory_manager.check_product_availability(product_id, quantity)
        
    except Exception as e:
        error_message = f"خطأ في التحقق من توفر المنتج: {str(e)}"
        print(error_message)
        
        if parent:
            QMessageBox.warning(
                parent,
                "خطأ في التحقق من المخزون",
                error_message
            )
            
        # In case of error, assume product is available to avoid blocking sales
        return True
