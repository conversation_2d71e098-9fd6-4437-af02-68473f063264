#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
New Invoice Editor for فوترها (Fawterha)
Redesigned dialog for creating and editing invoices with clear layout and proper sizing
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QGridLayout, QLabel,
    QLineEdit, QDateEdit, QComboBox, QTableWidget, QTableWidgetItem,
    QPushButton, QDoubleSpinBox, QTextEdit, QMessageBox, QFrame,
    QHeaderView, QAbstractItemView, QWidget, QSplitter, QApplication,
    QProgressDialog
)
from PySide6.QtCore import Qt, QDate, QSize, Signal
from PySide6.QtGui import QFont, QIcon

from models.invoice_item import InvoiceItem
from models.inventory_transaction import InventoryTransaction
from ui.products_view import ProductsView
from utils.currency_helper import convert_currency, format_thousands
from ui.invoice_item_dialog import InvoiceItemDialog
from ui.themes import THEMES
from ui.theme_manager import ThemeManager
from utils.inventory_helper import get_inventory_manager
from ui.update_inventory import update_inventory_for_invoice
from utils.translation_manager import tr

class NewInvoiceEditor(QDialog):
    """A completely redesigned invoice editor dialog with clear layout and proper sizing."""

    def __init__(self, parent=None, db_manager=None, invoice=None, items=None, view_only=False, currency_manager=None, theme_manager=None):
        """Initialize the invoice editor.

        Args:
            parent: Parent widget
            db_manager: Database manager
            invoice: Invoice object to edit
            items: List of invoice items
            view_only: Whether to show in view-only mode
            currency_manager: Currency manager
            theme_manager: Theme manager for handling theme changes
        """
        super().__init__(parent)

        # Store parameters
        self.db_manager = db_manager
        self.invoice = invoice
        self.view_only = view_only
        self.currency_manager = currency_manager
        self.theme_manager = theme_manager
        self.items_list = []
        self.primary_currency = None
        self.current_currency = None

        # Set window properties
        self.setWindowTitle(tr("invoices.new_invoice", "فاتورة جديدة") if not invoice else f"{tr('invoices.invoice', 'فاتورة')} {invoice.invoice_number}")
        self.setFixedSize(1600, 900)  # Fixed size to prevent resizing
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Remove maximize button to prevent resizing
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowMaximizeButtonHint)

        # Get current theme from settings or theme manager
        theme_key = "default"
        if self.theme_manager:
            theme_key = self.theme_manager.get_current_theme()
        else:
            try:
                theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
                if theme_setting and theme_setting[0]['value']:
                    theme_key = theme_setting[0]['value']
            except Exception as e:
                print(f"Error loading theme setting: {e}")
                theme_key = "default"

        # Get theme colors
        theme = THEMES.get(theme_key, THEMES["default"])
        self.theme_key = theme_key

        # Define color scheme based on current theme
        self.colors = {
            'primary': theme["primary"],
            'primary_light': theme["primary_light"],
            'primary_dark': theme["primary_dark"],
            'accent': theme["accent"],
            'accent_light': theme.get("accent_light", "#FFCCBC"),
            'success': theme["success"],
            'success_dark': theme["success_dark"],
            'success_light': theme.get("success_light", "#E8F5E9"),
            'warning': theme["warning"],
            'warning_dark': theme["warning_dark"],
            'warning_light': theme.get("warning_light", "#FFF8E1"),
            'danger': theme["danger"],
            'danger_dark': theme["danger_dark"],
            'danger_light': theme.get("danger_light", "#FFEBEE"),
            'text': theme["main_text"],
            'text_secondary': theme.get("text_secondary", "#757575"),
            'divider': theme["border_color"],
            'background': theme["main_bg"],
            'card': theme.get("panel_bg", "#FFFFFF"),
            'disabled': theme.get("input_disabled_bg", "#EEEEEE"),
            'button_text': theme.get("button_text", "#FFFFFF"),
            'input_bg': theme.get("input_bg", "#FFFFFF"),
            'input_text': theme.get("input_text", "#212121"),
            'table_bg': theme.get("table_bg", theme.get("panel_bg", "#FFFFFF")),
            'table_alternate_bg': theme.get("table_alternate_bg", theme.get("main_bg", "#F5F5F5")),
            'table_header_bg': theme.get("table_header_bg", theme["primary_dark"]),
            'table_header_text': theme.get("table_header_text", "#FFFFFF"),
            'table_text': theme.get("table_text", theme["main_text"]),
            'table_border': theme.get("table_border", theme["border_color"]),
            'table_selection_bg': theme.get("table_selection_bg", theme["primary_light"]),
            'table_selection_text': theme.get("table_selection_text", theme["main_text"]),
            'table_hover_bg': theme.get("table_hover_bg", theme.get("tab_hover_bg", "#E3F2FD"))
        }

        # Set window background and global styles
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.colors['background']};
                color: {self.colors['text']};
            }}

            /* Global styles for all widgets */
            QLabel {{
                color: {self.colors['text']};
            }}

            QLineEdit, QTextEdit, QDateEdit, QComboBox, QDoubleSpinBox {{
                background-color: {self.colors['input_bg']};
                color: {self.colors['input_text']};
                border: 1px solid {self.colors['divider']};
                border-radius: 6px;
                padding: 5px;
            }}

            QPushButton {{
                background-color: {self.colors['primary']};
                color: {self.colors['button_text']};
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }}

            QPushButton:hover {{
                background-color: {self.colors['primary_dark']};
            }}

            QTableWidget {{
                background-color: {self.colors['table_bg']};
                alternate-background-color: {self.colors['table_alternate_bg']};
                color: {self.colors['table_text']};
                gridline-color: {self.colors['table_border']};
                border: 1px solid {self.colors['table_border']};
            }}

            QTableWidget::item {{
                color: {self.colors['table_text']};
            }}

            QHeaderView::section {{
                background-color: {self.colors['table_header_bg']};
                color: {self.colors['table_header_text']};
                font-weight: bold;
            }}

            QScrollBar {{
                background-color: {self.colors['table_bg']};
                border: 1px solid {self.colors['table_border']};
            }}

            QScrollBar::handle {{
                background-color: {self.colors['primary']};
            }}

            /* Widget backgrounds */
            QWidget#headerWidget, QWidget#itemsWidget, QWidget#totalsWidget, QWidget#footerWidget {{
                background-color: {self.colors['card']};
            }}
        """)

        # Set up the UI
        self.setup_ui()

        # Load items if provided
        if items:
            for item_row in items:
                item = InvoiceItem.from_db_row(item_row)
                self.items_list.append(item)
                self.add_item_to_table(item)

        # Calculate initial totals
        self.calculate_totals()

        # Set view-only mode if specified
        if view_only:
            self.set_view_only_mode()

        # Connect to theme manager if available
        if self.theme_manager:
            self.theme_manager.theme_changed.connect(self.on_theme_changed)

    def setup_ui(self):
        """Set up the user interface with clear layout and proper sizing."""
        # Create main layout with proper spacing
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)

        # Create header section (invoice info and customer)
        header_widget = self.create_header_section()
        main_layout.addWidget(header_widget)

        # Create a splitter for the main content area
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)

        # Create items section (left side)
        items_widget = self.create_items_section()
        content_splitter.addWidget(items_widget)

        # Create totals section (right side)
        totals_widget = self.create_totals_section()
        content_splitter.addWidget(totals_widget)

        # Set the initial sizes for the splitter (70% items, 30% totals)
        content_splitter.setSizes([700, 300])

        # Add the splitter to the main layout
        main_layout.addWidget(content_splitter, 1)  # Give it stretch factor

        # Create footer with action buttons
        footer_widget = self.create_footer_section()
        main_layout.addWidget(footer_widget)

    def create_header_section(self):
        """Create the invoice header section with proper sizing."""
        header_widget = QWidget()
        header_widget.setObjectName("headerWidget")
        self.headerWidget = header_widget  # Store reference for theme updates
        header_widget.setStyleSheet(f"""
            #headerWidget {{
                background-color: {self.colors['card']};
                border-radius: 10px;
                border: 2px solid {self.colors['primary']};
            }}
        """)

        # Create layout with proper spacing
        header_layout = QHBoxLayout(header_widget)
        header_layout.setContentsMargins(20, 20, 20, 20)
        header_layout.setSpacing(20)

        # Create left side layout (invoice info)
        left_layout = QFormLayout()
        left_layout.setLabelAlignment(Qt.AlignRight)
        left_layout.setFormAlignment(Qt.AlignLeft)
        left_layout.setSpacing(15)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # Invoice number with proper sizing
        self.invoice_number_edit = QLineEdit()
        self.invoice_number_edit.setReadOnly(True)
        self.invoice_number_edit.setStyleSheet(f"""
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['primary']};
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['input_text']};
            min-height: 20px;
        """)
        self.invoice_number_edit.setMinimumHeight(40)  # Ensure minimum height
        self.invoice_number_edit.setMinimumWidth(200)  # Ensure minimum width

        if self.invoice:
            self.invoice_number_edit.setText(self.invoice.invoice_number)

        invoice_number_label = QLabel(tr("invoices.invoice_number", "رقم الفاتورة:"))
        invoice_number_label.setStyleSheet(f"""
            font-weight: bold;
            font-size: 14pt;
            color: {self.colors['text']};
            min-width: 120px;
        """)

        left_layout.addRow(invoice_number_label, self.invoice_number_edit)

        # Status with proper sizing
        self.status_combo = QComboBox()
        self.status_combo.setStyleSheet(f"""
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['primary']};
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['input_text']};
            min-height: 20px;
        """)
        self.status_combo.setMinimumHeight(40)  # Ensure minimum height
        self.status_combo.setMinimumWidth(200)  # Ensure minimum width

        self.status_combo.addItem(tr("invoices.draft", "مسودة"), "draft")
        self.status_combo.addItem(tr("invoices.pending", "منتظرة"), "pending")
        self.status_combo.addItem(tr("invoices.partially_paid", "مدفوع جزئياً"), "partially_paid")
        self.status_combo.addItem(tr("invoices.paid", "مدفوعة"), "paid")
        self.status_combo.addItem(tr("invoices.cancelled", "ملغاة"), "cancelled")

        if self.invoice and self.invoice.status:
            for i in range(self.status_combo.count()):
                if self.status_combo.itemData(i) == self.invoice.status:
                    self.status_combo.setCurrentIndex(i)
                    break

        status_label = QLabel(tr("common.status", "الحالة:"))
        status_label.setStyleSheet(f"""
            font-weight: bold;
            font-size: 14pt;
            color: {self.colors['text']};
            min-width: 120px;
        """)

        left_layout.addRow(status_label, self.status_combo)

        # Create right side layout (customer and dates)
        right_layout = QFormLayout()
        right_layout.setLabelAlignment(Qt.AlignRight)
        right_layout.setFormAlignment(Qt.AlignLeft)
        right_layout.setSpacing(15)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # Customer selection with proper sizing
        self.customer_combo = QComboBox()
        self.customer_combo.setStyleSheet(f"""
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['primary']};
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['input_text']};
            min-height: 20px;
        """)
        self.customer_combo.setMinimumHeight(40)  # Ensure minimum height
        self.customer_combo.setMinimumWidth(250)  # Ensure minimum width

        self.load_customers()

        if self.invoice and self.invoice.customer_id:
            for i in range(self.customer_combo.count()):
                if self.customer_combo.itemData(i) == self.invoice.customer_id:
                    self.customer_combo.setCurrentIndex(i)
                    break

        customer_label = QLabel(tr("invoices.customer", "العميل:"))
        customer_label.setStyleSheet(f"""
            font-weight: bold;
            font-size: 14pt;
            color: {self.colors['text']};
            min-width: 120px;
        """)

        right_layout.addRow(customer_label, self.customer_combo)

        # Issue date with proper sizing
        self.issue_date_edit = QDateEdit()
        self.issue_date_edit.setCalendarPopup(True)
        self.issue_date_edit.setDate(QDate.currentDate())
        self.issue_date_edit.setStyleSheet(f"""
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['primary']};
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['input_text']};
            min-height: 20px;
        """)
        self.issue_date_edit.setMinimumHeight(40)  # Ensure minimum height
        self.issue_date_edit.setMinimumWidth(250)  # Ensure minimum width

        if self.invoice and self.invoice.issue_date:
            self.issue_date_edit.setDate(QDate.fromString(self.invoice.issue_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        issue_date_label = QLabel(tr("invoices.issue_date", "تاريخ الإصدار:"))
        issue_date_label.setStyleSheet(f"""
            font-weight: bold;
            font-size: 14pt;
            color: {self.colors['text']};
            min-width: 120px;
        """)

        right_layout.addRow(issue_date_label, self.issue_date_edit)

        # Due date with proper sizing
        self.due_date_edit = QDateEdit()
        self.due_date_edit.setCalendarPopup(True)
        self.due_date_edit.setDate(QDate.currentDate().addDays(30))
        self.due_date_edit.setStyleSheet(f"""
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['primary']};
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['input_text']};
            min-height: 20px;
        """)
        self.due_date_edit.setMinimumHeight(40)  # Ensure minimum height
        self.due_date_edit.setMinimumWidth(250)  # Ensure minimum width

        if self.invoice and self.invoice.due_date:
            self.due_date_edit.setDate(QDate.fromString(self.invoice.due_date.strftime('%Y-%m-%d'), 'yyyy-MM-dd'))

        due_date_label = QLabel(tr("invoices.due_date", "تاريخ الاستحقاق:"))
        due_date_label.setStyleSheet(f"""
            font-weight: bold;
            font-size: 14pt;
            color: {self.colors['text']};
            min-width: 120px;
        """)

        right_layout.addRow(due_date_label, self.due_date_edit)

        # Add layouts to header
        header_layout.addLayout(left_layout, 1)
        header_layout.addLayout(right_layout, 2)

        return header_widget

    def create_items_section(self):
        """Create the invoice items section with proper sizing."""
        items_widget = QWidget()
        items_widget.setObjectName("itemsWidget")
        self.itemsWidget = items_widget  # Store reference for theme updates
        items_widget.setStyleSheet(f"""
            #itemsWidget {{
                background-color: {self.colors['card']};
                border-radius: 10px;
                border: 2px solid {self.colors['primary']};
            }}
        """)

        # Create layout with proper spacing
        items_layout = QVBoxLayout(items_widget)
        items_layout.setContentsMargins(20, 20, 20, 20)
        items_layout.setSpacing(15)

        # Create title with clear styling
        title_label = QLabel(tr("products.title", "المنتجات والخدمات"))
        title_label.setStyleSheet(f"""
            font-size: 18pt;
            font-weight: bold;
            color: {self.colors['primary_dark']};
            padding: 5px;
            border-bottom: 2px solid {self.colors['primary']};
            margin-bottom: 10px;
            background-color: transparent;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        items_layout.addWidget(title_label)

        # Create buttons section with proper spacing
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(15)

        # Add item button with proper sizing
        self.add_item_button = QPushButton(tr("invoices.add_item_manually", "إضافة منتج/خدمة يدوياً"))
        self.add_item_button.setStyleSheet(f"""
            background-color: {self.colors['primary']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
        """)
        self.add_item_button.setMinimumHeight(45)  # Ensure minimum height
        self.add_item_button.setMinimumWidth(200)  # Ensure minimum width
        self.add_item_button.clicked.connect(self.add_item)

        # Select product button with proper sizing
        self.select_product_button = QPushButton(tr("invoices.select_from_products", "اختيار من المنتجات والخدمات"))
        self.select_product_button.setStyleSheet(f"""
            background-color: {self.colors['accent']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
        """)
        self.select_product_button.setMinimumHeight(45)  # Ensure minimum height
        self.select_product_button.setMinimumWidth(200)  # Ensure minimum width
        self.select_product_button.clicked.connect(self.select_product)

        buttons_layout.addWidget(self.add_item_button)
        buttons_layout.addWidget(self.select_product_button)
        items_layout.addLayout(buttons_layout)

        # Create items table with proper sizing
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(7)
        self.items_table.setHorizontalHeaderLabels([
            tr("common.description", "الوصف"),
            tr("common.quantity", "الكمية"),
            tr("invoices.unit_price", "سعر الوحدة"),
            tr("common.discount", "الخصم"),
            tr("common.tax", "الضريبة"),
            tr("common.total", "الإجمالي"),
            ""
        ])

        # Configure table appearance with proper sizing
        self.items_table.setStyleSheet(f"""
            QTableWidget {{
                background-color: {self.colors['table_bg']};
                alternate-background-color: {self.colors['table_alternate_bg']};
                border: 1px solid {self.colors['table_border']};
                gridline-color: {self.colors['table_border']};
                selection-background-color: {self.colors['table_selection_bg']};
                selection-color: {self.colors['table_selection_text']};
                font-size: 13pt;
                color: {self.colors['table_text']};
            }}
            QTableWidget::item {{
                padding: 8px;
                border-bottom: 1px solid {self.colors['table_border']};
                min-height: 30px;
                color: {self.colors['table_text']};
            }}
            QHeaderView::section {{
                background-color: {self.colors['table_header_bg']};
                color: {self.colors['table_header_text']};
                padding: 12px;
                border: none;
                font-weight: bold;
                font-size: 14pt;
            }}
            QTableWidget::item:hover {{
                background-color: {self.colors['table_hover_bg']};
            }}
        """)

        # Configure column sizes
        self.items_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        for i in range(1, 6):
            self.items_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.ResizeToContents)
        self.items_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)
        self.items_table.setColumnWidth(6, 150)  # Wider column for buttons

        # Configure table behavior
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.verticalHeader().setVisible(False)
        self.items_table.setShowGrid(True)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setMinimumHeight(300)  # Ensure minimum height

        items_layout.addWidget(self.items_table, 1)  # Give it stretch factor

        # Create notes section with proper sizing
        notes_layout = QVBoxLayout()
        notes_layout.setSpacing(10)

        notes_label = QLabel(tr("common.notes", "ملاحظات"))
        notes_label.setStyleSheet(f"""
            font-size: 16pt;
            font-weight: bold;
            color: {self.colors['text']};
            margin-top: 10px;
        """)
        notes_layout.addWidget(notes_label)

        self.notes_edit = QTextEdit()
        self.notes_edit.setStyleSheet(f"""
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['divider']};
            border-radius: 6px;
            padding: 10px;
            font-size: 13pt;
            color: {self.colors['input_text']};
        """)
        self.notes_edit.setMinimumHeight(100)  # Ensure minimum height
        self.notes_edit.setMaximumHeight(150)  # Limit maximum height

        if self.invoice and self.invoice.notes:
            self.notes_edit.setText(self.invoice.notes)

        notes_layout.addWidget(self.notes_edit)
        items_layout.addLayout(notes_layout)

        return items_widget

    def create_totals_section(self):
        """Create the invoice totals section with proper sizing and clear layout."""
        totals_widget = QWidget()
        totals_widget.setObjectName("totalsWidget")
        self.totalsWidget = totals_widget  # Store reference for theme updates
        totals_widget.setFixedWidth(280)  # Width to match the provided design
        totals_widget.setStyleSheet(f"""
            #totalsWidget {{
                background-color: {self.colors['card']};
                border-radius: 8px;
                border: 2px solid {self.colors['primary']};
            }}
        """)

        # Create main layout with proper spacing
        totals_layout = QVBoxLayout(totals_widget)
        totals_layout.setContentsMargins(12, 12, 12, 12)  # Smaller padding to match design
        totals_layout.setSpacing(8)  # Smaller spacing to match design

        # Create title with clear styling
        title_label = QLabel(tr("invoices.totals", "الإجماليات"))
        title_label.setStyleSheet(f"""
            font-size: 14pt;
            font-weight: bold;
            color: {self.colors['primary']};
            padding: 0px;
            margin-bottom: 12px;
            background-color: transparent;
            text-align: center;
        """)
        title_label.setAlignment(Qt.AlignCenter)
        totals_layout.addWidget(title_label)

        # Create form layout for the totals items
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFormAlignment(Qt.AlignLeft)
        form_layout.setSpacing(8)  # Smaller spacing between rows
        form_layout.setContentsMargins(0, 0, 0, 0)

        # Subtotal row
        subtotal_label = QLabel(tr("common.subtotal", "الإجمالي الفرعي:"))
        subtotal_label.setStyleSheet(f"""
            font-size: 13px;
            color: {self.colors['text']};
        """)

        self.subtotal_spin = QDoubleSpinBox()
        self.subtotal_spin.setMinimum(0.0)
        self.subtotal_spin.setMaximum(9999999.99)
        self.subtotal_spin.setDecimals(2)
        self.subtotal_spin.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            text-align: right;
            background-color: {self.colors['input_bg']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['input_text']};
        """)
        # Mostrar los botones para que sea claro que es un campo interactivo
        self.subtotal_spin.setButtonSymbols(QDoubleSpinBox.UpDownArrows)
        self.subtotal_spin.valueChanged.connect(self.calculate_totals)
        form_layout.addRow(subtotal_label, self.subtotal_spin)

        # Discount row
        discount_label = QLabel(tr("invoices.total_discount", "إجمالي الخصم:"))
        discount_label.setStyleSheet(f"""
            font-size: 13px;
            color: {self.colors['text']};
        """)

        self.discount_spin = QDoubleSpinBox()
        self.discount_spin.setMinimum(0.0)
        self.discount_spin.setMaximum(9999999.99)
        self.discount_spin.setDecimals(2)
        self.discount_spin.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            text-align: right;
            background-color: {self.colors['input_bg']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['input_text']};
        """)
        # Mostrar los botones para que sea claro que es un campo interactivo
        self.discount_spin.setButtonSymbols(QDoubleSpinBox.UpDownArrows)
        # Conectar el cambio de valor para actualizar los totales
        self.discount_spin.valueChanged.connect(self.calculate_totals)
        form_layout.addRow(discount_label, self.discount_spin)

        # Tax row
        tax_label = QLabel(tr("invoices.total_tax", "إجمالي الضريبة:"))
        tax_label.setStyleSheet(f"""
            font-size: 13px;
            color: {self.colors['text']};
        """)

        self.tax_spin = QDoubleSpinBox()
        self.tax_spin.setMinimum(0.0)
        self.tax_spin.setMaximum(9999999.99)
        self.tax_spin.setDecimals(2)
        self.tax_spin.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            text-align: right;
            background-color: {self.colors['input_bg']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['input_text']};
        """)
        # Mostrar los botones para que sea claro que es un campo interactivo
        self.tax_spin.setButtonSymbols(QDoubleSpinBox.UpDownArrows)
        # Conectar el cambio de valor para actualizar los totales
        self.tax_spin.valueChanged.connect(self.calculate_totals)
        form_layout.addRow(tax_label, self.tax_spin)

        # Total row with special styling
        total_label = QLabel(tr("invoices.final_total", "الإجمالي النهائي:"))
        total_label.setStyleSheet(f"""
            font-size: 13px;
            font-weight: bold;
            color: {self.colors['text']};
        """)

        self.total_spin = QDoubleSpinBox()
        self.total_spin.setMinimum(0.0)
        self.total_spin.setMaximum(9999999.99)
        self.total_spin.setDecimals(2)
        self.total_spin.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            text-align: right;
            background-color: {self.colors['primary_light']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['input_text']};
            font-weight: bold;
        """)
        # Mostrar los botones para que sea claro que es un campo interactivo
        self.total_spin.setButtonSymbols(QDoubleSpinBox.UpDownArrows)
        self.total_spin.valueChanged.connect(self.on_total_changed)
        form_layout.addRow(total_label, self.total_spin)

        # Currency row
        currency_label = QLabel(tr("currency.title", "العملة:"))
        currency_label.setStyleSheet(f"""
            font-size: 13px;
            color: {self.colors['text']};
        """)

        self.currency_combo = QComboBox()
        self.currency_combo.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            background-color: {self.colors['input_bg']};
            border: 1px solid {self.colors['divider']};
            color: {self.colors['input_text']};
        """)

        if self.currency_manager:
            self.load_currencies()
            self.currency_combo.currentIndexChanged.connect(self.on_currency_changed)

        form_layout.addRow(currency_label, self.currency_combo)

        # Add the form layout to the main layout
        totals_layout.addLayout(form_layout)

        # Add spacer before payment section
        totals_layout.addSpacing(10)

        # Create payment section
        payment_container = self.create_payment_section()
        totals_layout.addWidget(payment_container)

        # Add spacer to push everything to the top
        totals_layout.addStretch(1)

        return totals_widget



    def create_payment_section(self):
        """Create the payment section with proper sizing."""
        payment_container = QWidget()
        payment_container.setStyleSheet(f"""
            border: 2px solid {self.colors['primary']};
            border-radius: 8px;
            padding: 10px 12px;
            background-color: {self.colors['card']};
        """)

        # Create layout with proper spacing
        payment_layout = QVBoxLayout(payment_container)
        payment_layout.setContentsMargins(0, 0, 0, 0)
        payment_layout.setSpacing(8)  # Smaller spacing to match design

        # Create title with clear styling
        payment_title = QLabel(tr("invoices.payment_info", "معلومات الدفع"))
        payment_title.setStyleSheet(f"""
            color: {self.colors['primary']};
            text-align: center;
            margin-bottom: 12px;
            font-weight: bold;
            font-size: 14pt;
            background-color: transparent;
        """)
        payment_title.setAlignment(Qt.AlignCenter)
        payment_layout.addWidget(payment_title)

        # Create form layout for payment items
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFormAlignment(Qt.AlignLeft)
        form_layout.setSpacing(8)  # Smaller spacing between rows
        form_layout.setContentsMargins(0, 0, 0, 0)

        # Paid amount row
        paid_label = QLabel(tr("invoices.paid_amount", "المدفوع:"))
        paid_label.setStyleSheet(f"""
            font-size: 13px;
            color: {self.colors['text']};
        """)

        self.amount_paid_spin = QDoubleSpinBox()
        self.amount_paid_spin.setMinimum(0.0)
        self.amount_paid_spin.setMaximum(9999999.99)
        self.amount_paid_spin.setDecimals(2)
        self.amount_paid_spin.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            text-align: right;
            background-color: {self.colors['input_bg']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['input_text']};
        """)
        # Mostrar los botones para que sea claro que es un campo interactivo
        self.amount_paid_spin.setButtonSymbols(QDoubleSpinBox.UpDownArrows)
        # Conectar el cambio de valor para actualizar el monto restante
        self.amount_paid_spin.valueChanged.connect(self.calculate_totals)
        form_layout.addRow(paid_label, self.amount_paid_spin)

        # Due amount row with special styling
        due_label = QLabel(tr("invoices.remaining_amount", "المتبقي:"))
        due_label.setStyleSheet(f"""
            font-size: 13px;
            font-weight: bold;
            color: {self.colors['text']};
        """)

        self.amount_due_spin = QDoubleSpinBox()
        self.amount_due_spin.setMinimum(0.0)
        self.amount_due_spin.setMaximum(9999999.99)
        self.amount_due_spin.setDecimals(2)
        self.amount_due_spin.setReadOnly(True)
        self.amount_due_spin.setStyleSheet(f"""
            padding: 4px;
            font-size: 13px;
            text-align: right;
            background-color: {self.colors['primary_light']};
            border: 2px solid {self.colors['primary']};
            border-radius: 4px;
            color: {self.colors['input_text']};
        """)
        # Para este campo mantenemos los botones ocultos ya que es de solo lectura
        self.amount_due_spin.setButtonSymbols(QDoubleSpinBox.NoButtons)
        form_layout.addRow(due_label, self.amount_due_spin)

        # Add the form layout to the main layout
        payment_layout.addLayout(form_layout)

        return payment_container

    def create_footer_section(self):
        """Create the footer section with action buttons."""
        footer_widget = QWidget()
        footer_widget.setObjectName("footerWidget")
        self.footerWidget = footer_widget  # Store reference for theme updates
        footer_widget.setStyleSheet(f"""
            #footerWidget {{
                background-color: {self.colors['card']};
                border-radius: 10px;
                border: 2px solid {self.colors['primary']};
            }}
        """)

        # Create layout with proper spacing
        footer_layout = QHBoxLayout(footer_widget)
        footer_layout.setContentsMargins(20, 20, 20, 20)
        footer_layout.setSpacing(20)

        # Add spacer to push buttons to the right
        footer_layout.addStretch(1)

        # Cancel button with proper sizing
        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.setStyleSheet(f"""
            background-color: {self.colors['danger']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
        """)
        self.cancel_button.setMinimumHeight(50)  # Ensure minimum height
        self.cancel_button.setMinimumWidth(150)  # Ensure minimum width
        self.cancel_button.clicked.connect(self.reject)

        # Save button with proper sizing
        self.save_button = QPushButton(tr("invoices.create_invoice", "إنشاء فاتورة"))
        self.save_button.setStyleSheet(f"""
            background-color: {self.colors['success']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
        """)
        self.save_button.setMinimumHeight(50)  # Ensure minimum height
        self.save_button.setMinimumWidth(150)  # Ensure minimum width
        self.save_button.clicked.connect(self.save_invoice)

        footer_layout.addWidget(self.cancel_button)
        footer_layout.addWidget(self.save_button)

        return footer_widget

    def on_total_changed(self):
        """Handle changes to the total value."""
        # This function is called when the user manually changes the total
        # We need to adjust the tax value to match the new total

        # Get current values
        subtotal = self.subtotal_spin.value()
        discount = self.discount_spin.value()
        total = self.total_spin.value()

        # Calculate what the tax should be to achieve this total
        new_tax = total - subtotal + discount

        # Update tax value without triggering signals
        self.tax_spin.blockSignals(True)
        self.tax_spin.setValue(max(0, new_tax))  # Ensure tax is not negative
        self.tax_spin.blockSignals(False)

        # Update amount due
        amount_paid = self.amount_paid_spin.value()
        amount_due = max(0, total - amount_paid)  # Ensure amount due is not negative

        # Update amount due without triggering signals
        self.amount_due_spin.blockSignals(True)
        self.amount_due_spin.setValue(amount_due)
        self.amount_due_spin.blockSignals(False)

        # Update status based on payment
        if hasattr(self, 'status_combo'):
            if amount_paid <= 0:
                self.status_combo.setCurrentIndex(self.status_combo.findData("draft"))
            elif amount_paid < total:
                self.status_combo.setCurrentIndex(self.status_combo.findData("partially_paid"))
            else:
                self.status_combo.setCurrentIndex(self.status_combo.findData("paid"))

    def calculate_totals(self):
        """Calculate invoice totals with proper formatting."""
        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Calculate subtotal
        subtotal = sum(item.total for item in self.items_list)

        # Get discount and tax
        discount = self.discount_spin.value()
        tax = self.tax_spin.value()

        # Calculate total
        total = subtotal - discount + tax

        # Update spinbox values without triggering signals
        self.subtotal_spin.blockSignals(True)
        self.subtotal_spin.setValue(subtotal)
        self.subtotal_spin.blockSignals(False)

        self.total_spin.blockSignals(True)
        self.total_spin.setValue(total)
        self.total_spin.blockSignals(False)

        # Update amount due
        amount_paid = self.amount_paid_spin.value()
        amount_due = max(0, total - amount_paid)  # Ensure amount due is not negative

        # Update amount due without triggering signals
        self.amount_due_spin.blockSignals(True)
        self.amount_due_spin.setValue(amount_due)
        self.amount_due_spin.blockSignals(False)

        # Update spinbox suffixes
        self.subtotal_spin.setSuffix(f" {currency_symbol}")
        self.discount_spin.setSuffix(f" {currency_symbol}")
        self.tax_spin.setSuffix(f" {currency_symbol}")
        self.total_spin.setSuffix(f" {currency_symbol}")
        self.amount_paid_spin.setSuffix(f" {currency_symbol}")
        self.amount_due_spin.setSuffix(f" {currency_symbol}")

        # Update status based on payment if status combo exists
        if hasattr(self, 'status_combo'):
            if amount_paid <= 0:
                self.status_combo.setCurrentIndex(self.status_combo.findData("draft"))
            elif amount_paid < total:
                self.status_combo.setCurrentIndex(self.status_combo.findData("partially_paid"))
            else:
                self.status_combo.setCurrentIndex(self.status_combo.findData("paid"))

    def add_item_to_table(self, item):
        """Add an item to the table with proper formatting."""
        row = self.items_table.rowCount()
        self.items_table.insertRow(row)

        # Get currency symbol
        currency_symbol = "ج.م"  # Default
        if hasattr(self, 'current_currency') and self.current_currency:
            currency_symbol = self.current_currency.symbol

        # Format with thousand separators for better readability
        formatted_unit_price = format_thousands(f"{item.unit_price:.2f}")
        formatted_discount = format_thousands(f"{item.discount:.2f}")
        formatted_tax = format_thousands(f"{item.tax:.2f}")
        formatted_total = format_thousands(f"{item.total:.2f}")

        # Set item data with proper formatting
        self.items_table.setItem(row, 0, QTableWidgetItem(item.description))
        self.items_table.setItem(row, 1, QTableWidgetItem(f"{item.quantity:.2f}"))
        self.items_table.setItem(row, 2, QTableWidgetItem(f"{formatted_unit_price} {currency_symbol}"))
        self.items_table.setItem(row, 3, QTableWidgetItem(f"{formatted_discount} {currency_symbol}"))
        self.items_table.setItem(row, 4, QTableWidgetItem(f"{formatted_tax} {currency_symbol}"))
        self.items_table.setItem(row, 5, QTableWidgetItem(f"{formatted_total} {currency_symbol}"))

        # Create action buttons with proper sizing
        actions_widget = QWidget()
        actions_widget.setStyleSheet(f"""
            background-color: transparent;
        """)
        actions_layout = QHBoxLayout(actions_widget)
        actions_layout.setContentsMargins(5, 5, 5, 5)
        actions_layout.setSpacing(10)

        # Edit button
        edit_button = QPushButton(tr("common.edit", "تعديل"))
        edit_button.setStyleSheet(f"""
            background-color: {self.colors['primary']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 4px;
            padding: 5px;
            font-size: 12pt;
            font-weight: bold;
        """)
        edit_button.setMinimumHeight(35)  # Ensure minimum height
        edit_button.clicked.connect(lambda _, r=row: self.edit_item(r))

        # Delete button
        delete_button = QPushButton(tr("common.delete", "حذف"))
        delete_button.setStyleSheet(f"""
            background-color: {self.colors['danger']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 4px;
            padding: 5px;
            font-size: 12pt;
            font-weight: bold;
        """)
        delete_button.setMinimumHeight(35)  # Ensure minimum height
        delete_button.clicked.connect(lambda _, r=row: self.delete_item(r))

        actions_layout.addWidget(edit_button)
        actions_layout.addWidget(delete_button)

        self.items_table.setCellWidget(row, 6, actions_widget)

    def showEvent(self, event):
        """Override showEvent to ensure the window size is fixed."""
        super().showEvent(event)
        # Ensure window size is fixed
        self.setFixedSize(1600, 900)

    def set_view_only_mode(self):
        """Set the dialog to view-only mode with proper styling."""
        # Change window title
        self.setWindowTitle(f"{tr('invoices.view_invoice', 'عرض الفاتورة')} {self.invoice.invoice_number}")

        # Disable all editable fields
        self.customer_combo.setEnabled(False)
        self.issue_date_edit.setEnabled(False)
        self.due_date_edit.setEnabled(False)
        self.status_combo.setEnabled(False)
        self.currency_combo.setEnabled(False)
        self.discount_spin.setEnabled(False)
        self.tax_spin.setEnabled(False)
        self.amount_paid_spin.setEnabled(False)
        self.amount_due_spin.setEnabled(False)
        self.notes_edit.setEnabled(False)

        # Hide action buttons
        self.add_item_button.setVisible(False)
        self.select_product_button.setVisible(False)

        # Change save button to close button
        self.save_button.setText(tr("common.close", "إغلاق"))
        self.save_button.setStyleSheet(f"""
            background-color: {self.colors['primary']};
            color: {self.colors['button_text']};
            border: none;
            border-radius: 6px;
            padding: 10px;
            font-size: 14pt;
            font-weight: bold;
        """)
        # self.save_button.clicked.disconnect()
        # self.save_button.clicked.connect(self.accept)

        # Hide cancel button
        self.cancel_button.setVisible(False)

        # Apply view-only styling
        self.setStyleSheet(f"""
            QDialog {{
                background-color: {self.colors['background']};
            }}
            QLineEdit, QDateEdit, QComboBox, QDoubleSpinBox, QTextEdit {{
                background-color: {self.colors['input_bg']};
                border: 1px solid {self.colors['divider']};
                border-radius: 6px;
                padding: 10px;
                font-size: 14pt;
                color: {self.colors['input_text']};
            }}
        """)

    def load_currencies(self):
        """Load currencies from the database with proper error handling."""
        if not self.currency_manager:
            return

        try:
            currencies = self.currency_manager.get_active_currencies()

            self.currency_combo.clear()

            # Find primary currency
            for currency in currencies:
                if currency.is_primary:
                    self.primary_currency = currency
                    break

            # Add currencies to combo box
            for currency in currencies:
                self.currency_combo.addItem(f"{currency.name} ({currency.code})", currency.id)

                # Set current currency if this is the primary currency
                if currency.is_primary:
                    self.current_currency = currency
                    self.currency_combo.setCurrentIndex(self.currency_combo.count() - 1)
        except Exception as e:
            print(f"Error loading currencies: {str(e)}")
            # Add a default currency option
            self.currency_combo.addItem("جنيه مصري (EGP)", 1)

    def load_customers(self):
        """Load customers from the database with proper error handling."""
        if not self.db_manager:
            return

        try:
            query = "SELECT id, name FROM customers ORDER BY name"
            rows = self.db_manager.execute_query(query)

            self.customer_combo.clear()
            for row in rows:
                self.customer_combo.addItem(row['name'], row['id'])
        except Exception as e:
            print(f"Error loading customers: {str(e)}")
            # Add a default customer option
            self.customer_combo.addItem("عميل افتراضي", 1)

    def save_invoice(self):
        """Save the invoice to the database with enhanced error handling."""
        # Validate required fields
        if self.customer_combo.currentIndex() < 0:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("invoices.must_select_customer", "يجب اختيار العميل"))
            return

        if len(self.items_list) == 0:
            QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("invoices.must_add_at_least_one_item", "يجب إضافة منتج أو خدمة واحدة على الأقل"))
            return

        # Usar un QProgressDialog en lugar de QMessageBox para mostrar el progreso
        from PySide6.QtCore import Qt
        progress_dialog = QProgressDialog(tr("invoices.saving_invoice", "جاري حفظ الفاتورة..."), None, 0, 100, self)
        progress_dialog.setWindowTitle(tr("invoices.saving", "جاري الحفظ"))
        progress_dialog.setWindowModality(Qt.WindowModal)
        progress_dialog.setMinimumDuration(0)  # Mostrar inmediatamente
        progress_dialog.setValue(0)
        progress_dialog.setAutoClose(True)
        progress_dialog.setAutoReset(True)
        progress_dialog.setCancelButton(None)  # Eliminar el botón de cancelar

        # Mostrar el diálogo
        progress_dialog.show()
        QApplication.processEvents()

        try:
            # Get form data
            customer_id = self.customer_combo.currentData()
            issue_date = self.issue_date_edit.date().toString("yyyy-MM-dd")
            due_date = self.due_date_edit.date().toString("yyyy-MM-dd")
            status = self.status_combo.currentData()
            notes = self.notes_edit.toPlainText()

            # Actualizar progreso
            progress_dialog.setValue(10)
            QApplication.processEvents()

            # Get totals
            subtotal = sum(item.total for item in self.items_list)
            discount = self.discount_spin.value()
            tax = self.tax_spin.value()
            total = subtotal - discount + tax
            amount_paid = self.amount_paid_spin.value()
            amount_due = self.amount_due_spin.value()

            # Get currency
            currency_id = None
            if self.currency_manager and self.current_currency:
                currency_id = self.current_currency.id

            # Actualizar progreso
            progress_dialog.setValue(20)
            QApplication.processEvents()

            # Use our improved database methods instead of direct connection
            if self.invoice and self.invoice.id:
                # Update existing invoice
                update_query = """
                UPDATE invoices
                SET customer_id = ?, issue_date = ?, due_date = ?, status = ?,
                    subtotal = ?, discount = ?, tax = ?, total = ?,
                    amount_paid = ?, amount_due = ?, notes = ?, currency_id = ?
                WHERE id = ?
                """
                params = (
                    customer_id, issue_date, due_date, status,
                    subtotal, discount, tax, total,
                    amount_paid, amount_due, notes, currency_id,
                    self.invoice.id
                )

                try:
                    # Update the invoice
                    self.db_manager.execute_update(update_query, params)

                    # Actualizar progreso
                    progress_dialog.setValue(40)
                    QApplication.processEvents()

                    # Delete existing items
                    delete_query = "DELETE FROM invoice_items WHERE invoice_id = ?"
                    self.db_manager.execute_update(delete_query, (self.invoice.id,))

                    invoice_id = self.invoice.id
                    invoice_number = self.invoice.invoice_number

                    # Actualizar progreso
                    progress_dialog.setValue(50)
                    QApplication.processEvents()

                except Exception as e:
                    error_msg = str(e)
                    print(f"Error updating invoice: {error_msg}")
                    if "database is locked" in error_msg.lower() or "closed database" in error_msg.lower():
                        # Try to repair the database
                        progress_dialog.setLabelText(tr("database.repairing_database", "جاري إصلاح قاعدة البيانات..."))
                        QApplication.processEvents()

                        self.db_manager.repair_database()

                        # Try again
                        progress_dialog.setLabelText(tr("messages.retrying", "جاري إعادة المحاولة..."))
                        QApplication.processEvents()

                        self.db_manager.execute_update(update_query, params)
                        self.db_manager.execute_update(delete_query, (self.invoice.id,))
                    else:
                        raise e
            else:
                # Generate invoice number
                try:
                    settings_query = "SELECT value FROM settings WHERE key = ?"
                    prefix_row = self.db_manager.execute_query(settings_query, ("invoice_prefix",))
                    number_row = self.db_manager.execute_query(settings_query, ("next_invoice_number",))

                    prefix = prefix_row[0]['value'] if prefix_row else "INV-"
                    next_number = number_row[0]['value'] if number_row else "1001"

                    invoice_number = f"{prefix}{next_number}"
                except Exception as e:
                    # If there's an error getting settings, use defaults
                    print(f"Error getting invoice settings: {e}")
                    prefix = "INV-"
                    next_number = "1001"
                    invoice_number = f"{prefix}{next_number}"

                # Actualizar progreso
                progress_dialog.setValue(30)
                QApplication.processEvents()

                # Insert new invoice
                insert_query = """
                INSERT INTO invoices (
                    invoice_number, customer_id, issue_date, due_date, status,
                    subtotal, discount, tax, total, amount_paid, amount_due, notes, currency_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                params = (
                    invoice_number, customer_id, issue_date, due_date, status,
                    subtotal, discount, tax, total, amount_paid, amount_due, notes, currency_id
                )

                try:
                    # Insert the invoice
                    invoice_id = self.db_manager.execute_insert(insert_query, params)

                    # Actualizar progreso
                    progress_dialog.setValue(40)
                    QApplication.processEvents()

                    # Increment next invoice number
                    next_number_int = int(next_number) + 1
                    update_query = "UPDATE settings SET value = ? WHERE key = ?"
                    self.db_manager.execute_update(update_query, (str(next_number_int), "next_invoice_number"))

                    # Actualizar progreso
                    progress_dialog.setValue(50)
                    QApplication.processEvents()

                except Exception as e:
                    error_msg = str(e)
                    print(f"Error inserting invoice: {error_msg}")
                    if "database is locked" in error_msg.lower() or "closed database" in error_msg.lower():
                        # Try to repair the database
                        progress_dialog.setLabelText(tr("database.repairing_database", "جاري إصلاح قاعدة البيانات..."))
                        QApplication.processEvents()

                        self.db_manager.repair_database()

                        # Try again
                        progress_dialog.setLabelText(tr("messages.retrying", "جاري إعادة المحاولة..."))
                        QApplication.processEvents()

                        invoice_id = self.db_manager.execute_insert(insert_query, params)
                        self.db_manager.execute_update(update_query, (str(next_number_int), "next_invoice_number"))
                    else:
                        raise e

            # Actualizar progreso
            progress_dialog.setValue(60)
            progress_dialog.setLabelText(tr("invoices.saving_items", "جاري حفظ العناصر..."))
            QApplication.processEvents()

            # Insert items
            item_count = len(self.items_list)
            for i, item in enumerate(self.items_list):
                item_query = """
                INSERT INTO invoice_items (
                    invoice_id, description, quantity, unit_price,
                    discount, tax, total, product_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """
                product_id = getattr(item, 'product_id', None)
                params = (
                    invoice_id, item.description, item.quantity, item.unit_price,
                    item.discount, item.tax, item.total, product_id
                )

                try:
                    self.db_manager.execute_insert(item_query, params)

                    # Actualizar progreso basado en el número de elementos
                    progress_value = 60 + int((i + 1) / item_count * 20)
                    progress_dialog.setValue(progress_value)
                    QApplication.processEvents()

                except Exception as e:
                    error_msg = str(e)
                    print(f"Error inserting item {i+1}: {error_msg}")
                    if "database is locked" in error_msg.lower() or "closed database" in error_msg.lower():
                        # Try to repair the database
                        progress_dialog.setLabelText(tr("database.repairing_database_item", f"جاري إصلاح قاعدة البيانات (العنصر {i+1})..."))
                        QApplication.processEvents()

                        self.db_manager.repair_database()

                        # Try again
                        progress_dialog.setLabelText(tr("messages.retrying_item", f"جاري إعادة المحاولة (العنصر {i+1})..."))
                        QApplication.processEvents()

                        self.db_manager.execute_insert(item_query, params)
                    else:
                        raise e

            # Actualizar progreso
            progress_dialog.setValue(80)
            progress_dialog.setLabelText(tr("inventory.updating_inventory", "جاري تحديث المخزون..."))
            QApplication.processEvents()

            # Update inventory if needed
            try:
                success, updated_products = update_inventory_for_invoice(self.db_manager, invoice_id, self.items_list, self)
                if not success:
                    print("Warning: Some inventory updates failed")
                    QMessageBox.warning(
                        self,
                        tr("messages.warning", "تحذير"),
                        tr("inventory.update_failed", "تم حفظ الفاتورة ولكن فشل تحديث المخزون. يرجى التحقق من المخزون يدوياً.")
                    )
                else:
                    print(f"Successfully updated inventory for {len(updated_products)} products")
            except Exception as e:
                print(f"Error updating inventory: {e}")
                QMessageBox.warning(
                    self,
                    tr("messages.warning", "تحذير"),
                    tr("inventory.update_error", f"تم حفظ الفاتورة ولكن حدث خطأ أثناء تحديث المخزون: {str(e)}")
                )
                # Continue even if inventory update fails

            # Finalizar progreso
            progress_dialog.setValue(100)
            progress_dialog.setLabelText(tr("messages.save_successful", "تم الحفظ بنجاح!"))
            QApplication.processEvents()

            # Cerrar el diálogo de progreso
            progress_dialog.close()

            QMessageBox.information(
                self,
                tr("messages.success", "نجاح"),
                tr("invoices.invoice_saved_success", f"تم {'تحديث' if self.invoice and self.invoice.id else 'إنشاء'} الفاتورة {invoice_number} بنجاح")
            )
            self.accept()

        except Exception as e:
            # Cerrar el diálogo de progreso
            progress_dialog.close()

            error_msg = str(e)
            print(f"Error saving invoice: {error_msg}")

            if "database is locked" in error_msg.lower() or "closed database" in error_msg.lower():
                # Try to repair the database
                repair_msg = tr("database.locked_will_repair", "قاعدة البيانات مقفلة أو مغلقة. سيتم محاولة إصلاحها الآن.")
                QMessageBox.warning(self, tr("errors.database_error", "خطأ في قاعدة البيانات"), repair_msg)

                if self.db_manager.repair_database():
                    retry_msg = tr("database.repaired_retry", "تم إصلاح قاعدة البيانات. هل تريد المحاولة مرة أخرى؟")
                    reply = QMessageBox.question(
                        self, tr("messages.retry", "إعادة المحاولة"),
                        retry_msg,
                        QMessageBox.Yes | QMessageBox.No, QMessageBox.Yes
                    )

                    if reply == QMessageBox.Yes:
                        # Try again
                        self.save_invoice()
                        return
                else:
                    QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("database.repair_failed", "فشل إصلاح قاعدة البيانات. يرجى إعادة تشغيل التطبيق."))
            else:
                QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("invoices.save_error", f"حدث خطأ أثناء حفظ الفاتورة: {str(e)}"))

    def add_item(self):
        """Add a new item to the invoice manually."""
        dialog = InvoiceItemDialog(self)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, tr("messages.error", "خطأ"), tr("invoices.description_required", "يجب إدخال وصف المنتج أو الخدمة"))
                return

            # Create item object
            item = InvoiceItem(
                description=item_data['description'],
                quantity=item_data['quantity'],
                unit_price=item_data['unit_price'],
                discount=item_data['discount'],
                tax=item_data['tax'],
                total=item_data['total']
            )

            # Add to list and table
            self.items_list.append(item)
            self.add_item_to_table(item)

            # Recalculate totals
            self.calculate_totals()

    def select_product(self):
        """Select a product or service from the products list."""
        # Create products view in select mode
        products_dialog = QDialog(self)
        products_dialog.setWindowTitle(tr("invoices.select_product_service", "اختيار منتج أو خدمة"))
        products_dialog.setMinimumSize(800, 600)

        # Set RTL layout direction
        products_dialog.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        layout = QVBoxLayout(products_dialog)

        # Create products view
        products_view = ProductsView(self.db_manager, select_mode=True)
        # Pass currency manager if available
        if hasattr(self, 'currency_manager') and self.currency_manager:
            products_view.currency_manager = self.currency_manager

        # Connect the product_selected signal to accept the dialog
        products_view.product_selected.connect(lambda _: products_dialog.accept())

        layout.addWidget(products_view)

        # Create button box
        button_box = QHBoxLayout()
        cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        cancel_button.clicked.connect(products_dialog.reject)
        button_box.addWidget(cancel_button)

        # No need for a separate select button since we're using the buttons in the table
        # select_button = QPushButton("اختيار")
        # select_button.clicked.connect(products_dialog.accept)
        # button_box.addWidget(select_button)

        layout.addLayout(button_box)

        # Show dialog
        if products_dialog.exec():
            # Get selected product
            selected_product = products_view.get_selected_product()
            if selected_product:
                # Create item from product
                item = InvoiceItem(
                    description=selected_product.name,
                    quantity=1,
                    unit_price=selected_product.price,
                    discount=0,
                    tax=0,
                    total=selected_product.price
                )

                # Add product_id and is_product attributes
                item.product_id = selected_product.id
                item.is_product = selected_product.type == "product"
                item.track_inventory = selected_product.track_inventory if hasattr(selected_product, 'track_inventory') else False

                # Add to list and table
                self.items_list.append(item)
                self.add_item_to_table(item)

                # Recalculate totals
                self.calculate_totals()

    def edit_item(self, row):
        """Edit an item in the invoice."""
        if row < 0 or row >= len(self.items_list):
            return

        item = self.items_list[row]
        dialog = InvoiceItemDialog(self, item)
        if dialog.exec():
            item_data = dialog.get_item_data()

            # Validate data
            if not item_data['description']:
                QMessageBox.warning(self, "خطأ", "يجب إدخال وصف المنتج أو الخدمة")
                return

            # Update item
            item.description = item_data['description']
            item.quantity = item_data['quantity']
            item.unit_price = item_data['unit_price']
            item.discount = item_data['discount']
            item.tax = item_data['tax']
            item.total = item_data['total']

            # Update table
            self.items_table.setItem(row, 0, QTableWidgetItem(item.description))
            self.items_table.setItem(row, 1, QTableWidgetItem(f"{item.quantity:.2f}"))

            # Get currency symbol
            currency_symbol = "ج.م"  # Default
            if hasattr(self, 'current_currency') and self.current_currency:
                currency_symbol = self.current_currency.symbol

            # Format with thousand separators for better readability
            formatted_unit_price = format_thousands(f"{item.unit_price:.2f}")
            formatted_discount = format_thousands(f"{item.discount:.2f}")
            formatted_tax = format_thousands(f"{item.tax:.2f}")
            formatted_total = format_thousands(f"{item.total:.2f}")

            self.items_table.setItem(row, 2, QTableWidgetItem(f"{formatted_unit_price} {currency_symbol}"))
            self.items_table.setItem(row, 3, QTableWidgetItem(f"{formatted_discount} {currency_symbol}"))
            self.items_table.setItem(row, 4, QTableWidgetItem(f"{formatted_tax} {currency_symbol}"))
            self.items_table.setItem(row, 5, QTableWidgetItem(f"{formatted_total} {currency_symbol}"))

            # Recalculate totals
            self.calculate_totals()

    def delete_item(self, row):
        """Delete an item from the invoice."""
        if row < 0 or row >= len(self.items_list):
            return

        # Confirm deletion
        reply = QMessageBox.question(
            self, "تأكيد الحذف",
            "هل أنت متأكد من حذف هذا العنصر؟",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # Remove from list
            self.items_list.pop(row)

            # Remove from table
            self.items_table.removeRow(row)

            # Recalculate totals
            self.calculate_totals()

    def on_theme_changed(self, theme_key):
        """Update UI when theme changes.

        Args:
            theme_key (str): The key of the new theme
        """
        try:
            # Update theme key
            self.theme_key = theme_key

            # Get new theme colors
            theme = THEMES.get(theme_key, THEMES["default"])

            # Update color scheme
            self.colors = {
                'primary': theme["primary"],
                'primary_light': theme["primary_light"],
                'primary_dark': theme["primary_dark"],
                'accent': theme["accent"],
                'accent_light': theme.get("accent_light", "#FFCCBC"),
                'success': theme["success"],
                'success_dark': theme["success_dark"],
                'success_light': theme.get("success_light", "#E8F5E9"),
                'warning': theme["warning"],
                'warning_dark': theme["warning_dark"],
                'warning_light': theme.get("warning_light", "#FFF8E1"),
                'danger': theme["danger"],
                'danger_dark': theme["danger_dark"],
                'danger_light': theme.get("danger_light", "#FFEBEE"),
                'text': theme["main_text"],
                'text_secondary': theme.get("text_secondary", "#757575"),
                'divider': theme["border_color"],
                'background': theme["main_bg"],
                'card': theme.get("panel_bg", "#FFFFFF"),
                'disabled': theme.get("input_disabled_bg", "#EEEEEE"),
                'button_text': theme.get("button_text", "#FFFFFF"),
                'input_bg': theme.get("input_bg", "#FFFFFF"),
                'input_text': theme.get("input_text", "#212121"),
                'table_bg': theme.get("table_bg", theme.get("panel_bg", "#FFFFFF")),
                'table_alternate_bg': theme.get("table_alternate_bg", theme.get("main_bg", "#F5F5F5")),
                'table_header_bg': theme.get("table_header_bg", theme["primary_dark"]),
                'table_header_text': theme.get("table_header_text", "#FFFFFF"),
                'table_text': theme.get("table_text", theme["main_text"]),
                'table_border': theme.get("table_border", theme["border_color"]),
                'table_selection_bg': theme.get("table_selection_bg", theme["primary_light"]),
                'table_selection_text': theme.get("table_selection_text", theme["main_text"]),
                'table_hover_bg': theme.get("table_hover_bg", theme.get("tab_hover_bg", "#E3F2FD"))
            }

            # Update window background
            self.setStyleSheet(f"""
                QDialog {{
                    background-color: {self.colors['background']};
                    color: {self.colors['text']};
                }}

                QLabel {{
                    color: {self.colors['text']};
                }}

                QLineEdit, QTextEdit, QDateEdit, QComboBox, QDoubleSpinBox {{
                    background-color: {self.colors['input_bg']};
                    color: {self.colors['input_text']};
                }}

                QPushButton {{
                    background-color: {self.colors['primary']};
                    color: {self.colors['button_text']};
                }}

                QTableWidget {{
                    background-color: {self.colors['table_bg']};
                    alternate-background-color: {self.colors['table_alternate_bg']};
                    color: {self.colors['table_text']};
                    gridline-color: {self.colors['table_border']};
                }}

                QTableWidget::item {{
                    color: {self.colors['table_text']};
                }}

                QHeaderView::section {{
                    background-color: {self.colors['table_header_bg']};
                    color: {self.colors['table_header_text']};
                }}

                QScrollBar {{
                    background-color: {self.colors['table_bg']};
                }}

                QScrollBar::handle {{
                    background-color: {self.colors['primary']};
                }}

                QWidget#headerWidget, QWidget#itemsWidget, QWidget#totalsWidget, QWidget#footerWidget {{
                    background-color: {self.colors['card']};
                }}
            """)

            # Update header widget
            if hasattr(self, 'headerWidget'):
                self.headerWidget.setStyleSheet(f"""
                    #headerWidget {{
                        background-color: {self.colors['card']};
                        border-radius: 10px;
                        border: 2px solid {self.colors['primary']};
                    }}
                """)

            # Update items widget
            if hasattr(self, 'itemsWidget'):
                self.itemsWidget.setStyleSheet(f"""
                    #itemsWidget {{
                        background-color: {self.colors['card']};
                        border-radius: 10px;
                        border: 2px solid {self.colors['primary']};
                    }}
                """)

            # Update totals widget
            if hasattr(self, 'totalsWidget'):
                self.totalsWidget.setStyleSheet(f"""
                    #totalsWidget {{
                        background-color: {self.colors['card']};
                        border-radius: 10px;
                        border: 2px solid {self.colors['primary']};
                    }}
                """)

            # Update footer widget
            if hasattr(self, 'footerWidget'):
                self.footerWidget.setStyleSheet(f"""
                    #footerWidget {{
                        background-color: {self.colors['card']};
                        border-radius: 10px;
                        border: 2px solid {self.colors['primary']};
                    }}
                """)

            # Update items table
            if hasattr(self, 'items_table'):
                self.items_table.setStyleSheet(f"""
                    QTableWidget {{
                        background-color: {self.colors['table_bg']};
                        alternate-background-color: {self.colors['table_alternate_bg']};
                        color: {self.colors['table_text']};
                    }}
                    QTableWidget::item {{
                        color: {self.colors['table_text']};
                    }}
                    QHeaderView::section {{
                        background-color: {self.colors['table_header_bg']};
                        color: {self.colors['table_header_text']};
                    }}
                """)

            # Update total label (الإجمالي النهائي)
            if hasattr(self, 'total_label'):
                self.total_label.setStyleSheet(f"""
                    padding: 4px;
                    font-size: 13px;
                    text-align: right;
                    background-color: {self.colors['primary_light']};
                    border: 1px solid {self.colors['divider']};
                    color: {self.colors['input_text']};
                """)

            # Update amount due spin (المتبقي)
            if hasattr(self, 'amount_due_spin'):
                self.amount_due_spin.setStyleSheet(f"""
                    padding: 4px;
                    font-size: 13px;
                    text-align: right;
                    background-color: {self.colors['primary_light']};
                    border: 1px solid {self.colors['divider']};
                    color: {self.colors['input_text']};
                """)

            # Force update
            self.update()

        except Exception as e:
            print(f"Error updating theme: {str(e)}")

    def on_currency_changed(self, index):
        """Handle currency change with proper error handling."""
        if index < 0 or not self.currency_manager:
            return

        try:
            # Get the selected currency ID
            currency_id = self.currency_combo.itemData(index)

            # Get the currency object
            new_currency = self.currency_manager.get_currency_by_id(currency_id)

            if not new_currency:
                print("No se pudo obtener la nueva moneda")
                return

            if self.current_currency and new_currency.id == self.current_currency.id:
                print(f"La moneda no ha cambiado: {new_currency.code}")
                return

            # Store old currency for conversion
            old_currency = self.current_currency
            self.current_currency = new_currency

            print(f"Cambiando de {old_currency.code} (rate: {old_currency.exchange_rate}) a {new_currency.code} (rate: {new_currency.exchange_rate})")

            # Update currency symbols in the UI
            currency_suffix = f" {new_currency.symbol}"

            # Actualizar la tabla de elementos
            self.items_table.setRowCount(0)  # Limpiar la tabla

            # Update items in the list
            for item in self.items_list:
                # Guardar valores originales para depuración
                old_unit_price = item.unit_price
                old_discount = item.discount
                old_tax = item.tax
                old_total = item.total

                # Convertir valores
                item.unit_price = convert_currency(item.unit_price, old_currency.exchange_rate, new_currency.exchange_rate)
                item.discount = convert_currency(item.discount, old_currency.exchange_rate, new_currency.exchange_rate)
                item.tax = convert_currency(item.tax, old_currency.exchange_rate, new_currency.exchange_rate)
                item.total = convert_currency(item.total, old_currency.exchange_rate, new_currency.exchange_rate)

                # Imprimir información de depuración
                print(f"Item: {item.description}")
                print(f"  Unit Price: {old_unit_price} {old_currency.symbol} -> {item.unit_price} {new_currency.symbol}")
                print(f"  Discount: {old_discount} {old_currency.symbol} -> {item.discount} {new_currency.symbol}")
                print(f"  Tax: {old_tax} {old_currency.symbol} -> {item.tax} {new_currency.symbol}")
                print(f"  Total: {old_total} {old_currency.symbol} -> {item.total} {new_currency.symbol}")

                # Añadir el elemento a la tabla con los nuevos valores
                self.add_item_to_table(item)

            # Update discount and tax spinboxes
            self.discount_spin.setSuffix(currency_suffix)
            old_discount = self.discount_spin.value()
            new_discount = convert_currency(old_discount, old_currency.exchange_rate, new_currency.exchange_rate)
            print(f"Discount: {old_discount} {old_currency.symbol} -> {new_discount} {new_currency.symbol}")
            self.discount_spin.setValue(new_discount)

            self.tax_spin.setSuffix(currency_suffix)
            old_tax = self.tax_spin.value()
            new_tax = convert_currency(old_tax, old_currency.exchange_rate, new_currency.exchange_rate)
            print(f"Tax: {old_tax} {old_currency.symbol} -> {new_tax} {new_currency.symbol}")
            self.tax_spin.setValue(new_tax)

            # Update amount paid and amount due spinboxes
            self.amount_paid_spin.setSuffix(currency_suffix)
            old_amount_paid = self.amount_paid_spin.value()
            new_amount_paid = convert_currency(old_amount_paid, old_currency.exchange_rate, new_currency.exchange_rate)
            print(f"Amount Paid: {old_amount_paid} {old_currency.symbol} -> {new_amount_paid} {new_currency.symbol}")
            self.amount_paid_spin.setValue(new_amount_paid)

            self.amount_due_spin.setSuffix(currency_suffix)

            # Recalculate totals
            self.calculate_totals()
        except Exception as e:
            print(f"Error changing currency: {str(e)}")
            import traceback
            traceback.print_exc()
