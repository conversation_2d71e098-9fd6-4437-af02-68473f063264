#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Account Manager for فوترها (Fawterha)
Handles database operations for accounts and account categories
"""

from models.account import Account
from models.account_category import AccountCategory


class AccountManager:
    """Manager for account database operations."""

    def __init__(self, db_manager):
        """Initialize the account manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    # Account Category Methods

    def get_all_categories(self):
        """Get all account categories.

        Returns:
            list: List of AccountCategory objects
        """
        query = """
        SELECT id, name, code, type, description, parent_id, created_at, updated_at
        FROM account_categories
        ORDER BY code
        """
        rows = self.db_manager.execute_query(query)
        return [AccountCategory.from_db_row(row) for row in rows]

    def get_category_by_id(self, category_id):
        """Get an account category by ID.

        Args:
            category_id (int): Category ID

        Returns:
            AccountCategory: AccountCategory object or None if not found
        """
        query = """
        SELECT id, name, code, type, description, parent_id, created_at, updated_at
        FROM account_categories
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (category_id,))
        return AccountCategory.from_db_row(rows[0]) if rows else None

    def get_categories_by_type(self, category_type):
        """Get account categories by type.

        Args:
            category_type (str): Category type

        Returns:
            list: List of AccountCategory objects
        """
        query = """
        SELECT id, name, code, type, description, parent_id, created_at, updated_at
        FROM account_categories
        WHERE type = ?
        ORDER BY code
        """
        rows = self.db_manager.execute_query(query, (category_type,))
        return [AccountCategory.from_db_row(row) for row in rows]

    def add_category(self, category):
        """Add a new account category.

        Args:
            category (AccountCategory): AccountCategory object to add

        Returns:
            int: ID of the new category
        """
        query = """
        INSERT INTO account_categories (name, code, type, description, parent_id)
        VALUES (?, ?, ?, ?, ?)
        """
        params = (
            category.name,
            category.code,
            category.type,
            category.description,
            category.parent_id
        )
        return self.db_manager.execute_insert(query, params)

    def update_category(self, category):
        """Update an existing account category.

        Args:
            category (AccountCategory): AccountCategory object to update

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE account_categories
        SET name = ?, code = ?, type = ?, description = ?, parent_id = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            category.name,
            category.code,
            category.type,
            category.description,
            category.parent_id,
            category.id
        )
        return self.db_manager.execute_update(query, params) > 0

    def delete_category(self, category_id):
        """Delete an account category.

        Args:
            category_id (int): Category ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if there are any accounts using this category
        check_query = """
        SELECT COUNT(*) FROM accounts WHERE category_id = ?
        """
        result = self.db_manager.execute_query(check_query, (category_id,))
        if result and result[0][0] > 0:
            return False  # Category is in use

        # Check if there are any child categories
        check_children_query = """
        SELECT COUNT(*) FROM account_categories WHERE parent_id = ?
        """
        result = self.db_manager.execute_query(check_children_query, (category_id,))
        if result and result[0][0] > 0:
            return False  # Category has children

        # Delete the category
        query = """
        DELETE FROM account_categories WHERE id = ?
        """
        return self.db_manager.execute_update(query, (category_id,)) > 0

    # Account Methods

    def get_all_accounts(self):
        """Get all accounts.

        Returns:
            list: List of Account objects
        """
        query = """
        SELECT id, name, code, type, category_id, description, is_active, balance, currency_id, created_at, updated_at
        FROM accounts
        ORDER BY code
        """
        rows = self.db_manager.execute_query(query)
        return [Account.from_db_row(row) for row in rows]

    def get_account_by_id(self, account_id):
        """Get an account by ID.

        Args:
            account_id (int): Account ID

        Returns:
            Account: Account object or None if not found
        """
        query = """
        SELECT id, name, code, type, category_id, description, is_active, balance, currency_id, created_at, updated_at
        FROM accounts
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (account_id,))
        return Account.from_db_row(rows[0]) if rows else None

    def get_accounts_by_type(self, account_type):
        """Get accounts by type.

        Args:
            account_type (str): Account type

        Returns:
            list: List of Account objects
        """
        query = """
        SELECT id, name, code, type, category_id, description, is_active, balance, currency_id, created_at, updated_at
        FROM accounts
        WHERE type = ? AND is_active = 1
        ORDER BY code
        """
        rows = self.db_manager.execute_query(query, (account_type,))
        return [Account.from_db_row(row) for row in rows]

    def get_accounts_by_category(self, category_id):
        """Get accounts by category.

        Args:
            category_id (int): Category ID

        Returns:
            list: List of Account objects
        """
        query = """
        SELECT id, name, code, type, category_id, description, is_active, balance, currency_id, created_at, updated_at
        FROM accounts
        WHERE category_id = ? AND is_active = 1
        ORDER BY code
        """
        rows = self.db_manager.execute_query(query, (category_id,))
        return [Account.from_db_row(row) for row in rows]

    def add_account(self, account):
        """Add a new account.

        Args:
            account (Account): Account object to add

        Returns:
            int: ID of the new account
        """
        query = """
        INSERT INTO accounts (name, code, type, category_id, description, is_active, balance, currency_id)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            account.name,
            account.code,
            account.type,
            account.category_id,
            account.description,
            1 if account.is_active else 0,
            account.balance,
            account.currency_id
        )
        return self.db_manager.execute_insert(query, params)

    def update_account(self, account):
        """Update an existing account.

        Args:
            account (Account): Account object to update

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE accounts
        SET name = ?, code = ?, type = ?, category_id = ?, description = ?, is_active = ?, currency_id = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (
            account.name,
            account.code,
            account.type,
            account.category_id,
            account.description,
            1 if account.is_active else 0,
            account.currency_id,
            account.id
        )
        return self.db_manager.execute_update(query, params) > 0

    def update_account_balance(self, account_id, new_balance):
        """Update an account's balance.

        Args:
            account_id (int): Account ID
            new_balance (float): New balance

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE accounts
        SET balance = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        params = (new_balance, account_id)
        return self.db_manager.execute_update(query, params) > 0

    def delete_account(self, account_id):
        """Delete an account.

        Args:
            account_id (int): Account ID

        Returns:
            bool: True if successful, False otherwise
        """
        # Check if there are any transactions using this account
        check_query = """
        SELECT COUNT(*) FROM transaction_details WHERE account_id = ?
        """
        result = self.db_manager.execute_query(check_query, (account_id,))
        if result and result[0][0] > 0:
            return False  # Account is in use

        # Delete the account
        query = """
        DELETE FROM accounts WHERE id = ?
        """
        return self.db_manager.execute_update(query, (account_id,)) > 0
