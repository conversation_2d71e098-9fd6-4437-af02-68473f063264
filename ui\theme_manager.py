#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Theme Manager for فوترها (Fawterha)
Manages application themes and provides a central point for theme application
"""

from PySide6.QtWidgets import QApplication, QWidget, QComboBox, QLabel, QHBoxLayout
from PySide6.QtCore import Qt, Signal, QObject, QSize, QEvent
from PySide6.QtGui import QIcon, QPixmap, QColor
from PySide6.QtWidgets import QDialog, QMessageBox
from ui.themes import THEMES, get_theme_style


class ThemeManager(QObject):
    """Manages application themes."""

    # Signal emitted when theme changes
    theme_changed = Signal(str)

    def __init__(self, db_manager):
        """Initialize the theme manager.

        Args:
            db_manager: Database manager instance
        """
        super().__init__()
        self.db_manager = db_manager
        self.current_theme = "default"
        self.load_theme_from_settings()

    def load_theme_from_settings(self):
        """Load the theme from settings."""
        try:
            theme_setting = self.db_manager.execute_query("SELECT value FROM settings WHERE key = 'theme'")
            if theme_setting and theme_setting[0]['value']:
                self.current_theme = theme_setting[0]['value']
        except Exception as e:
            print(f"Error loading theme setting: {e}")
            self.current_theme = "default"

    def apply_theme(self, theme_key=None):
        """Apply the specified theme to the application.

        Args:
            theme_key (str, optional): Key of the theme to apply. If None, uses current theme.
        """
        if theme_key is not None:
            self.current_theme = theme_key

        # Get the theme style
        style = get_theme_style(self.current_theme)

        # Apply the style to the application
        app = QApplication.instance()
        app.setStyleSheet(style)

        # Apply theme to all top-level windows
        for widget in app.topLevelWidgets():
            widget.setStyleSheet("")  # Clear any local styles
            widget.update()  # Force update

        # Install event filter to apply theme to new dialogs and popups
        app.installEventFilter(self)

        # Emit theme changed signal
        self.theme_changed.emit(self.current_theme)

    def save_theme(self, theme_key):
        """Save the theme to settings.

        Args:
            theme_key (str): Key of the theme to save
        """
        try:
            conn = self.db_manager.connect()
            cursor = conn.cursor()
            cursor.execute(
                "UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?",
                (theme_key, 'theme')
            )
            conn.commit()
            self.db_manager.close()
            self.current_theme = theme_key
            return True
        except Exception as e:
            print(f"Error saving theme setting: {e}")
            return False

    def get_current_theme(self):
        """Get the current theme key.

        Returns:
            str: Current theme key
        """
        return self.current_theme

    def get_theme_names(self):
        """Get a list of available theme names.

        Returns:
            list: List of theme names
        """
        return [(key, theme["name"]) for key, theme in THEMES.items()]

    def eventFilter(self, watched, event):
        """Event filter to apply theme to new dialogs and popups.

        Args:
            watched: Object being watched
            event: Event that occurred

        Returns:
            bool: True if event was handled, False otherwise
        """
        # Apply theme to new dialogs and message boxes when they're shown
        if event.type() == QEvent.Type.Show:
            if isinstance(watched, (QDialog, QMessageBox)):
                # Clear any local styles
                watched.setStyleSheet("")
                watched.update()

        # Let the event propagate
        return False

    def get_theme_preview_style(self, theme_key):
        """Get a preview style for the specified theme.

        Args:
            theme_key (str): Key of the theme to preview

        Returns:
            str: CSS style for the theme preview
        """
        if theme_key not in THEMES:
            theme_key = "default"

        theme = THEMES[theme_key]

        # Create a simplified preview style
        main_bg = theme["main_bg"]
        if "main_bg_gradient" in theme and theme["main_bg_gradient"]:
            main_bg = f"background: {theme['main_bg_gradient']}"
        else:
            main_bg = f"background-color: {theme['main_bg']}"

        # No need for button_bg variable since we're not using it

        return f"""
            {main_bg};
            color: {theme["main_text"]};
            border: 1px solid {theme["border_color"]};
            border-radius: {theme["border_radius"]};
            padding: 5px;
        """


class ThemeSelector(QWidget):
    """Widget for selecting themes."""

    # Signal emitted when theme changes
    theme_changed = Signal(str)

    def __init__(self, theme_manager):
        """Initialize the theme selector.

        Args:
            theme_manager: Theme manager instance
        """
        super().__init__()
        self.theme_manager = theme_manager

        # Create layout
        layout = QHBoxLayout(self)
        layout.setContentsMargins(5, 0, 5, 0)
        layout.setSpacing(10)

        # Set fixed height for the selector
        self.setFixedHeight(40)

        # Create label
        self.label = QLabel("النمط:")
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setObjectName("themeLabel")
        layout.addWidget(self.label)

        # Create combo box with custom styling
        self.combo = QComboBox()
        self.combo.setMinimumWidth(150)
        self.combo.setMaximumWidth(250)
        self.combo.setIconSize(QSize(24, 24))
        self.combo.setObjectName("themeComboBox")
        # Set text alignment to center
        self.combo.setEditable(True)
        self.combo.lineEdit().setReadOnly(True)
        self.combo.lineEdit().setAlignment(Qt.AlignCenter)

        # Add themes to combo box with preview colors
        for key, name in self.theme_manager.get_theme_names():
            # Create a colored icon for the theme
            theme = THEMES[key]

            # Create a pixmap with the theme's primary color
            pixmap = QPixmap(24, 24)
            if "main_bg_gradient" in theme and theme["main_bg_gradient"]:
                # For gradients, use the primary color
                pixmap.fill(QColor(theme["primary"]))
            else:
                pixmap.fill(QColor(theme["main_bg"]))

            # Add the item with icon and theme name
            self.combo.addItem(QIcon(pixmap), name, key)

        # Set current theme
        current_theme = self.theme_manager.get_current_theme()
        for i in range(self.combo.count()):
            if self.combo.itemData(i) == current_theme:
                self.combo.setCurrentIndex(i)
                break

        # Connect signals
        self.combo.currentIndexChanged.connect(self.on_theme_changed)

        layout.addWidget(self.combo)

        # The theme selector will be styled by the global theme system
        # We don't need to apply custom styling here anymore

    def on_theme_changed(self, index):
        """Handle theme change event.

        Args:
            index (int): Index of the selected theme
        """
        theme_key = self.combo.itemData(index)

        # Save and apply the theme
        if self.theme_manager.save_theme(theme_key):
            self.theme_manager.apply_theme(theme_key)

            # Emit theme changed signal
            self.theme_changed.emit(theme_key)
