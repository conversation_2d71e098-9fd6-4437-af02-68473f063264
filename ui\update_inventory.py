#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Update Module for فوترها (Fawterha)
Handles inventory updates when invoices are created or modified
"""

from models.inventory_transaction import InventoryTransaction
from utils.inventory_helper import get_inventory_manager

def update_inventory_for_invoice(db_manager, invoice_id, items_list, parent=None):
    """Update inventory for products in an invoice.

    Args:
        db_manager: Database manager instance
        invoice_id: ID of the invoice
        items_list: List of invoice items
        parent: Parent widget for error messages

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create inventory manager with proper error handling
        inventory_manager = get_inventory_manager(db_manager, show_error=True, parent=parent)

        # If inventory manager is None, return failure
        if not inventory_manager:
            print("Failed to initialize inventory manager")
            return False, []

        # Track success
        success = True
        updated_products = []

        # Check if we need to update inventory
        for item in items_list:
            if hasattr(item, 'product_id') and item.product_id:
                # Get product details
                query = "SELECT type, track_inventory FROM products WHERE id = ?"
                rows = db_manager.execute_query(query, (item.product_id,))

                if rows and rows[0]['type'] == 'product' and rows[0]['track_inventory']:
                    # Create inventory transaction
                    transaction = InventoryTransaction(
                        product_id=item.product_id,
                        transaction_type=InventoryTransaction.TYPE_SALE,
                        quantity=item.quantity,
                        reference_type="invoice",
                        reference_id=invoice_id,
                        notes=f"بيع من خلال الفاتورة رقم {invoice_id}"
                    )

                    try:
                        # Add transaction (this will also update the product stock)
                        transaction_id = inventory_manager.add_transaction(transaction)

                        # Debug print after inventory update
                        new_stock = inventory_manager.get_product_stock(item.product_id)
                        print(f"Updated inventory for product ID: {item.product_id}, Quantity: {item.quantity}, New stock: {new_stock}, Transaction ID: {transaction_id}")

                        # Add to updated products list
                        updated_products.append({
                            'product_id': item.product_id,
                            'quantity': item.quantity,
                            'new_stock': new_stock,
                            'transaction_id': transaction_id
                        })
                    except Exception as e:
                        print(f"Error updating inventory for product {item.product_id}: {e}")
                        success = False

        return success, updated_products
    except Exception as e:
        print(f"Error in update_inventory_for_invoice: {e}")
        return False, []
