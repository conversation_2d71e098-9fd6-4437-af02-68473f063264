#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Warehouses View for فوترها (Fawterha)
Provides a UI for managing warehouses
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDialog, QFormLayout, QLineEdit, QTextEdit, QMessageBox,
    QCheckBox
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QFont, QAction

from database.enhanced_inventory_manager import EnhancedInventoryManager
from models.warehouse import Warehouse
from utils.translation_manager import tr


class WarehousesView(QWidget):
    """Widget for managing warehouses."""

    def __init__(self, db_manager):
        """Initialize the warehouses view.

        Args:
            db_manager: Database manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.inventory_manager = EnhancedInventoryManager(db_manager)

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Title
        title_label = QLabel(tr("inventory.warehouses", "المستودعات"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Description
        description_label = QLabel(tr("inventory.warehouses_description", "إدارة المستودعات ومواقعها"))
        description_label.setStyleSheet("font-size: 10pt;")
        main_layout.addWidget(description_label)

        # Toolbar
        toolbar_layout = QHBoxLayout()
        main_layout.addLayout(toolbar_layout)

        # Add warehouse button
        self.add_warehouse_button = QPushButton(tr("inventory.add_warehouse", "إضافة مستودع"))
        self.add_warehouse_button.setIcon(QIcon("resources/icons/add.png"))
        self.add_warehouse_button.clicked.connect(self.add_warehouse)
        toolbar_layout.addWidget(self.add_warehouse_button)

        # Refresh button
        self.refresh_button = QPushButton(tr("common.refresh", "تحديث"))
        self.refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        self.refresh_button.clicked.connect(self.load_data)
        toolbar_layout.addWidget(self.refresh_button)

        toolbar_layout.addStretch()

        # Show inactive checkbox
        self.show_inactive_check = QCheckBox(tr("inventory.show_inactive", "عرض المستودعات غير النشطة"))
        self.show_inactive_check.stateChanged.connect(self.load_data)
        toolbar_layout.addWidget(self.show_inactive_check)

        # Warehouses table
        self.warehouses_table = QTableWidget()
        self.warehouses_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.warehouses_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.warehouses_table.setAlternatingRowColors(True)
        self.warehouses_table.verticalHeader().setVisible(False)
        self.warehouses_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.warehouses_table.setColumnCount(5)
        self.warehouses_table.setHorizontalHeaderLabels([
            tr("inventory.name", "الاسم"),
            tr("inventory.location", "الموقع"),
            tr("inventory.description", "الوصف"),
            tr("inventory.status", "الحالة"),
            tr("common.actions", "الإجراءات")
        ])
        main_layout.addWidget(self.warehouses_table)

    def load_data(self):
        """Load warehouses data."""
        # Clear the table
        self.warehouses_table.setRowCount(0)

        # Get warehouses
        show_inactive = self.show_inactive_check.isChecked()
        warehouses = self.inventory_manager.get_all_warehouses(active_only=not show_inactive)

        # Populate the table
        self.warehouses_table.setRowCount(len(warehouses))
        for row, warehouse in enumerate(warehouses):
            # Name
            name_item = QTableWidgetItem(warehouse.name)
            self.warehouses_table.setItem(row, 0, name_item)

            # Location
            location_item = QTableWidgetItem(warehouse.location)
            self.warehouses_table.setItem(row, 1, location_item)

            # Description
            description_item = QTableWidgetItem(warehouse.description)
            self.warehouses_table.setItem(row, 2, description_item)

            # Status
            status_text = tr("common.active", "نشط") if warehouse.is_active else tr("common.inactive", "غير نشط")
            status_item = QTableWidgetItem(status_text)
            if not warehouse.is_active:
                status_item.setForeground(Qt.red)
            self.warehouses_table.setItem(row, 3, status_item)

            # Actions
            actions_widget = QWidget()
            actions_layout = QHBoxLayout(actions_widget)
            actions_layout.setContentsMargins(0, 0, 0, 0)
            actions_layout.setSpacing(5)

            # Edit button
            edit_button = QPushButton(tr("common.edit", "تعديل"))
            edit_button.setProperty("warehouse_id", warehouse.id)
            edit_button.clicked.connect(self.edit_warehouse)
            actions_layout.addWidget(edit_button)

            # View inventory button
            view_button = QPushButton(tr("inventory.view_inventory", "عرض المخزون"))
            view_button.setProperty("warehouse_id", warehouse.id)
            view_button.clicked.connect(self.view_warehouse_inventory)
            actions_layout.addWidget(view_button)

            # Delete button
            delete_button = QPushButton(tr("common.delete", "حذف"))
            delete_button.setProperty("warehouse_id", warehouse.id)
            delete_button.clicked.connect(self.delete_warehouse)
            actions_layout.addWidget(delete_button)

            self.warehouses_table.setCellWidget(row, 4, actions_widget)

    def add_warehouse(self):
        """Add a new warehouse."""
        dialog = WarehouseDialog(self, self.db_manager)
        if dialog.exec():
            self.load_data()

    def edit_warehouse(self):
        """Edit an existing warehouse."""
        button = self.sender()
        if button:
            warehouse_id = button.property("warehouse_id")
            dialog = WarehouseDialog(self, self.db_manager, warehouse_id)
            if dialog.exec():
                self.load_data()

    def view_warehouse_inventory(self):
        """View inventory for a warehouse."""
        button = self.sender()
        if button:
            warehouse_id = button.property("warehouse_id")
            # TODO: Implement warehouse inventory view
            QMessageBox.information(
                self,
                tr("inventory.warehouse_inventory", "مخزون المستودع"),
                tr("inventory.feature_coming_soon", "هذه الميزة قادمة قريباً")
            )

    def delete_warehouse(self):
        """Delete a warehouse."""
        button = self.sender()
        if button:
            warehouse_id = button.property("warehouse_id")
            warehouse = self.inventory_manager.get_warehouse_by_id(warehouse_id)
            
            if not warehouse:
                return
                
            # Confirm deletion
            confirm = QMessageBox.question(
                self,
                tr("inventory.confirm_delete", "تأكيد الحذف"),
                tr("inventory.confirm_delete_warehouse", "هل أنت متأكد من حذف المستودع '{0}'؟").format(warehouse.name),
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if confirm == QMessageBox.Yes:
                # Try to delete the warehouse
                if self.inventory_manager.delete_warehouse(warehouse_id):
                    QMessageBox.information(
                        self,
                        tr("messages.success", "نجاح"),
                        tr("inventory.warehouse_deleted", "تم حذف المستودع بنجاح")
                    )
                    self.load_data()
                else:
                    QMessageBox.warning(
                        self,
                        tr("messages.error", "خطأ"),
                        tr("inventory.warehouse_in_use", "لا يمكن حذف المستودع لأنه يحتوي على منتجات")
                    )


class WarehouseDialog(QDialog):
    """Dialog for adding or editing a warehouse."""

    def __init__(self, parent, db_manager, warehouse_id=None):
        """Initialize the warehouse dialog.

        Args:
            parent: Parent widget
            db_manager: Database manager instance
            warehouse_id (int, optional): Warehouse ID to edit. Defaults to None.
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.inventory_manager = EnhancedInventoryManager(db_manager)
        self.warehouse_id = warehouse_id
        self.warehouse = None

        if warehouse_id:
            self.warehouse = self.inventory_manager.get_warehouse_by_id(warehouse_id)
            if not self.warehouse:
                QMessageBox.critical(
                    self,
                    tr("messages.error", "خطأ"),
                    tr("inventory.warehouse_not_found", "لم يتم العثور على المستودع")
                )
                self.reject()
                return

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        self.setWindowTitle(
            tr("inventory.edit_warehouse", "تعديل المستودع") if self.warehouse_id else
            tr("inventory.add_warehouse", "إضافة مستودع")
        )
        self.resize(400, 300)

        # Main layout
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)

        # Form layout
        form_layout = QFormLayout()
        layout.addLayout(form_layout)

        # Name
        self.name_edit = QLineEdit()
        form_layout.addRow(tr("inventory.name", "الاسم:"), self.name_edit)

        # Location
        self.location_edit = QLineEdit()
        form_layout.addRow(tr("inventory.location", "الموقع:"), self.location_edit)

        # Description
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(100)
        form_layout.addRow(tr("inventory.description", "الوصف:"), self.description_edit)

        # Active status
        self.active_check = QCheckBox(tr("common.active", "نشط"))
        self.active_check.setChecked(True)
        form_layout.addRow("", self.active_check)

        # Buttons
        buttons_layout = QHBoxLayout()
        layout.addLayout(buttons_layout)

        buttons_layout.addStretch()

        # Cancel button
        self.cancel_button = QPushButton(tr("common.cancel", "إلغاء"))
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        # Save button
        self.save_button = QPushButton(tr("common.save", "حفظ"))
        self.save_button.clicked.connect(self.save)
        buttons_layout.addWidget(self.save_button)

    def load_data(self):
        """Load warehouse data into the form."""
        if self.warehouse:
            self.name_edit.setText(self.warehouse.name)
            self.location_edit.setText(self.warehouse.location)
            self.description_edit.setText(self.warehouse.description)
            self.active_check.setChecked(self.warehouse.is_active)

    def save(self):
        """Save the warehouse."""
        # Validate form
        if not self.name_edit.text():
            QMessageBox.warning(
                self,
                tr("messages.validation_error", "خطأ في التحقق"),
                tr("inventory.name_required", "يجب إدخال اسم المستودع")
            )
            self.name_edit.setFocus()
            return

        # Create warehouse object
        warehouse = Warehouse(
            id=self.warehouse.id if self.warehouse else None,
            name=self.name_edit.text(),
            location=self.location_edit.text(),
            description=self.description_edit.toPlainText(),
            is_active=self.active_check.isChecked()
        )

        try:
            if self.warehouse:
                # Update existing warehouse
                if self.inventory_manager.update_warehouse(warehouse):
                    QMessageBox.information(
                        self,
                        tr("messages.success", "نجاح"),
                        tr("inventory.warehouse_updated", "تم تحديث المستودع بنجاح")
                    )
                    self.accept()
                else:
                    QMessageBox.critical(
                        self,
                        tr("messages.error", "خطأ"),
                        tr("inventory.update_error", "حدث خطأ أثناء تحديث المستودع")
                    )
            else:
                # Add new warehouse
                warehouse_id = self.inventory_manager.add_warehouse(warehouse)
                if warehouse_id:
                    QMessageBox.information(
                        self,
                        tr("messages.success", "نجاح"),
                        tr("inventory.warehouse_added", "تم إضافة المستودع بنجاح")
                    )
                    self.accept()
                else:
                    QMessageBox.critical(
                        self,
                        tr("messages.error", "خطأ"),
                        tr("inventory.add_error", "حدث خطأ أثناء إضافة المستودع")
                    )
        except Exception as e:
            QMessageBox.critical(
                self,
                tr("messages.error", "خطأ"),
                f"{tr('inventory.save_error', 'حدث خطأ أثناء حفظ المستودع')}: {str(e)}"
            )
