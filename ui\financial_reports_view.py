#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Financial Reports View for فوترها (Fawterha)
Displays financial reports such as income statement and balance sheet
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDateEdit, QLineEdit, QMessageBox, QTabWidget,
    QSplitter, QFrame, QGroupBox, QRadioButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QMenu, QToolBar,
    QSizePolicy, QFileDialog
)
from PySide6.QtCore import Qt, QDate, Signal, QSize
from PySide6.QtGui import QIcon, QColor, QFont, QAction

from datetime import datetime, timedelta
import os

from database.account_manager import AccountManager
from database.transaction_manager import TransactionManager
from database.accounting_period_manager import AccountingPeriodManager
from models.account import Account
from utils.currency_helper import format_currency
from utils.translation_manager import tr


class FinancialReportsView(QWidget):
    """Financial reports view widget."""

    def __init__(self, db_manager, currency_manager=None):
        """Initialize the financial reports view.

        Args:
            db_manager: Database manager instance
            currency_manager: Currency manager instance
        """
        super().__init__()

        self.db_manager = db_manager
        self.currency_manager = currency_manager

        # Initialize managers
        self.account_manager = AccountManager(db_manager)
        self.transaction_manager = TransactionManager(db_manager, self.account_manager)
        self.period_manager = AccountingPeriodManager(db_manager)

        # Set up UI
        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Title
        title_label = QLabel(tr("accounting.financial_reports", "التقارير المالية"))
        title_label.setStyleSheet("font-size: 18pt; font-weight: bold;")
        main_layout.addWidget(title_label)

        # Tab widget
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # Income statement tab
        self.income_statement_widget = QWidget()
        self.tab_widget.addTab(self.income_statement_widget, tr("accounting.income_statement", "قائمة الدخل"))
        self.setup_income_statement_tab()

        # Balance sheet tab
        self.balance_sheet_widget = QWidget()
        self.tab_widget.addTab(self.balance_sheet_widget, tr("accounting.balance_sheet", "الميزانية العمومية"))
        self.setup_balance_sheet_tab()

        # Connect tab changed signal
        self.tab_widget.currentChanged.connect(self.on_tab_changed)

    def setup_income_statement_tab(self):
        """Set up the income statement tab."""
        # Layout
        layout = QVBoxLayout(self.income_statement_widget)
        layout.setContentsMargins(0, 10, 0, 0)
        layout.setSpacing(10)

        # Toolbar
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        layout.addWidget(toolbar)

        # Export button
        self.export_income_statement_action = QAction(QIcon("resources/icons/export.png"), tr("accounting.export", "تصدير"), self)
        self.export_income_statement_action.triggered.connect(self.export_income_statement)
        toolbar.addAction(self.export_income_statement_action)

        # Print button
        self.print_income_statement_action = QAction(QIcon("resources/icons/print.png"), tr("accounting.print", "طباعة"), self)
        self.print_income_statement_action.triggered.connect(self.print_income_statement)
        toolbar.addAction(self.print_income_statement_action)

        # Filter section
        filter_layout = QHBoxLayout()
        layout.addLayout(filter_layout)

        # Date range
        filter_layout.addWidget(QLabel(tr("accounting.period", "الفترة:")))

        self.income_period_combo = QComboBox()
        self.income_period_combo.setMinimumWidth(200)
        filter_layout.addWidget(self.income_period_combo)

        # Custom date range
        filter_layout.addWidget(QLabel(tr("accounting.from", "من:")))

        self.income_start_date_edit = QDateEdit()
        self.income_start_date_edit.setCalendarPopup(True)
        self.income_start_date_edit.setDate(QDate.currentDate().addMonths(-1))
        filter_layout.addWidget(self.income_start_date_edit)

        filter_layout.addWidget(QLabel(tr("accounting.to", "إلى:")))

        self.income_end_date_edit = QDateEdit()
        self.income_end_date_edit.setCalendarPopup(True)
        self.income_end_date_edit.setDate(QDate.currentDate())
        filter_layout.addWidget(self.income_end_date_edit)

        # Apply filter button
        self.income_filter_button = QPushButton(tr("accounting.generate_report", "إنشاء التقرير"))
        self.income_filter_button.clicked.connect(self.generate_income_statement)
        filter_layout.addWidget(self.income_filter_button)

        # Income statement table
        self.income_table = QTableWidget()
        self.income_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.income_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.income_table.setAlternatingRowColors(True)
        self.income_table.verticalHeader().setVisible(False)
        self.income_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.income_table.setColumnCount(2)
        self.income_table.setHorizontalHeaderLabels([
            tr("accounting.account", "الحساب"),
            tr("accounting.amount", "المبلغ")
        ])
        layout.addWidget(self.income_table)

    def setup_balance_sheet_tab(self):
        """Set up the balance sheet tab."""
        # Layout
        layout = QVBoxLayout(self.balance_sheet_widget)
        layout.setContentsMargins(0, 10, 0, 0)
        layout.setSpacing(10)

        # Toolbar
        toolbar = QToolBar()
        toolbar.setIconSize(QSize(24, 24))
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        layout.addWidget(toolbar)

        # Export button
        self.export_balance_sheet_action = QAction(QIcon("resources/icons/export.png"), tr("accounting.export", "تصدير"), self)
        self.export_balance_sheet_action.triggered.connect(self.export_balance_sheet)
        toolbar.addAction(self.export_balance_sheet_action)

        # Print button
        self.print_balance_sheet_action = QAction(QIcon("resources/icons/print.png"), tr("accounting.print", "طباعة"), self)
        self.print_balance_sheet_action.triggered.connect(self.print_balance_sheet)
        toolbar.addAction(self.print_balance_sheet_action)

        # Filter section
        filter_layout = QHBoxLayout()
        layout.addLayout(filter_layout)

        # Date
        filter_layout.addWidget(QLabel(tr("accounting.as_of_date", "كما في تاريخ:")))

        self.balance_date_edit = QDateEdit()
        self.balance_date_edit.setCalendarPopup(True)
        self.balance_date_edit.setDate(QDate.currentDate())
        filter_layout.addWidget(self.balance_date_edit)

        # Apply filter button
        self.balance_filter_button = QPushButton(tr("accounting.generate_report", "إنشاء التقرير"))
        self.balance_filter_button.clicked.connect(self.generate_balance_sheet)
        filter_layout.addWidget(self.balance_filter_button)

        # Balance sheet table
        self.balance_table = QTableWidget()
        self.balance_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.balance_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.balance_table.setAlternatingRowColors(True)
        self.balance_table.verticalHeader().setVisible(False)
        self.balance_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.balance_table.setColumnCount(2)
        self.balance_table.setHorizontalHeaderLabels([
            tr("accounting.account", "الحساب"),
            tr("accounting.amount", "المبلغ")
        ])
        layout.addWidget(self.balance_table)

    def load_data(self):
        """Load data into the view."""
        self.load_periods()
        self.generate_income_statement()
        self.generate_balance_sheet()

    def load_periods(self):
        """Load accounting periods into the period combo box."""
        self.income_period_combo.clear()
        self.income_period_combo.addItem(tr("accounting.custom_period", "فترة مخصصة"), "custom")
        self.income_period_combo.addItem(tr("accounting.current_month", "الشهر الحالي"), "current_month")
        self.income_period_combo.addItem(tr("accounting.previous_month", "الشهر السابق"), "previous_month")
        self.income_period_combo.addItem(tr("accounting.current_quarter", "الربع الحالي"), "current_quarter")
        self.income_period_combo.addItem(tr("accounting.current_year", "السنة الحالية"), "current_year")
        self.income_period_combo.addItem(tr("accounting.previous_year", "السنة السابقة"), "previous_year")

        # Add accounting periods from database
        periods = self.period_manager.get_all_periods()
        for period in periods:
            self.income_period_combo.addItem(period.name, period.id)

        # Connect period changed signal
        self.income_period_combo.currentIndexChanged.connect(self.on_period_changed)

    def on_period_changed(self, index):
        """Handle period combo box change.

        Args:
            index (int): Index of the selected period
        """
        period_id = self.income_period_combo.currentData()

        if period_id == "custom":
            # Enable custom date range
            self.income_start_date_edit.setEnabled(True)
            self.income_end_date_edit.setEnabled(True)
        else:
            # Disable custom date range and set dates based on selected period
            self.income_start_date_edit.setEnabled(False)
            self.income_end_date_edit.setEnabled(False)

            today = QDate.currentDate()

            if period_id == "current_month":
                start_date = QDate(today.year(), today.month(), 1)
                end_date = today
            elif period_id == "previous_month":
                if today.month() == 1:
                    start_date = QDate(today.year() - 1, 12, 1)
                    end_date = QDate(today.year() - 1, 12, 31)
                else:
                    start_date = QDate(today.year(), today.month() - 1, 1)
                    end_date = QDate(today.year(), today.month(), 1).addDays(-1)
            elif period_id == "current_quarter":
                quarter = (today.month() - 1) // 3 + 1
                start_date = QDate(today.year(), (quarter - 1) * 3 + 1, 1)
                if quarter == 4:
                    end_date = QDate(today.year(), 12, 31)
                else:
                    end_date = QDate(today.year(), quarter * 3, 1).addDays(-1)
            elif period_id == "current_year":
                start_date = QDate(today.year(), 1, 1)
                end_date = today
            elif period_id == "previous_year":
                start_date = QDate(today.year() - 1, 1, 1)
                end_date = QDate(today.year() - 1, 12, 31)
            else:
                # Get period from database
                period = self.period_manager.get_period_by_id(period_id)
                if period:
                    start_date = QDate.fromString(str(period.start_date), "yyyy-MM-dd")
                    end_date = QDate.fromString(str(period.end_date), "yyyy-MM-dd")
                else:
                    start_date = today.addMonths(-1)
                    end_date = today

            self.income_start_date_edit.setDate(start_date)
            self.income_end_date_edit.setDate(end_date)

    def generate_income_statement(self):
        """Generate the income statement report."""
        # Clear the table
        self.income_table.setRowCount(0)

        # Get date range
        start_date = self.income_start_date_edit.date().toPython()
        end_date = self.income_end_date_edit.date().toPython()

        # TODO: Generate income statement from database
        # For now, we'll add some dummy data
        data = [
            {"account": "الإيرادات", "amount": 0, "is_header": True, "is_total": False},
            {"account": "إيرادات المبيعات", "amount": 10000, "is_header": False, "is_total": False},
            {"account": "إيرادات الخدمات", "amount": 5000, "is_header": False, "is_total": False},
            {"account": "إيرادات أخرى", "amount": 1000, "is_header": False, "is_total": False},
            {"account": "إجمالي الإيرادات", "amount": 16000, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "المصروفات", "amount": 0, "is_header": True, "is_total": False},
            {"account": "تكلفة البضاعة المباعة", "amount": 6000, "is_header": False, "is_total": False},
            {"account": "مصروفات الإيجار", "amount": 2000, "is_header": False, "is_total": False},
            {"account": "مصروفات الكهرباء والماء", "amount": 500, "is_header": False, "is_total": False},
            {"account": "الرواتب والأجور", "amount": 3000, "is_header": False, "is_total": False},
            {"account": "مصروفات أخرى", "amount": 1000, "is_header": False, "is_total": False},
            {"account": "إجمالي المصروفات", "amount": 12500, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "صافي الدخل", "amount": 3500, "is_header": False, "is_total": True}
        ]

        # Populate the table
        self.income_table.setRowCount(len(data))

        for row, item in enumerate(data):
            # Account
            account_item = QTableWidgetItem(item["account"])
            if item["is_header"]:
                account_item.setFont(QFont("", -1, QFont.Bold))
                account_item.setBackground(QColor("#f0f0f0"))
            elif item["is_total"]:
                account_item.setFont(QFont("", -1, QFont.Bold))
            self.income_table.setItem(row, 0, account_item)

            # Amount
            if item["amount"] != 0 or item["is_total"]:
                amount_text = format_currency(item["amount"], "EGP")
            else:
                amount_text = ""
            amount_item = QTableWidgetItem(amount_text)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            if item["is_header"]:
                amount_item.setBackground(QColor("#f0f0f0"))
            elif item["is_total"]:
                amount_item.setFont(QFont("", -1, QFont.Bold))
            self.income_table.setItem(row, 1, amount_item)

        # Resize columns to content
        self.income_table.resizeColumnsToContents()

    def generate_balance_sheet(self):
        """Generate the balance sheet report."""
        # Clear the table
        self.balance_table.setRowCount(0)

        # Get date
        as_of_date = self.balance_date_edit.date().toPython()

        # TODO: Generate balance sheet from database
        # For now, we'll add some dummy data
        data = [
            {"account": "الأصول", "amount": 0, "is_header": True, "is_total": False},
            {"account": "الأصول المتداولة", "amount": 0, "is_header": True, "is_total": False},
            {"account": "النقدية في الصندوق", "amount": 5000, "is_header": False, "is_total": False},
            {"account": "الحساب البنكي", "amount": 15000, "is_header": False, "is_total": False},
            {"account": "ذمم العملاء", "amount": 8000, "is_header": False, "is_total": False},
            {"account": "مخزون البضائع", "amount": 12000, "is_header": False, "is_total": False},
            {"account": "إجمالي الأصول المتداولة", "amount": 40000, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "الأصول الثابتة", "amount": 0, "is_header": True, "is_total": False},
            {"account": "أثاث ومعدات", "amount": 10000, "is_header": False, "is_total": False},
            {"account": "إجمالي الأصول الثابتة", "amount": 10000, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "إجمالي الأصول", "amount": 50000, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "الخصوم وحقوق الملكية", "amount": 0, "is_header": True, "is_total": False},
            {"account": "الخصوم", "amount": 0, "is_header": True, "is_total": False},
            {"account": "ذمم الموردين", "amount": 6000, "is_header": False, "is_total": False},
            {"account": "قروض قصيرة الأجل", "amount": 10000, "is_header": False, "is_total": False},
            {"account": "إجمالي الخصوم", "amount": 16000, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "حقوق الملكية", "amount": 0, "is_header": True, "is_total": False},
            {"account": "رأس المال", "amount": 30000, "is_header": False, "is_total": False},
            {"account": "الأرباح المحتجزة", "amount": 4000, "is_header": False, "is_total": False},
            {"account": "إجمالي حقوق الملكية", "amount": 34000, "is_header": False, "is_total": True},
            {"account": "", "amount": 0, "is_header": False, "is_total": False},
            {"account": "إجمالي الخصوم وحقوق الملكية", "amount": 50000, "is_header": False, "is_total": True}
        ]

        # Populate the table
        self.balance_table.setRowCount(len(data))

        for row, item in enumerate(data):
            # Account
            account_item = QTableWidgetItem(item["account"])
            if item["is_header"]:
                account_item.setFont(QFont("", -1, QFont.Bold))
                account_item.setBackground(QColor("#f0f0f0"))
            elif item["is_total"]:
                account_item.setFont(QFont("", -1, QFont.Bold))
            self.balance_table.setItem(row, 0, account_item)

            # Amount
            if item["amount"] != 0 or item["is_total"]:
                amount_text = format_currency(item["amount"], "EGP")
            else:
                amount_text = ""
            amount_item = QTableWidgetItem(amount_text)
            amount_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            if item["is_header"]:
                amount_item.setBackground(QColor("#f0f0f0"))
            elif item["is_total"]:
                amount_item.setFont(QFont("", -1, QFont.Bold))
            self.balance_table.setItem(row, 1, amount_item)

        # Resize columns to content
        self.balance_table.resizeColumnsToContents()

    def on_tab_changed(self, index):
        """Handle tab changed event.

        Args:
            index (int): Index of the selected tab
        """
        if index == 0:
            self.generate_income_statement()
        else:
            self.generate_balance_sheet()

    def export_income_statement(self):
        """Export the income statement to Excel."""
        QMessageBox.information(
            self,
            tr("accounting.not_implemented", "غير مكتمل"),
            tr("accounting.feature_not_implemented", "هذه الميزة غير مكتملة بعد")
        )

    def print_income_statement(self):
        """Print the income statement."""
        QMessageBox.information(
            self,
            tr("accounting.not_implemented", "غير مكتمل"),
            tr("accounting.feature_not_implemented", "هذه الميزة غير مكتملة بعد")
        )

    def export_balance_sheet(self):
        """Export the balance sheet to Excel."""
        QMessageBox.information(
            self,
            tr("accounting.not_implemented", "غير مكتمل"),
            tr("accounting.feature_not_implemented", "هذه الميزة غير مكتملة بعد")
        )

    def print_balance_sheet(self):
        """Print the balance sheet."""
        QMessageBox.information(
            self,
            tr("accounting.not_implemented", "غير مكتمل"),
            tr("accounting.feature_not_implemented", "هذه الميزة غير مكتملة بعد")
        )
