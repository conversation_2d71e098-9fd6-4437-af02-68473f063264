#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
POS Inventory Report for فوترها (Fawterha)
Provides a dialog for viewing inventory reports in the POS system
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
    QDateEdit, QLineEdit, QMessageBox, QTabWidget,
    QSplitter, QFrame, QGroupBox, QRadioButton,
    QCheckBox, QSpinBox, QDoubleSpinBox, QFileDialog,
    QWidget
)
from PySide6.QtCore import Qt, QDate, Signal
from PySide6.QtGui import QIcon, QColor

from datetime import datetime, timedelta
import os

from models.pos_inventory import POSInventory
from models.pos_inventory_transaction import POSInventoryTransaction
from utils.translation_manager import tr
from utils.currency_helper import format_currency
from utils.excel_exporter import export_to_excel


class POSInventoryReportDialog(QDialog):
    """Dialog for viewing POS inventory reports."""

    def __init__(self, db_manager, pos_manager, parent=None):
        """Initialize the POS inventory report dialog.

        Args:
            db_manager: Database manager instance
            pos_manager: POS manager instance
            parent: Parent widget
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.pos_manager = pos_manager

        # Set window properties
        self.setWindowTitle(tr("pos.inventory_report", "تقرير المخزون"))
        self.setMinimumSize(900, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        try:
            self.setup_ui()

            # Load data
            self.load_data()
        except Exception as e:
            print(f"Error initializing inventory report dialog: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.inventory_report_error", f"حدث خطأ أثناء تهيئة تقرير المخزون: {str(e)}")
            )

    def setup_ui(self):
        """Set up the user interface."""
        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # Create tabs
        self.tabs = QTabWidget()
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #0d47a1;
                border-radius: 5px;
                padding: 5px;
            }
            QTabBar::tab {
                background-color: #e3f2fd;
                color: #0d47a1;
                border: 1px solid #0d47a1;
                border-bottom: none;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                padding: 8px 15px;
                margin-right: 2px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #0d47a1;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #bbdefb;
            }
        """)

        # Create inventory tab
        self.inventory_tab = QWidget()
        self.setup_inventory_tab()
        self.tabs.addTab(self.inventory_tab, tr("inventory.current_inventory", "المخزون الحالي"))

        # Create transactions tab
        self.transactions_tab = QWidget()
        self.setup_transactions_tab()
        self.tabs.addTab(self.transactions_tab, tr("inventory.inventory_transactions", "حركات المخزون"))

        # Create low stock tab
        self.low_stock_tab = QWidget()
        self.setup_low_stock_tab()
        self.tabs.addTab(self.low_stock_tab, tr("inventory.low_stock", "المخزون المنخفض"))

        # Add tabs to main layout
        main_layout.addWidget(self.tabs)

        # Add buttons
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)

        # Export button
        self.export_button = QPushButton(tr("inventory.export_to_excel", "تصدير إلى Excel"))
        self.export_button.setIcon(QIcon("resources/icons/excel.png"))
        self.export_button.clicked.connect(self.export_to_excel)
        button_layout.addWidget(self.export_button)

        # Refresh button
        self.refresh_button = QPushButton(tr("inventory.refresh", "تحديث"))
        self.refresh_button.setIcon(QIcon("resources/icons/refresh.png"))
        self.refresh_button.clicked.connect(self.load_data)
        button_layout.addWidget(self.refresh_button)

        # Close button
        self.close_button = QPushButton(tr("inventory.close", "إغلاق"))
        self.close_button.setIcon(QIcon("resources/icons/close.png"))
        self.close_button.clicked.connect(self.accept)
        button_layout.addWidget(self.close_button)

        main_layout.addLayout(button_layout)

    def setup_inventory_tab(self):
        """Set up the inventory tab."""
        # Create layout
        layout = QVBoxLayout(self.inventory_tab)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # Create filter layout
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(10)

        # Search field
        filter_layout.addWidget(QLabel(tr("inventory.search", "بحث:")))
        self.inventory_search = QLineEdit()
        self.inventory_search.setPlaceholderText(tr("inventory.search_placeholder", "بحث عن منتج..."))
        self.inventory_search.textChanged.connect(self.filter_inventory)
        filter_layout.addWidget(self.inventory_search)

        # Show zero stock checkbox
        self.show_zero_stock = QCheckBox(tr("inventory.show_zero_stock", "إظهار المنتجات بدون مخزون"))
        self.show_zero_stock.setChecked(True)
        self.show_zero_stock.stateChanged.connect(self.filter_inventory)
        filter_layout.addWidget(self.show_zero_stock)

        layout.addLayout(filter_layout)

        # Create inventory table
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(6)
        self.inventory_table.setHorizontalHeaderLabels([
            tr("inventory.product_id", "رقم المنتج"),
            tr("inventory.product_name", "اسم المنتج"),
            tr("inventory.barcode", "الباركود"),
            tr("inventory.stock_quantity", "الكمية"),
            tr("inventory.min_stock_level", "الحد الأدنى"),
            tr("inventory.status", "الحالة")
        ])
        self.inventory_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.inventory_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.inventory_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.inventory_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.inventory_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.inventory_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.inventory_table.verticalHeader().setVisible(False)
        self.inventory_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.inventory_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.inventory_table.setAlternatingRowColors(True)
        self.inventory_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f5f5f5;
                border: 1px solid #0d47a1;
                border-radius: 5px;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #0d47a1;
            }
            QHeaderView::section {
                background-color: #0d47a1;
                color: white;
                padding: 5px;
                border: 1px solid #0d47a1;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.inventory_table)

        # Add summary
        summary_layout = QHBoxLayout()
        summary_layout.setContentsMargins(0, 0, 0, 0)
        summary_layout.setSpacing(20)

        # Total products
        self.total_products_label = QLabel(tr("pos.total_products", "إجمالي المنتجات: 0"))
        self.total_products_label.setStyleSheet("font-weight: bold; font-size: 12pt;")
        summary_layout.addWidget(self.total_products_label)

        # Total quantity
        self.total_quantity_label = QLabel(tr("pos.total_quantity", "إجمالي الكمية: 0"))
        self.total_quantity_label.setStyleSheet("font-weight: bold; font-size: 12pt;")
        summary_layout.addWidget(self.total_quantity_label)

        # Low stock count
        self.low_stock_label = QLabel(tr("pos.low_stock_count", "منتجات منخفضة المخزون: 0"))
        self.low_stock_label.setStyleSheet("font-weight: bold; font-size: 12pt; color: #f44336;")
        summary_layout.addWidget(self.low_stock_label)

        layout.addLayout(summary_layout)

    def setup_transactions_tab(self):
        """Set up the transactions tab."""
        # Create layout
        layout = QVBoxLayout(self.transactions_tab)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # Create filter layout
        filter_layout = QHBoxLayout()
        filter_layout.setContentsMargins(0, 0, 0, 0)
        filter_layout.setSpacing(10)

        # Date range
        filter_layout.addWidget(QLabel(tr("pos.date_range", "الفترة:")))

        self.start_date = QDateEdit()
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        self.start_date.setCalendarPopup(True)
        filter_layout.addWidget(self.start_date)

        filter_layout.addWidget(QLabel(tr("pos.to", "إلى")))

        self.end_date = QDateEdit()
        self.end_date.setDate(QDate.currentDate())
        self.end_date.setCalendarPopup(True)
        filter_layout.addWidget(self.end_date)

        # Transaction type
        filter_layout.addWidget(QLabel(tr("pos.transaction_type", "نوع الحركة:")))

        self.transaction_type = QComboBox()
        self.transaction_type.addItem(tr("pos.all_transactions", "جميع الحركات"), None)
        self.transaction_type.addItem(tr("pos.purchase", "شراء"), POSInventoryTransaction.TYPE_PURCHASE)
        self.transaction_type.addItem(tr("pos.sale", "بيع"), POSInventoryTransaction.TYPE_SALE)
        self.transaction_type.addItem(tr("pos.adjustment", "تعديل"), POSInventoryTransaction.TYPE_ADJUSTMENT)
        self.transaction_type.addItem(tr("pos.return", "مرتجع"), POSInventoryTransaction.TYPE_RETURN)
        filter_layout.addWidget(self.transaction_type)

        # Apply button
        self.apply_filter_button = QPushButton(tr("pos.apply_filter", "تطبيق"))
        self.apply_filter_button.clicked.connect(self.load_transactions)
        filter_layout.addWidget(self.apply_filter_button)

        layout.addLayout(filter_layout)

        # Create transactions table
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels([
            tr("pos.date", "التاريخ"),
            tr("pos.product", "المنتج"),
            tr("pos.transaction_type", "نوع الحركة"),
            tr("pos.quantity", "الكمية"),
            tr("pos.reference", "المرجع"),
            tr("pos.notes", "ملاحظات"),
            tr("pos.user", "المستخدم")
        ])
        self.transactions_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.transactions_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.transactions_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)
        self.transactions_table.verticalHeader().setVisible(False)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transactions_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f5f5f5;
                border: 1px solid #0d47a1;
                border-radius: 5px;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #0d47a1;
            }
            QHeaderView::section {
                background-color: #0d47a1;
                color: white;
                padding: 5px;
                border: 1px solid #0d47a1;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.transactions_table)

    def setup_low_stock_tab(self):
        """Set up the low stock tab."""
        # Create layout
        layout = QVBoxLayout(self.low_stock_tab)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(10)

        # Create low stock table
        self.low_stock_table = QTableWidget()
        self.low_stock_table.setColumnCount(6)
        self.low_stock_table.setHorizontalHeaderLabels([
            tr("pos.product_id", "رقم المنتج"),
            tr("pos.product_name", "اسم المنتج"),
            tr("pos.barcode", "الباركود"),
            tr("pos.stock_quantity", "الكمية الحالية"),
            tr("pos.min_stock_level", "الحد الأدنى"),
            tr("pos.needed", "الكمية المطلوبة")
        ])
        self.low_stock_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.low_stock_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        self.low_stock_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)
        self.low_stock_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.ResizeToContents)
        self.low_stock_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)
        self.low_stock_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)
        self.low_stock_table.verticalHeader().setVisible(False)
        self.low_stock_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.low_stock_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.low_stock_table.setAlternatingRowColors(True)
        self.low_stock_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
                alternate-background-color: #f5f5f5;
                border: 1px solid #0d47a1;
                border-radius: 5px;
                padding: 5px;
            }
            QTableWidget::item {
                padding: 5px;
            }
            QTableWidget::item:selected {
                background-color: #bbdefb;
                color: #0d47a1;
            }
            QHeaderView::section {
                background-color: #0d47a1;
                color: white;
                padding: 5px;
                border: 1px solid #0d47a1;
                font-weight: bold;
            }
        """)
        layout.addWidget(self.low_stock_table)

        # Add actions
        action_layout = QHBoxLayout()
        action_layout.setContentsMargins(0, 0, 0, 0)
        action_layout.setSpacing(10)

        # Print low stock report
        self.print_low_stock_button = QPushButton(tr("pos.print_low_stock", "طباعة تقرير المخزون المنخفض"))
        self.print_low_stock_button.setIcon(QIcon("resources/icons/print.png"))
        self.print_low_stock_button.clicked.connect(self.print_low_stock_report)
        action_layout.addWidget(self.print_low_stock_button)

        # Export low stock report
        self.export_low_stock_button = QPushButton(tr("pos.export_low_stock", "تصدير تقرير المخزون المنخفض"))
        self.export_low_stock_button.setIcon(QIcon("resources/icons/excel.png"))
        self.export_low_stock_button.clicked.connect(self.export_low_stock_report)
        action_layout.addWidget(self.export_low_stock_button)

        layout.addLayout(action_layout)

    def load_data(self):
        """Load all data for the dialog."""
        try:
            print("Loading inventory data...")
            self.load_inventory()

            print("Loading transaction data...")
            self.load_transactions()

            print("Loading low stock data...")
            self.load_low_stock()

            print("All data loaded successfully")
        except Exception as e:
            print(f"Error loading data: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.data_load_error", f"حدث خطأ أثناء تحميل البيانات: {str(e)}")
            )

    def load_inventory(self):
        """Load inventory data."""
        try:
            # Get inventory items
            print("Getting inventory items...")
            inventory_items = self.pos_manager.get_all_pos_inventory()
            print(f"Retrieved {len(inventory_items)} inventory items")

            # Clear table
            self.inventory_table.setRowCount(0)

            # Track totals
            total_products = 0
            total_quantity = 0
            low_stock_count = 0

            # Add items to table
            for item in inventory_items:
                try:
                    # Skip zero stock if not showing
                    if not self.show_zero_stock.isChecked() and item.stock_quantity <= 0:
                        continue

                    # Add row
                    row = self.inventory_table.rowCount()
                    self.inventory_table.insertRow(row)

                    # Set data
                    self.inventory_table.setItem(row, 0, QTableWidgetItem(str(item.product_id)))
                    self.inventory_table.setItem(row, 1, QTableWidgetItem(item.product_name or ""))
                    self.inventory_table.setItem(row, 2, QTableWidgetItem(item.product_barcode or ""))
                    self.inventory_table.setItem(row, 3, QTableWidgetItem(str(item.stock_quantity)))
                    self.inventory_table.setItem(row, 4, QTableWidgetItem(str(item.min_stock_level)))

                    # Set status
                    status_item = QTableWidgetItem()
                    if item.stock_quantity <= 0:
                        status_item.setText(tr("pos.out_of_stock", "نفذ المخزون"))
                        status_item.setForeground(QColor("#f44336"))  # Red
                    elif item.stock_quantity <= item.min_stock_level:
                        status_item.setText(tr("pos.low_stock", "منخفض"))
                        status_item.setForeground(QColor("#ff9800"))  # Orange
                        low_stock_count += 1
                    else:
                        status_item.setText(tr("pos.in_stock", "متوفر"))
                        status_item.setForeground(QColor("#4caf50"))  # Green

                    self.inventory_table.setItem(row, 5, status_item)

                    # Update totals
                    total_products += 1
                    total_quantity += item.stock_quantity
                except Exception as item_error:
                    print(f"Error processing inventory item: {item_error}")
                    continue

            # Update summary
            self.total_products_label.setText(tr("pos.total_products", f"إجمالي المنتجات: {total_products}"))
            self.total_quantity_label.setText(tr("pos.total_quantity", f"إجمالي الكمية: {total_quantity}"))
            self.low_stock_label.setText(tr("pos.low_stock_count", f"منتجات منخفضة المخزون: {low_stock_count}"))

        except Exception as e:
            print(f"Error loading inventory: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.inventory_load_error", f"حدث خطأ أثناء تحميل بيانات المخزون: {str(e)}")
            )

    def filter_inventory(self):
        """Filter inventory table based on search text and show zero stock setting."""
        search_text = self.inventory_search.text().lower()

        for row in range(self.inventory_table.rowCount()):
            # Get product name and quantity
            product_name = self.inventory_table.item(row, 1).text().lower()
            quantity = int(self.inventory_table.item(row, 3).text())

            # Check if row should be hidden
            hide_row = False

            # Hide based on search
            if search_text and search_text not in product_name:
                hide_row = True

            # Hide based on zero stock
            if not self.show_zero_stock.isChecked() and quantity <= 0:
                hide_row = True

            # Set row visibility
            self.inventory_table.setRowHidden(row, hide_row)

    def load_transactions(self):
        """Load transaction data."""
        try:
            # Get filter values
            start_date = self.start_date.date().toString("yyyy-MM-dd")
            end_date = self.end_date.date().toString("yyyy-MM-dd")
            transaction_type = self.transaction_type.currentData()

            print(f"Loading transactions from {start_date} to {end_date}, type: {transaction_type}")

            # Get transactions - note that the current implementation doesn't support date filtering
            # We'll get all transactions and filter them manually
            transactions = self.pos_manager.get_pos_inventory_transactions(
                limit=1000  # Limit to 1000 transactions for performance
            )

            print(f"Retrieved {len(transactions)} transactions")

            # Clear table
            self.transactions_table.setRowCount(0)

            # Add transactions to table
            for transaction in transactions:
                try:
                    # Get inventory item
                    inventory_item = self.pos_manager.get_pos_inventory_by_id(transaction.pos_inventory_id)
                    if not inventory_item:
                        print(f"Warning: No inventory item found for transaction {transaction.id}, inventory_id: {transaction.pos_inventory_id}")
                        continue

                    # Filter by date if specified
                    if transaction.created_at:
                        transaction_date = transaction.created_at.split(" ")[0] if isinstance(transaction.created_at, str) else transaction.created_at.strftime("%Y-%m-%d")
                        if transaction_date < start_date or transaction_date > end_date:
                            continue

                    # Filter by transaction type if specified
                    if transaction_type and transaction.transaction_type != transaction_type:
                        continue

                    # Add row
                    row = self.transactions_table.rowCount()
                    self.transactions_table.insertRow(row)

                    # Format date
                    if isinstance(transaction.created_at, str):
                        date_str = transaction.created_at
                    else:
                        date_str = transaction.created_at.strftime("%Y-%m-%d %H:%M")

                    # Format transaction type
                    if transaction.transaction_type == POSInventoryTransaction.TYPE_PURCHASE:
                        type_str = tr("pos.purchase", "شراء")
                        type_color = QColor("#4caf50")  # Green
                    elif transaction.transaction_type == POSInventoryTransaction.TYPE_SALE:
                        type_str = tr("pos.sale", "بيع")
                        type_color = QColor("#f44336")  # Red
                    elif transaction.transaction_type == POSInventoryTransaction.TYPE_ADJUSTMENT:
                        type_str = tr("pos.adjustment", "تعديل")
                        type_color = QColor("#ff9800")  # Orange
                    elif transaction.transaction_type == POSInventoryTransaction.TYPE_RETURN:
                        type_str = tr("pos.return", "مرتجع")
                        type_color = QColor("#2196f3")  # Blue
                    else:
                        type_str = tr("pos.unknown", "غير معروف")
                        type_color = QColor("#9e9e9e")  # Gray

                    # Format reference
                    reference = ""
                    if transaction.reference_type and transaction.reference_id:
                        reference = f"{transaction.reference_type} #{transaction.reference_id}"

                    # Set data
                    self.transactions_table.setItem(row, 0, QTableWidgetItem(date_str))
                    self.transactions_table.setItem(row, 1, QTableWidgetItem(inventory_item.product_name))

                    type_item = QTableWidgetItem(type_str)
                    type_item.setForeground(type_color)
                    self.transactions_table.setItem(row, 2, type_item)

                    self.transactions_table.setItem(row, 3, QTableWidgetItem(str(transaction.quantity)))
                    self.transactions_table.setItem(row, 4, QTableWidgetItem(reference))
                    self.transactions_table.setItem(row, 5, QTableWidgetItem(transaction.notes or ""))
                    self.transactions_table.setItem(row, 6, QTableWidgetItem(transaction.user_id or ""))
                except Exception as item_error:
                    print(f"Error processing transaction: {item_error}")
                    continue

        except Exception as e:
            print(f"Error loading transactions: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.transactions_load_error", f"حدث خطأ أثناء تحميل بيانات حركات المخزون: {str(e)}")
            )

    def load_low_stock(self):
        """Load low stock data."""
        try:
            # Get inventory items
            print("Getting inventory items for low stock report...")
            inventory_items = self.pos_manager.get_all_pos_inventory()
            print(f"Retrieved {len(inventory_items)} inventory items")

            # Clear table
            self.low_stock_table.setRowCount(0)

            # Add low stock items to table
            low_stock_count = 0
            for item in inventory_items:
                try:
                    # Skip if not low stock
                    if item.stock_quantity > item.min_stock_level:
                        continue

                    # Add row
                    row = self.low_stock_table.rowCount()
                    self.low_stock_table.insertRow(row)

                    # Calculate needed quantity
                    needed = max(0, item.min_stock_level - item.stock_quantity)

                    # Set data
                    self.low_stock_table.setItem(row, 0, QTableWidgetItem(str(item.product_id)))
                    self.low_stock_table.setItem(row, 1, QTableWidgetItem(item.product_name or ""))
                    self.low_stock_table.setItem(row, 2, QTableWidgetItem(item.product_barcode or ""))
                    self.low_stock_table.setItem(row, 3, QTableWidgetItem(str(item.stock_quantity)))
                    self.low_stock_table.setItem(row, 4, QTableWidgetItem(str(item.min_stock_level)))
                    self.low_stock_table.setItem(row, 5, QTableWidgetItem(str(needed)))

                    # Color code based on severity
                    if item.stock_quantity <= 0:
                        for col in range(6):
                            self.low_stock_table.item(row, col).setBackground(QColor("#ffebee"))  # Light red
                    elif item.stock_quantity <= item.min_stock_level * 0.5:
                        for col in range(6):
                            self.low_stock_table.item(row, col).setBackground(QColor("#fff8e1"))  # Light orange

                    low_stock_count += 1
                except Exception as item_error:
                    print(f"Error processing low stock item: {item_error}")
                    continue

            print(f"Added {low_stock_count} items to low stock report")

        except Exception as e:
            print(f"Error loading low stock: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.low_stock_load_error", f"حدث خطأ أثناء تحميل بيانات المخزون المنخفض: {str(e)}")
            )

    def export_to_excel(self):
        """Export current tab data to Excel."""
        try:
            # Get current tab
            current_tab = self.tabs.currentWidget()
            print(f"Exporting data from current tab: {current_tab}")

            # Determine which table to export
            if current_tab == self.inventory_tab:
                table = self.inventory_table
                filename = "inventory_report"
                title = tr("pos.inventory_report", "تقرير المخزون")
                print("Exporting inventory report")
            elif current_tab == self.transactions_tab:
                table = self.transactions_table
                filename = "inventory_transactions_report"
                title = tr("pos.inventory_transactions", "حركات المخزون")
                print("Exporting transactions report")
            elif current_tab == self.low_stock_tab:
                table = self.low_stock_table
                filename = "low_stock_report"
                title = tr("pos.low_stock", "المخزون المنخفض")
                print("Exporting low stock report")
            else:
                print(f"Unknown tab selected: {current_tab}")
                return

            # Get file path
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                tr("pos.export_to_excel", "تصدير إلى Excel"),
                f"{filename}_{datetime.now().strftime('%Y%m%d')}.xlsx",
                "Excel Files (*.xlsx)"
            )

            if not file_path:
                print("Export cancelled by user")
                return

            print(f"Exporting to file: {file_path}")

            # Get headers
            headers = []
            for col in range(table.columnCount()):
                headers.append(table.horizontalHeaderItem(col).text())
            print(f"Headers: {headers}")

            # Get data
            data = []
            visible_rows = 0
            for row in range(table.rowCount()):
                if table.isRowHidden(row):
                    continue

                visible_rows += 1
                row_data = []
                for col in range(table.columnCount()):
                    if table.item(row, col) is None:
                        row_data.append("")
                    else:
                        row_data.append(table.item(row, col).text())

                data.append(row_data)

            print(f"Exporting {visible_rows} rows of data")

            # Export to Excel
            export_to_excel(file_path, title, headers, data)

            QMessageBox.information(
                self,
                tr("pos.export_success", "تم التصدير"),
                tr("pos.export_success_message", f"تم تصدير البيانات بنجاح إلى {file_path}")
            )

        except Exception as e:
            print(f"Error exporting to Excel: {e}")
            import traceback
            traceback.print_exc()
            QMessageBox.warning(
                self,
                tr("errors.error", "خطأ"),
                tr("pos.export_error", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")
            )

    def print_low_stock_report(self):
        """Print low stock report."""
        # This is a placeholder for the actual printing functionality
        QMessageBox.information(
            self,
            tr("pos.print", "طباعة"),
            tr("pos.print_not_implemented", "لم يتم تنفيذ وظيفة الطباعة بعد")
        )

    def export_low_stock_report(self):
        """Export low stock report to Excel."""
        # Switch to low stock tab and use the general export function
        self.tabs.setCurrentWidget(self.low_stock_tab)
        self.export_to_excel()
