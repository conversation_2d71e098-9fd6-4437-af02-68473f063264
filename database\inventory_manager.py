#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Inventory Manager for فوترها (Fawterha)
Handles database operations for inventory
"""

from models.inventory_transaction import InventoryTransaction
from models.product import Product

class InventoryManager:
    """Manager for inventory database operations."""

    def __init__(self, db_manager):
        """Initialize the inventory manager.

        Args:
            db_manager: Database manager instance
        """
        self.db_manager = db_manager

    def get_product_stock(self, product_id):
        """Get the current stock quantity for a product.

        Args:
            product_id (int): Product ID

        Returns:
            int: Current stock quantity
        """
        query = """
        SELECT stock_quantity FROM products
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (product_id,))
        return rows[0]['stock_quantity'] if rows else 0

    def update_product_stock(self, product_id, new_quantity):
        """Update the stock quantity for a product.

        Args:
            product_id (int): Product ID
            new_quantity (int): New stock quantity

        Returns:
            bool: True if successful, False otherwise
        """
        query = """
        UPDATE products
        SET stock_quantity = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        return self.db_manager.execute_update(query, (new_quantity, product_id)) > 0

    def add_transaction(self, transaction):
        """Add a new inventory transaction.

        Args:
            transaction (InventoryTransaction): Transaction to add

        Returns:
            int: ID of the new transaction
        """
        try:
            # First, get current stock
            current_stock = self.get_product_stock(transaction.product_id)

            # Calculate new stock based on transaction type
            if transaction.transaction_type in [InventoryTransaction.TYPE_PURCHASE, InventoryTransaction.TYPE_RETURN]:
                # For purchases and returns, add to stock
                new_stock = current_stock + transaction.quantity
            elif transaction.transaction_type == InventoryTransaction.TYPE_SALE:
                # For sales, subtract from stock
                new_stock = current_stock - transaction.quantity
            elif transaction.transaction_type == InventoryTransaction.TYPE_ADJUSTMENT:
                # For adjustments, the quantity can be positive (add) or negative (subtract)
                # If quantity is positive, it means we're adding to stock
                # If quantity is negative, it means we're subtracting from stock
                new_stock = current_stock + transaction.quantity

            # Ensure stock doesn't go below zero
            new_stock = max(0, new_stock)

            # Insert transaction using db_manager
            query = """
            INSERT INTO inventory_transactions (
                product_id, transaction_type, quantity,
                reference_type, reference_id, notes
            ) VALUES (?, ?, ?, ?, ?, ?)
            """
            params = (
                transaction.product_id,
                transaction.transaction_type,
                transaction.quantity,
                transaction.reference_type,
                transaction.reference_id,
                transaction.notes
            )
            transaction_id = self.db_manager.execute_insert(query, params)

            # Update product stock
            self.update_product_stock(transaction.product_id, new_stock)

            return transaction_id
        except Exception as e:
            print(f"Error in add_transaction: {str(e)}")
            raise e

    def get_transactions(self, product_id=None, transaction_type=None, limit=100):
        """Get inventory transactions.

        Args:
            product_id (int, optional): Filter by product ID. Defaults to None.
            transaction_type (str, optional): Filter by transaction type. Defaults to None.
            limit (int, optional): Maximum number of transactions to return. Defaults to 100.

        Returns:
            list: List of InventoryTransaction objects
        """
        query = """
        SELECT t.*, p.name as product_name
        FROM inventory_transactions t
        JOIN products p ON t.product_id = p.id
        """

        params = []
        where_clauses = []

        if product_id:
            where_clauses.append("t.product_id = ?")
            params.append(product_id)

        if transaction_type:
            where_clauses.append("t.transaction_type = ?")
            params.append(transaction_type)

        if where_clauses:
            query += " WHERE " + " AND ".join(where_clauses)

        query += " ORDER BY t.created_at DESC LIMIT ?"
        params.append(limit)

        rows = self.db_manager.execute_query(query, tuple(params))
        return [InventoryTransaction.from_db_row(row) for row in rows]

    def get_low_stock_products(self, limit=20):
        """Get products with stock below minimum level.

        Args:
            limit (int, optional): Maximum number of products to return. Defaults to 20.

        Returns:
            list: List of Product objects
        """
        query = """
        SELECT *
        FROM products
        WHERE type = 'product' AND track_inventory = 1 AND stock_quantity <= min_stock_level
        ORDER BY (min_stock_level - stock_quantity) DESC
        LIMIT ?
        """

        rows = self.db_manager.execute_query(query, (limit,))
        return [Product.from_db_row(row) for row in rows]

    def update_stock(self, product_id, quantity, notes=""):
        """Update stock for a product by creating a transaction.

        Args:
            product_id (int): Product ID
            quantity (int): Quantity to add (positive) or subtract (negative)
            notes (str, optional): Transaction notes. Defaults to "".

        Returns:
            int: ID of the new transaction
        """
        # Determine transaction type based on quantity
        if quantity > 0:
            transaction_type = InventoryTransaction.TYPE_PURCHASE
        else:
            transaction_type = InventoryTransaction.TYPE_SALE
            # Make quantity positive for the transaction record
            # (the sign is determined by the transaction type)
            quantity = abs(quantity)

        # Create transaction object
        transaction = InventoryTransaction(
            product_id=product_id,
            transaction_type=transaction_type,
            quantity=quantity,
            notes=notes
        )

        # Add transaction (this will also update the product stock)
        return self.add_transaction(transaction)

    def check_product_availability(self, product_id, quantity):
        """Check if a product is available in the requested quantity.

        Args:
            product_id (int): Product ID
            quantity (int): Requested quantity

        Returns:
            bool: True if available, False otherwise
        """
        # Get product
        query = """
        SELECT * FROM products
        WHERE id = ?
        """
        rows = self.db_manager.execute_query(query, (product_id,))

        if not rows:
            return False

        product = Product.from_db_row(rows[0])

        # If product is a service or inventory tracking is disabled, always return True
        if product.type == 'service' or not product.track_inventory:
            return True

        # Check if stock is sufficient
        return product.stock_quantity >= quantity
