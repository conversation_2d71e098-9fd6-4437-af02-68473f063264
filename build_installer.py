#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
فوترها - نظام إدارة الفواتير
ملف إنشاء حزمة تثبيت للويندوز
"""

import os
import sys
import subprocess

def build_installer():
    """إنشاء حزمة تثبيت للويندوز باستخدام PyInstaller."""
    print("جاري إنشاء حزمة تثبيت لتطبيق فوترها...")
    
    # التأكد من تثبيت PyInstaller
    try:
        import PyInstaller
    except ImportError:
        print("تثبيت PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
    
    # إنشاء ملف spec
    spec_content = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('resources', 'resources'),
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)
pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='فوترها',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='resources/icons/logo.ico',
)
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='فوترها',
)
    """
    
    with open("fawterha.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    # إنشاء الحزمة
    subprocess.check_call([
        sys.executable, "-m", "PyInstaller", "fawterha.spec",
        "--noconfirm", "--clean"
    ])
    
    print("تم إنشاء حزمة التثبيت بنجاح!")
    print("يمكنك العثور على الملفات في مجلد 'dist/فوترها'")

if __name__ == "__main__":
    build_installer()
