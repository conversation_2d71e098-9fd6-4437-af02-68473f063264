#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WeasyPrint PDF Generator for فوترها (Fawterha)
Generates PDF files for invoices and reports using WeasyPrint
"""

import os
import tempfile
from datetime import datetime
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration

# HTML template for customer statement
CUSTOMER_STATEMENT_TEMPLATE = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>كشف حساب العميل</title>
    <style>
        @page {
            margin: 1cm;
            @top-center {
                content: "كشف حساب العميل";
                font-family: 'Traditional Arabic', Arial, sans-serif;
                font-size: 12pt;
            }
            @bottom-center {
                content: "صفحة " counter(page) " من " counter(pages);
                font-family: 'Traditional Arabic', Arial, sans-serif;
                font-size: 10pt;
            }
        }
        
        body {
            font-family: 'Traditional Arabic', Arial, sans-serif;
            margin: 0;
            padding: 0;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
            font-size: 14pt;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            border-bottom: 2px solid #000066;
        }
        
        .title {
            font-size: 24pt;
            font-weight: bold;
            color: #000066;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 18pt;
            color: #000066;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .info {
            margin-bottom: 8px;
            font-size: 14pt;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            border: 2px solid #000066;
        }
        
        th {
            background-color: #000066;
            color: white;
            padding: 12px;
            text-align: center;
            font-weight: bold;
            font-size: 14pt;
            border: 1px solid #fff;
        }
        
        td {
            padding: 10px;
            text-align: center;
            border: 1px solid #aaa;
            font-size: 14pt;
        }
        
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        tr.even {
            background-color: #f2f2f2;
        }
        
        tr.odd {
            background-color: #ffffff;
        }
        
        td.amount {
            font-weight: bold;
            color: #000066;
            direction: ltr;
            text-align: left;
        }
        
        .summary {
            margin-top: 25px;
            font-weight: bold;
            font-size: 16pt;
            color: #000066;
            border-top: 1px solid #000066;
            padding-top: 15px;
        }
        
        .summary div {
            margin-bottom: 8px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12pt;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">كشف حساب العميل</div>
    </div>
    
    <div class="info">
        <div class="subtitle">العميل: {customer_name}</div>
        <div class="info">الفترة: من {start_date} إلى {end_date}</div>
        <div class="info">تاريخ التقرير: {report_date}</div>
        {customer_email}
        {customer_phone}
        {customer_address}
    </div>
    
    <table>
        <thead>
            <tr>
                {header_cells}
            </tr>
        </thead>
        <tbody>
            {row_cells}
        </tbody>
    </table>
    
    <div class="summary">
        <div>إجمالي الفواتير: {total_invoices}</div>
        <div>إجمالي المدفوعات: {total_paid}</div>
        <div>الرصيد الحالي: {balance}</div>
    </div>
    
    <div class="footer">
        تم إنشاء هذا التقرير بواسطة تطبيق فوترها - {timestamp}
    </div>
</body>
</html>
"""

def generate_customer_statement_pdf(file_path, customer, start_date, end_date, headers, rows, total_invoices, total_paid, balance):
    """Generate a PDF file for a customer statement using WeasyPrint.
    
    Args:
        file_path (str): Path to save the PDF file
        customer (Customer): Customer object
        start_date (str): Start date
        end_date (str): End date
        headers (list): Column headers
        rows (list): Statement data rows
        total_invoices (str): Total invoices amount
        total_paid (str): Total paid amount
        balance (str): Current balance
    """
    try:
        # Format customer information
        customer_email = f'<div class="info">البريد الإلكتروني: {customer.email}</div>' if customer.email else ''
        customer_phone = f'<div class="info">الهاتف: {customer.phone}</div>' if customer.phone else ''
        customer_address = f'<div class="info">العنوان: {customer.address}</div>' if customer.address else ''
        
        # Format headers
        header_cells = ''.join([f'<th>{header}</th>' for header in headers])
        
        # Format rows with improved styling
        row_cells = ''
        for i, row in enumerate(rows):
            # Add alternating row classes
            row_class = 'even' if i % 2 == 0 else 'odd'
            row_cells += f'<tr class="{row_class}">'
            
            for j, cell in enumerate(row):
                # Add special styling for amount columns (columns 4, 5, 6)
                cell_class = ''
                if j in [4, 5, 6]:  # Amount columns
                    cell_class = 'amount'
                    
                row_cells += f'<td class="{cell_class}">{cell}</td>'
            row_cells += '</tr>'
        
        # Fill template
        html_content = CUSTOMER_STATEMENT_TEMPLATE.format(
            customer_name=customer.name,
            start_date=start_date,
            end_date=end_date,
            report_date=datetime.now().strftime('%Y-%m-%d'),
            customer_email=customer_email,
            customer_phone=customer_phone,
            customer_address=customer_address,
            header_cells=header_cells,
            row_cells=row_cells,
            total_invoices=total_invoices,
            total_paid=total_paid,
            balance=balance,
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M')
        )
        
        # Save HTML file for debugging (optional)
        debug_html_path = file_path.replace('.pdf', '.html')
        with open(debug_html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        # Configure fonts
        font_config = FontConfiguration()
        
        # Create PDF
        HTML(string=html_content).write_pdf(
            file_path,
            font_config=font_config
        )
        
        return file_path
    except Exception as e:
        print(f"Error generating customer statement PDF: {str(e)}")
        import traceback
        traceback.print_exc()
        raise
