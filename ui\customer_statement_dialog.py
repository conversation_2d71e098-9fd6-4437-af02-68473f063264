#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Customer Statement Dialog for فوترها (Fawterha)
Displays detailed account statement for a customer
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QTableWidget, QTableWidgetItem, QDateEdit, QComboBox,
    QHeaderView, QAbstractItemView, QMessageBox, QFileDialog
)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QColor, QIcon
from datetime import datetime
import os

from models.customer import Customer
from utils.currency_helper import format_currency
from utils.excel_exporter import export_customer_statement_to_excel
from utils.arabic_pdf_generator import generate_customer_statement_pdf
from utils.translation_manager import tr


class CustomerStatementDialog(QDialog):
    """Dialog for displaying customer account statement."""

    def __init__(self, parent=None, db_manager=None, customer_id=None, currency_manager=None):
        """Initialize the customer statement dialog.

        Args:
            parent: Parent widget
            db_manager: Database manager instance
            customer_id: Customer ID
            currency_manager: Currency manager instance
        """
        super().__init__(parent)

        self.db_manager = db_manager
        self.customer_id = customer_id
        self.currency_manager = currency_manager
        self.customer = None
        self.primary_currency = None

        # Get primary currency
        if self.currency_manager:
            self.primary_currency = self.currency_manager.get_primary_currency()

        # Load customer data
        self.load_customer()

        # Set window properties
        self.setWindowTitle(tr("customers.account_statement_title", f"كشف حساب العميل: {self.customer.name if self.customer else ''}"))
        self.setMinimumSize(900, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # Create layout
        self.setup_ui()

        # Load transactions
        self.load_transactions()

    def load_customer(self):
        """Load customer data from database."""
        if not self.customer_id or not self.db_manager:
            return

        query = "SELECT * FROM customers WHERE id = ?"
        rows = self.db_manager.execute_query(query, (self.customer_id,))

        if rows:
            self.customer = Customer.from_db_row(rows[0])

    def setup_ui(self):
        """Set up the user interface."""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(15)

        # Customer info section
        info_layout = QHBoxLayout()
        layout.addLayout(info_layout)

        if self.customer:
            customer_name = QLabel(f"<h2>{self.customer.name}</h2>")
            info_layout.addWidget(customer_name)

            customer_details = QLabel(
                f"{tr('customers.email', 'البريد الإلكتروني')}: {self.customer.email or tr('common.not_available', 'غير متوفر')}<br>"
                f"{tr('customers.phone', 'الهاتف')}: {self.customer.phone or tr('common.not_available', 'غير متوفر')}<br>"
                f"{tr('customers.address', 'العنوان')}: {self.customer.address or tr('common.not_available', 'غير متوفر')}"
            )
            info_layout.addWidget(customer_details)
            info_layout.addStretch()

        # Filter section
        filter_layout = QHBoxLayout()
        layout.addLayout(filter_layout)

        # Date range filter
        date_label = QLabel(tr("common.period", "الفترة:"))
        filter_layout.addWidget(date_label)

        self.start_date_edit = QDateEdit()
        self.start_date_edit.setCalendarPopup(True)
        self.start_date_edit.setDate(QDate.currentDate().addMonths(-3))  # Last 3 months by default
        self.start_date_edit.dateChanged.connect(self.load_transactions)
        filter_layout.addWidget(self.start_date_edit)

        to_label = QLabel(tr("common.to", "إلى:"))
        filter_layout.addWidget(to_label)

        self.end_date_edit = QDateEdit()
        self.end_date_edit.setCalendarPopup(True)
        self.end_date_edit.setDate(QDate.currentDate())
        self.end_date_edit.dateChanged.connect(self.load_transactions)
        filter_layout.addWidget(self.end_date_edit)

        # Transaction type filter
        type_label = QLabel(tr("customers.transaction_type", "نوع المعاملة:"))
        filter_layout.addWidget(type_label)

        self.type_combo = QComboBox()
        self.type_combo.addItem(tr("common.all", "الكل"), "all")
        self.type_combo.addItem(tr("invoices.invoices", "الفواتير"), "invoices")
        self.type_combo.addItem(tr("customers.payments", "المدفوعات"), "payments")
        self.type_combo.currentIndexChanged.connect(self.load_transactions)
        filter_layout.addWidget(self.type_combo)

        filter_layout.addStretch()

        # Export buttons
        export_pdf_button = QPushButton(tr("reports.export_to_pdf", "تصدير PDF"))
        export_pdf_button.setIcon(QIcon("resources/icons/pdf.png") if os.path.exists("resources/icons/pdf.png") else QIcon())
        export_pdf_button.clicked.connect(self.export_to_pdf)
        filter_layout.addWidget(export_pdf_button)

        export_excel_button = QPushButton(tr("reports.export_to_excel", "تصدير Excel"))
        export_excel_button.setIcon(QIcon("resources/icons/excel.png") if os.path.exists("resources/icons/excel.png") else QIcon())
        export_excel_button.clicked.connect(self.export_to_excel)
        filter_layout.addWidget(export_excel_button)

        # Transactions table
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels([
            tr("common.date", "التاريخ"),
            tr("customers.reference_number", "رقم المرجع"),
            tr("customers.transaction_type", "نوع المعاملة"),
            tr("customers.description", "البيان"),
            tr("customers.debit", "مدين"),
            tr("customers.credit", "دائن"),
            tr("customers.balance", "الرصيد")
        ])

        # Set column widths
        self.transactions_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # Date
        self.transactions_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.ResizeToContents)  # Reference
        self.transactions_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.ResizeToContents)  # Type
        self.transactions_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Stretch)          # Description
        self.transactions_table.horizontalHeader().setSectionResizeMode(4, QHeaderView.ResizeToContents)  # Debit
        self.transactions_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.ResizeToContents)  # Credit
        self.transactions_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.ResizeToContents)  # Balance

        self.transactions_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.transactions_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.verticalHeader().setVisible(False)
        self.transactions_table.setShowGrid(True)
        self.transactions_table.verticalHeader().setDefaultSectionSize(40)  # Row height
        layout.addWidget(self.transactions_table)

        # Summary section
        summary_layout = QHBoxLayout()
        layout.addLayout(summary_layout)

        self.total_invoices_label = QLabel(tr("customers.total_invoices", "إجمالي الفواتير: 0"))
        self.total_invoices_label.setStyleSheet("font-weight: bold;")
        summary_layout.addWidget(self.total_invoices_label)

        self.total_paid_label = QLabel(tr("customers.total_payments", "إجمالي المدفوعات: 0"))
        self.total_paid_label.setStyleSheet("font-weight: bold; color: #2E7D32;")  # Green
        summary_layout.addWidget(self.total_paid_label)

        self.balance_label = QLabel(tr("customers.current_balance", "الرصيد الحالي: 0"))
        self.balance_label.setStyleSheet("font-weight: bold; font-size: 14px; color: #0D47A1;")  # Blue
        summary_layout.addWidget(self.balance_label)

        summary_layout.addStretch()

        # Close button
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)

        button_layout.addStretch()
        close_button = QPushButton(tr("common.close", "إغلاق"))
        close_button.clicked.connect(self.accept)
        close_button.setMinimumWidth(120)
        button_layout.addWidget(close_button)

    def load_transactions(self):
        """Load customer transactions from database."""
        if not self.customer_id or not self.db_manager:
            return

        # Get date range
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Get transaction type
        transaction_type = self.type_combo.currentData()

        # Clear table
        self.transactions_table.setRowCount(0)

        # Get invoices
        invoices = []
        if transaction_type in ["all", "invoices"]:
            invoice_query = """
            SELECT i.*, 'invoice' as transaction_type
            FROM invoices i
            WHERE i.customer_id = ? AND i.issue_date BETWEEN ? AND ?
            ORDER BY i.issue_date
            """
            invoice_rows = self.db_manager.execute_query(invoice_query, (self.customer_id, start_date, end_date))
            invoices = [dict(row) for row in invoice_rows]

        # Combine and sort transactions by date
        transactions = sorted(invoices, key=lambda x: x['issue_date'])

        # Calculate running balance
        balance = 0
        total_invoices = 0
        total_paid = 0

        # Add transactions to table
        for transaction in transactions:
            row_position = self.transactions_table.rowCount()
            self.transactions_table.insertRow(row_position)

            # Date
            date_item = QTableWidgetItem(transaction['issue_date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row_position, 0, date_item)

            # Reference number
            ref_item = QTableWidgetItem(transaction['invoice_number'])
            ref_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row_position, 1, ref_item)

            # Transaction type
            type_text = self.get_transaction_type_display(transaction['transaction_type'], transaction.get('status'))
            type_item = QTableWidgetItem(type_text)
            type_item.setTextAlignment(Qt.AlignCenter)
            self.transactions_table.setItem(row_position, 2, type_item)

            # Description
            description = tr("customers.invoice_number", f"فاتورة رقم {transaction['invoice_number']}")
            if transaction.get('status') == 'cancelled':
                description += f" ({tr('invoices.cancelled', 'ملغاة')})"
            desc_item = QTableWidgetItem(description)
            self.transactions_table.setItem(row_position, 3, desc_item)

            # Convert amount to primary currency if needed
            amount = float(transaction['total']) if transaction['total'] is not None else 0
            if self.currency_manager and self.primary_currency and transaction.get('currency_id') and transaction['currency_id'] != self.primary_currency.id:
                amount = self.currency_manager.convert_amount(
                    amount, transaction['currency_id'], self.primary_currency.id
                )

            # Debit (invoice amount)
            if transaction['transaction_type'] == 'invoice' and transaction.get('status') != 'cancelled':
                debit_amount = amount
                total_invoices += debit_amount
                balance += debit_amount
                debit_item = QTableWidgetItem(format_currency(debit_amount, self.primary_currency.symbol if self.primary_currency else ""))
                debit_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                debit_item.setForeground(QColor(183, 28, 28))  # Dark red
                self.transactions_table.setItem(row_position, 4, debit_item)
                self.transactions_table.setItem(row_position, 5, QTableWidgetItem(""))  # Empty credit
            else:
                self.transactions_table.setItem(row_position, 4, QTableWidgetItem(""))  # Empty debit

            # Credit (payment amount)
            if transaction['transaction_type'] == 'invoice' and transaction.get('status') != 'cancelled':
                paid_amount = float(transaction['amount_paid']) if transaction['amount_paid'] is not None else 0
                if self.currency_manager and self.primary_currency and transaction.get('currency_id') and transaction['currency_id'] != self.primary_currency.id:
                    paid_amount = self.currency_manager.convert_amount(
                        paid_amount, transaction['currency_id'], self.primary_currency.id
                    )

                if paid_amount > 0:
                    total_paid += paid_amount
                    balance -= paid_amount
                    credit_item = QTableWidgetItem(format_currency(paid_amount, self.primary_currency.symbol if self.primary_currency else ""))
                    credit_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                    credit_item.setForeground(QColor(27, 94, 32))  # Dark green
                    self.transactions_table.setItem(row_position, 5, credit_item)

            # Balance
            balance_item = QTableWidgetItem(format_currency(balance, self.primary_currency.symbol if self.primary_currency else ""))
            balance_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            if balance > 0:
                balance_item.setForeground(QColor(183, 28, 28))  # Dark red
            elif balance < 0:
                balance_item.setForeground(QColor(27, 94, 32))  # Dark green
            self.transactions_table.setItem(row_position, 6, balance_item)

        # Update summary labels
        self.total_invoices_label.setText(tr("customers.total_invoices_value", f"إجمالي الفواتير: {format_currency(total_invoices, self.primary_currency.symbol if self.primary_currency else '')}"))
        self.total_paid_label.setText(tr("customers.total_payments_value", f"إجمالي المدفوعات: {format_currency(total_paid, self.primary_currency.symbol if self.primary_currency else '')}"))
        self.balance_label.setText(tr("customers.current_balance_value", f"الرصيد الحالي: {format_currency(balance, self.primary_currency.symbol if self.primary_currency else '')}"))

    def get_transaction_type_display(self, transaction_type, status=None):
        """Get display text for transaction type.

        Args:
            transaction_type (str): Transaction type
            status (str, optional): Status for invoices

        Returns:
            str: Display text
        """
        if transaction_type == 'invoice':
            if status == 'paid':
                return tr("invoices.paid_invoice", "فاتورة (مدفوعة)")
            elif status == 'partially_paid':
                return tr("invoices.partially_paid_invoice", "فاتورة (مدفوعة جزئياً)")
            elif status == 'cancelled':
                return tr("invoices.cancelled_invoice", "فاتورة (ملغاة)")
            else:
                return tr("invoices.invoice", "فاتورة")
        elif transaction_type == 'payment':
            return tr("customers.payment", "دفعة")
        else:
            return transaction_type

    def export_to_pdf(self):
        """Export customer statement to PDF."""
        if not self.customer:
            return

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, tr("customers.save_statement_as_pdf", "حفظ كشف الحساب كملف PDF"),
            f"كشف_حساب_{self.customer.name}_{datetime.now().strftime('%Y%m%d')}.pdf",
            tr("common.pdf_files", "PDF Files (*.pdf)")
        )

        if not file_path:
            return

        # Get data for export
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Prepare data for PDF generator
        headers = [
            tr("common.date", "التاريخ"),
            tr("customers.reference_number", "رقم المرجع"),
            tr("customers.transaction_type", "نوع المعاملة"),
            tr("customers.description", "البيان"),
            tr("customers.debit", "مدين"),
            tr("customers.credit", "دائن"),
            tr("customers.balance", "الرصيد")
        ]
        rows = []

        for row in range(self.transactions_table.rowCount()):
            row_data = []
            for col in range(self.transactions_table.columnCount()):
                item = self.transactions_table.item(row, col)
                row_data.append(item.text() if item else "")
            rows.append(row_data)

        # Get summary data
        total_invoices = self.total_invoices_label.text().split(": ")[1]
        total_paid = self.total_paid_label.text().split(": ")[1]
        balance = self.balance_label.text().split(": ")[1]

        # Generate PDF
        try:
            generate_customer_statement_pdf(
                file_path,
                self.customer,
                start_date,
                end_date,
                headers,
                rows,
                total_invoices,
                total_paid,
                balance
            )
            QMessageBox.information(self, tr("messages.export_success", "تم التصدير"), tr("customers.statement_export_success", f"تم تصدير كشف الحساب إلى {file_path} بنجاح"))
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("customers.statement_export_error", f"حدث خطأ أثناء تصدير كشف الحساب: {str(e)}"))

    def export_to_excel(self):
        """Export customer statement to Excel."""
        if not self.customer:
            return

        # Get file path
        file_path, _ = QFileDialog.getSaveFileName(
            self, tr("customers.save_statement_as_excel", "حفظ كشف الحساب كملف Excel"),
            f"كشف_حساب_{self.customer.name}_{datetime.now().strftime('%Y%m%d')}.xlsx",
            tr("common.excel_files", "Excel Files (*.xlsx)")
        )

        if not file_path:
            return

        # Get data for export
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # Prepare data for Excel exporter
        headers = [
            tr("common.date", "التاريخ"),
            tr("customers.reference_number", "رقم المرجع"),
            tr("customers.transaction_type", "نوع المعاملة"),
            tr("customers.description", "البيان"),
            tr("customers.debit", "مدين"),
            tr("customers.credit", "دائن"),
            tr("customers.balance", "الرصيد")
        ]
        rows = []

        for row in range(self.transactions_table.rowCount()):
            row_data = []
            for col in range(self.transactions_table.columnCount()):
                item = self.transactions_table.item(row, col)
                row_data.append(item.text() if item else "")
            rows.append(row_data)

        # Get summary data
        total_invoices = self.total_invoices_label.text().split(": ")[1]
        total_paid = self.total_paid_label.text().split(": ")[1]
        balance = self.balance_label.text().split(": ")[1]

        # Export to Excel
        try:
            export_customer_statement_to_excel(
                file_path,
                self.customer,
                start_date,
                end_date,
                headers,
                rows,
                total_invoices,
                total_paid,
                balance
            )
            QMessageBox.information(self, tr("messages.export_success", "تم التصدير"), tr("customers.statement_export_success", f"تم تصدير كشف الحساب إلى {file_path} بنجاح"))
        except Exception as e:
            QMessageBox.critical(self, tr("messages.error", "خطأ"), tr("customers.statement_export_error", f"حدث خطأ أثناء تصدير كشف الحساب: {str(e)}"))
